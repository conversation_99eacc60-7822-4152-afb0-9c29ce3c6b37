{"ast": null, "code": "import { useEffect as $Vsl8o$useEffect } from \"react\";\nfunction $9daab02d461809db$var$hasResizeObserver() {\n  return typeof window.ResizeObserver !== 'undefined';\n}\nfunction $9daab02d461809db$export$683480f191c0e3ea(options) {\n  const {\n    ref: ref,\n    box: box,\n    onResize: onResize\n  } = options;\n  (0, $Vsl8o$useEffect)(() => {\n    let element = ref === null || ref === void 0 ? void 0 : ref.current;\n    if (!element) return;\n    if (!$9daab02d461809db$var$hasResizeObserver()) {\n      window.addEventListener('resize', onResize, false);\n      return () => {\n        window.removeEventListener('resize', onResize, false);\n      };\n    } else {\n      const resizeObserverInstance = new window.ResizeObserver(entries => {\n        if (!entries.length) return;\n        onResize();\n      });\n      resizeObserverInstance.observe(element, {\n        box: box\n      });\n      return () => {\n        if (element) resizeObserverInstance.unobserve(element);\n      };\n    }\n  }, [onResize, ref, box]);\n}\nexport { $9daab02d461809db$export$683480f191c0e3ea as useResizeObserver };", "map": {"version": 3, "names": ["$9daab02d461809db$var$hasResizeObserver", "window", "ResizeObserver", "$9daab02d461809db$export$683480f191c0e3ea", "options", "ref", "box", "onResize", "$Vsl8o$useEffect", "element", "current", "addEventListener", "removeEventListener", "resizeObserverInstance", "entries", "length", "observe", "unobserve"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useResizeObserver.ts"], "sourcesContent": ["\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\nfunction hasResizeObserver() {\n  return typeof window.ResizeObserver !== 'undefined';\n}\n\ntype useResizeObserverOptionsType<T> = {\n  ref: RefObject<T | undefined | null> | undefined,\n  box?: ResizeObserverBoxOptions,\n  onResize: () => void\n}\n\nexport function useResizeObserver<T extends Element>(options: useResizeObserverOptionsType<T>): void {\n  const {ref, box, onResize} = options;\n\n  useEffect(() => {\n    let element = ref?.current;\n    if (!element) {\n      return;\n    }\n\n    if (!hasResizeObserver()) {\n      window.addEventListener('resize', onResize, false);\n      return () => {\n        window.removeEventListener('resize', onResize, false);\n      };\n    } else {\n\n      const resizeObserverInstance = new window.ResizeObserver((entries) => {\n        if (!entries.length) {\n          return;\n        }\n\n        onResize();\n      });\n      resizeObserverInstance.observe(element, {box});\n\n      return () => {\n        if (element) {\n          resizeObserverInstance.unobserve(element);\n        }\n      };\n    }\n\n  }, [onResize, ref, box]);\n}\n"], "mappings": ";AAIA,SAASA,wCAAA;EACP,OAAO,OAAOC,MAAA,CAAOC,cAAc,KAAK;AAC1C;AAQO,SAASC,0CAAqCC,OAAwC;EAC3F,MAAM;IAAAC,GAAA,EAACA,GAAG;IAAAC,GAAA,EAAEA,GAAG;IAAAC,QAAA,EAAEA;EAAQ,CAAC,GAAGH,OAAA;EAE7B,IAAAI,gBAAQ,EAAE;IACR,IAAIC,OAAA,GAAUJ,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAKK,OAAO;IAC1B,IAAI,CAACD,OAAA,EACH;IAGF,IAAI,CAACT,uCAAA,IAAqB;MACxBC,MAAA,CAAOU,gBAAgB,CAAC,UAAUJ,QAAA,EAAU;MAC5C,OAAO;QACLN,MAAA,CAAOW,mBAAmB,CAAC,UAAUL,QAAA,EAAU;MACjD;IACF,OAAO;MAEL,MAAMM,sBAAA,GAAyB,IAAIZ,MAAA,CAAOC,cAAc,CAAEY,OAAA;QACxD,IAAI,CAACA,OAAA,CAAQC,MAAM,EACjB;QAGFR,QAAA;MACF;MACAM,sBAAA,CAAuBG,OAAO,CAACP,OAAA,EAAS;aAACH;MAAG;MAE5C,OAAO;QACL,IAAIG,OAAA,EACFI,sBAAA,CAAuBI,SAAS,CAACR,OAAA;MAErC;IACF;EAEF,GAAG,CAACF,QAAA,EAAUF,GAAA,EAAKC,GAAA,CAAI;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}