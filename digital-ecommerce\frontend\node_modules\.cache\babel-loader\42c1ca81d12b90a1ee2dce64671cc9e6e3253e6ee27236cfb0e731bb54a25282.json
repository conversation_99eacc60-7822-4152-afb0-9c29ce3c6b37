{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"id\"];\nimport m, { createContext as T, useContext as u, useMemo as c, useState as P } from \"react\";\nimport { useEvent as g } from '../../hooks/use-event.js';\nimport { useId as x } from '../../hooks/use-id.js';\nimport { useIsoMorphicEffect as y } from '../../hooks/use-iso-morphic-effect.js';\nimport { useSyncRefs as E } from '../../hooks/use-sync-refs.js';\nimport { useDisabled as v } from '../../internal/disabled.js';\nimport { forwardRefWithAs as R, useRender as I } from '../../utils/render.js';\nlet a = T(null);\na.displayName = \"DescriptionContext\";\nfunction f() {\n  let r = u(a);\n  if (r === null) {\n    let e = new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");\n    throw Error.captureStackTrace && Error.captureStackTrace(e, f), e;\n  }\n  return r;\n}\nfunction U() {\n  var r, e;\n  return (e = (r = u(a)) == null ? void 0 : r.value) != null ? e : void 0;\n}\nfunction w() {\n  let [r, e] = P([]);\n  return [r.length > 0 ? r.join(\" \") : void 0, c(() => function (t) {\n    let i = g(n => (e(s => [...s, n]), () => e(s => {\n        let o = s.slice(),\n          p = o.indexOf(n);\n        return p !== -1 && o.splice(p, 1), o;\n      }))),\n      l = c(() => ({\n        register: i,\n        slot: t.slot,\n        name: t.name,\n        props: t.props,\n        value: t.value\n      }), [i, t.slot, t.name, t.props, t.value]);\n    return m.createElement(a.Provider, {\n      value: l\n    }, t.children);\n  }, [e])];\n}\nlet S = \"p\";\nfunction C(r, e) {\n  let d = x(),\n    t = v(),\n    {\n      id: i = \"headlessui-description-\".concat(d)\n    } = r,\n    l = _objectWithoutProperties(r, _excluded),\n    n = f(),\n    s = E(e);\n  y(() => n.register(i), [i, n.register]);\n  let o = t || !1,\n    p = c(() => _objectSpread(_objectSpread({}, n.slot), {}, {\n      disabled: o\n    }), [n.slot, o]),\n    D = _objectSpread(_objectSpread({\n      ref: s\n    }, n.props), {}, {\n      id: i\n    });\n  return I()({\n    ourProps: D,\n    theirProps: l,\n    slot: p,\n    defaultTag: S,\n    name: n.name || \"Description\"\n  });\n}\nlet _ = R(C),\n  H = Object.assign(_, {});\nexport { H as Description, U as useDescribedBy, w as useDescriptions };", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "m", "createContext", "T", "useContext", "u", "useMemo", "c", "useState", "P", "useEvent", "g", "useId", "x", "useIsoMorphicEffect", "y", "useSyncRefs", "E", "useDisabled", "v", "forwardRefWithAs", "R", "useRender", "I", "a", "displayName", "f", "r", "e", "Error", "captureStackTrace", "U", "value", "w", "length", "join", "t", "i", "n", "s", "o", "slice", "p", "indexOf", "splice", "l", "register", "slot", "name", "props", "createElement", "Provider", "children", "S", "C", "d", "id", "concat", "disabled", "D", "ref", "ourProps", "theirProps", "defaultTag", "_", "H", "Object", "assign", "Description", "useDescribedBy", "useDescriptions"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/description/description.js"], "sourcesContent": ["\"use client\";import m,{createContext as T,useContext as u,useMemo as c,useState as P}from\"react\";import{useEvent as g}from'../../hooks/use-event.js';import{useId as x}from'../../hooks/use-id.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as E}from'../../hooks/use-sync-refs.js';import{useDisabled as v}from'../../internal/disabled.js';import{forwardRefWithAs as R,useRender as I}from'../../utils/render.js';let a=T(null);a.displayName=\"DescriptionContext\";function f(){let r=u(a);if(r===null){let e=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return r}function U(){var r,e;return(e=(r=u(a))==null?void 0:r.value)!=null?e:void 0}function w(){let[r,e]=P([]);return[r.length>0?r.join(\" \"):void 0,c(()=>function(t){let i=g(n=>(e(s=>[...s,n]),()=>e(s=>{let o=s.slice(),p=o.indexOf(n);return p!==-1&&o.splice(p,1),o}))),l=c(()=>({register:i,slot:t.slot,name:t.name,props:t.props,value:t.value}),[i,t.slot,t.name,t.props,t.value]);return m.createElement(a.Provider,{value:l},t.children)},[e])]}let S=\"p\";function C(r,e){let d=x(),t=v(),{id:i=`headlessui-description-${d}`,...l}=r,n=f(),s=E(e);y(()=>n.register(i),[i,n.register]);let o=t||!1,p=c(()=>({...n.slot,disabled:o}),[n.slot,o]),D={ref:s,...n.props,id:i};return I()({ourProps:D,theirProps:l,slot:p,defaultTag:S,name:n.name||\"Description\"})}let _=R(C),H=Object.assign(_,{});export{H as Description,U as useDescribedBy,w as useDescriptions};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AAAA,OAAOC,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAACrB,CAAC,CAAC,IAAI,CAAC;AAACqB,CAAC,CAACC,WAAW,GAAC,oBAAoB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACtB,CAAC,CAACmB,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,+EAA+E,CAAC;IAAC,MAAMA,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACF,CAAC,CAAC,EAACE,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,IAAIJ,CAAC,EAACC,CAAC;EAAC,OAAM,CAACA,CAAC,GAAC,CAACD,CAAC,GAACtB,CAAC,CAACmB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACG,CAAC,CAACK,KAAK,KAAG,IAAI,GAACJ,CAAC,GAAC,KAAK,CAAC;AAAA;AAAC,SAASK,CAACA,CAAA,EAAE;EAAC,IAAG,CAACN,CAAC,EAACC,CAAC,CAAC,GAACnB,CAAC,CAAC,EAAE,CAAC;EAAC,OAAM,CAACkB,CAAC,CAACO,MAAM,GAAC,CAAC,GAACP,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAC,KAAK,CAAC,EAAC5B,CAAC,CAAC,MAAI,UAAS6B,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC1B,CAAC,CAAC2B,CAAC,KAAGV,CAAC,CAACW,CAAC,IAAE,CAAC,GAAGA,CAAC,EAACD,CAAC,CAAC,CAAC,EAAC,MAAIV,CAAC,CAACW,CAAC,IAAE;QAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,KAAK,CAAC,CAAC;UAACC,CAAC,GAACF,CAAC,CAACG,OAAO,CAACL,CAAC,CAAC;QAAC,OAAOI,CAAC,KAAG,CAAC,CAAC,IAAEF,CAAC,CAACI,MAAM,CAACF,CAAC,EAAC,CAAC,CAAC,EAACF,CAAC;MAAA,CAAC,CAAC,CAAC,CAAC;MAACK,CAAC,GAACtC,CAAC,CAAC,OAAK;QAACuC,QAAQ,EAACT,CAAC;QAACU,IAAI,EAACX,CAAC,CAACW,IAAI;QAACC,IAAI,EAACZ,CAAC,CAACY,IAAI;QAACC,KAAK,EAACb,CAAC,CAACa,KAAK;QAACjB,KAAK,EAACI,CAAC,CAACJ;MAAK,CAAC,CAAC,EAAC,CAACK,CAAC,EAACD,CAAC,CAACW,IAAI,EAACX,CAAC,CAACY,IAAI,EAACZ,CAAC,CAACa,KAAK,EAACb,CAAC,CAACJ,KAAK,CAAC,CAAC;IAAC,OAAO/B,CAAC,CAACiD,aAAa,CAAC1B,CAAC,CAAC2B,QAAQ,EAAC;MAACnB,KAAK,EAACa;IAAC,CAAC,EAACT,CAAC,CAACgB,QAAQ,CAAC;EAAA,CAAC,EAAC,CAACxB,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIyB,CAAC,GAAC,GAAG;AAAC,SAASC,CAACA,CAAC3B,CAAC,EAACC,CAAC,EAAC;EAAC,IAAI2B,CAAC,GAAC1C,CAAC,CAAC,CAAC;IAACuB,CAAC,GAACjB,CAAC,CAAC,CAAC;IAAC;MAACqC,EAAE,EAACnB,CAAC,6BAAAoB,MAAA,CAA2BF,CAAC;IAAO,CAAC,GAAC5B,CAAC;IAAJkB,CAAC,GAAA9C,wBAAA,CAAE4B,CAAC,EAAA3B,SAAA;IAACsC,CAAC,GAACZ,CAAC,CAAC,CAAC;IAACa,CAAC,GAACtB,CAAC,CAACW,CAAC,CAAC;EAACb,CAAC,CAAC,MAAIuB,CAAC,CAACQ,QAAQ,CAACT,CAAC,CAAC,EAAC,CAACA,CAAC,EAACC,CAAC,CAACQ,QAAQ,CAAC,CAAC;EAAC,IAAIN,CAAC,GAACJ,CAAC,IAAE,CAAC,CAAC;IAACM,CAAC,GAACnC,CAAC,CAAC,MAAAT,aAAA,CAAAA,aAAA,KAASwC,CAAC,CAACS,IAAI;MAACW,QAAQ,EAAClB;IAAC,EAAE,EAAC,CAACF,CAAC,CAACS,IAAI,EAACP,CAAC,CAAC,CAAC;IAACmB,CAAC,GAAA7D,aAAA,CAAAA,aAAA;MAAE8D,GAAG,EAACrB;IAAC,GAAID,CAAC,CAACW,KAAK;MAACO,EAAE,EAACnB;IAAC,EAAC;EAAC,OAAOd,CAAC,CAAC,CAAC,CAAC;IAACsC,QAAQ,EAACF,CAAC;IAACG,UAAU,EAACjB,CAAC;IAACE,IAAI,EAACL,CAAC;IAACqB,UAAU,EAACV,CAAC;IAACL,IAAI,EAACV,CAAC,CAACU,IAAI,IAAE;EAAa,CAAC,CAAC;AAAA;AAAC,IAAIgB,CAAC,GAAC3C,CAAC,CAACiC,CAAC,CAAC;EAACW,CAAC,GAACC,MAAM,CAACC,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC,CAAC;AAAC,SAAOC,CAAC,IAAIG,WAAW,EAACrC,CAAC,IAAIsC,cAAc,EAACpC,CAAC,IAAIqC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}