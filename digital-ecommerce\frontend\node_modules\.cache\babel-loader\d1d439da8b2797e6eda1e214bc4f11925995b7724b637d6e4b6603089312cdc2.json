{"ast": null, "code": "import { useSyncExternalStoreWithSelector as a } from \"use-sync-external-store/with-selector\";\nimport { useEvent as t } from './hooks/use-event.js';\nimport { shallowEqual as o } from './machine.js';\nfunction S(e, n, r = o) {\n  return a(t(i => e.subscribe(s, i)), t(() => e.state), t(() => e.state), t(n), r);\n}\nfunction s(e) {\n  return e;\n}\nexport { S as useSlice };", "map": {"version": 3, "names": ["useSyncExternalStoreWithSelector", "a", "useEvent", "t", "shallowEqual", "o", "S", "e", "n", "r", "i", "subscribe", "s", "state", "useSlice"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/react-glue.js"], "sourcesContent": ["import{useSyncExternalStoreWithSelector as a}from\"use-sync-external-store/with-selector\";import{useEvent as t}from'./hooks/use-event.js';import{shallowEqual as o}from'./machine.js';function S(e,n,r=o){return a(t(i=>e.subscribe(s,i)),t(()=>e.state),t(()=>e.state),t(n),r)}function s(e){return e}export{S as useSlice};\n"], "mappings": "AAAA,SAAOA,gCAAgC,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,cAAc;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,GAACJ,CAAC,EAAC;EAAC,OAAOJ,CAAC,CAACE,CAAC,CAACO,CAAC,IAAEH,CAAC,CAACI,SAAS,CAACC,CAAC,EAACF,CAAC,CAAC,CAAC,EAACP,CAAC,CAAC,MAAII,CAAC,CAACM,KAAK,CAAC,EAACV,CAAC,CAAC,MAAII,CAAC,CAACM,KAAK,CAAC,EAACV,CAAC,CAACK,CAAC,CAAC,EAACC,CAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAACL,CAAC,EAAC;EAAC,OAAOA,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}