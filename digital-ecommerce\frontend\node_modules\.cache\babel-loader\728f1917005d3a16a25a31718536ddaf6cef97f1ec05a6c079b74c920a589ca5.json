{"ast": null, "code": "import { createContext as r, useContext as a, useMemo as m } from \"react\";\nimport { useOnUnmount as c } from '../../hooks/use-on-unmount.js';\nimport { ComboboxMachine as i } from './combobox-machine.js';\nconst u = r(null);\nfunction p(n) {\n  let o = a(u);\n  if (o === null) {\n    let e = new Error(\"<\".concat(n, \" /> is missing a parent <Combobox /> component.\"));\n    throw Error.captureStackTrace && Error.captureStackTrace(e, b), e;\n  }\n  return o;\n}\nfunction b(_ref) {\n  let {\n    id: n,\n    virtual: o = null,\n    __demoMode: e = !1\n  } = _ref;\n  let t = m(() => i.new({\n    id: n,\n    virtual: o,\n    __demoMode: e\n  }), []);\n  return c(() => t.dispose()), t;\n}\nexport { u as ComboboxContext, b as useComboboxMachine, p as useComboboxMachineContext };", "map": {"version": 3, "names": ["createContext", "r", "useContext", "a", "useMemo", "m", "useOnUnmount", "c", "ComboboxMachine", "i", "u", "p", "n", "o", "e", "Error", "concat", "captureStackTrace", "b", "_ref", "id", "virtual", "__demoMode", "t", "new", "dispose", "ComboboxContext", "useComboboxMachine", "useComboboxMachineContext"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/combobox/combobox-machine-glue.js"], "sourcesContent": ["import{createContext as r,useContext as a,useMemo as m}from\"react\";import{useOnUnmount as c}from'../../hooks/use-on-unmount.js';import{ComboboxMachine as i}from'./combobox-machine.js';const u=r(null);function p(n){let o=a(u);if(o===null){let e=new Error(`<${n} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,b),e}return o}function b({id:n,virtual:o=null,__demoMode:e=!1}){let t=m(()=>i.new({id:n,virtual:o,__demoMode:e}),[]);return c(()=>t.dispose()),t}export{u as ComboboxContext,b as useComboboxMachine,p as useComboboxMachineContext};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,uBAAuB;AAAC,MAAMC,CAAC,GAACT,CAAC,CAAC,IAAI,CAAC;AAAC,SAASU,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAACO,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,KAAAC,MAAA,CAAKJ,CAAC,oDAAiD,CAAC;IAAC,MAAMG,KAAK,CAACE,iBAAiB,IAAEF,KAAK,CAACE,iBAAiB,CAACH,CAAC,EAACI,CAAC,CAAC,EAACJ,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASK,CAACA,CAAAC,IAAA,EAAuC;EAAA,IAAtC;IAACC,EAAE,EAACR,CAAC;IAACS,OAAO,EAACR,CAAC,GAAC,IAAI;IAACS,UAAU,EAACR,CAAC,GAAC,CAAC;EAAC,CAAC,GAAAK,IAAA;EAAE,IAAII,CAAC,GAAClB,CAAC,CAAC,MAAII,CAAC,CAACe,GAAG,CAAC;IAACJ,EAAE,EAACR,CAAC;IAACS,OAAO,EAACR,CAAC;IAACS,UAAU,EAACR;EAAC,CAAC,CAAC,EAAC,EAAE,CAAC;EAAC,OAAOP,CAAC,CAAC,MAAIgB,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAACF,CAAC;AAAA;AAAC,SAAOb,CAAC,IAAIgB,eAAe,EAACR,CAAC,IAAIS,kBAAkB,EAAChB,CAAC,IAAIiB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}