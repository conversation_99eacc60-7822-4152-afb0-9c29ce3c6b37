{"ast": null, "code": "import n, { createContext as d, useContext as i } from \"react\";\nlet e = d(void 0);\nfunction u() {\n  return i(e);\n}\nfunction f(_ref) {\n  let {\n    id: t,\n    children: r\n  } = _ref;\n  return n.createElement(e.Provider, {\n    value: t\n  }, r);\n}\nexport { f as IdProvider, u as useProvidedId };", "map": {"version": 3, "names": ["n", "createContext", "d", "useContext", "i", "e", "u", "f", "_ref", "id", "t", "children", "r", "createElement", "Provider", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useProvidedId"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/id.js"], "sourcesContent": ["import n,{createContext as d,useContext as i}from\"react\";let e=d(void 0);function u(){return i(e)}function f({id:t,children:r}){return n.createElement(e.Provider,{value:t},r)}export{f as IdProvider,u as useProvidedId};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,KAAK,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAAAC,IAAA,EAAmB;EAAA,IAAlB;IAACC,EAAE,EAACC,CAAC;IAACC,QAAQ,EAACC;EAAC,CAAC,GAAAJ,IAAA;EAAE,OAAOR,CAAC,CAACa,aAAa,CAACR,CAAC,CAACS,QAAQ,EAAC;IAACC,KAAK,EAACL;EAAC,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIS,UAAU,EAACV,CAAC,IAAIW,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}