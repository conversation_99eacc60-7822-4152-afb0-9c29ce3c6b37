{"ast": null, "code": "import n, { createContext as r, useContext as i } from \"react\";\nlet e = r(void 0);\nfunction a() {\n  return i(e);\n}\nfunction l({\n  value: t,\n  children: o\n}) {\n  return n.createElement(e.Provider, {\n    value: t\n  }, o);\n}\nexport { l as DisabledProvider, a as useDisabled };", "map": {"version": 3, "names": ["n", "createContext", "r", "useContext", "i", "e", "a", "l", "value", "t", "children", "o", "createElement", "Provider", "Disable<PERSON><PERSON><PERSON><PERSON>", "useDisabled"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/disabled.js"], "sourcesContent": ["import n,{createContext as r,useContext as i}from\"react\";let e=r(void 0);function a(){return i(e)}function l({value:t,children:o}){return n.createElement(e.Provider,{value:t},o)}export{l as DisabledProvider,a as useDisabled};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,KAAK,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAAC;EAACC,KAAK,EAACC,CAAC;EAACC,QAAQ,EAACC;AAAC,CAAC,EAAC;EAAC,OAAOX,CAAC,CAACY,aAAa,CAACP,CAAC,CAACQ,QAAQ,EAAC;IAACL,KAAK,EAACC;EAAC,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIO,gBAAgB,EAACR,CAAC,IAAIS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}