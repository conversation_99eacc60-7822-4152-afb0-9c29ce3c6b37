{"ast": null, "code": "import { isFocusVisible as $isWE5$isFocusVisible, useFocusVisibleListener as $isWE5$useFocusVisibleListener, useFocus as $isWE5$useFocus, useFocusWithin as $isWE5$useFocusWithin } from \"@react-aria/interactions\";\nimport { useRef as $isWE5$useRef, useState as $isWE5$useState, useCallback as $isWE5$useCallback } from \"react\";\nfunction $f7dceffc5ad7768b$export$4e328f61c538687f() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let {\n    autoFocus = false,\n    isTextInput: isTextInput,\n    within: within\n  } = props;\n  let state = (0, $isWE5$useRef)({\n    isFocused: false,\n    isFocusVisible: autoFocus || (0, $isWE5$isFocusVisible)()\n  });\n  let [isFocused, setFocused] = (0, $isWE5$useState)(false);\n  let [isFocusVisibleState, setFocusVisible] = (0, $isWE5$useState)(() => state.current.isFocused && state.current.isFocusVisible);\n  let updateState = (0, $isWE5$useCallback)(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n  let onFocusChange = (0, $isWE5$useCallback)(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n  (0, $isWE5$useFocusVisibleListener)(isFocusVisible => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {\n    isTextInput: isTextInput\n  });\n  let {\n    focusProps: focusProps\n  } = (0, $isWE5$useFocus)({\n    isDisabled: within,\n    onFocusChange: onFocusChange\n  });\n  let {\n    focusWithinProps: focusWithinProps\n  } = (0, $isWE5$useFocusWithin)({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n  return {\n    isFocused: isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\nexport { $f7dceffc5ad7768b$export$4e328f61c538687f as useFocusRing };", "map": {"version": 3, "names": ["$f7dceffc5ad7768b$export$4e328f61c538687f", "props", "arguments", "length", "undefined", "autoFocus", "isTextInput", "within", "state", "$isWE5$useRef", "isFocused", "isFocusVisible", "$isWE5$isFocusVisible", "setFocused", "$isWE5$useState", "isFocusVisibleState", "setFocusVisible", "current", "updateState", "$isWE5$useCallback", "onFocusChange", "$isWE5$useFocusVisibleListener", "focusProps", "$isWE5$useFocus", "isDisabled", "focusWithinProps", "$isWE5$useFocusWithin", "onFocusWithinChange"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\focus\\dist\\packages\\@react-aria\\focus\\src\\useFocusRing.ts"], "sourcesContent": ["import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n"], "mappings": ";;AAoCO,SAASA,0CAAA,EAA2C;EAAA,IAA9BC,KAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA4B,CAAC,CAAC;EACzD,IAAI;IACFG,SAAA,GAAY;IAAAC,WAAA,EACZA,WAAW;IAAAC,MAAA,EACXA;EAAM,CACP,GAAGN,KAAA;EACJ,IAAIO,KAAA,GAAQ,IAAAC,aAAK,EAAE;IACjBC,SAAA,EAAW;IACXC,cAAA,EAAgBN,SAAA,IAAa,IAAAO,qBAAa;EAC5C;EACA,IAAI,CAACF,SAAA,EAAWG,UAAA,CAAW,GAAG,IAAAC,eAAO,EAAE;EACvC,IAAI,CAACC,mBAAA,EAAqBC,eAAA,CAAgB,GAAG,IAAAF,eAAO,EAAE,MAAMN,KAAA,CAAMS,OAAO,CAACP,SAAS,IAAIF,KAAA,CAAMS,OAAO,CAACN,cAAc;EAEnH,IAAIO,WAAA,GAAc,IAAAC,kBAAU,EAAE,MAAMH,eAAA,CAAgBR,KAAA,CAAMS,OAAO,CAACP,SAAS,IAAIF,KAAA,CAAMS,OAAO,CAACN,cAAc,GAAG,EAAE;EAEhH,IAAIS,aAAA,GAAgB,IAAAD,kBAAU,EAAET,SAAA;IAC9BF,KAAA,CAAMS,OAAO,CAACP,SAAS,GAAGA,SAAA;IAC1BG,UAAA,CAAWH,SAAA;IACXQ,WAAA;EACF,GAAG,CAACA,WAAA,CAAY;EAEhB,IAAAG,8BAAsB,EAAGV,cAAA;IACvBH,KAAA,CAAMS,OAAO,CAACN,cAAc,GAAGA,cAAA;IAC/BO,WAAA;EACF,GAAG,EAAE,EAAE;iBAACZ;EAAW;EAEnB,IAAI;IAAAgB,UAAA,EAACA;EAAU,CAAC,GAAG,IAAAC,eAAO,EAAE;IAC1BC,UAAA,EAAYjB,MAAA;mBACZa;EACF;EAEA,IAAI;IAAAK,gBAAA,EAACA;EAAgB,CAAC,GAAG,IAAAC,qBAAa,EAAE;IACtCF,UAAA,EAAY,CAACjB,MAAA;IACboB,mBAAA,EAAqBP;EACvB;EAEA,OAAO;eACLV,SAAA;IACAC,cAAA,EAAgBI,mBAAA;IAChBO,UAAA,EAAYf,MAAA,GAASkB,gBAAA,GAAmBH;EAC1C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}