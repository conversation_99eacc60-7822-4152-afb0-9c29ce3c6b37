{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminDashboardPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { ShoppingBagIcon, TagIcon, UserGroupIcon, CurrencyDollarIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon, EyeIcon, PlusIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useAdmin } from '../contexts/AdminContext';\nimport AdminLayout from '../components/AdminLayout';\nimport { products, categories } from '../data/products';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboardPage = () => {\n  _s();\n  const {\n    getThemeClasses\n  } = useTheme();\n  const {\n    admin\n  } = useAdmin();\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalCategories: 0,\n    totalRevenue: 0,\n    totalOrders: 0,\n    recentProducts: [],\n    lowStockProducts: []\n  });\n  useEffect(() => {\n    // Calculate dashboard statistics\n    const totalProducts = products.length;\n    const totalCategories = categories.length;\n    const totalRevenue = products.reduce((sum, product) => sum + product.price * (product.sold || 0), 0);\n    const totalOrders = products.reduce((sum, product) => sum + (product.sold || 0), 0);\n\n    // Get recent products (last 5)\n    const recentProducts = products.slice(-5).reverse();\n\n    // Get low stock products\n    const lowStockProducts = products.filter(product => product.stockCount && product.stockCount < 10).slice(0, 5);\n    setStats({\n      totalProducts,\n      totalCategories,\n      totalRevenue,\n      totalOrders,\n      recentProducts,\n      lowStockProducts\n    });\n  }, []);\n  const StatCard = ({\n    title,\n    value,\n    icon: Icon,\n    trend,\n    trendValue,\n    color = 'blue'\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: `p-6 rounded-xl shadow-lg transition-all duration-300 ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm font-medium ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-3xl font-bold ${getThemeClasses('text-gray-900', 'text-white')}`,\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), trend && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2\",\n          children: [trend === 'up' ? /*#__PURE__*/_jsxDEV(ArrowTrendingUpIcon, {\n            className: \"w-4 h-4 text-green-500 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(ArrowTrendingDownIcon, {\n            className: \"w-4 h-4 text-red-500 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`,\n            children: trendValue\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-3 rounded-full ${color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' : color === 'green' ? 'bg-green-100 dark:bg-green-900/20' : color === 'yellow' ? 'bg-yellow-100 dark:bg-yellow-900/20' : 'bg-purple-100 dark:bg-purple-900/20'}`,\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          className: `w-6 h-6 ${color === 'blue' ? 'text-blue-600' : color === 'green' ? 'text-green-600' : color === 'yellow' ? 'text-yellow-600' : 'text-purple-600'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: `text-3xl font-bold ${getThemeClasses('text-gray-900', 'text-white')}`,\n            children: [\"Welcome back, \", admin === null || admin === void 0 ? void 0 : admin.firstName, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `mt-2 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n            children: \"Here's what's happening with your store today.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Products\",\n          value: stats.totalProducts,\n          icon: ShoppingBagIcon,\n          trend: \"up\",\n          trendValue: \"+12%\",\n          color: \"blue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Categories\",\n          value: stats.totalCategories,\n          icon: TagIcon,\n          color: \"green\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Revenue\",\n          value: `$${stats.totalRevenue.toLocaleString()}`,\n          icon: CurrencyDollarIcon,\n          trend: \"up\",\n          trendValue: \"+8.2%\",\n          color: \"yellow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Orders\",\n          value: stats.totalOrders,\n          icon: UserGroupIcon,\n          trend: \"up\",\n          trendValue: \"+23%\",\n          color: \"purple\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: `p-6 rounded-xl shadow-lg ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${getThemeClasses('text-gray-900', 'text-white')}`,\n              children: \"Recent Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `text-sm ${getThemeClasses('text-light-orange-600 hover:text-light-orange-700', 'text-light-orange-400 hover:text-light-orange-300')}`,\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: stats.recentProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-12 h-12 rounded-lg object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-medium truncate ${getThemeClasses('text-gray-900', 'text-white')}`,\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                  children: [\"$\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `p-2 rounded-lg ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')}`,\n                children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: `p-6 rounded-xl shadow-lg ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${getThemeClasses('text-gray-900', 'text-white')}`,\n              children: \"Low Stock Alert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full\",\n              children: [stats.lowStockProducts.length, \" items\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: stats.lowStockProducts.length > 0 ? stats.lowStockProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: product.name,\n                  className: \"w-10 h-10 rounded-lg object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm font-medium ${getThemeClasses('text-gray-900', 'text-white')}`,\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-red-600\",\n                    children: [product.stockCount, \" left\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors\",\n                children: \"Restock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n              children: \"All products are well stocked!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboardPage, \"fbJdpbFNh7RXgfnk1Wmar7eWp8M=\", false, function () {\n  return [useTheme, useAdmin];\n});\n_c = AdminDashboardPage;\nexport default AdminDashboardPage;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboardPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "ShoppingBagIcon", "TagIcon", "UserGroupIcon", "CurrencyDollarIcon", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "EyeIcon", "PlusIcon", "useTheme", "useAdmin", "AdminLayout", "products", "categories", "jsxDEV", "_jsxDEV", "AdminDashboardPage", "_s", "getThemeClasses", "admin", "stats", "setStats", "totalProducts", "totalCategories", "totalRevenue", "totalOrders", "recentProducts", "lowStockProducts", "length", "reduce", "sum", "product", "price", "sold", "slice", "reverse", "filter", "stockCount", "StatCard", "title", "value", "icon", "Icon", "trend", "trendValue", "color", "div", "initial", "opacity", "y", "animate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "button", "whileHover", "scale", "whileTap", "toLocaleString", "x", "map", "index", "src", "image", "alt", "name", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminDashboardPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ShoppingBagIcon,\n  TagIcon,\n  UserGroupIcon,\n  CurrencyDollarIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n  EyeIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { useAdmin } from '../contexts/AdminContext';\nimport AdminLayout from '../components/AdminLayout';\nimport { products, categories } from '../data/products';\n\nconst AdminDashboardPage = () => {\n  const { getThemeClasses } = useTheme();\n  const { admin } = useAdmin();\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalCategories: 0,\n    totalRevenue: 0,\n    totalOrders: 0,\n    recentProducts: [],\n    lowStockProducts: []\n  });\n\n  useEffect(() => {\n    // Calculate dashboard statistics\n    const totalProducts = products.length;\n    const totalCategories = categories.length;\n    const totalRevenue = products.reduce((sum, product) => sum + (product.price * (product.sold || 0)), 0);\n    const totalOrders = products.reduce((sum, product) => sum + (product.sold || 0), 0);\n    \n    // Get recent products (last 5)\n    const recentProducts = products.slice(-5).reverse();\n    \n    // Get low stock products\n    const lowStockProducts = products.filter(product => \n      product.stockCount && product.stockCount < 10\n    ).slice(0, 5);\n\n    setStats({\n      totalProducts,\n      totalCategories,\n      totalRevenue,\n      totalOrders,\n      recentProducts,\n      lowStockProducts\n    });\n  }, []);\n\n  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue' }) => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className={`p-6 rounded-xl shadow-lg transition-all duration-300 ${\n        getThemeClasses('bg-white', 'bg-slate-800')\n      }`}\n    >\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className={`text-sm font-medium ${\n            getThemeClasses('text-gray-600', 'text-gray-400')\n          }`}>\n            {title}\n          </p>\n          <p className={`text-3xl font-bold ${\n            getThemeClasses('text-gray-900', 'text-white')\n          }`}>\n            {value}\n          </p>\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              {trend === 'up' ? (\n                <ArrowTrendingUpIcon className=\"w-4 h-4 text-green-500 mr-1\" />\n              ) : (\n                <ArrowTrendingDownIcon className=\"w-4 h-4 text-red-500 mr-1\" />\n              )}\n              <span className={`text-sm ${\n                trend === 'up' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trendValue}\n              </span>\n            </div>\n          )}\n        </div>\n        <div className={`p-3 rounded-full ${\n          color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' :\n          color === 'green' ? 'bg-green-100 dark:bg-green-900/20' :\n          color === 'yellow' ? 'bg-yellow-100 dark:bg-yellow-900/20' :\n          'bg-purple-100 dark:bg-purple-900/20'\n        }`}>\n          <Icon className={`w-6 h-6 ${\n            color === 'blue' ? 'text-blue-600' :\n            color === 'green' ? 'text-green-600' :\n            color === 'yellow' ? 'text-yellow-600' :\n            'text-purple-600'\n          }`} />\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-8\">\n        {/* Welcome Section */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className={`text-3xl font-bold ${\n              getThemeClasses('text-gray-900', 'text-white')\n            }`}>\n              Welcome back, {admin?.firstName}!\n            </h1>\n            <p className={`mt-2 ${\n              getThemeClasses('text-gray-600', 'text-gray-400')\n            }`}>\n              Here's what's happening with your store today.\n            </p>\n          </div>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <PlusIcon className=\"w-5 h-5\" />\n            <span>Add Product</span>\n          </motion.button>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <StatCard\n            title=\"Total Products\"\n            value={stats.totalProducts}\n            icon={ShoppingBagIcon}\n            trend=\"up\"\n            trendValue=\"+12%\"\n            color=\"blue\"\n          />\n          <StatCard\n            title=\"Categories\"\n            value={stats.totalCategories}\n            icon={TagIcon}\n            color=\"green\"\n          />\n          <StatCard\n            title=\"Total Revenue\"\n            value={`$${stats.totalRevenue.toLocaleString()}`}\n            icon={CurrencyDollarIcon}\n            trend=\"up\"\n            trendValue=\"+8.2%\"\n            color=\"yellow\"\n          />\n          <StatCard\n            title=\"Total Orders\"\n            value={stats.totalOrders}\n            icon={UserGroupIcon}\n            trend=\"up\"\n            trendValue=\"+23%\"\n            color=\"purple\"\n          />\n        </div>\n\n        {/* Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Products */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className={`p-6 rounded-xl shadow-lg ${\n              getThemeClasses('bg-white', 'bg-slate-800')\n            }`}\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className={`text-lg font-semibold ${\n                getThemeClasses('text-gray-900', 'text-white')\n              }`}>\n                Recent Products\n              </h3>\n              <button className={`text-sm ${\n                getThemeClasses('text-light-orange-600 hover:text-light-orange-700', 'text-light-orange-400 hover:text-light-orange-300')\n              }`}>\n                View All\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {stats.recentProducts.map((product, index) => (\n                <div key={product.id} className=\"flex items-center space-x-4\">\n                  <img\n                    src={product.image}\n                    alt={product.name}\n                    className=\"w-12 h-12 rounded-lg object-cover\"\n                  />\n                  <div className=\"flex-1 min-w-0\">\n                    <p className={`text-sm font-medium truncate ${\n                      getThemeClasses('text-gray-900', 'text-white')\n                    }`}>\n                      {product.name}\n                    </p>\n                    <p className={`text-sm ${\n                      getThemeClasses('text-gray-500', 'text-gray-400')\n                    }`}>\n                      ${product.price}\n                    </p>\n                  </div>\n                  <button className={`p-2 rounded-lg ${\n                    getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')\n                  }`}>\n                    <EyeIcon className=\"w-4 h-4 text-gray-400\" />\n                  </button>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Low Stock Alert */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className={`p-6 rounded-xl shadow-lg ${\n              getThemeClasses('bg-white', 'bg-slate-800')\n            }`}\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className={`text-lg font-semibold ${\n                getThemeClasses('text-gray-900', 'text-white')\n              }`}>\n                Low Stock Alert\n              </h3>\n              <span className=\"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full\">\n                {stats.lowStockProducts.length} items\n              </span>\n            </div>\n            <div className=\"space-y-4\">\n              {stats.lowStockProducts.length > 0 ? (\n                stats.lowStockProducts.map((product) => (\n                  <div key={product.id} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <img\n                        src={product.image}\n                        alt={product.name}\n                        className=\"w-10 h-10 rounded-lg object-cover\"\n                      />\n                      <div>\n                        <p className={`text-sm font-medium ${\n                          getThemeClasses('text-gray-900', 'text-white')\n                        }`}>\n                          {product.name}\n                        </p>\n                        <p className=\"text-xs text-red-600\">\n                          {product.stockCount} left\n                        </p>\n                      </div>\n                    </div>\n                    <button className=\"px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors\">\n                      Restock\n                    </button>\n                  </div>\n                ))\n              ) : (\n                <p className={`text-sm ${\n                  getThemeClasses('text-gray-500', 'text-gray-400')\n                }`}>\n                  All products are well stocked!\n                </p>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboardPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,eAAe,EACfC,OAAO,EACPC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,qBAAqB,EACrBC,OAAO,EACPC,QAAQ,QACH,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,QAAQ,EAAEC,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAgB,CAAC,GAAGT,QAAQ,CAAC,CAAC;EACtC,MAAM;IAAEU;EAAM,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAC5B,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC;IACjCwB,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF5B,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,aAAa,GAAGV,QAAQ,CAACgB,MAAM;IACrC,MAAML,eAAe,GAAGV,UAAU,CAACe,MAAM;IACzC,MAAMJ,YAAY,GAAGZ,QAAQ,CAACiB,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAIC,OAAO,CAACC,KAAK,IAAID,OAAO,CAACE,IAAI,IAAI,CAAC,CAAE,EAAE,CAAC,CAAC;IACtG,MAAMR,WAAW,GAAGb,QAAQ,CAACiB,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACE,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEnF;IACA,MAAMP,cAAc,GAAGd,QAAQ,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;IAEnD;IACA,MAAMR,gBAAgB,GAAGf,QAAQ,CAACwB,MAAM,CAACL,OAAO,IAC9CA,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,EAC7C,CAAC,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAEbb,QAAQ,CAAC;MACPC,aAAa;MACbC,eAAe;MACfC,YAAY;MACZC,WAAW;MACXC,cAAc;MACdC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI,EAAEC,IAAI;IAAEC,KAAK;IAAEC,UAAU;IAAEC,KAAK,GAAG;EAAO,CAAC,kBAC/E9B,OAAA,CAACf,MAAM,CAAC8C,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAE,wDACTjC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;IAAAkC,QAAA,eAEHrC,OAAA;MAAKoC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrC,OAAA;QAAAqC,QAAA,gBACErC,OAAA;UAAGoC,SAAS,EAAE,uBACZjC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;UAAAkC,QAAA,EACAb;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzC,OAAA;UAAGoC,SAAS,EAAE,sBACZjC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;UAAAkC,QAAA,EACAZ;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACHb,KAAK,iBACJ5B,OAAA;UAAKoC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GACpCT,KAAK,KAAK,IAAI,gBACb5B,OAAA,CAACV,mBAAmB;YAAC8C,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE/DzC,OAAA,CAACT,qBAAqB;YAAC6C,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC/D,eACDzC,OAAA;YAAMoC,SAAS,EAAE,WACfR,KAAK,KAAK,IAAI,GAAG,gBAAgB,GAAG,cAAc,EACjD;YAAAS,QAAA,EACAR;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNzC,OAAA;QAAKoC,SAAS,EAAE,oBACdN,KAAK,KAAK,MAAM,GAAG,iCAAiC,GACpDA,KAAK,KAAK,OAAO,GAAG,mCAAmC,GACvDA,KAAK,KAAK,QAAQ,GAAG,qCAAqC,GAC1D,qCAAqC,EACpC;QAAAO,QAAA,eACDrC,OAAA,CAAC2B,IAAI;UAACS,SAAS,EAAE,WACfN,KAAK,KAAK,MAAM,GAAG,eAAe,GAClCA,KAAK,KAAK,OAAO,GAAG,gBAAgB,GACpCA,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GACtC,iBAAiB;QAChB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,oBACEzC,OAAA,CAACJ,WAAW;IAAAyC,QAAA,eACVrC,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBrC,OAAA;QAAKoC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDrC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAIoC,SAAS,EAAE,sBACbjC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;YAAAkC,QAAA,GAAC,gBACY,EAACjC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEsC,SAAS,EAAC,GAClC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzC,OAAA;YAAGoC,SAAS,EAAE,QACZjC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;YAAAkC,QAAA,EAAC;UAEJ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNzC,OAAA,CAACf,MAAM,CAAC0D,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BT,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvIrC,OAAA,CAACP,QAAQ;YAAC2C,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCzC,OAAA;YAAAqC,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGNzC,OAAA;QAAKoC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnErC,OAAA,CAACuB,QAAQ;UACPC,KAAK,EAAC,gBAAgB;UACtBC,KAAK,EAAEpB,KAAK,CAACE,aAAc;UAC3BmB,IAAI,EAAExC,eAAgB;UACtB0C,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,MAAM;UACjBC,KAAK,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACFzC,OAAA,CAACuB,QAAQ;UACPC,KAAK,EAAC,YAAY;UAClBC,KAAK,EAAEpB,KAAK,CAACG,eAAgB;UAC7BkB,IAAI,EAAEvC,OAAQ;UACd2C,KAAK,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACFzC,OAAA,CAACuB,QAAQ;UACPC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAE,IAAIpB,KAAK,CAACI,YAAY,CAACsC,cAAc,CAAC,CAAC,EAAG;UACjDrB,IAAI,EAAErC,kBAAmB;UACzBuC,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,OAAO;UAClBC,KAAK,EAAC;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFzC,OAAA,CAACuB,QAAQ;UACPC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEpB,KAAK,CAACK,WAAY;UACzBgB,IAAI,EAAEtC,aAAc;UACpBwC,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,MAAM;UACjBC,KAAK,EAAC;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzC,OAAA;QAAKoC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDrC,OAAA,CAACf,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCb,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BZ,SAAS,EAAE,4BACTjC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;UAAAkC,QAAA,gBAEHrC,OAAA;YAAKoC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrC,OAAA;cAAIoC,SAAS,EAAE,yBACbjC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;cAAAkC,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzC,OAAA;cAAQoC,SAAS,EAAE,WACjBjC,eAAe,CAAC,mDAAmD,EAAE,mDAAmD,CAAC,EACxH;cAAAkC,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhC,KAAK,CAACM,cAAc,CAACsC,GAAG,CAAC,CAACjC,OAAO,EAAEkC,KAAK,kBACvClD,OAAA;cAAsBoC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC3DrC,OAAA;gBACEmD,GAAG,EAAEnC,OAAO,CAACoC,KAAM;gBACnBC,GAAG,EAAErC,OAAO,CAACsC,IAAK;gBAClBlB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACFzC,OAAA;gBAAKoC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrC,OAAA;kBAAGoC,SAAS,EAAE,gCACZjC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;kBAAAkC,QAAA,EACArB,OAAO,CAACsC;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACJzC,OAAA;kBAAGoC,SAAS,EAAE,WACZjC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAkC,QAAA,GAAC,GACD,EAACrB,OAAO,CAACC,KAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNzC,OAAA;gBAAQoC,SAAS,EAAE,kBACjBjC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;gBAAAkC,QAAA,eACDrC,OAAA,CAACR,OAAO;kBAAC4C,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA,GAtBDzB,OAAO,CAACuC,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbzC,OAAA,CAACf,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAG,CAAE;UAC/Bb,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BZ,SAAS,EAAE,4BACTjC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;UAAAkC,QAAA,gBAEHrC,OAAA;YAAKoC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrC,OAAA;cAAIoC,SAAS,EAAE,yBACbjC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;cAAAkC,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzC,OAAA;cAAMoC,SAAS,EAAC,uFAAuF;cAAAC,QAAA,GACpGhC,KAAK,CAACO,gBAAgB,CAACC,MAAM,EAAC,QACjC;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhC,KAAK,CAACO,gBAAgB,CAACC,MAAM,GAAG,CAAC,GAChCR,KAAK,CAACO,gBAAgB,CAACqC,GAAG,CAAEjC,OAAO,iBACjChB,OAAA;cAAsBoC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBACjErC,OAAA;gBAAKoC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CrC,OAAA;kBACEmD,GAAG,EAAEnC,OAAO,CAACoC,KAAM;kBACnBC,GAAG,EAAErC,OAAO,CAACsC,IAAK;kBAClBlB,SAAS,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACFzC,OAAA;kBAAAqC,QAAA,gBACErC,OAAA;oBAAGoC,SAAS,EAAE,uBACZjC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;oBAAAkC,QAAA,EACArB,OAAO,CAACsC;kBAAI;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACJzC,OAAA;oBAAGoC,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAChCrB,OAAO,CAACM,UAAU,EAAC,OACtB;kBAAA;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzC,OAAA;gBAAQoC,SAAS,EAAC,yGAAyG;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GApBDzB,OAAO,CAACuC,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBf,CACN,CAAC,gBAEFzC,OAAA;cAAGoC,SAAS,EAAE,WACZjC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;cAAAkC,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAACvC,EAAA,CAnQID,kBAAkB;EAAA,QACMP,QAAQ,EAClBC,QAAQ;AAAA;AAAA6D,EAAA,GAFtBvD,kBAAkB;AAqQxB,eAAeA,kBAAkB;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}