{"ast": null, "code": "import { useEffect as a } from \"react\";\nimport { useLatestValue as f } from './use-latest-value.js';\nfunction s(t, e, o, n) {\n  let i = f(o);\n  a(() => {\n    if (!t) return;\n    function r(d) {\n      i.current(d);\n    }\n    return window.addEventListener(e, r, n), () => window.removeEventListener(e, r, n);\n  }, [t, e, n]);\n}\nexport { s as useWindowEvent };", "map": {"version": 3, "names": ["useEffect", "a", "useLatestValue", "f", "s", "t", "e", "o", "n", "i", "r", "d", "current", "window", "addEventListener", "removeEventListener", "useWindowEvent"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-window-event.js"], "sourcesContent": ["import{useEffect as a}from\"react\";import{useLatestValue as f}from'./use-latest-value.js';function s(t,e,o,n){let i=f(o);a(()=>{if(!t)return;function r(d){i.current(d)}return window.addEventListener(e,r,n),()=>window.removeEventListener(e,r,n)},[t,e,n])}export{s as useWindowEvent};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAACI,CAAC,CAAC;EAACN,CAAC,CAAC,MAAI;IAAC,IAAG,CAACI,CAAC,EAAC;IAAO,SAASK,CAACA,CAACC,CAAC,EAAC;MAACF,CAAC,CAACG,OAAO,CAACD,CAAC,CAAC;IAAA;IAAC,OAAOE,MAAM,CAACC,gBAAgB,CAACR,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC,EAAC,MAAIK,MAAM,CAACE,mBAAmB,CAACT,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC;EAAA,CAAC,EAAC,CAACH,CAAC,EAACC,CAAC,EAACE,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}