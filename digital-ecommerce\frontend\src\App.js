import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import ModernNavigation from './components/ModernNavigation';
import { CartProvider } from './components/ShoppingCart';
import { UserProvider } from './contexts/UserContext';
import { AdminProvider } from './contexts/AdminContext';
import { ProductProvider } from './contexts/ProductContext';
import HomePage from './pages/HomePage';
import ProductsPage from './pages/ProductsPage';
import DigitalProductsPage from './pages/DigitalProductsPage';
import AboutPage from './pages/AboutPage';
import ContactPage from './pages/ContactPage';
import CheckoutPage from './pages/CheckoutPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import AccountPage from './pages/AccountPage';
import WishlistPage from './pages/WishlistPage';

import AdminLoginPage from './pages/AdminLoginPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AdminProductsPage from './pages/AdminProductsPage';
import AdminCategoriesPage from './pages/AdminCategoriesPage';
import ProtectedRoute from './components/ProtectedRoute';
import AdminProtectedRoute from './components/AdminProtectedRoute';
import {
  HelpPage,
  ReturnsPage,
  ShippingPage,
  TrackOrderPage,
  PrivacyPage,
  TermsPage,
  CookiesPage,
  OrdersPage
} from './pages/PlaceholderPage';
import MultiLanguageSupport from './components/MultiLanguageSupport';
import EmailNotifications from './components/EmailNotifications';
import './App.css';

function App() {
  return (
    <ProductProvider>
      <AdminProvider>
        <UserProvider>
          <CartProvider>
            <Router>
          <div className="min-h-screen bg-gradient-to-br from-light-orange-50 to-white">
          <ModernNavigation />

        <AnimatePresence mode="wait">
          <Routes>
            <Route path="/" element={
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <HomePage />
              </motion.div>
            } />
            <Route path="/products" element={
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <ProductsPage />
              </motion.div>
            } />
            <Route path="/digital-products" element={
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <DigitalProductsPage />
              </motion.div>
            } />
            <Route path="/about" element={
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <AboutPage />
              </motion.div>
            } />
            <Route path="/contact" element={
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <ContactPage />
              </motion.div>
            } />
            <Route path="/checkout" element={
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <CheckoutPage />
              </motion.div>
            } />
            <Route path="/help" element={<HelpPage />} />
            <Route path="/returns" element={<ReturnsPage />} />
            <Route path="/shipping" element={<ShippingPage />} />
            <Route path="/track" element={<TrackOrderPage />} />
            <Route path="/orders" element={<OrdersPage />} />
            <Route path="/privacy" element={<PrivacyPage />} />
            <Route path="/terms" element={<TermsPage />} />
            <Route path="/cookies" element={<CookiesPage />} />
            <Route path="/login" element={
              <ProtectedRoute requireAuth={false}>
                <LoginPage />
              </ProtectedRoute>
            } />
            <Route path="/register" element={
              <ProtectedRoute requireAuth={false}>
                <RegisterPage />
              </ProtectedRoute>
            } />
            <Route path="/reset-password" element={
              <ProtectedRoute requireAuth={false}>
                <ResetPasswordPage />
              </ProtectedRoute>
            } />
            <Route path="/account" element={
              <ProtectedRoute>
                <AccountPage />
              </ProtectedRoute>
            } />
            <Route path="/wishlist" element={
              <ProtectedRoute>
                <WishlistPage />
              </ProtectedRoute>
            } />


            {/* Admin Routes */}
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/admin/dashboard" element={
              <AdminProtectedRoute>
                <AdminDashboardPage />
              </AdminProtectedRoute>
            } />
            <Route path="/admin/products" element={
              <AdminProtectedRoute requiredPermission="products">
                <AdminProductsPage />
              </AdminProtectedRoute>
            } />
            <Route path="/admin/categories" element={
              <AdminProtectedRoute requiredPermission="categories">
                <AdminCategoriesPage />
              </AdminProtectedRoute>
            } />
          </Routes>
        </AnimatePresence>

        {/* Enhanced Footer */}
        <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* Company Info */}
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  </div>
                  <span className="text-2xl font-bold">ShopHub</span>
                </div>
                <p className="text-gray-300 mb-4 max-w-md">
                  Your premier destination for quality products and exceptional shopping experiences.
                  We're committed to bringing you the best deals and customer service.
                </p>
                <div className="flex space-x-4">
                  <MultiLanguageSupport />
                  <EmailNotifications />
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
                <ul className="space-y-2">
                  <li><Link to="/" className="text-gray-300 hover:text-light-orange-400 transition-colors">Home</Link></li>
                  <li><Link to="/products" className="text-gray-300 hover:text-light-orange-400 transition-colors">Products</Link></li>
                  <li><Link to="/digital-products" className="text-gray-300 hover:text-light-orange-400 transition-colors">Digital Products</Link></li>
                  <li><Link to="/about" className="text-gray-300 hover:text-light-orange-400 transition-colors">About Us</Link></li>
                  <li><Link to="/contact" className="text-gray-300 hover:text-light-orange-400 transition-colors">Contact</Link></li>
                </ul>
              </div>

              {/* Customer Service */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Customer Service</h3>
                <ul className="space-y-2">
                  <li><Link to="/help" className="text-gray-300 hover:text-light-orange-400 transition-colors">Help Center</Link></li>
                  <li><Link to="/returns" className="text-gray-300 hover:text-light-orange-400 transition-colors">Returns</Link></li>
                  <li><Link to="/shipping" className="text-gray-300 hover:text-light-orange-400 transition-colors">Shipping Info</Link></li>
                  <li><Link to="/track" className="text-gray-300 hover:text-light-orange-400 transition-colors">Track Order</Link></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © 2024 ShopHub. All rights reserved.
              </p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <Link to="/privacy" className="text-gray-400 hover:text-light-orange-400 transition-colors text-sm">Privacy Policy</Link>
                <Link to="/terms" className="text-gray-400 hover:text-light-orange-400 transition-colors text-sm">Terms of Service</Link>
                <Link to="/cookies" className="text-gray-400 hover:text-light-orange-400 transition-colors text-sm">Cookie Policy</Link>
              </div>
            </div>
          </div>
        </footer>
        </div>
            </Router>
          </CartProvider>
        </UserProvider>
      </AdminProvider>
    </ProductProvider>
  );
}

export default App;
