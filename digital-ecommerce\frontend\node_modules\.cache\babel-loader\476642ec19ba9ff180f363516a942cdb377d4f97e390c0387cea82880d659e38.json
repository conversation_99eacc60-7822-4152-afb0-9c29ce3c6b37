{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport ThemeToggle from './ThemeToggle';\nimport { useUser } from '../contexts/UserContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const {\n    theme,\n    getThemeClasses\n  } = useTheme();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Implement your search logic here\n      console.log('Searching for:', searchQuery);\n    }\n  };\n  // const { totalItems } = useCart(); // Available for future use\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigationItems = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? getThemeClasses('bg-white/95 backdrop-blur-md shadow-lg', 'bg-slate-900/95 backdrop-blur-md shadow-xl shadow-black/20') : 'bg-transparent'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                rotate: 360\n              },\n              transition: {\n                duration: 0.5\n              },\n              className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-2xl font-bold transition-colors duration-300 ${isScrolled ? getThemeClasses('text-gray-900', 'text-white') : 'text-white'}`,\n              children: \"ShopHub\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-8\",\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n              to: item.href,\n              className: `relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${isActive(item.href) ? isScrolled ? 'text-light-orange-600' : 'text-yellow-300' : isScrolled ? getThemeClasses('text-gray-700 hover:text-light-orange-600', 'text-gray-300 hover:text-light-orange-400') : 'text-white hover:text-yellow-300'}`,\n              children: [item.name, isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeTab\",\n                className: `absolute bottom-0 left-0 right-0 h-0.5 ${isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this)]\n            }, item.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center flex-1 max-w-md mx-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 ${isScrolled ? getThemeClasses('text-gray-400', 'text-gray-400') : 'text-white/70'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${isScrolled ? getThemeClasses('bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300', 'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-2 focus:ring-light-orange-400') : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.1\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? getThemeClasses('text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50', 'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700') : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n              children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/account\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? getThemeClasses('text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50', 'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700') : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ThemeToggle, {\n              size: \"md\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: `relative p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.profilePicture,\n                  alt: \"Profile\",\n                  className: \"w-8 h-8 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: user === null || user === void 0 ? void 0 : user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/account\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"My Account\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/orders\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Order History\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/wishlist\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Wishlist\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: logout,\n                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(!isOpen),\n              className: `lg:hidden p-2 rounded-md transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600' : 'text-white hover:text-yellow-300'}`,\n              children: isOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto'\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: `lg:hidden backdrop-blur-md border-t transition-colors duration-300 ${getThemeClasses('bg-white/95 border-gray-200', 'bg-slate-900/95 border-slate-700')}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-6 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 ${getThemeClasses('text-gray-400', 'text-gray-400')}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-10 pr-4 py-3 rounded-lg focus:ring-2 transition-colors duration-300 ${getThemeClasses('bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-light-orange-300', 'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-light-orange-400')}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                onClick: () => setIsOpen(false),\n                className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${isActive(item.href) ? getThemeClasses('bg-light-orange-100 text-light-orange-700', 'bg-light-orange-900/20 text-light-orange-400') : getThemeClasses('text-gray-700 hover:bg-gray-100', 'text-gray-300 hover:bg-slate-700')}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 23\n                }, this)]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center justify-around pt-4 border-t transition-colors duration-300 ${getThemeClasses('border-gray-200', 'border-slate-700')}`,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `flex flex-col items-center space-y-1 transition-colors duration-300 ${getThemeClasses('text-gray-600 hover:text-light-orange-600', 'text-gray-400 hover:text-light-orange-400')}`,\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: `flex flex-col items-center space-y-1 transition-colors duration-300 ${getThemeClasses('text-gray-600 hover:text-light-orange-600', 'text-gray-400 hover:text-light-orange-400')}`,\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(ThemeToggle, {\n                  size: \"sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs transition-colors duration-300 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n                  children: \"Theme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs transition-colors duration-300 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n                  children: \"Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 lg:h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navigation, \"//Av9IAWCFMl1WVFtU308VKxjBQ=\", false, function () {\n  return [useLocation, useUser, useTheme];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ShoppingCart", "ThemeToggle", "useUser", "useTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Navigation", "_s", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "user", "isAuthenticated", "logout", "theme", "getThemeClasses", "handleSearch", "e", "preventDefault", "trim", "console", "log", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navigationItems", "name", "href", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "to", "div", "whileHover", "rotate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "layoutId", "type", "placeholder", "value", "onChange", "target", "button", "scale", "whileTap", "size", "profilePicture", "src", "alt", "firstName", "lastName", "email", "onClick", "opacity", "height", "exit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport ThemeToggle from './ThemeToggle';\nimport { useUser } from '../contexts/UserContext';\nimport { useTheme } from '../contexts/ThemeContext';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n  const { theme, getThemeClasses } = useTheme();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Implement your search logic here\n      console.log('Searching for:', searchQuery);\n    }\n  };\n  // const { totalItems } = useCart(); // Available for future use\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? getThemeClasses(\n                'bg-white/95 backdrop-blur-md shadow-lg',\n                'bg-slate-900/95 backdrop-blur-md shadow-xl shadow-black/20'\n              )\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 lg:h-20\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3\">\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.5 }}\n                className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n              >\n                <ShoppingBagIcon className=\"w-6 h-6 text-white\" />\n              </motion.div>\n              <span className={`text-2xl font-bold transition-colors duration-300 ${\n                isScrolled\n                  ? getThemeClasses('text-gray-900', 'text-white')\n                  : 'text-white'\n              }`}>\n                ShopHub\n              </span>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${\n                    isActive(item.href)\n                      ? isScrolled\n                        ? 'text-light-orange-600'\n                        : 'text-yellow-300'\n                      : isScrolled\n                        ? getThemeClasses(\n                            'text-gray-700 hover:text-light-orange-600',\n                            'text-gray-300 hover:text-light-orange-400'\n                          )\n                        : 'text-white hover:text-yellow-300'\n                  }`}\n                >\n                  {item.name}\n                  {isActive(item.href) && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className={`absolute bottom-0 left-0 right-0 h-0.5 ${\n                        isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'\n                      }`}\n                    />\n                  )}\n                </Link>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 ${\n                    isScrolled\n                      ? getThemeClasses('text-gray-400', 'text-gray-400')\n                      : 'text-white/70'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search products...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className={`w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${\n                    isScrolled\n                      ? getThemeClasses(\n                          'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300',\n                          'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-2 focus:ring-light-orange-400'\n                        )\n                      : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'\n                  }`}\n                />\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                className={`p-2 rounded-full transition-colors duration-300 ${\n                  isScrolled\n                    ? getThemeClasses(\n                        'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50',\n                        'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700'\n                      )\n                    : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                }`}\n              >\n                <HeartIcon className=\"w-6 h-6\" />\n              </motion.button>\n\n              <Link to=\"/account\">\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className={`p-2 rounded-full transition-colors duration-300 ${\n                    isScrolled\n                      ? getThemeClasses(\n                          'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50',\n                          'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700'\n                        )\n                      : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                  }`}\n                >\n                  <UserIcon className=\"w-6 h-6\" />\n                </motion.button>\n              </Link>\n\n              <ShoppingCart />\n\n              {/* Theme Toggle */}\n              <ThemeToggle size=\"md\" />\n\n              {/* User Account */}\n              {isAuthenticated ? (\n                <div className=\"relative group\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className={`relative p-2 rounded-full transition-colors duration-300 ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                        : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                    }`}\n                  >\n                    {user?.profilePicture ? (\n                      <img\n                        src={user.profilePicture}\n                        alt=\"Profile\"\n                        className=\"w-8 h-8 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <UserIcon className=\"w-6 h-6\" />\n                    )}\n                  </motion.button>\n\n                  {/* User Dropdown */}\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                    <div className=\"p-3 border-b border-gray-100\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {user?.firstName} {user?.lastName}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                    <div className=\"py-1\">\n                      <Link\n                        to=\"/account\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        My Account\n                      </Link>\n                      <Link\n                        to=\"/orders\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Order History\n                      </Link>\n                      <Link\n                        to=\"/wishlist\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Wishlist\n                      </Link>\n                      <button\n                        onClick={logout}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Sign Out\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <Link to=\"/login\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                      }`}\n                    >\n                      Sign In\n                    </motion.button>\n                  </Link>\n                  <Link to=\"/register\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\"\n                    >\n                      Sign Up\n                    </motion.button>\n                  </Link>\n                </div>\n              )}\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600' \n                    : 'text-white hover:text-yellow-300'\n                }`}\n              >\n                {isOpen ? (\n                  <XMarkIcon className=\"w-6 h-6\" />\n                ) : (\n                  <Bars3Icon className=\"w-6 h-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className={`lg:hidden backdrop-blur-md border-t transition-colors duration-300 ${\n                getThemeClasses(\n                  'bg-white/95 border-gray-200',\n                  'bg-slate-900/95 border-slate-700'\n                )\n              }`}\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {/* Mobile Search */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className={`h-5 w-5 ${\n                      getThemeClasses('text-gray-400', 'text-gray-400')\n                    }`} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className={`w-full pl-10 pr-4 py-3 rounded-lg focus:ring-2 transition-colors duration-300 ${\n                      getThemeClasses(\n                        'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-light-orange-300',\n                        'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-light-orange-400'\n                      )\n                    }`}\n                  />\n                </div>\n\n                {/* Mobile Navigation Links */}\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setIsOpen(false)}\n                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${\n                        isActive(item.href)\n                          ? getThemeClasses(\n                              'bg-light-orange-100 text-light-orange-700',\n                              'bg-light-orange-900/20 text-light-orange-400'\n                            )\n                          : getThemeClasses(\n                              'text-gray-700 hover:bg-gray-100',\n                              'text-gray-300 hover:bg-slate-700'\n                            )\n                      }`}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ))}\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className={`flex items-center justify-around pt-4 border-t transition-colors duration-300 ${\n                  getThemeClasses('border-gray-200', 'border-slate-700')\n                }`}>\n                  <button className={`flex flex-col items-center space-y-1 transition-colors duration-300 ${\n                    getThemeClasses(\n                      'text-gray-600 hover:text-light-orange-600',\n                      'text-gray-400 hover:text-light-orange-400'\n                    )\n                  }`}>\n                    <HeartIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Wishlist</span>\n                  </button>\n                  <Link to=\"/account\" className={`flex flex-col items-center space-y-1 transition-colors duration-300 ${\n                    getThemeClasses(\n                      'text-gray-600 hover:text-light-orange-600',\n                      'text-gray-400 hover:text-light-orange-400'\n                    )\n                  }`}>\n                    <UserIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Account</span>\n                  </Link>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ThemeToggle size=\"sm\" />\n                    <span className={`text-xs transition-colors duration-300 ${\n                      getThemeClasses('text-gray-600', 'text-gray-400')\n                    }`}>Theme</span>\n                  </div>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ShoppingCart />\n                    <span className={`text-xs transition-colors duration-300 ${\n                      getThemeClasses('text-gray-600', 'text-gray-400')\n                    }`}>Cart</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16 lg:h-20\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,QAChB,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMgC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGjB,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEkB,KAAK;IAAEC;EAAgB,CAAC,GAAGlB,QAAQ,CAAC,CAAC;EAE7C,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIV,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACtB;MACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEb,WAAW,CAAC;IAC5C;EACF,CAAC;EACD;;EAEA7B,SAAS,CAAC,MAAM;IACd,MAAM2C,YAAY,GAAGA,CAAA,KAAM;MACzBf,aAAa,CAACgB,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAExC;EAAS,CAAC,EAC3C;IAAEsC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEvC;EAAQ,CAAC,EACtD;IAAEqC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAEvC;EAAQ,CAAC,EAC7D;IAAEqC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAErC;EAAsB,CAAC,EAC9D;IAAEmC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEtC;EAAU,CAAC,CACvD;EAED,MAAMuC,QAAQ,GAAIC,IAAI,IAAKtB,QAAQ,CAACuB,QAAQ,KAAKD,IAAI;EAErD,oBACEjC,OAAA,CAAAE,SAAA;IAAAiC,QAAA,gBACEnC,OAAA,CAACjB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,SAAS,EAAE,+DACTjC,UAAU,GACNS,eAAe,CACb,wCAAwC,EACxC,4DACF,CAAC,GACD,gBAAgB,EACnB;MAAAmB,QAAA,gBAEHnC,OAAA;QAAKwC,SAAS,EAAC,wCAAwC;QAAAL,QAAA,eACrDnC,OAAA;UAAKwC,SAAS,EAAC,gDAAgD;UAAAL,QAAA,gBAE7DnC,OAAA,CAACnB,IAAI;YAAC4D,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAClDnC,OAAA,CAACjB,MAAM,CAAC2D,GAAG;cACTC,UAAU,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cAC5BC,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9BN,SAAS,EAAC,8HAA8H;cAAAL,QAAA,eAExInC,OAAA,CAACb,eAAe;gBAACqD,SAAS,EAAC;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACblD,OAAA;cAAMwC,SAAS,EAAE,qDACfjC,UAAU,GACNS,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,GAC9C,YAAY,EACf;cAAAmB,QAAA,EAAC;YAEJ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPlD,OAAA;YAAKwC,SAAS,EAAC,uCAAuC;YAAAL,QAAA,EACnDP,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACxBpD,OAAA,CAACnB,IAAI;cAEH4D,EAAE,EAAEW,IAAI,CAACtB,IAAK;cACdU,SAAS,EAAE,yEACTR,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,GACfvB,UAAU,GACR,uBAAuB,GACvB,iBAAiB,GACnBA,UAAU,GACRS,eAAe,CACb,2CAA2C,EAC3C,2CACF,CAAC,GACD,kCAAkC,EACvC;cAAAmB,QAAA,GAEFiB,IAAI,CAACvB,IAAI,EACTG,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,iBAClB9B,OAAA,CAACjB,MAAM,CAAC2D,GAAG;gBACTW,QAAQ,EAAC,WAAW;gBACpBb,SAAS,EAAE,0CACTjC,UAAU,GAAG,qBAAqB,GAAG,eAAe;cACnD;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACF;YAAA,GAvBIE,IAAI,CAACvB,IAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlD,OAAA;YAAKwC,SAAS,EAAC,kDAAkD;YAAAL,QAAA,eAC/DnC,OAAA;cAAKwC,SAAS,EAAC,iBAAiB;cAAAL,QAAA,gBAC9BnC,OAAA;gBAAKwC,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFnC,OAAA,CAACZ,mBAAmB;kBAACoD,SAAS,EAAE,WAC9BjC,UAAU,GACNS,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,GACjD,eAAe;gBAClB;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlD,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAE/C,WAAY;gBACnBgD,QAAQ,EAAGvC,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;gBAChDhB,SAAS,EAAE,mEACTjC,UAAU,GACNS,eAAe,CACb,wGAAwG,EACxG,0GACF,CAAC,GACD,iHAAiH;cACpH;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlD,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAC1CnC,OAAA,CAACjB,MAAM,CAAC4E,MAAM;cACZhB,UAAU,EAAE;gBAAEiB,KAAK,EAAE;cAAI,CAAE;cAC3BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAI,CAAE;cACzBpB,SAAS,EAAE,mDACTjC,UAAU,GACNS,eAAe,CACb,oEAAoE,EACpE,8DACF,CAAC,GACD,oDAAoD,EACvD;cAAAmB,QAAA,eAEHnC,OAAA,CAACV,SAAS;gBAACkD,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAEhBlD,OAAA,CAACnB,IAAI;cAAC4D,EAAE,EAAC,UAAU;cAAAN,QAAA,eACjBnC,OAAA,CAACjB,MAAM,CAAC4E,MAAM;gBACZhB,UAAU,EAAE;kBAAEiB,KAAK,EAAE;gBAAI,CAAE;gBAC3BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAI,CAAE;gBACzBpB,SAAS,EAAE,mDACTjC,UAAU,GACNS,eAAe,CACb,oEAAoE,EACpE,8DACF,CAAC,GACD,oDAAoD,EACvD;gBAAAmB,QAAA,eAEHnC,OAAA,CAACX,QAAQ;kBAACmD,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEPlD,OAAA,CAACL,YAAY;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGhBlD,OAAA,CAACJ,WAAW;cAACkE,IAAI,EAAC;YAAI;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAGxBrC,eAAe,gBACdb,OAAA;cAAKwC,SAAS,EAAC,gBAAgB;cAAAL,QAAA,gBAC7BnC,OAAA,CAACjB,MAAM,CAAC4E,MAAM;gBACZhB,UAAU,EAAE;kBAAEiB,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BpB,SAAS,EAAE,4DACTjC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;gBAAA4B,QAAA,EAEFvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmD,cAAc,gBACnB/D,OAAA;kBACEgE,GAAG,EAAEpD,IAAI,CAACmD,cAAe;kBACzBE,GAAG,EAAC,SAAS;kBACbzB,SAAS,EAAC;gBAAmC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEFlD,OAAA,CAACX,QAAQ;kBAACmD,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAChC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC,eAGhBlD,OAAA;gBAAKwC,SAAS,EAAC,kLAAkL;gBAAAL,QAAA,gBAC/LnC,OAAA;kBAAKwC,SAAS,EAAC,8BAA8B;kBAAAL,QAAA,gBAC3CnC,OAAA;oBAAGwC,SAAS,EAAC,mCAAmC;oBAAAL,QAAA,GAC7CvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,SAAS,EAAC,GAAC,EAACtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,QAAQ;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACJlD,OAAA;oBAAGwC,SAAS,EAAC,uBAAuB;oBAAAL,QAAA,EAAEvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD;kBAAK;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACNlD,OAAA;kBAAKwC,SAAS,EAAC,MAAM;kBAAAL,QAAA,gBACnBnC,OAAA,CAACnB,IAAI;oBACH4D,EAAE,EAAC,UAAU;oBACbD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPlD,OAAA,CAACnB,IAAI;oBACH4D,EAAE,EAAC,SAAS;oBACZD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPlD,OAAA,CAACnB,IAAI;oBACH4D,EAAE,EAAC,WAAW;oBACdD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPlD,OAAA;oBACEqE,OAAO,EAAEvD,MAAO;oBAChB0B,SAAS,EAAC,6GAA6G;oBAAAL,QAAA,EACxH;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENlD,OAAA;cAAKwC,SAAS,EAAC,6BAA6B;cAAAL,QAAA,gBAC1CnC,OAAA,CAACnB,IAAI;gBAAC4D,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACfnC,OAAA,CAACjB,MAAM,CAAC4E,MAAM;kBACZhB,UAAU,EAAE;oBAAEiB,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BpB,SAAS,EAAE,6EACTjC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;kBAAA4B,QAAA,EACJ;gBAED;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACPlD,OAAA,CAACnB,IAAI;gBAAC4D,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClBnC,OAAA,CAACjB,MAAM,CAAC4E,MAAM;kBACZhB,UAAU,EAAE;oBAAEiB,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BpB,SAAS,EAAC,oIAAoI;kBAAAL,QAAA,EAC/I;gBAED;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGDlD,OAAA;cACEqE,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,CAACD,MAAM,CAAE;cAClCmC,SAAS,EAAE,2DACTjC,UAAU,GACN,2CAA2C,GAC3C,kCAAkC,EACrC;cAAA4B,QAAA,EAEF9B,MAAM,gBACLL,OAAA,CAACd,SAAS;gBAACsD,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjClD,OAAA,CAACf,SAAS;gBAACuD,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA,CAAChB,eAAe;QAAAmD,QAAA,EACb9B,MAAM,iBACLL,OAAA,CAACjB,MAAM,CAAC2D,GAAG;UACTL,OAAO,EAAE;YAAEiC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACnChC,OAAO,EAAE;YAAE+B,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAE;UACxCC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAChC/B,SAAS,EAAE,sEACTxB,eAAe,CACb,6BAA6B,EAC7B,kCACF,CAAC,EACA;UAAAmB,QAAA,eAEHnC,OAAA;YAAKwC,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAElCnC,OAAA;cAAKwC,SAAS,EAAC,UAAU;cAAAL,QAAA,gBACvBnC,OAAA;gBAAKwC,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFnC,OAAA,CAACZ,mBAAmB;kBAACoD,SAAS,EAAE,WAC9BxB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC;gBAChD;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlD,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAE/C,WAAY;gBACnBgD,QAAQ,EAAGvC,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;gBAChDhB,SAAS,EAAE,iFACTxB,eAAe,CACb,2FAA2F,EAC3F,6FACF,CAAC;cACA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlD,OAAA;cAAKwC,SAAS,EAAC,WAAW;cAAAL,QAAA,EACvBP,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACxBpD,OAAA,CAACnB,IAAI;gBAEH4D,EAAE,EAAEW,IAAI,CAACtB,IAAK;gBACduC,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,KAAK,CAAE;gBAChCkC,SAAS,EAAE,mFACTR,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,GACfd,eAAe,CACb,2CAA2C,EAC3C,8CACF,CAAC,GACDA,eAAe,CACb,iCAAiC,EACjC,kCACF,CAAC,EACJ;gBAAAmB,QAAA,gBAEHnC,OAAA,CAACoD,IAAI,CAACrB,IAAI;kBAACS,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjClD,OAAA;kBAAMwC,SAAS,EAAC,aAAa;kBAAAL,QAAA,EAAEiB,IAAI,CAACvB;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAhB3CE,IAAI,CAACvB,IAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlD,OAAA;cAAKwC,SAAS,EAAE,iFACdxB,eAAe,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACrD;cAAAmB,QAAA,gBACDnC,OAAA;gBAAQwC,SAAS,EAAE,uEACjBxB,eAAe,CACb,2CAA2C,EAC3C,2CACF,CAAC,EACA;gBAAAmB,QAAA,gBACDnC,OAAA,CAACV,SAAS;kBAACkD,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjClD,OAAA;kBAAMwC,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACTlD,OAAA,CAACnB,IAAI;gBAAC4D,EAAE,EAAC,UAAU;gBAACD,SAAS,EAAE,uEAC7BxB,eAAe,CACb,2CAA2C,EAC3C,2CACF,CAAC,EACA;gBAAAmB,QAAA,gBACDnC,OAAA,CAACX,QAAQ;kBAACmD,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChClD,OAAA;kBAAMwC,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAO;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACPlD,OAAA;gBAAKwC,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnDnC,OAAA,CAACJ,WAAW;kBAACkE,IAAI,EAAC;gBAAI;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzBlD,OAAA;kBAAMwC,SAAS,EAAE,0CACfxB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAmB,QAAA,EAAC;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACNlD,OAAA;gBAAKwC,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnDnC,OAAA,CAACL,YAAY;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChBlD,OAAA;kBAAMwC,SAAS,EAAE,0CACfxB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAmB,QAAA,EAAC;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGblD,OAAA;MAAKwC,SAAS,EAAC;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAAC9C,EAAA,CA3XID,UAAU;EAAA,QAIGrB,WAAW,EACce,OAAO,EACdC,QAAQ;AAAA;AAAA2E,EAAA,GANvCtE,UAAU;AA6XhB,eAAeA,UAAU;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}