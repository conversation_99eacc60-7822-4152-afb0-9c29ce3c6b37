{"ast": null, "code": "import { useEffect as u, useRef as n } from \"react\";\nimport { microTask as o } from '../utils/micro-task.js';\nimport { useEvent as f } from './use-event.js';\nfunction c(t) {\n  let r = f(t),\n    e = n(!1);\n  u(() => (e.current = !1, () => {\n    e.current = !0, o(() => {\n      e.current && r();\n    });\n  }), [r]);\n}\nexport { c as useOnUnmount };", "map": {"version": 3, "names": ["useEffect", "u", "useRef", "n", "microTask", "o", "useEvent", "f", "c", "t", "r", "e", "current", "useOnUnmount"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-on-unmount.js"], "sourcesContent": ["import{useEffect as u,useRef as n}from\"react\";import{microTask as o}from'../utils/micro-task.js';import{useEvent as f}from'./use-event.js';function c(t){let r=f(t),e=n(!1);u(()=>(e.current=!1,()=>{e.current=!0,o(()=>{e.current&&r()})}),[r])}export{c as useOnUnmount};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACH,CAAC,CAACE,CAAC,CAAC;IAACE,CAAC,GAACR,CAAC,CAAC,CAAC,CAAC,CAAC;EAACF,CAAC,CAAC,OAAKU,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,EAAC,MAAI;IAACD,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,EAACP,CAAC,CAAC,MAAI;MAACM,CAAC,CAACC,OAAO,IAAEF,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}