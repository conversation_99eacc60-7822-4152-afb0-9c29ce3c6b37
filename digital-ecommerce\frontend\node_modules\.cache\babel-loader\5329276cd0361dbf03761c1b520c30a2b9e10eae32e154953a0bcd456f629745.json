{"ast": null, "code": "var S = Object.defineProperty;\nvar f = (t, n, e) => n in t ? S(t, n, {\n  enumerable: !0,\n  configurable: !0,\n  writable: !0,\n  value: e\n}) : t[n] = e;\nvar p = (t, n, e) => (f(t, typeof n != \"symbol\" ? n + \"\" : n, e), e);\nimport { Machine as m } from '../../machine.js';\nimport { stackMachines as P } from '../../machines/stack-machine.js';\nimport * as s from '../../utils/dom.js';\nimport { getFocusableElements as b } from '../../utils/focus-management.js';\nimport { match as y } from '../../utils/match.js';\nvar I = (e => (e[e.Open = 0] = \"Open\", e[e.Closed = 1] = \"Closed\", e))(I || {}),\n  M = (l => (l[l.OpenPopover = 0] = \"OpenPopover\", l[l.ClosePopover = 1] = \"ClosePopover\", l[l.SetButton = 2] = \"SetButton\", l[l.SetButtonId = 3] = \"SetButtonId\", l[l.SetPanel = 4] = \"SetPanel\", l[l.SetPanelId = 5] = \"SetPanelId\", l))(M || {});\nlet T = {\n  [0]: t => t.popoverState === 0 ? t : {\n    ...t,\n    popoverState: 0,\n    __demoMode: !1\n  },\n  [1](t) {\n    return t.popoverState === 1 ? t : {\n      ...t,\n      popoverState: 1,\n      __demoMode: !1\n    };\n  },\n  [2](t, n) {\n    return t.button === n.button ? t : {\n      ...t,\n      button: n.button\n    };\n  },\n  [3](t, n) {\n    return t.buttonId === n.buttonId ? t : {\n      ...t,\n      buttonId: n.buttonId\n    };\n  },\n  [4](t, n) {\n    return t.panel === n.panel ? t : {\n      ...t,\n      panel: n.panel\n    };\n  },\n  [5](t, n) {\n    return t.panelId === n.panelId ? t : {\n      ...t,\n      panelId: n.panelId\n    };\n  }\n};\nclass i extends m {\n  constructor(e) {\n    super(e);\n    p(this, \"actions\", {\n      close: () => this.send({\n        type: 1\n      }),\n      refocusableClose: e => {\n        this.actions.close();\n        let o = (() => e ? s.isHTMLElement(e) ? e : \"current\" in e && s.isHTMLElement(e.current) ? e.current : this.state.button : this.state.button)();\n        o == null || o.focus();\n      },\n      open: () => this.send({\n        type: 0\n      }),\n      setButtonId: e => this.send({\n        type: 3,\n        buttonId: e\n      }),\n      setButton: e => this.send({\n        type: 2,\n        button: e\n      }),\n      setPanelId: e => this.send({\n        type: 5,\n        panelId: e\n      }),\n      setPanel: e => this.send({\n        type: 4,\n        panel: e\n      })\n    });\n    p(this, \"selectors\", {\n      isPortalled: e => {\n        if (!e.button || !e.panel) return !1;\n        for (let r of document.querySelectorAll(\"body > *\")) if (Number(r == null ? void 0 : r.contains(e.button)) ^ Number(r == null ? void 0 : r.contains(e.panel))) return !0;\n        let o = b(),\n          u = o.indexOf(e.button),\n          a = (u + o.length - 1) % o.length,\n          l = (u + 1) % o.length,\n          d = o[a],\n          c = o[l];\n        return !e.panel.contains(d) && !e.panel.contains(c);\n      }\n    });\n    {\n      let o = this.state.id,\n        u = P.get(null);\n      this.on(0, () => u.actions.push(o)), this.on(1, () => u.actions.pop(o));\n    }\n  }\n  static new({\n    id: e,\n    __demoMode: o = !1\n  }) {\n    return new i({\n      id: e,\n      __demoMode: o,\n      popoverState: o ? 0 : 1,\n      buttons: {\n        current: []\n      },\n      button: null,\n      buttonId: null,\n      panel: null,\n      panelId: null,\n      beforePanelSentinel: {\n        current: null\n      },\n      afterPanelSentinel: {\n        current: null\n      },\n      afterButtonSentinel: {\n        current: null\n      }\n    });\n  }\n  reduce(e, o) {\n    return y(o.type, T, e, o);\n  }\n}\nexport { M as ActionTypes, i as PopoverMachine, I as PopoverStates };", "map": {"version": 3, "names": ["S", "Object", "defineProperty", "f", "t", "n", "e", "enumerable", "configurable", "writable", "value", "p", "Machine", "m", "stackMachines", "P", "s", "getFocusableElements", "b", "match", "y", "I", "Open", "Closed", "M", "l", "OpenPopover", "ClosePopover", "SetButton", "SetButtonId", "SetPanel", "SetPanelId", "T", "popoverState", "__demoMode", "button", "buttonId", "panel", "panelId", "i", "constructor", "close", "send", "type", "refocusableClose", "actions", "o", "isHTMLElement", "current", "state", "focus", "open", "setButtonId", "setButton", "setPanelId", "setPanel", "isPortalled", "r", "document", "querySelectorAll", "Number", "contains", "u", "indexOf", "a", "length", "d", "c", "id", "get", "on", "push", "pop", "new", "buttons", "beforePanelSentinel", "afterPanelSentinel", "afterButtonSentinel", "reduce", "ActionTypes", "PopoverMachine", "PopoverStates"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/popover/popover-machine.js"], "sourcesContent": ["var S=Object.defineProperty;var f=(t,n,e)=>n in t?S(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e;var p=(t,n,e)=>(f(t,typeof n!=\"symbol\"?n+\"\":n,e),e);import{Machine as m}from'../../machine.js';import{stackMachines as P}from'../../machines/stack-machine.js';import*as s from'../../utils/dom.js';import{getFocusableElements as b}from'../../utils/focus-management.js';import{match as y}from'../../utils/match.js';var I=(e=>(e[e.Open=0]=\"Open\",e[e.Closed=1]=\"Closed\",e))(I||{}),M=(l=>(l[l.OpenPopover=0]=\"OpenPopover\",l[l.ClosePopover=1]=\"ClosePopover\",l[l.SetButton=2]=\"SetButton\",l[l.SetButtonId=3]=\"SetButtonId\",l[l.SetPanel=4]=\"SetPanel\",l[l.SetPanelId=5]=\"SetPanelId\",l))(M||{});let T={[0]:t=>t.popoverState===0?t:{...t,popoverState:0,__demoMode:!1},[1](t){return t.popoverState===1?t:{...t,popoverState:1,__demoMode:!1}},[2](t,n){return t.button===n.button?t:{...t,button:n.button}},[3](t,n){return t.buttonId===n.buttonId?t:{...t,buttonId:n.buttonId}},[4](t,n){return t.panel===n.panel?t:{...t,panel:n.panel}},[5](t,n){return t.panelId===n.panelId?t:{...t,panelId:n.panelId}}};class i extends m{constructor(e){super(e);p(this,\"actions\",{close:()=>this.send({type:1}),refocusableClose:e=>{this.actions.close();let o=(()=>e?s.isHTMLElement(e)?e:\"current\"in e&&s.isHTMLElement(e.current)?e.current:this.state.button:this.state.button)();o==null||o.focus()},open:()=>this.send({type:0}),setButtonId:e=>this.send({type:3,buttonId:e}),setButton:e=>this.send({type:2,button:e}),setPanelId:e=>this.send({type:5,panelId:e}),setPanel:e=>this.send({type:4,panel:e})});p(this,\"selectors\",{isPortalled:e=>{if(!e.button||!e.panel)return!1;for(let r of document.querySelectorAll(\"body > *\"))if(Number(r==null?void 0:r.contains(e.button))^Number(r==null?void 0:r.contains(e.panel)))return!0;let o=b(),u=o.indexOf(e.button),a=(u+o.length-1)%o.length,l=(u+1)%o.length,d=o[a],c=o[l];return!e.panel.contains(d)&&!e.panel.contains(c)}});{let o=this.state.id,u=P.get(null);this.on(0,()=>u.actions.push(o)),this.on(1,()=>u.actions.pop(o))}}static new({id:e,__demoMode:o=!1}){return new i({id:e,__demoMode:o,popoverState:o?0:1,buttons:{current:[]},button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:{current:null},afterPanelSentinel:{current:null},afterButtonSentinel:{current:null}})}reduce(e,o){return y(o.type,T,e,o)}}export{M as ActionTypes,i as PopoverMachine,I as PopoverStates};\n"], "mappings": "AAAA,IAAIA,CAAC,GAACC,MAAM,CAACC,cAAc;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,IAAID,CAAC,GAACJ,CAAC,CAACI,CAAC,EAACC,CAAC,EAAC;EAACE,UAAU,EAAC,CAAC,CAAC;EAACC,YAAY,EAAC,CAAC,CAAC;EAACC,QAAQ,EAAC,CAAC,CAAC;EAACC,KAAK,EAACJ;AAAC,CAAC,CAAC,GAACF,CAAC,CAACC,CAAC,CAAC,GAACC,CAAC;AAAC,IAAIK,CAAC,GAACA,CAACP,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIH,CAAC,CAACC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,GAACA,CAAC,GAAC,EAAE,GAACA,CAAC,EAACC,CAAC,CAAC,EAACA,CAAC,CAAC;AAAC,SAAOM,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,iCAAiC;AAAC,OAAM,KAAIC,CAAC,MAAK,oBAAoB;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,sBAAsB;AAAC,IAAIC,CAAC,GAAC,CAACf,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACgB,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAAChB,CAAC,CAACA,CAAC,CAACiB,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACjB,CAAC,CAAC,EAAEe,CAAC,IAAE,CAAC,CAAC,CAAC;EAACG,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,WAAW,GAAC,CAAC,CAAC,GAAC,aAAa,EAACD,CAAC,CAACA,CAAC,CAACE,YAAY,GAAC,CAAC,CAAC,GAAC,cAAc,EAACF,CAAC,CAACA,CAAC,CAACG,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACH,CAAC,CAACA,CAAC,CAACI,WAAW,GAAC,CAAC,CAAC,GAAC,aAAa,EAACJ,CAAC,CAACA,CAAC,CAACK,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACL,CAAC,CAACA,CAAC,CAACM,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACN,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,IAAIQ,CAAC,GAAC;EAAC,CAAC,CAAC,GAAE5B,CAAC,IAAEA,CAAC,CAAC6B,YAAY,KAAG,CAAC,GAAC7B,CAAC,GAAC;IAAC,GAAGA,CAAC;IAAC6B,YAAY,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;EAAC,CAAC;EAAC,CAAC,CAAC,EAAE9B,CAAC,EAAC;IAAC,OAAOA,CAAC,CAAC6B,YAAY,KAAG,CAAC,GAAC7B,CAAC,GAAC;MAAC,GAAGA,CAAC;MAAC6B,YAAY,EAAC,CAAC;MAACC,UAAU,EAAC,CAAC;IAAC,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAE9B,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOD,CAAC,CAAC+B,MAAM,KAAG9B,CAAC,CAAC8B,MAAM,GAAC/B,CAAC,GAAC;MAAC,GAAGA,CAAC;MAAC+B,MAAM,EAAC9B,CAAC,CAAC8B;IAAM,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAE/B,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOD,CAAC,CAACgC,QAAQ,KAAG/B,CAAC,CAAC+B,QAAQ,GAAChC,CAAC,GAAC;MAAC,GAAGA,CAAC;MAACgC,QAAQ,EAAC/B,CAAC,CAAC+B;IAAQ,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAEhC,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOD,CAAC,CAACiC,KAAK,KAAGhC,CAAC,CAACgC,KAAK,GAACjC,CAAC,GAAC;MAAC,GAAGA,CAAC;MAACiC,KAAK,EAAChC,CAAC,CAACgC;IAAK,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAEjC,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOD,CAAC,CAACkC,OAAO,KAAGjC,CAAC,CAACiC,OAAO,GAAClC,CAAC,GAAC;MAAC,GAAGA,CAAC;MAACkC,OAAO,EAACjC,CAAC,CAACiC;IAAO,CAAC;EAAA;AAAC,CAAC;AAAC,MAAMC,CAAC,SAAS1B,CAAC;EAAC2B,WAAWA,CAAClC,CAAC,EAAC;IAAC,KAAK,CAACA,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,SAAS,EAAC;MAAC8B,KAAK,EAACA,CAAA,KAAI,IAAI,CAACC,IAAI,CAAC;QAACC,IAAI,EAAC;MAAC,CAAC,CAAC;MAACC,gBAAgB,EAACtC,CAAC,IAAE;QAAC,IAAI,CAACuC,OAAO,CAACJ,KAAK,CAAC,CAAC;QAAC,IAAIK,CAAC,GAAC,CAAC,MAAIxC,CAAC,GAACU,CAAC,CAAC+B,aAAa,CAACzC,CAAC,CAAC,GAACA,CAAC,GAAC,SAAS,IAAGA,CAAC,IAAEU,CAAC,CAAC+B,aAAa,CAACzC,CAAC,CAAC0C,OAAO,CAAC,GAAC1C,CAAC,CAAC0C,OAAO,GAAC,IAAI,CAACC,KAAK,CAACd,MAAM,GAAC,IAAI,CAACc,KAAK,CAACd,MAAM,EAAE,CAAC;QAACW,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACI,KAAK,CAAC,CAAC;MAAA,CAAC;MAACC,IAAI,EAACA,CAAA,KAAI,IAAI,CAACT,IAAI,CAAC;QAACC,IAAI,EAAC;MAAC,CAAC,CAAC;MAACS,WAAW,EAAC9C,CAAC,IAAE,IAAI,CAACoC,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACP,QAAQ,EAAC9B;MAAC,CAAC,CAAC;MAAC+C,SAAS,EAAC/C,CAAC,IAAE,IAAI,CAACoC,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACR,MAAM,EAAC7B;MAAC,CAAC,CAAC;MAACgD,UAAU,EAAChD,CAAC,IAAE,IAAI,CAACoC,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACL,OAAO,EAAChC;MAAC,CAAC,CAAC;MAACiD,QAAQ,EAACjD,CAAC,IAAE,IAAI,CAACoC,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACN,KAAK,EAAC/B;MAAC,CAAC;IAAC,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,WAAW,EAAC;MAAC6C,WAAW,EAAClD,CAAC,IAAE;QAAC,IAAG,CAACA,CAAC,CAAC6B,MAAM,IAAE,CAAC7B,CAAC,CAAC+B,KAAK,EAAC,OAAM,CAAC,CAAC;QAAC,KAAI,IAAIoB,CAAC,IAAIC,QAAQ,CAACC,gBAAgB,CAAC,UAAU,CAAC,EAAC,IAAGC,MAAM,CAACH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACI,QAAQ,CAACvD,CAAC,CAAC6B,MAAM,CAAC,CAAC,GAACyB,MAAM,CAACH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACI,QAAQ,CAACvD,CAAC,CAAC+B,KAAK,CAAC,CAAC,EAAC,OAAM,CAAC,CAAC;QAAC,IAAIS,CAAC,GAAC5B,CAAC,CAAC,CAAC;UAAC4C,CAAC,GAAChB,CAAC,CAACiB,OAAO,CAACzD,CAAC,CAAC6B,MAAM,CAAC;UAAC6B,CAAC,GAAC,CAACF,CAAC,GAAChB,CAAC,CAACmB,MAAM,GAAC,CAAC,IAAEnB,CAAC,CAACmB,MAAM;UAACxC,CAAC,GAAC,CAACqC,CAAC,GAAC,CAAC,IAAEhB,CAAC,CAACmB,MAAM;UAACC,CAAC,GAACpB,CAAC,CAACkB,CAAC,CAAC;UAACG,CAAC,GAACrB,CAAC,CAACrB,CAAC,CAAC;QAAC,OAAM,CAACnB,CAAC,CAAC+B,KAAK,CAACwB,QAAQ,CAACK,CAAC,CAAC,IAAE,CAAC5D,CAAC,CAAC+B,KAAK,CAACwB,QAAQ,CAACM,CAAC,CAAC;MAAA;IAAC,CAAC,CAAC;IAAC;MAAC,IAAIrB,CAAC,GAAC,IAAI,CAACG,KAAK,CAACmB,EAAE;QAACN,CAAC,GAAC/C,CAAC,CAACsD,GAAG,CAAC,IAAI,CAAC;MAAC,IAAI,CAACC,EAAE,CAAC,CAAC,EAAC,MAAIR,CAAC,CAACjB,OAAO,CAAC0B,IAAI,CAACzB,CAAC,CAAC,CAAC,EAAC,IAAI,CAACwB,EAAE,CAAC,CAAC,EAAC,MAAIR,CAAC,CAACjB,OAAO,CAAC2B,GAAG,CAAC1B,CAAC,CAAC,CAAC;IAAA;EAAC;EAAC,OAAO2B,GAAGA,CAAC;IAACL,EAAE,EAAC9D,CAAC;IAAC4B,UAAU,EAACY,CAAC,GAAC,CAAC;EAAC,CAAC,EAAC;IAAC,OAAO,IAAIP,CAAC,CAAC;MAAC6B,EAAE,EAAC9D,CAAC;MAAC4B,UAAU,EAACY,CAAC;MAACb,YAAY,EAACa,CAAC,GAAC,CAAC,GAAC,CAAC;MAAC4B,OAAO,EAAC;QAAC1B,OAAO,EAAC;MAAE,CAAC;MAACb,MAAM,EAAC,IAAI;MAACC,QAAQ,EAAC,IAAI;MAACC,KAAK,EAAC,IAAI;MAACC,OAAO,EAAC,IAAI;MAACqC,mBAAmB,EAAC;QAAC3B,OAAO,EAAC;MAAI,CAAC;MAAC4B,kBAAkB,EAAC;QAAC5B,OAAO,EAAC;MAAI,CAAC;MAAC6B,mBAAmB,EAAC;QAAC7B,OAAO,EAAC;MAAI;IAAC,CAAC,CAAC;EAAA;EAAC8B,MAAMA,CAACxE,CAAC,EAACwC,CAAC,EAAC;IAAC,OAAO1B,CAAC,CAAC0B,CAAC,CAACH,IAAI,EAACX,CAAC,EAAC1B,CAAC,EAACwC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOtB,CAAC,IAAIuD,WAAW,EAACxC,CAAC,IAAIyC,cAAc,EAAC3D,CAAC,IAAI4D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}