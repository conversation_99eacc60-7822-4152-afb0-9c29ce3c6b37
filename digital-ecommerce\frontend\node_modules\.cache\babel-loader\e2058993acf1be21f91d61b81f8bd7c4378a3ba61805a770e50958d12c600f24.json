{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [theme, setTheme] = useState('light');\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const initializeTheme = () => {\n      try {\n        // Check localStorage first\n        const savedTheme = localStorage.getItem('theme');\n        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n          setTheme(savedTheme);\n        } else {\n          // Check system preference\n          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n          const systemTheme = prefersDark ? 'dark' : 'light';\n          setTheme(systemTheme);\n          localStorage.setItem('theme', systemTheme);\n        }\n      } catch (error) {\n        console.error('Error initializing theme:', error);\n        setTheme('light');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    initializeTheme();\n  }, []);\n\n  // Apply theme to document root\n  useEffect(() => {\n    if (!isLoading) {\n      const root = document.documentElement;\n      if (theme === 'dark') {\n        root.classList.add('dark');\n      } else {\n        root.classList.remove('dark');\n      }\n\n      // Update meta theme-color for mobile browsers\n      const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n      if (metaThemeColor) {\n        metaThemeColor.setAttribute('content', theme === 'dark' ? '#1f2937' : '#FFB366');\n      }\n    }\n  }, [theme, isLoading]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleSystemThemeChange = e => {\n      // Only update if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('theme');\n      if (!savedTheme) {\n        const newTheme = e.matches ? 'dark' : 'light';\n        setTheme(newTheme);\n        localStorage.setItem('theme', newTheme);\n      }\n    };\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n    return () => {\n      mediaQuery.removeEventListener('change', handleSystemThemeChange);\n    };\n  }, []);\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n  const setLightTheme = () => {\n    setTheme('light');\n    localStorage.setItem('theme', 'light');\n  };\n  const setDarkTheme = () => {\n    setTheme('dark');\n    localStorage.setItem('theme', 'dark');\n  };\n\n  // Theme-aware utility functions\n  const getThemeClasses = (lightClasses, darkClasses) => {\n    return theme === 'dark' ? darkClasses : lightClasses;\n  };\n  const getThemeValue = (lightValue, darkValue) => {\n    return theme === 'dark' ? darkValue : lightValue;\n  };\n  const value = {\n    theme,\n    isLoading,\n    isDark: theme === 'dark',\n    isLight: theme === 'light',\n    toggleTheme,\n    setLightTheme,\n    setDarkTheme,\n    getThemeClasses,\n    getThemeValue\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"+ROYjCB9uDOQqs4N7J2ujnhscUw=\");\n_c = ThemeProvider;\nexport default ThemeContext;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "theme", "setTheme", "isLoading", "setIsLoading", "initializeTheme", "savedTheme", "localStorage", "getItem", "prefersDark", "window", "matchMedia", "matches", "systemTheme", "setItem", "error", "console", "root", "document", "documentElement", "classList", "add", "remove", "metaThemeColor", "querySelector", "setAttribute", "mediaQuery", "handleSystemThemeChange", "e", "newTheme", "addEventListener", "removeEventListener", "toggleTheme", "setLightTheme", "setDarkTheme", "getThemeClasses", "lightClasses", "darkClasses", "getThemeValue", "lightValue", "darkValue", "value", "isDark", "isLight", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState('light');\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const initializeTheme = () => {\n      try {\n        // Check localStorage first\n        const savedTheme = localStorage.getItem('theme');\n        \n        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n          setTheme(savedTheme);\n        } else {\n          // Check system preference\n          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n          const systemTheme = prefersDark ? 'dark' : 'light';\n          setTheme(systemTheme);\n          localStorage.setItem('theme', systemTheme);\n        }\n      } catch (error) {\n        console.error('Error initializing theme:', error);\n        setTheme('light');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeTheme();\n  }, []);\n\n  // Apply theme to document root\n  useEffect(() => {\n    if (!isLoading) {\n      const root = document.documentElement;\n\n      if (theme === 'dark') {\n        root.classList.add('dark');\n      } else {\n        root.classList.remove('dark');\n      }\n\n      // Update meta theme-color for mobile browsers\n      const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n      if (metaThemeColor) {\n        metaThemeColor.setAttribute('content', theme === 'dark' ? '#1f2937' : '#FFB366');\n      }\n    }\n  }, [theme, isLoading]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleSystemThemeChange = (e) => {\n      // Only update if user hasn't manually set a preference\n      const savedTheme = localStorage.getItem('theme');\n      if (!savedTheme) {\n        const newTheme = e.matches ? 'dark' : 'light';\n        setTheme(newTheme);\n        localStorage.setItem('theme', newTheme);\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n    \n    return () => {\n      mediaQuery.removeEventListener('change', handleSystemThemeChange);\n    };\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n\n  const setLightTheme = () => {\n    setTheme('light');\n    localStorage.setItem('theme', 'light');\n  };\n\n  const setDarkTheme = () => {\n    setTheme('dark');\n    localStorage.setItem('theme', 'dark');\n  };\n\n  // Theme-aware utility functions\n  const getThemeClasses = (lightClasses, darkClasses) => {\n    return theme === 'dark' ? darkClasses : lightClasses;\n  };\n\n  const getThemeValue = (lightValue, darkValue) => {\n    return theme === 'dark' ? darkValue : lightValue;\n  };\n\n  const value = {\n    theme,\n    isLoading,\n    isDark: theme === 'dark',\n    isLight: theme === 'light',\n    toggleTheme,\n    setLightTheme,\n    setDarkTheme,\n    getThemeClasses,\n    getThemeValue\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport default ThemeContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMe,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI;QACF;QACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAEhD,IAAIF,UAAU,KAAKA,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,MAAM,CAAC,EAAE;UACnEJ,QAAQ,CAACI,UAAU,CAAC;QACtB,CAAC,MAAM;UACL;UACA,MAAMG,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;UAC7E,MAAMC,WAAW,GAAGJ,WAAW,GAAG,MAAM,GAAG,OAAO;UAClDP,QAAQ,CAACW,WAAW,CAAC;UACrBN,YAAY,CAACO,OAAO,CAAC,OAAO,EAAED,WAAW,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDb,QAAQ,CAAC,OAAO,CAAC;MACnB,CAAC,SAAS;QACRE,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAf,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,SAAS,EAAE;MACd,MAAMc,IAAI,GAAGC,QAAQ,CAACC,eAAe;MAErC,IAAIlB,KAAK,KAAK,MAAM,EAAE;QACpBgB,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLJ,IAAI,CAACG,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;MAC/B;;MAEA;MACA,MAAMC,cAAc,GAAGL,QAAQ,CAACM,aAAa,CAAC,0BAA0B,CAAC;MACzE,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACE,YAAY,CAAC,SAAS,EAAExB,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;MAClF;IACF;EACF,CAAC,EAAE,CAACA,KAAK,EAAEE,SAAS,CAAC,CAAC;;EAEtB;EACAb,SAAS,CAAC,MAAM;IACd,MAAMoC,UAAU,GAAGhB,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IAEpE,MAAMgB,uBAAuB,GAAIC,CAAC,IAAK;MACrC;MACA,MAAMtB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAChD,IAAI,CAACF,UAAU,EAAE;QACf,MAAMuB,QAAQ,GAAGD,CAAC,CAAChB,OAAO,GAAG,MAAM,GAAG,OAAO;QAC7CV,QAAQ,CAAC2B,QAAQ,CAAC;QAClBtB,YAAY,CAACO,OAAO,CAAC,OAAO,EAAEe,QAAQ,CAAC;MACzC;IACF,CAAC;IAEDH,UAAU,CAACI,gBAAgB,CAAC,QAAQ,EAAEH,uBAAuB,CAAC;IAE9D,OAAO,MAAM;MACXD,UAAU,CAACK,mBAAmB,CAAC,QAAQ,EAAEJ,uBAAuB,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMH,QAAQ,GAAG5B,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IACrDC,QAAQ,CAAC2B,QAAQ,CAAC;IAClBtB,YAAY,CAACO,OAAO,CAAC,OAAO,EAAEe,QAAQ,CAAC;EACzC,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B/B,QAAQ,CAAC,OAAO,CAAC;IACjBK,YAAY,CAACO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;EACxC,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzBhC,QAAQ,CAAC,MAAM,CAAC;IAChBK,YAAY,CAACO,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;EACvC,CAAC;;EAED;EACA,MAAMqB,eAAe,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;IACrD,OAAOpC,KAAK,KAAK,MAAM,GAAGoC,WAAW,GAAGD,YAAY;EACtD,CAAC;EAED,MAAME,aAAa,GAAGA,CAACC,UAAU,EAAEC,SAAS,KAAK;IAC/C,OAAOvC,KAAK,KAAK,MAAM,GAAGuC,SAAS,GAAGD,UAAU;EAClD,CAAC;EAED,MAAME,KAAK,GAAG;IACZxC,KAAK;IACLE,SAAS;IACTuC,MAAM,EAAEzC,KAAK,KAAK,MAAM;IACxB0C,OAAO,EAAE1C,KAAK,KAAK,OAAO;IAC1B+B,WAAW;IACXC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfG;EACF,CAAC;EAED,oBACE9C,OAAA,CAACC,YAAY,CAACmD,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAA1C,QAAA,EACjCA;EAAQ;IAAA8C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAChD,GAAA,CAjHWF,aAAa;AAAAmD,EAAA,GAAbnD,aAAa;AAmH1B,eAAeL,YAAY;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}