{"ast": null, "code": "\"use client\";\n\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"value\", \"defaultValue\", \"onChange\", \"form\", \"name\", \"by\", \"invalid\", \"disabled\", \"onClose\", \"__demoMode\", \"multiple\", \"immediate\", \"virtual\", \"nullable\"],\n  _excluded2 = [\"id\", \"onChange\", \"displayValue\", \"disabled\", \"autoFocus\", \"type\"],\n  _excluded3 = [\"id\", \"disabled\", \"autoFocus\"],\n  _excluded4 = [\"id\", \"hold\", \"anchor\", \"portal\", \"modal\", \"transition\"],\n  _excluded5 = [\"id\", \"value\", \"disabled\", \"order\"];\nimport { useFocusRing as ve } from \"@react-aria/focus\";\nimport { useHover as Pe } from \"@react-aria/interactions\";\nimport { useVirtualizer as Le } from \"@tanstack/react-virtual\";\nimport F, { Fragment as Ee, createContext as Oe, useCallback as Z, useContext as he, useMemo as K, useRef as me, useState as xe } from \"react\";\nimport { flushSync as re } from \"react-dom\";\nimport { useActivePress as Ve } from '../../hooks/use-active-press.js';\nimport { useByComparator as we } from '../../hooks/use-by-comparator.js';\nimport { useControllable as Be } from '../../hooks/use-controllable.js';\nimport { useDefaultValue as Ne } from '../../hooks/use-default-value.js';\nimport { useDisposables as ke } from '../../hooks/use-disposables.js';\nimport { useElementSize as Ae } from '../../hooks/use-element-size.js';\nimport { useEvent as O } from '../../hooks/use-event.js';\nimport { useId as le } from '../../hooks/use-id.js';\nimport { useInertOthers as Ue } from '../../hooks/use-inert-others.js';\nimport { useIsoMorphicEffect as ee } from '../../hooks/use-iso-morphic-effect.js';\nimport { useLatestValue as He } from '../../hooks/use-latest-value.js';\nimport { useOnDisappear as Ge } from '../../hooks/use-on-disappear.js';\nimport { useOutsideClick as ze } from '../../hooks/use-outside-click.js';\nimport { useOwnerDocument as ge } from '../../hooks/use-owner.js';\nimport { Action as ie, useQuickRelease as Ke } from '../../hooks/use-quick-release.js';\nimport { useRefocusableInput as Ie } from '../../hooks/use-refocusable-input.js';\nimport { useResolveButtonType as We } from '../../hooks/use-resolve-button-type.js';\nimport { useScrollLock as Xe } from '../../hooks/use-scroll-lock.js';\nimport { useSyncRefs as ce } from '../../hooks/use-sync-refs.js';\nimport { useTrackedPointer as $e } from '../../hooks/use-tracked-pointer.js';\nimport { transitionDataAttributes as Je, useTransition as je } from '../../hooks/use-transition.js';\nimport { useTreeWalker as qe } from '../../hooks/use-tree-walker.js';\nimport { useWatch as Re } from '../../hooks/use-watch.js';\nimport { useDisabled as Qe } from '../../internal/disabled.js';\nimport { FloatingProvider as Ye, useFloatingPanel as Ze, useFloatingPanelProps as eo, useFloatingReference as oo, useResolvedAnchor as to } from '../../internal/floating.js';\nimport { FormFields as no } from '../../internal/form-fields.js';\nimport { Frozen as ro, useFrozenData as De } from '../../internal/frozen.js';\nimport { useProvidedId as ao } from '../../internal/id.js';\nimport { OpenClosedProvider as lo, State as fe, useOpenClosed as io } from '../../internal/open-closed.js';\nimport { stackMachines as so } from '../../machines/stack-machine.js';\nimport { useSlice as D } from '../../react-glue.js';\nimport { history as _e } from '../../utils/active-element-history.js';\nimport { isDisabledReactIssue7711 as uo } from '../../utils/bugs.js';\nimport { Focus as L } from '../../utils/calculate-active-index.js';\nimport { disposables as po } from '../../utils/disposables.js';\nimport * as bo from '../../utils/dom.js';\nimport { match as Te } from '../../utils/match.js';\nimport { isMobile as mo } from '../../utils/platform.js';\nimport { RenderFeatures as Fe, forwardRefWithAs as se, mergeProps as ye, useRender as ue } from '../../utils/render.js';\nimport { useDescribedBy as co } from '../description/description.js';\nimport { Keys as V } from '../keyboard.js';\nimport { Label as fo, useLabelledBy as Ce, useLabels as To } from '../label/label.js';\nimport { MouseButton as Se } from '../mouse.js';\nimport { Portal as xo } from '../portal/portal.js';\nimport { ActionTypes as go, ActivationTrigger as oe, ComboboxState as l, ValueMode as N } from './combobox-machine.js';\nimport { ComboboxContext as yo, useComboboxMachine as Co, useComboboxMachineContext as pe } from './combobox-machine-glue.js';\nlet de = Oe(null);\nde.displayName = \"ComboboxDataContext\";\nfunction ae(C) {\n  let h = he(de);\n  if (h === null) {\n    let e = new Error(\"<\".concat(C, \" /> is missing a parent <Combobox /> component.\"));\n    throw Error.captureStackTrace && Error.captureStackTrace(e, ae), e;\n  }\n  return h;\n}\nlet Me = Oe(null);\nfunction vo(C) {\n  let h = pe(\"VirtualProvider\"),\n    e = ae(\"VirtualProvider\"),\n    {\n      options: o\n    } = e.virtual,\n    A = D(h, a => a.optionsElement),\n    [R, v] = K(() => {\n      let a = A;\n      if (!a) return [0, 0];\n      let u = window.getComputedStyle(a);\n      return [parseFloat(u.paddingBlockStart || u.paddingTop), parseFloat(u.paddingBlockEnd || u.paddingBottom)];\n    }, [A]),\n    T = Le({\n      enabled: o.length !== 0,\n      scrollPaddingStart: R,\n      scrollPaddingEnd: v,\n      count: o.length,\n      estimateSize() {\n        return 40;\n      },\n      getScrollElement() {\n        return h.state.optionsElement;\n      },\n      overscan: 12\n    }),\n    [I, m] = xe(0);\n  ee(() => {\n    m(a => a + 1);\n  }, [o]);\n  let g = T.getVirtualItems(),\n    n = D(h, a => a.activationTrigger === oe.Pointer),\n    f = D(h, h.selectors.activeOptionIndex);\n  return g.length === 0 ? null : F.createElement(Me.Provider, {\n    value: T\n  }, F.createElement(\"div\", {\n    style: {\n      position: \"relative\",\n      width: \"100%\",\n      height: \"\".concat(T.getTotalSize(), \"px\")\n    },\n    ref: a => {\n      a && (n || f !== null && o.length > f && T.scrollToIndex(f));\n    }\n  }, g.map(a => {\n    var u;\n    return F.createElement(Ee, {\n      key: a.key\n    }, F.cloneElement((u = C.children) == null ? void 0 : u.call(C, _objectSpread(_objectSpread({}, C.slot), {}, {\n      option: o[a.index]\n    })), {\n      key: \"\".concat(I, \"-\").concat(a.key),\n      \"data-index\": a.index,\n      \"aria-setsize\": o.length,\n      \"aria-posinset\": a.index + 1,\n      style: {\n        position: \"absolute\",\n        top: 0,\n        left: 0,\n        transform: \"translateY(\".concat(a.start, \"px)\"),\n        overflowAnchor: \"none\"\n      }\n    }));\n  })));\n}\nlet Po = Ee;\nfunction Eo(C, h) {\n  let e = le(),\n    o = Qe(),\n    {\n      value: A,\n      defaultValue: R,\n      onChange: v,\n      form: T,\n      name: I,\n      by: m,\n      invalid: g = !1,\n      disabled: n = o || !1,\n      onClose: f,\n      __demoMode: a = !1,\n      multiple: u = !1,\n      immediate: S = !1,\n      virtual: d = null,\n      nullable: k\n    } = C,\n    W = _objectWithoutProperties(C, _excluded),\n    y = Ne(R),\n    [x = u ? [] : void 0, P] = Be(A, v, y),\n    b = Co({\n      id: e,\n      virtual: d,\n      __demoMode: a\n    }),\n    G = me({\n      static: !1,\n      hold: !1\n    }),\n    _ = we(m),\n    z = O(s => d ? m === null ? d.options.indexOf(s) : d.options.findIndex(c => _(c, s)) : b.state.options.findIndex(c => _(c.dataRef.current.value, s))),\n    U = Z(s => Te(r.mode, {\n      [N.Multi]: () => x.some(c => _(c, s)),\n      [N.Single]: () => _(x, s)\n    }), [x]),\n    w = D(b, s => s.virtual),\n    J = O(() => f == null ? void 0 : f()),\n    r = K(() => ({\n      __demoMode: a,\n      immediate: S,\n      optionsPropsRef: G,\n      value: x,\n      defaultValue: y,\n      disabled: n,\n      invalid: g,\n      mode: u ? N.Multi : N.Single,\n      virtual: d ? w : null,\n      onChange: P,\n      isSelected: U,\n      calculateIndex: z,\n      compare: _,\n      onClose: J\n    }), [x, y, n, g, u, P, U, a, b, d, w, J]);\n  ee(() => {\n    var s;\n    d && b.send({\n      type: go.UpdateVirtualConfiguration,\n      options: d.options,\n      disabled: (s = d.disabled) != null ? s : null\n    });\n  }, [d, d == null ? void 0 : d.options, d == null ? void 0 : d.disabled]), ee(() => {\n    b.state.dataRef.current = r;\n  }, [r]);\n  let [M, X, i, H] = D(b, s => [s.comboboxState, s.buttonElement, s.inputElement, s.optionsElement]),\n    j = so.get(null),\n    q = D(j, Z(s => j.selectors.isTop(s, e), [j, e]));\n  ze(q, [X, i, H], () => b.actions.closeCombobox());\n  let Q = D(b, b.selectors.activeOptionIndex),\n    $ = D(b, b.selectors.activeOption),\n    be = K(() => ({\n      open: M === l.Open,\n      disabled: n,\n      invalid: g,\n      activeIndex: Q,\n      activeOption: $,\n      value: x\n    }), [r, n, x, g, $, M]),\n    [Y, te] = To(),\n    t = h === null ? {} : {\n      ref: h\n    },\n    B = Z(() => {\n      if (y !== void 0) return P == null ? void 0 : P(y);\n    }, [P, y]),\n    E = ue();\n  return F.createElement(te, {\n    value: Y,\n    props: {\n      htmlFor: i == null ? void 0 : i.id\n    },\n    slot: {\n      open: M === l.Open,\n      disabled: n\n    }\n  }, F.createElement(Ye, null, F.createElement(de.Provider, {\n    value: r\n  }, F.createElement(yo.Provider, {\n    value: b\n  }, F.createElement(lo, {\n    value: Te(M, {\n      [l.Open]: fe.Open,\n      [l.Closed]: fe.Closed\n    })\n  }, I != null && F.createElement(no, {\n    disabled: n,\n    data: x != null ? {\n      [I]: x\n    } : {},\n    form: T,\n    onReset: B\n  }), E({\n    ourProps: t,\n    theirProps: W,\n    slot: be,\n    defaultTag: Po,\n    name: \"Combobox\"\n  }))))));\n}\nlet Oo = \"input\";\nfunction ho(C, h) {\n  var Y, te;\n  let e = pe(\"Combobox.Input\"),\n    o = ae(\"Combobox.Input\"),\n    A = le(),\n    R = ao(),\n    {\n      id: v = R || \"headlessui-combobox-input-\".concat(A),\n      onChange: T,\n      displayValue: I,\n      disabled: m = o.disabled || !1,\n      autoFocus: g = !1,\n      type: n = \"text\"\n    } = C,\n    f = _objectWithoutProperties(C, _excluded2),\n    [a] = D(e, t => [t.inputElement]),\n    u = me(null),\n    S = ce(u, h, oo(), e.actions.setInputElement),\n    d = ge(a),\n    [k, W] = D(e, t => [t.comboboxState, t.isTyping]),\n    y = ke(),\n    x = O(() => {\n      e.actions.onChange(null), e.state.optionsElement && (e.state.optionsElement.scrollTop = 0), e.actions.goToOption({\n        focus: L.Nothing\n      });\n    }),\n    P = K(() => {\n      var t;\n      return typeof I == \"function\" && o.value !== void 0 ? (t = I(o.value)) != null ? t : \"\" : typeof o.value == \"string\" ? o.value : \"\";\n    }, [o.value, I]);\n  Re((_ref, _ref2) => {\n    let [t, B] = _ref;\n    let [E, s] = _ref2;\n    if (e.state.isTyping) return;\n    let c = u.current;\n    c && ((s === l.Open && B === l.Closed || t !== E) && (c.value = t), requestAnimationFrame(() => {\n      if (e.state.isTyping || !c || (d == null ? void 0 : d.activeElement) !== c) return;\n      let {\n        selectionStart: p,\n        selectionEnd: ne\n      } = c;\n      Math.abs((ne != null ? ne : 0) - (p != null ? p : 0)) === 0 && p === 0 && c.setSelectionRange(c.value.length, c.value.length);\n    }));\n  }, [P, k, d, W]), Re((_ref3, _ref4) => {\n    let [t] = _ref3;\n    let [B] = _ref4;\n    if (t === l.Open && B === l.Closed) {\n      if (e.state.isTyping) return;\n      let E = u.current;\n      if (!E) return;\n      let s = E.value,\n        {\n          selectionStart: c,\n          selectionEnd: p,\n          selectionDirection: ne\n        } = E;\n      E.value = \"\", E.value = s, ne !== null ? E.setSelectionRange(c, p, ne) : E.setSelectionRange(c, p);\n    }\n  }, [k]);\n  let b = me(!1),\n    G = O(() => {\n      b.current = !0;\n    }),\n    _ = O(() => {\n      y.nextFrame(() => {\n        b.current = !1;\n      });\n    }),\n    z = O(t => {\n      switch (e.actions.setIsTyping(!0), t.key) {\n        case V.Enter:\n          if (e.state.comboboxState !== l.Open || b.current) return;\n          if (t.preventDefault(), t.stopPropagation(), e.selectors.activeOptionIndex(e.state) === null) {\n            e.actions.closeCombobox();\n            return;\n          }\n          e.actions.selectActiveOption(), o.mode === N.Single && e.actions.closeCombobox();\n          break;\n        case V.ArrowDown:\n          return t.preventDefault(), t.stopPropagation(), Te(e.state.comboboxState, {\n            [l.Open]: () => e.actions.goToOption({\n              focus: L.Next\n            }),\n            [l.Closed]: () => e.actions.openCombobox()\n          });\n        case V.ArrowUp:\n          return t.preventDefault(), t.stopPropagation(), Te(e.state.comboboxState, {\n            [l.Open]: () => e.actions.goToOption({\n              focus: L.Previous\n            }),\n            [l.Closed]: () => {\n              re(() => e.actions.openCombobox()), o.value || e.actions.goToOption({\n                focus: L.Last\n              });\n            }\n          });\n        case V.Home:\n          if (t.shiftKey) break;\n          return t.preventDefault(), t.stopPropagation(), e.actions.goToOption({\n            focus: L.First\n          });\n        case V.PageUp:\n          return t.preventDefault(), t.stopPropagation(), e.actions.goToOption({\n            focus: L.First\n          });\n        case V.End:\n          if (t.shiftKey) break;\n          return t.preventDefault(), t.stopPropagation(), e.actions.goToOption({\n            focus: L.Last\n          });\n        case V.PageDown:\n          return t.preventDefault(), t.stopPropagation(), e.actions.goToOption({\n            focus: L.Last\n          });\n        case V.Escape:\n          return e.state.comboboxState !== l.Open ? void 0 : (t.preventDefault(), e.state.optionsElement && !o.optionsPropsRef.current.static && t.stopPropagation(), o.mode === N.Single && o.value === null && x(), e.actions.closeCombobox());\n        case V.Tab:\n          if (e.state.comboboxState !== l.Open) return;\n          o.mode === N.Single && e.state.activationTrigger !== oe.Focus && e.actions.selectActiveOption(), e.actions.closeCombobox();\n          break;\n      }\n    }),\n    U = O(t => {\n      T == null || T(t), o.mode === N.Single && t.target.value === \"\" && x(), e.actions.openCombobox();\n    }),\n    w = O(t => {\n      var E, s, c;\n      let B = (E = t.relatedTarget) != null ? E : _e.find(p => p !== t.currentTarget);\n      if (!((s = e.state.optionsElement) != null && s.contains(B)) && !((c = e.state.buttonElement) != null && c.contains(B)) && e.state.comboboxState === l.Open) return t.preventDefault(), o.mode === N.Single && o.value === null && x(), e.actions.closeCombobox();\n    }),\n    J = O(t => {\n      var E, s, c;\n      let B = (E = t.relatedTarget) != null ? E : _e.find(p => p !== t.currentTarget);\n      (s = e.state.buttonElement) != null && s.contains(B) || (c = e.state.optionsElement) != null && c.contains(B) || o.disabled || o.immediate && e.state.comboboxState !== l.Open && y.microTask(() => {\n        re(() => e.actions.openCombobox()), e.actions.setActivationTrigger(oe.Focus);\n      });\n    }),\n    r = Ce(),\n    M = co(),\n    {\n      isFocused: X,\n      focusProps: i\n    } = ve({\n      autoFocus: g\n    }),\n    {\n      isHovered: H,\n      hoverProps: j\n    } = Pe({\n      isDisabled: m\n    }),\n    q = D(e, t => t.optionsElement),\n    Q = K(() => ({\n      open: k === l.Open,\n      disabled: m,\n      invalid: o.invalid,\n      hover: H,\n      focus: X,\n      autofocus: g\n    }), [o, H, X, g, m, o.invalid]),\n    $ = ye({\n      ref: S,\n      id: v,\n      role: \"combobox\",\n      type: n,\n      \"aria-controls\": q == null ? void 0 : q.id,\n      \"aria-expanded\": k === l.Open,\n      \"aria-activedescendant\": D(e, e.selectors.activeDescendantId),\n      \"aria-labelledby\": r,\n      \"aria-describedby\": M,\n      \"aria-autocomplete\": \"list\",\n      defaultValue: (te = (Y = C.defaultValue) != null ? Y : o.defaultValue !== void 0 ? I == null ? void 0 : I(o.defaultValue) : null) != null ? te : o.defaultValue,\n      disabled: m || void 0,\n      autoFocus: g,\n      onCompositionStart: G,\n      onCompositionEnd: _,\n      onKeyDown: z,\n      onChange: U,\n      onFocus: J,\n      onBlur: w\n    }, i, j);\n  return ue()({\n    ourProps: $,\n    theirProps: f,\n    slot: Q,\n    defaultTag: Oo,\n    name: \"Combobox.Input\"\n  });\n}\nlet Ao = \"button\";\nfunction Io(C, h) {\n  let e = pe(\"Combobox.Button\"),\n    o = ae(\"Combobox.Button\"),\n    [A, R] = xe(null),\n    v = ce(h, R, e.actions.setButtonElement),\n    T = le(),\n    {\n      id: I = \"headlessui-combobox-button-\".concat(T),\n      disabled: m = o.disabled || !1,\n      autoFocus: g = !1\n    } = C,\n    n = _objectWithoutProperties(C, _excluded3),\n    [f, a, u] = D(e, r => [r.comboboxState, r.inputElement, r.optionsElement]),\n    S = Ie(a),\n    d = f === l.Open;\n  Ke(d, {\n    trigger: A,\n    action: Z(r => {\n      if (A != null && A.contains(r.target)) return ie.Ignore;\n      if (a != null && a.contains(r.target)) return ie.Ignore;\n      let M = r.target.closest('[role=\"option\"]:not([data-disabled])');\n      return bo.isHTMLElement(M) ? ie.Select(M) : u != null && u.contains(r.target) ? ie.Ignore : ie.Close;\n    }, [A, a, u]),\n    close: e.actions.closeCombobox,\n    select: e.actions.selectActiveOption\n  });\n  let k = O(r => {\n      switch (r.key) {\n        case V.Space:\n        case V.Enter:\n          r.preventDefault(), r.stopPropagation(), e.state.comboboxState === l.Closed && re(() => e.actions.openCombobox()), S();\n          return;\n        case V.ArrowDown:\n          r.preventDefault(), r.stopPropagation(), e.state.comboboxState === l.Closed && (re(() => e.actions.openCombobox()), e.state.dataRef.current.value || e.actions.goToOption({\n            focus: L.First\n          })), S();\n          return;\n        case V.ArrowUp:\n          r.preventDefault(), r.stopPropagation(), e.state.comboboxState === l.Closed && (re(() => e.actions.openCombobox()), e.state.dataRef.current.value || e.actions.goToOption({\n            focus: L.Last\n          })), S();\n          return;\n        case V.Escape:\n          if (e.state.comboboxState !== l.Open) return;\n          r.preventDefault(), e.state.optionsElement && !o.optionsPropsRef.current.static && r.stopPropagation(), re(() => e.actions.closeCombobox()), S();\n          return;\n        default:\n          return;\n      }\n    }),\n    W = O(r => {\n      r.preventDefault(), !uo(r.currentTarget) && (r.button === Se.Left && (e.state.comboboxState === l.Open ? e.actions.closeCombobox() : e.actions.openCombobox()), S());\n    }),\n    y = Ce([I]),\n    {\n      isFocusVisible: x,\n      focusProps: P\n    } = ve({\n      autoFocus: g\n    }),\n    {\n      isHovered: b,\n      hoverProps: G\n    } = Pe({\n      isDisabled: m\n    }),\n    {\n      pressed: _,\n      pressProps: z\n    } = Ve({\n      disabled: m\n    }),\n    U = K(() => ({\n      open: f === l.Open,\n      active: _ || f === l.Open,\n      disabled: m,\n      invalid: o.invalid,\n      value: o.value,\n      hover: b,\n      focus: x\n    }), [o, b, x, _, m, f]),\n    w = ye({\n      ref: v,\n      id: I,\n      type: We(C, A),\n      tabIndex: -1,\n      \"aria-haspopup\": \"listbox\",\n      \"aria-controls\": u == null ? void 0 : u.id,\n      \"aria-expanded\": f === l.Open,\n      \"aria-labelledby\": y,\n      disabled: m || void 0,\n      autoFocus: g,\n      onPointerDown: W,\n      onKeyDown: k\n    }, P, G, z);\n  return ue()({\n    ourProps: w,\n    theirProps: n,\n    slot: U,\n    defaultTag: Ao,\n    name: \"Combobox.Button\"\n  });\n}\nlet Ro = \"div\",\n  Do = Fe.RenderStrategy | Fe.Static;\nfunction _o(C, h) {\n  var E, s, c;\n  let e = le(),\n    {\n      id: o = \"headlessui-combobox-options-\".concat(e),\n      hold: A = !1,\n      anchor: R,\n      portal: v = !1,\n      modal: T = !0,\n      transition: I = !1\n    } = C,\n    m = _objectWithoutProperties(C, _excluded4),\n    g = pe(\"Combobox.Options\"),\n    n = ae(\"Combobox.Options\"),\n    f = to(R);\n  f && (v = !0);\n  let [a, u] = Ze(f),\n    [S, d] = xe(null),\n    k = eo(),\n    W = ce(h, f ? a : null, g.actions.setOptionsElement, d),\n    [y, x, P, b, G] = D(g, p => [p.comboboxState, p.inputElement, p.buttonElement, p.optionsElement, p.activationTrigger]),\n    _ = ge(x || P),\n    z = ge(b),\n    U = io(),\n    [w, J] = je(I, S, U !== null ? (U & fe.Open) === fe.Open : y === l.Open);\n  Ge(w, x, g.actions.closeCombobox);\n  let r = n.__demoMode ? !1 : T && y === l.Open;\n  Xe(r, z);\n  let M = n.__demoMode ? !1 : T && y === l.Open;\n  Ue(M, {\n    allowed: Z(() => [x, P, b], [x, P, b])\n  }), ee(() => {\n    var p;\n    n.optionsPropsRef.current.static = (p = C.static) != null ? p : !1;\n  }, [n.optionsPropsRef, C.static]), ee(() => {\n    n.optionsPropsRef.current.hold = A;\n  }, [n.optionsPropsRef, A]), qe(y === l.Open, {\n    container: b,\n    accept(p) {\n      return p.getAttribute(\"role\") === \"option\" ? NodeFilter.FILTER_REJECT : p.hasAttribute(\"role\") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;\n    },\n    walk(p) {\n      p.setAttribute(\"role\", \"none\");\n    }\n  });\n  let X = Ce([P == null ? void 0 : P.id]),\n    i = K(() => ({\n      open: y === l.Open,\n      option: void 0\n    }), [y]),\n    H = O(() => {\n      g.actions.setActivationTrigger(oe.Pointer);\n    }),\n    j = O(p => {\n      p.preventDefault(), g.actions.setActivationTrigger(oe.Pointer);\n    }),\n    q = ye(f ? k() : {}, _objectSpread({\n      \"aria-labelledby\": X,\n      role: \"listbox\",\n      \"aria-multiselectable\": n.mode === N.Multi ? !0 : void 0,\n      id: o,\n      ref: W,\n      style: _objectSpread(_objectSpread(_objectSpread({}, m.style), u), {}, {\n        \"--input-width\": Ae(x, !0).width,\n        \"--button-width\": Ae(P, !0).width\n      }),\n      onWheel: G === oe.Pointer ? void 0 : H,\n      onMouseDown: j\n    }, Je(J))),\n    Q = w && y === l.Closed,\n    $ = De(Q, (E = n.virtual) == null ? void 0 : E.options),\n    be = De(Q, n.value),\n    Y = O(p => n.compare(be, p)),\n    te = K(() => {\n      if (!n.virtual) return n;\n      if ($ === void 0) throw new Error(\"Missing `options` in virtual mode\");\n      return $ !== n.virtual.options ? _objectSpread(_objectSpread({}, n), {}, {\n        virtual: _objectSpread(_objectSpread({}, n.virtual), {}, {\n          options: $\n        })\n      }) : n;\n    }, [n, $, (s = n.virtual) == null ? void 0 : s.options]);\n  n.virtual && Object.assign(m, {\n    children: F.createElement(de.Provider, {\n      value: te\n    }, F.createElement(vo, {\n      slot: i\n    }, m.children))\n  });\n  let t = ue(),\n    B = K(() => n.mode === N.Multi ? n : _objectSpread(_objectSpread({}, n), {}, {\n      isSelected: Y\n    }), [n, Y]);\n  return F.createElement(xo, {\n    enabled: v ? C.static || w : !1,\n    ownerDocument: _\n  }, F.createElement(de.Provider, {\n    value: B\n  }, t({\n    ourProps: q,\n    theirProps: _objectSpread(_objectSpread({}, m), {}, {\n      children: F.createElement(ro, {\n        freeze: Q\n      }, typeof m.children == \"function\" ? (c = m.children) == null ? void 0 : c.call(m, i) : m.children)\n    }),\n    slot: i,\n    defaultTag: Ro,\n    features: Do,\n    visible: w,\n    name: \"Combobox.Options\"\n  })));\n}\nlet Fo = \"div\";\nfunction So(C, h) {\n  var r, M, X;\n  let e = ae(\"Combobox.Option\"),\n    o = pe(\"Combobox.Option\"),\n    A = le(),\n    {\n      id: R = \"headlessui-combobox-option-\".concat(A),\n      value: v,\n      disabled: T = (X = (M = (r = e.virtual) == null ? void 0 : r.disabled) == null ? void 0 : M.call(r, v)) != null ? X : !1,\n      order: I = null\n    } = C,\n    m = _objectWithoutProperties(C, _excluded5),\n    [g] = D(o, i => [i.inputElement]),\n    n = Ie(g),\n    f = D(o, Z(i => o.selectors.isActive(i, v, R), [v, R])),\n    a = e.isSelected(v),\n    u = me(null),\n    S = He({\n      disabled: T,\n      value: v,\n      domRef: u,\n      order: I\n    }),\n    d = he(Me),\n    k = ce(h, u, d ? d.measureElement : null),\n    W = O(() => {\n      o.actions.setIsTyping(!1), o.actions.onChange(v);\n    });\n  ee(() => o.actions.registerOption(R, S), [S, R]);\n  let y = D(o, Z(i => o.selectors.shouldScrollIntoView(i, v, R), [v, R]));\n  ee(() => {\n    if (y) return po().requestAnimationFrame(() => {\n      var i, H;\n      (H = (i = u.current) == null ? void 0 : i.scrollIntoView) == null || H.call(i, {\n        block: \"nearest\"\n      });\n    });\n  }, [y, u]);\n  let x = O(i => {\n      i.preventDefault(), i.button === Se.Left && (T || (W(), mo() || requestAnimationFrame(() => n()), e.mode === N.Single && o.actions.closeCombobox()));\n    }),\n    P = O(() => {\n      if (T) return o.actions.goToOption({\n        focus: L.Nothing\n      });\n      let i = e.calculateIndex(v);\n      o.actions.goToOption({\n        focus: L.Specific,\n        idx: i\n      });\n    }),\n    b = $e(),\n    G = O(i => b.update(i)),\n    _ = O(i => {\n      if (!b.wasMoved(i) || T || f) return;\n      let H = e.calculateIndex(v);\n      o.actions.goToOption({\n        focus: L.Specific,\n        idx: H\n      }, oe.Pointer);\n    }),\n    z = O(i => {\n      b.wasMoved(i) && (T || f && (e.optionsPropsRef.current.hold || o.actions.goToOption({\n        focus: L.Nothing\n      })));\n    }),\n    U = K(() => ({\n      active: f,\n      focus: f,\n      selected: a,\n      disabled: T\n    }), [f, a, T]),\n    w = {\n      id: R,\n      ref: k,\n      role: \"option\",\n      tabIndex: T === !0 ? void 0 : -1,\n      \"aria-disabled\": T === !0 ? !0 : void 0,\n      \"aria-selected\": a,\n      disabled: void 0,\n      onMouseDown: x,\n      onFocus: P,\n      onPointerEnter: G,\n      onMouseEnter: G,\n      onPointerMove: _,\n      onMouseMove: _,\n      onPointerLeave: z,\n      onMouseLeave: z\n    };\n  return ue()({\n    ourProps: w,\n    theirProps: m,\n    slot: U,\n    defaultTag: Fo,\n    name: \"Combobox.Option\"\n  });\n}\nlet Mo = se(Eo),\n  Lo = se(Io),\n  Vo = se(ho),\n  wo = fo,\n  Bo = se(_o),\n  No = se(So),\n  wt = Object.assign(Mo, {\n    Input: Vo,\n    Button: Lo,\n    Label: wo,\n    Options: Bo,\n    Option: No\n  });\nexport { wt as Combobox, Lo as ComboboxButton, Vo as ComboboxInput, wo as ComboboxLabel, No as ComboboxOption, Bo as ComboboxOptions };", "map": {"version": 3, "names": ["_objectWithoutProperties", "_objectSpread", "_excluded", "_excluded2", "_excluded3", "_excluded4", "_excluded5", "useFocusRing", "ve", "useHover", "Pe", "useVirtualizer", "Le", "F", "Fragment", "Ee", "createContext", "Oe", "useCallback", "Z", "useContext", "he", "useMemo", "K", "useRef", "me", "useState", "xe", "flushSync", "re", "useActivePress", "Ve", "useByComparator", "we", "useControllable", "Be", "useDefaultValue", "Ne", "useDisposables", "ke", "useElementSize", "Ae", "useEvent", "O", "useId", "le", "useInertOthers", "Ue", "useIsoMorphicEffect", "ee", "useLatestValue", "He", "useOnDisappear", "Ge", "useOutsideClick", "ze", "useOwnerDocument", "ge", "Action", "ie", "useQuickRelease", "<PERSON>", "useRefocusableInput", "Ie", "useResolveButtonType", "We", "useScrollLock", "Xe", "useSyncRefs", "ce", "useTrackedPointer", "$e", "transitionDataAttributes", "Je", "useTransition", "je", "useTreeWalker", "qe", "useWatch", "Re", "useDisabled", "Qe", "FloatingProvider", "Ye", "useFloatingPanel", "Ze", "useFloatingPanelProps", "eo", "useFloatingReference", "oo", "useResolvedAnchor", "to", "<PERSON><PERSON><PERSON>s", "no", "Frozen", "ro", "useFrozenData", "De", "useProvidedId", "ao", "OpenClosedProvider", "lo", "State", "fe", "useOpenClosed", "io", "stackMachines", "so", "useSlice", "D", "history", "_e", "isDisabledReactIssue7711", "uo", "Focus", "L", "disposables", "po", "bo", "match", "Te", "isMobile", "mo", "RenderFeatures", "Fe", "forwardRefWithAs", "se", "mergeProps", "ye", "useRender", "ue", "useDescribedBy", "co", "Keys", "V", "Label", "fo", "useLabelledBy", "Ce", "useLabels", "To", "MouseB<PERSON>on", "Se", "Portal", "xo", "ActionTypes", "go", "ActivationTrigger", "oe", "ComboboxState", "l", "ValueMode", "N", "ComboboxContext", "yo", "useComboboxMachine", "Co", "useComboboxMachineContext", "pe", "de", "displayName", "ae", "C", "h", "e", "Error", "concat", "captureStackTrace", "Me", "vo", "options", "o", "virtual", "A", "a", "optionsElement", "R", "v", "u", "window", "getComputedStyle", "parseFloat", "paddingBlockStart", "paddingTop", "paddingBlockEnd", "paddingBottom", "T", "enabled", "length", "scrollPaddingStart", "scrollPaddingEnd", "count", "estimateSize", "getScrollElement", "state", "overscan", "I", "m", "g", "getVirtualItems", "n", "activationTrigger", "Pointer", "f", "selectors", "activeOptionIndex", "createElement", "Provider", "value", "style", "position", "width", "height", "getTotalSize", "ref", "scrollToIndex", "map", "key", "cloneElement", "children", "call", "slot", "option", "index", "top", "left", "transform", "start", "overflowAnchor", "Po", "Eo", "defaultValue", "onChange", "form", "name", "by", "invalid", "disabled", "onClose", "__demoMode", "multiple", "immediate", "S", "d", "nullable", "k", "W", "y", "x", "P", "b", "id", "G", "static", "hold", "_", "z", "s", "indexOf", "findIndex", "c", "dataRef", "current", "U", "r", "mode", "Multi", "some", "Single", "w", "J", "optionsPropsRef", "isSelected", "calculateIndex", "compare", "send", "type", "UpdateVirtualConfiguration", "M", "X", "i", "H", "comboboxState", "buttonElement", "inputElement", "j", "get", "q", "isTop", "actions", "closeCombobox", "Q", "$", "activeOption", "be", "open", "Open", "activeIndex", "Y", "te", "t", "B", "E", "props", "htmlFor", "Closed", "data", "onReset", "ourProps", "theirProps", "defaultTag", "Oo", "ho", "displayValue", "autoFocus", "setInputElement", "isTyping", "scrollTop", "goToOption", "focus", "Nothing", "_ref", "_ref2", "requestAnimationFrame", "activeElement", "selectionStart", "p", "selectionEnd", "ne", "Math", "abs", "setSelectionRange", "_ref3", "_ref4", "selectionDirection", "next<PERSON><PERSON><PERSON>", "setIsTyping", "Enter", "preventDefault", "stopPropagation", "selectActiveOption", "ArrowDown", "Next", "openCombobox", "ArrowUp", "Previous", "Last", "Home", "shift<PERSON>ey", "First", "PageUp", "End", "PageDown", "Escape", "Tab", "target", "relatedTarget", "find", "currentTarget", "contains", "microTask", "setActivationTrigger", "isFocused", "focusProps", "isHovered", "hoverProps", "isDisabled", "hover", "autofocus", "role", "activeDescendantId", "onCompositionStart", "onCompositionEnd", "onKeyDown", "onFocus", "onBlur", "Ao", "Io", "setButtonElement", "trigger", "action", "Ignore", "closest", "isHTMLElement", "Select", "Close", "close", "select", "Space", "button", "Left", "isFocusVisible", "pressed", "pressProps", "active", "tabIndex", "onPointerDown", "Ro", "Do", "RenderStrategy", "Static", "_o", "anchor", "portal", "modal", "transition", "setOptionsElement", "allowed", "container", "accept", "getAttribute", "Node<PERSON><PERSON><PERSON>", "FILTER_REJECT", "hasAttribute", "FILTER_SKIP", "FILTER_ACCEPT", "walk", "setAttribute", "onWheel", "onMouseDown", "Object", "assign", "ownerDocument", "freeze", "features", "visible", "Fo", "So", "order", "isActive", "domRef", "measureElement", "registerOption", "shouldScrollIntoView", "scrollIntoView", "block", "Specific", "idx", "update", "wasMoved", "selected", "onPointerEnter", "onMouseEnter", "onPointerMove", "onMouseMove", "onPointerLeave", "onMouseLeave", "Mo", "Lo", "Vo", "wo", "<PERSON>", "No", "wt", "Input", "<PERSON><PERSON>", "Options", "Option", "Combobox", "ComboboxButton", "ComboboxInput", "ComboboxLabel", "ComboboxOption", "ComboboxOptions"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/combobox/combobox.js"], "sourcesContent": ["\"use client\";import{useFocusRing as ve}from\"@react-aria/focus\";import{useHover as Pe}from\"@react-aria/interactions\";import{useVirtualizer as Le}from\"@tanstack/react-virtual\";import F,{Fragment as Ee,create<PERSON>ontex<PERSON> as <PERSON><PERSON>,use<PERSON><PERSON><PERSON> as <PERSON>,use<PERSON>ontex<PERSON> as he,use<PERSON>em<PERSON> as K,useRef as me,useState as xe}from\"react\";import{flushSync as re}from\"react-dom\";import{useActivePress as Ve}from'../../hooks/use-active-press.js';import{useByComparator as we}from'../../hooks/use-by-comparator.js';import{useControllable as Be}from'../../hooks/use-controllable.js';import{useDefaultValue as Ne}from'../../hooks/use-default-value.js';import{useDisposables as ke}from'../../hooks/use-disposables.js';import{useElementSize as Ae}from'../../hooks/use-element-size.js';import{useEvent as O}from'../../hooks/use-event.js';import{useId as le}from'../../hooks/use-id.js';import{useInertOthers as Ue}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ee}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as He}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ze}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ge}from'../../hooks/use-owner.js';import{Action as ie,useQuickRelease as Ke}from'../../hooks/use-quick-release.js';import{useRefocusableInput as Ie}from'../../hooks/use-refocusable-input.js';import{useResolveButtonType as We}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Xe}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as ce}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as $e}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Je,useTransition as je}from'../../hooks/use-transition.js';import{useTreeWalker as qe}from'../../hooks/use-tree-walker.js';import{useWatch as Re}from'../../hooks/use-watch.js';import{useDisabled as Qe}from'../../internal/disabled.js';import{FloatingProvider as Ye,useFloatingPanel as Ze,useFloatingPanelProps as eo,useFloatingReference as oo,useResolvedAnchor as to}from'../../internal/floating.js';import{FormFields as no}from'../../internal/form-fields.js';import{Frozen as ro,useFrozenData as De}from'../../internal/frozen.js';import{useProvidedId as ao}from'../../internal/id.js';import{OpenClosedProvider as lo,State as fe,useOpenClosed as io}from'../../internal/open-closed.js';import{stackMachines as so}from'../../machines/stack-machine.js';import{useSlice as D}from'../../react-glue.js';import{history as _e}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as uo}from'../../utils/bugs.js';import{Focus as L}from'../../utils/calculate-active-index.js';import{disposables as po}from'../../utils/disposables.js';import*as bo from'../../utils/dom.js';import{match as Te}from'../../utils/match.js';import{isMobile as mo}from'../../utils/platform.js';import{RenderFeatures as Fe,forwardRefWithAs as se,mergeProps as ye,useRender as ue}from'../../utils/render.js';import{useDescribedBy as co}from'../description/description.js';import{Keys as V}from'../keyboard.js';import{Label as fo,useLabelledBy as Ce,useLabels as To}from'../label/label.js';import{MouseButton as Se}from'../mouse.js';import{Portal as xo}from'../portal/portal.js';import{ActionTypes as go,ActivationTrigger as oe,ComboboxState as l,ValueMode as N}from'./combobox-machine.js';import{ComboboxContext as yo,useComboboxMachine as Co,useComboboxMachineContext as pe}from'./combobox-machine-glue.js';let de=Oe(null);de.displayName=\"ComboboxDataContext\";function ae(C){let h=he(de);if(h===null){let e=new Error(`<${C} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,ae),e}return h}let Me=Oe(null);function vo(C){let h=pe(\"VirtualProvider\"),e=ae(\"VirtualProvider\"),{options:o}=e.virtual,A=D(h,a=>a.optionsElement),[R,v]=K(()=>{let a=A;if(!a)return[0,0];let u=window.getComputedStyle(a);return[parseFloat(u.paddingBlockStart||u.paddingTop),parseFloat(u.paddingBlockEnd||u.paddingBottom)]},[A]),T=Le({enabled:o.length!==0,scrollPaddingStart:R,scrollPaddingEnd:v,count:o.length,estimateSize(){return 40},getScrollElement(){return h.state.optionsElement},overscan:12}),[I,m]=xe(0);ee(()=>{m(a=>a+1)},[o]);let g=T.getVirtualItems(),n=D(h,a=>a.activationTrigger===oe.Pointer),f=D(h,h.selectors.activeOptionIndex);return g.length===0?null:F.createElement(Me.Provider,{value:T},F.createElement(\"div\",{style:{position:\"relative\",width:\"100%\",height:`${T.getTotalSize()}px`},ref:a=>{a&&(n||f!==null&&o.length>f&&T.scrollToIndex(f))}},g.map(a=>{var u;return F.createElement(Ee,{key:a.key},F.cloneElement((u=C.children)==null?void 0:u.call(C,{...C.slot,option:o[a.index]}),{key:`${I}-${a.key}`,\"data-index\":a.index,\"aria-setsize\":o.length,\"aria-posinset\":a.index+1,style:{position:\"absolute\",top:0,left:0,transform:`translateY(${a.start}px)`,overflowAnchor:\"none\"}}))})))}let Po=Ee;function Eo(C,h){let e=le(),o=Qe(),{value:A,defaultValue:R,onChange:v,form:T,name:I,by:m,invalid:g=!1,disabled:n=o||!1,onClose:f,__demoMode:a=!1,multiple:u=!1,immediate:S=!1,virtual:d=null,nullable:k,...W}=C,y=Ne(R),[x=u?[]:void 0,P]=Be(A,v,y),b=Co({id:e,virtual:d,__demoMode:a}),G=me({static:!1,hold:!1}),_=we(m),z=O(s=>d?m===null?d.options.indexOf(s):d.options.findIndex(c=>_(c,s)):b.state.options.findIndex(c=>_(c.dataRef.current.value,s))),U=Z(s=>Te(r.mode,{[N.Multi]:()=>x.some(c=>_(c,s)),[N.Single]:()=>_(x,s)}),[x]),w=D(b,s=>s.virtual),J=O(()=>f==null?void 0:f()),r=K(()=>({__demoMode:a,immediate:S,optionsPropsRef:G,value:x,defaultValue:y,disabled:n,invalid:g,mode:u?N.Multi:N.Single,virtual:d?w:null,onChange:P,isSelected:U,calculateIndex:z,compare:_,onClose:J}),[x,y,n,g,u,P,U,a,b,d,w,J]);ee(()=>{var s;d&&b.send({type:go.UpdateVirtualConfiguration,options:d.options,disabled:(s=d.disabled)!=null?s:null})},[d,d==null?void 0:d.options,d==null?void 0:d.disabled]),ee(()=>{b.state.dataRef.current=r},[r]);let[M,X,i,H]=D(b,s=>[s.comboboxState,s.buttonElement,s.inputElement,s.optionsElement]),j=so.get(null),q=D(j,Z(s=>j.selectors.isTop(s,e),[j,e]));ze(q,[X,i,H],()=>b.actions.closeCombobox());let Q=D(b,b.selectors.activeOptionIndex),$=D(b,b.selectors.activeOption),be=K(()=>({open:M===l.Open,disabled:n,invalid:g,activeIndex:Q,activeOption:$,value:x}),[r,n,x,g,$,M]),[Y,te]=To(),t=h===null?{}:{ref:h},B=Z(()=>{if(y!==void 0)return P==null?void 0:P(y)},[P,y]),E=ue();return F.createElement(te,{value:Y,props:{htmlFor:i==null?void 0:i.id},slot:{open:M===l.Open,disabled:n}},F.createElement(Ye,null,F.createElement(de.Provider,{value:r},F.createElement(yo.Provider,{value:b},F.createElement(lo,{value:Te(M,{[l.Open]:fe.Open,[l.Closed]:fe.Closed})},I!=null&&F.createElement(no,{disabled:n,data:x!=null?{[I]:x}:{},form:T,onReset:B}),E({ourProps:t,theirProps:W,slot:be,defaultTag:Po,name:\"Combobox\"}))))))}let Oo=\"input\";function ho(C,h){var Y,te;let e=pe(\"Combobox.Input\"),o=ae(\"Combobox.Input\"),A=le(),R=ao(),{id:v=R||`headlessui-combobox-input-${A}`,onChange:T,displayValue:I,disabled:m=o.disabled||!1,autoFocus:g=!1,type:n=\"text\",...f}=C,[a]=D(e,t=>[t.inputElement]),u=me(null),S=ce(u,h,oo(),e.actions.setInputElement),d=ge(a),[k,W]=D(e,t=>[t.comboboxState,t.isTyping]),y=ke(),x=O(()=>{e.actions.onChange(null),e.state.optionsElement&&(e.state.optionsElement.scrollTop=0),e.actions.goToOption({focus:L.Nothing})}),P=K(()=>{var t;return typeof I==\"function\"&&o.value!==void 0?(t=I(o.value))!=null?t:\"\":typeof o.value==\"string\"?o.value:\"\"},[o.value,I]);Re(([t,B],[E,s])=>{if(e.state.isTyping)return;let c=u.current;c&&((s===l.Open&&B===l.Closed||t!==E)&&(c.value=t),requestAnimationFrame(()=>{if(e.state.isTyping||!c||(d==null?void 0:d.activeElement)!==c)return;let{selectionStart:p,selectionEnd:ne}=c;Math.abs((ne!=null?ne:0)-(p!=null?p:0))===0&&p===0&&c.setSelectionRange(c.value.length,c.value.length)}))},[P,k,d,W]),Re(([t],[B])=>{if(t===l.Open&&B===l.Closed){if(e.state.isTyping)return;let E=u.current;if(!E)return;let s=E.value,{selectionStart:c,selectionEnd:p,selectionDirection:ne}=E;E.value=\"\",E.value=s,ne!==null?E.setSelectionRange(c,p,ne):E.setSelectionRange(c,p)}},[k]);let b=me(!1),G=O(()=>{b.current=!0}),_=O(()=>{y.nextFrame(()=>{b.current=!1})}),z=O(t=>{switch(e.actions.setIsTyping(!0),t.key){case V.Enter:if(e.state.comboboxState!==l.Open||b.current)return;if(t.preventDefault(),t.stopPropagation(),e.selectors.activeOptionIndex(e.state)===null){e.actions.closeCombobox();return}e.actions.selectActiveOption(),o.mode===N.Single&&e.actions.closeCombobox();break;case V.ArrowDown:return t.preventDefault(),t.stopPropagation(),Te(e.state.comboboxState,{[l.Open]:()=>e.actions.goToOption({focus:L.Next}),[l.Closed]:()=>e.actions.openCombobox()});case V.ArrowUp:return t.preventDefault(),t.stopPropagation(),Te(e.state.comboboxState,{[l.Open]:()=>e.actions.goToOption({focus:L.Previous}),[l.Closed]:()=>{re(()=>e.actions.openCombobox()),o.value||e.actions.goToOption({focus:L.Last})}});case V.Home:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.First});case V.PageUp:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.First});case V.End:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.Last});case V.PageDown:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.Last});case V.Escape:return e.state.comboboxState!==l.Open?void 0:(t.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&t.stopPropagation(),o.mode===N.Single&&o.value===null&&x(),e.actions.closeCombobox());case V.Tab:if(e.state.comboboxState!==l.Open)return;o.mode===N.Single&&e.state.activationTrigger!==oe.Focus&&e.actions.selectActiveOption(),e.actions.closeCombobox();break}}),U=O(t=>{T==null||T(t),o.mode===N.Single&&t.target.value===\"\"&&x(),e.actions.openCombobox()}),w=O(t=>{var E,s,c;let B=(E=t.relatedTarget)!=null?E:_e.find(p=>p!==t.currentTarget);if(!((s=e.state.optionsElement)!=null&&s.contains(B))&&!((c=e.state.buttonElement)!=null&&c.contains(B))&&e.state.comboboxState===l.Open)return t.preventDefault(),o.mode===N.Single&&o.value===null&&x(),e.actions.closeCombobox()}),J=O(t=>{var E,s,c;let B=(E=t.relatedTarget)!=null?E:_e.find(p=>p!==t.currentTarget);(s=e.state.buttonElement)!=null&&s.contains(B)||(c=e.state.optionsElement)!=null&&c.contains(B)||o.disabled||o.immediate&&e.state.comboboxState!==l.Open&&y.microTask(()=>{re(()=>e.actions.openCombobox()),e.actions.setActivationTrigger(oe.Focus)})}),r=Ce(),M=co(),{isFocused:X,focusProps:i}=ve({autoFocus:g}),{isHovered:H,hoverProps:j}=Pe({isDisabled:m}),q=D(e,t=>t.optionsElement),Q=K(()=>({open:k===l.Open,disabled:m,invalid:o.invalid,hover:H,focus:X,autofocus:g}),[o,H,X,g,m,o.invalid]),$=ye({ref:S,id:v,role:\"combobox\",type:n,\"aria-controls\":q==null?void 0:q.id,\"aria-expanded\":k===l.Open,\"aria-activedescendant\":D(e,e.selectors.activeDescendantId),\"aria-labelledby\":r,\"aria-describedby\":M,\"aria-autocomplete\":\"list\",defaultValue:(te=(Y=C.defaultValue)!=null?Y:o.defaultValue!==void 0?I==null?void 0:I(o.defaultValue):null)!=null?te:o.defaultValue,disabled:m||void 0,autoFocus:g,onCompositionStart:G,onCompositionEnd:_,onKeyDown:z,onChange:U,onFocus:J,onBlur:w},i,j);return ue()({ourProps:$,theirProps:f,slot:Q,defaultTag:Oo,name:\"Combobox.Input\"})}let Ao=\"button\";function Io(C,h){let e=pe(\"Combobox.Button\"),o=ae(\"Combobox.Button\"),[A,R]=xe(null),v=ce(h,R,e.actions.setButtonElement),T=le(),{id:I=`headlessui-combobox-button-${T}`,disabled:m=o.disabled||!1,autoFocus:g=!1,...n}=C,[f,a,u]=D(e,r=>[r.comboboxState,r.inputElement,r.optionsElement]),S=Ie(a),d=f===l.Open;Ke(d,{trigger:A,action:Z(r=>{if(A!=null&&A.contains(r.target))return ie.Ignore;if(a!=null&&a.contains(r.target))return ie.Ignore;let M=r.target.closest('[role=\"option\"]:not([data-disabled])');return bo.isHTMLElement(M)?ie.Select(M):u!=null&&u.contains(r.target)?ie.Ignore:ie.Close},[A,a,u]),close:e.actions.closeCombobox,select:e.actions.selectActiveOption});let k=O(r=>{switch(r.key){case V.Space:case V.Enter:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&re(()=>e.actions.openCombobox()),S();return;case V.ArrowDown:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&(re(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:L.First})),S();return;case V.ArrowUp:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&(re(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:L.Last})),S();return;case V.Escape:if(e.state.comboboxState!==l.Open)return;r.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&r.stopPropagation(),re(()=>e.actions.closeCombobox()),S();return;default:return}}),W=O(r=>{r.preventDefault(),!uo(r.currentTarget)&&(r.button===Se.Left&&(e.state.comboboxState===l.Open?e.actions.closeCombobox():e.actions.openCombobox()),S())}),y=Ce([I]),{isFocusVisible:x,focusProps:P}=ve({autoFocus:g}),{isHovered:b,hoverProps:G}=Pe({isDisabled:m}),{pressed:_,pressProps:z}=Ve({disabled:m}),U=K(()=>({open:f===l.Open,active:_||f===l.Open,disabled:m,invalid:o.invalid,value:o.value,hover:b,focus:x}),[o,b,x,_,m,f]),w=ye({ref:v,id:I,type:We(C,A),tabIndex:-1,\"aria-haspopup\":\"listbox\",\"aria-controls\":u==null?void 0:u.id,\"aria-expanded\":f===l.Open,\"aria-labelledby\":y,disabled:m||void 0,autoFocus:g,onPointerDown:W,onKeyDown:k},P,G,z);return ue()({ourProps:w,theirProps:n,slot:U,defaultTag:Ao,name:\"Combobox.Button\"})}let Ro=\"div\",Do=Fe.RenderStrategy|Fe.Static;function _o(C,h){var E,s,c;let e=le(),{id:o=`headlessui-combobox-options-${e}`,hold:A=!1,anchor:R,portal:v=!1,modal:T=!0,transition:I=!1,...m}=C,g=pe(\"Combobox.Options\"),n=ae(\"Combobox.Options\"),f=to(R);f&&(v=!0);let[a,u]=Ze(f),[S,d]=xe(null),k=eo(),W=ce(h,f?a:null,g.actions.setOptionsElement,d),[y,x,P,b,G]=D(g,p=>[p.comboboxState,p.inputElement,p.buttonElement,p.optionsElement,p.activationTrigger]),_=ge(x||P),z=ge(b),U=io(),[w,J]=je(I,S,U!==null?(U&fe.Open)===fe.Open:y===l.Open);Ge(w,x,g.actions.closeCombobox);let r=n.__demoMode?!1:T&&y===l.Open;Xe(r,z);let M=n.__demoMode?!1:T&&y===l.Open;Ue(M,{allowed:Z(()=>[x,P,b],[x,P,b])}),ee(()=>{var p;n.optionsPropsRef.current.static=(p=C.static)!=null?p:!1},[n.optionsPropsRef,C.static]),ee(()=>{n.optionsPropsRef.current.hold=A},[n.optionsPropsRef,A]),qe(y===l.Open,{container:b,accept(p){return p.getAttribute(\"role\")===\"option\"?NodeFilter.FILTER_REJECT:p.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(p){p.setAttribute(\"role\",\"none\")}});let X=Ce([P==null?void 0:P.id]),i=K(()=>({open:y===l.Open,option:void 0}),[y]),H=O(()=>{g.actions.setActivationTrigger(oe.Pointer)}),j=O(p=>{p.preventDefault(),g.actions.setActivationTrigger(oe.Pointer)}),q=ye(f?k():{},{\"aria-labelledby\":X,role:\"listbox\",\"aria-multiselectable\":n.mode===N.Multi?!0:void 0,id:o,ref:W,style:{...m.style,...u,\"--input-width\":Ae(x,!0).width,\"--button-width\":Ae(P,!0).width},onWheel:G===oe.Pointer?void 0:H,onMouseDown:j,...Je(J)}),Q=w&&y===l.Closed,$=De(Q,(E=n.virtual)==null?void 0:E.options),be=De(Q,n.value),Y=O(p=>n.compare(be,p)),te=K(()=>{if(!n.virtual)return n;if($===void 0)throw new Error(\"Missing `options` in virtual mode\");return $!==n.virtual.options?{...n,virtual:{...n.virtual,options:$}}:n},[n,$,(s=n.virtual)==null?void 0:s.options]);n.virtual&&Object.assign(m,{children:F.createElement(de.Provider,{value:te},F.createElement(vo,{slot:i},m.children))});let t=ue(),B=K(()=>n.mode===N.Multi?n:{...n,isSelected:Y},[n,Y]);return F.createElement(xo,{enabled:v?C.static||w:!1,ownerDocument:_},F.createElement(de.Provider,{value:B},t({ourProps:q,theirProps:{...m,children:F.createElement(ro,{freeze:Q},typeof m.children==\"function\"?(c=m.children)==null?void 0:c.call(m,i):m.children)},slot:i,defaultTag:Ro,features:Do,visible:w,name:\"Combobox.Options\"})))}let Fo=\"div\";function So(C,h){var r,M,X;let e=ae(\"Combobox.Option\"),o=pe(\"Combobox.Option\"),A=le(),{id:R=`headlessui-combobox-option-${A}`,value:v,disabled:T=(X=(M=(r=e.virtual)==null?void 0:r.disabled)==null?void 0:M.call(r,v))!=null?X:!1,order:I=null,...m}=C,[g]=D(o,i=>[i.inputElement]),n=Ie(g),f=D(o,Z(i=>o.selectors.isActive(i,v,R),[v,R])),a=e.isSelected(v),u=me(null),S=He({disabled:T,value:v,domRef:u,order:I}),d=he(Me),k=ce(h,u,d?d.measureElement:null),W=O(()=>{o.actions.setIsTyping(!1),o.actions.onChange(v)});ee(()=>o.actions.registerOption(R,S),[S,R]);let y=D(o,Z(i=>o.selectors.shouldScrollIntoView(i,v,R),[v,R]));ee(()=>{if(y)return po().requestAnimationFrame(()=>{var i,H;(H=(i=u.current)==null?void 0:i.scrollIntoView)==null||H.call(i,{block:\"nearest\"})})},[y,u]);let x=O(i=>{i.preventDefault(),i.button===Se.Left&&(T||(W(),mo()||requestAnimationFrame(()=>n()),e.mode===N.Single&&o.actions.closeCombobox()))}),P=O(()=>{if(T)return o.actions.goToOption({focus:L.Nothing});let i=e.calculateIndex(v);o.actions.goToOption({focus:L.Specific,idx:i})}),b=$e(),G=O(i=>b.update(i)),_=O(i=>{if(!b.wasMoved(i)||T||f)return;let H=e.calculateIndex(v);o.actions.goToOption({focus:L.Specific,idx:H},oe.Pointer)}),z=O(i=>{b.wasMoved(i)&&(T||f&&(e.optionsPropsRef.current.hold||o.actions.goToOption({focus:L.Nothing})))}),U=K(()=>({active:f,focus:f,selected:a,disabled:T}),[f,a,T]),w={id:R,ref:k,role:\"option\",tabIndex:T===!0?void 0:-1,\"aria-disabled\":T===!0?!0:void 0,\"aria-selected\":a,disabled:void 0,onMouseDown:x,onFocus:P,onPointerEnter:G,onMouseEnter:G,onPointerMove:_,onMouseMove:_,onPointerLeave:z,onMouseLeave:z};return ue()({ourProps:w,theirProps:m,slot:U,defaultTag:Fo,name:\"Combobox.Option\"})}let Mo=se(Eo),Lo=se(Io),Vo=se(ho),wo=fo,Bo=se(_o),No=se(So),wt=Object.assign(Mo,{Input:Vo,Button:Lo,Label:wo,Options:Bo,Option:No});export{wt as Combobox,Lo as ComboboxButton,Vo as ComboboxInput,wo as ComboboxLabel,No as ComboboxOption,Bo as ComboboxOptions};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,wBAAA;AAAA,OAAAC,aAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;EAAAC,UAAA;EAAAC,UAAA;EAAAC,UAAA;AAAA,SAAOC,YAAY,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,yBAAyB;AAAC,OAAOC,CAAC,IAAEC,QAAQ,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,EAACC,WAAW,IAAIC,CAAC,EAACC,UAAU,IAAIC,EAAE,EAACC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,EAAE,EAACC,QAAQ,IAAIC,EAAE,QAAK,OAAO;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,WAAW;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,mBAAmB,IAAIC,EAAE,QAAK,uCAAuC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,MAAM,IAAIC,EAAE,EAACC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,mBAAmB,IAAIC,EAAE,QAAK,sCAAsC;AAAC,SAAOC,oBAAoB,IAAIC,EAAE,QAAK,wCAAwC;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,8BAA8B;AAAC,SAAOC,iBAAiB,IAAIC,EAAE,QAAK,oCAAoC;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,4BAA4B;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,EAACC,gBAAgB,IAAIC,EAAE,EAACC,qBAAqB,IAAIC,EAAE,EAACC,oBAAoB,IAAIC,EAAE,EAACC,iBAAiB,IAAIC,EAAE,QAAK,4BAA4B;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,MAAM,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,kBAAkB,IAAIC,EAAE,EAACC,KAAK,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,uCAAuC;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,4BAA4B;AAAC,OAAM,KAAIC,EAAE,MAAK,oBAAoB;AAAC,SAAOC,KAAK,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,EAAE,EAACC,gBAAgB,IAAIC,EAAE,EAACC,UAAU,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,KAAK,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,aAAa;AAAC,SAAOC,MAAM,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,WAAW,IAAIC,EAAE,EAACC,iBAAiB,IAAIC,EAAE,EAACC,aAAa,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,eAAe,IAAIC,EAAE,EAACC,kBAAkB,IAAIC,EAAE,EAACC,yBAAyB,IAAIC,EAAE,QAAK,4BAA4B;AAAC,IAAIC,EAAE,GAAC9I,EAAE,CAAC,IAAI,CAAC;AAAC8I,EAAE,CAACC,WAAW,GAAC,qBAAqB;AAAC,SAASC,EAAEA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC9I,EAAE,CAAC0I,EAAE,CAAC;EAAC,IAAGI,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,KAAAC,MAAA,CAAKJ,CAAC,oDAAiD,CAAC;IAAC,MAAMG,KAAK,CAACE,iBAAiB,IAAEF,KAAK,CAACE,iBAAiB,CAACH,CAAC,EAACH,EAAE,CAAC,EAACG,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,IAAIK,EAAE,GAACvJ,EAAE,CAAC,IAAI,CAAC;AAAC,SAASwJ,EAAEA,CAACP,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,EAAE,CAAC,iBAAiB,CAAC;IAACM,CAAC,GAACH,EAAE,CAAC,iBAAiB,CAAC;IAAC;MAACS,OAAO,EAACC;IAAC,CAAC,GAACP,CAAC,CAACQ,OAAO;IAACC,CAAC,GAAChE,CAAC,CAACsD,CAAC,EAACW,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAC;IAAC,CAACC,CAAC,EAACC,CAAC,CAAC,GAAC1J,CAAC,CAAC,MAAI;MAAC,IAAIuJ,CAAC,GAACD,CAAC;MAAC,IAAG,CAACC,CAAC,EAAC,OAAM,CAAC,CAAC,EAAC,CAAC,CAAC;MAAC,IAAII,CAAC,GAACC,MAAM,CAACC,gBAAgB,CAACN,CAAC,CAAC;MAAC,OAAM,CAACO,UAAU,CAACH,CAAC,CAACI,iBAAiB,IAAEJ,CAAC,CAACK,UAAU,CAAC,EAACF,UAAU,CAACH,CAAC,CAACM,eAAe,IAAEN,CAAC,CAACO,aAAa,CAAC,CAAC;IAAA,CAAC,EAAC,CAACZ,CAAC,CAAC,CAAC;IAACa,CAAC,GAAC9K,EAAE,CAAC;MAAC+K,OAAO,EAAChB,CAAC,CAACiB,MAAM,KAAG,CAAC;MAACC,kBAAkB,EAACb,CAAC;MAACc,gBAAgB,EAACb,CAAC;MAACc,KAAK,EAACpB,CAAC,CAACiB,MAAM;MAACI,YAAYA,CAAA,EAAE;QAAC,OAAO,EAAE;MAAA,CAAC;MAACC,gBAAgBA,CAAA,EAAE;QAAC,OAAO9B,CAAC,CAAC+B,KAAK,CAACnB,cAAc;MAAA,CAAC;MAACoB,QAAQ,EAAC;IAAE,CAAC,CAAC;IAAC,CAACC,CAAC,EAACC,CAAC,CAAC,GAAC1K,EAAE,CAAC,CAAC,CAAC;EAACsB,EAAE,CAAC,MAAI;IAACoJ,CAAC,CAACvB,CAAC,IAAEA,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;EAAC,IAAI2B,CAAC,GAACZ,CAAC,CAACa,eAAe,CAAC,CAAC;IAACC,CAAC,GAAC3F,CAAC,CAACsD,CAAC,EAACW,CAAC,IAAEA,CAAC,CAAC2B,iBAAiB,KAAGrD,EAAE,CAACsD,OAAO,CAAC;IAACC,CAAC,GAAC9F,CAAC,CAACsD,CAAC,EAACA,CAAC,CAACyC,SAAS,CAACC,iBAAiB,CAAC;EAAC,OAAOP,CAAC,CAACV,MAAM,KAAG,CAAC,GAAC,IAAI,GAAC/K,CAAC,CAACiM,aAAa,CAACtC,EAAE,CAACuC,QAAQ,EAAC;IAACC,KAAK,EAACtB;EAAC,CAAC,EAAC7K,CAAC,CAACiM,aAAa,CAAC,KAAK,EAAC;IAACG,KAAK,EAAC;MAACC,QAAQ,EAAC,UAAU;MAACC,KAAK,EAAC,MAAM;MAACC,MAAM,KAAA9C,MAAA,CAAIoB,CAAC,CAAC2B,YAAY,CAAC,CAAC;IAAI,CAAC;IAACC,GAAG,EAACxC,CAAC,IAAE;MAACA,CAAC,KAAG0B,CAAC,IAAEG,CAAC,KAAG,IAAI,IAAEhC,CAAC,CAACiB,MAAM,GAACe,CAAC,IAAEjB,CAAC,CAAC6B,aAAa,CAACZ,CAAC,CAAC,CAAC;IAAA;EAAC,CAAC,EAACL,CAAC,CAACkB,GAAG,CAAC1C,CAAC,IAAE;IAAC,IAAII,CAAC;IAAC,OAAOrK,CAAC,CAACiM,aAAa,CAAC/L,EAAE,EAAC;MAAC0M,GAAG,EAAC3C,CAAC,CAAC2C;IAAG,CAAC,EAAC5M,CAAC,CAAC6M,YAAY,CAAC,CAACxC,CAAC,GAAChB,CAAC,CAACyD,QAAQ,KAAG,IAAI,GAAC,KAAK,CAAC,GAACzC,CAAC,CAAC0C,IAAI,CAAC1D,CAAC,EAAAjK,aAAA,CAAAA,aAAA,KAAKiK,CAAC,CAAC2D,IAAI;MAACC,MAAM,EAACnD,CAAC,CAACG,CAAC,CAACiD,KAAK;IAAC,EAAC,CAAC,EAAC;MAACN,GAAG,KAAAnD,MAAA,CAAI8B,CAAC,OAAA9B,MAAA,CAAIQ,CAAC,CAAC2C,GAAG,CAAE;MAAC,YAAY,EAAC3C,CAAC,CAACiD,KAAK;MAAC,cAAc,EAACpD,CAAC,CAACiB,MAAM;MAAC,eAAe,EAACd,CAAC,CAACiD,KAAK,GAAC,CAAC;MAACd,KAAK,EAAC;QAACC,QAAQ,EAAC,UAAU;QAACc,GAAG,EAAC,CAAC;QAACC,IAAI,EAAC,CAAC;QAACC,SAAS,gBAAA5D,MAAA,CAAeQ,CAAC,CAACqD,KAAK,QAAK;QAACC,cAAc,EAAC;MAAM;IAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIC,EAAE,GAACtN,EAAE;AAAC,SAASuN,EAAEA,CAACpE,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACvH,EAAE,CAAC,CAAC;IAAC8H,CAAC,GAAC1F,EAAE,CAAC,CAAC;IAAC;MAAC+H,KAAK,EAACnC,CAAC;MAAC0D,YAAY,EAACvD,CAAC;MAACwD,QAAQ,EAACvD,CAAC;MAACwD,IAAI,EAAC/C,CAAC;MAACgD,IAAI,EAACtC,CAAC;MAACuC,EAAE,EAACtC,CAAC;MAACuC,OAAO,EAACtC,CAAC,GAAC,CAAC,CAAC;MAACuC,QAAQ,EAACrC,CAAC,GAAC7B,CAAC,IAAE,CAAC,CAAC;MAACmE,OAAO,EAACnC,CAAC;MAACoC,UAAU,EAACjE,CAAC,GAAC,CAAC,CAAC;MAACkE,QAAQ,EAAC9D,CAAC,GAAC,CAAC,CAAC;MAAC+D,SAAS,EAACC,CAAC,GAAC,CAAC,CAAC;MAACtE,OAAO,EAACuE,CAAC,GAAC,IAAI;MAACC,QAAQ,EAACC;IAAM,CAAC,GAACnF,CAAC;IAAJoF,CAAC,GAAAtP,wBAAA,CAAEkK,CAAC,EAAAhK,SAAA;IAACqP,CAAC,GAAClN,EAAE,CAAC2I,CAAC,CAAC;IAAC,CAACwE,CAAC,GAACtE,CAAC,GAAC,EAAE,GAAC,KAAK,CAAC,EAACuE,CAAC,CAAC,GAACtN,EAAE,CAAC0I,CAAC,EAACI,CAAC,EAACsE,CAAC,CAAC;IAACG,CAAC,GAAC9F,EAAE,CAAC;MAAC+F,EAAE,EAACvF,CAAC;MAACQ,OAAO,EAACuE,CAAC;MAACJ,UAAU,EAACjE;IAAC,CAAC,CAAC;IAAC8E,CAAC,GAACnO,EAAE,CAAC;MAACoO,MAAM,EAAC,CAAC,CAAC;MAACC,IAAI,EAAC,CAAC;IAAC,CAAC,CAAC;IAACC,CAAC,GAAC9N,EAAE,CAACoK,CAAC,CAAC;IAAC2D,CAAC,GAACrN,CAAC,CAACsN,CAAC,IAAEd,CAAC,GAAC9C,CAAC,KAAG,IAAI,GAAC8C,CAAC,CAACzE,OAAO,CAACwF,OAAO,CAACD,CAAC,CAAC,GAACd,CAAC,CAACzE,OAAO,CAACyF,SAAS,CAACC,CAAC,IAAEL,CAAC,CAACK,CAAC,EAACH,CAAC,CAAC,CAAC,GAACP,CAAC,CAACxD,KAAK,CAACxB,OAAO,CAACyF,SAAS,CAACC,CAAC,IAAEL,CAAC,CAACK,CAAC,CAACC,OAAO,CAACC,OAAO,CAACtD,KAAK,EAACiD,CAAC,CAAC,CAAC,CAAC;IAACM,CAAC,GAACpP,CAAC,CAAC8O,CAAC,IAAEzI,EAAE,CAACgJ,CAAC,CAACC,IAAI,EAAC;MAAC,CAACjH,CAAC,CAACkH,KAAK,GAAE,MAAIlB,CAAC,CAACmB,IAAI,CAACP,CAAC,IAAEL,CAAC,CAACK,CAAC,EAACH,CAAC,CAAC,CAAC;MAAC,CAACzG,CAAC,CAACoH,MAAM,GAAE,MAAIb,CAAC,CAACP,CAAC,EAACS,CAAC;IAAC,CAAC,CAAC,EAAC,CAACT,CAAC,CAAC,CAAC;IAACqB,CAAC,GAAChK,CAAC,CAAC6I,CAAC,EAACO,CAAC,IAAEA,CAAC,CAACrF,OAAO,CAAC;IAACkG,CAAC,GAACnO,CAAC,CAAC,MAAIgK,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC;IAAC6D,CAAC,GAACjP,CAAC,CAAC,OAAK;MAACwN,UAAU,EAACjE,CAAC;MAACmE,SAAS,EAACC,CAAC;MAAC6B,eAAe,EAACnB,CAAC;MAAC5C,KAAK,EAACwC,CAAC;MAACjB,YAAY,EAACgB,CAAC;MAACV,QAAQ,EAACrC,CAAC;MAACoC,OAAO,EAACtC,CAAC;MAACmE,IAAI,EAACvF,CAAC,GAAC1B,CAAC,CAACkH,KAAK,GAAClH,CAAC,CAACoH,MAAM;MAAChG,OAAO,EAACuE,CAAC,GAAC0B,CAAC,GAAC,IAAI;MAACrC,QAAQ,EAACiB,CAAC;MAACuB,UAAU,EAACT,CAAC;MAACU,cAAc,EAACjB,CAAC;MAACkB,OAAO,EAACnB,CAAC;MAACjB,OAAO,EAACgC;IAAC,CAAC,CAAC,EAAC,CAACtB,CAAC,EAACD,CAAC,EAAC/C,CAAC,EAACF,CAAC,EAACpB,CAAC,EAACuE,CAAC,EAACc,CAAC,EAACzF,CAAC,EAAC4E,CAAC,EAACP,CAAC,EAAC0B,CAAC,EAACC,CAAC,CAAC,CAAC;EAAC7N,EAAE,CAAC,MAAI;IAAC,IAAIgN,CAAC;IAACd,CAAC,IAAEO,CAAC,CAACyB,IAAI,CAAC;MAACC,IAAI,EAAClI,EAAE,CAACmI,0BAA0B;MAAC3G,OAAO,EAACyE,CAAC,CAACzE,OAAO;MAACmE,QAAQ,EAAC,CAACoB,CAAC,GAACd,CAAC,CAACN,QAAQ,KAAG,IAAI,GAACoB,CAAC,GAAC;IAAI,CAAC,CAAC;EAAA,CAAC,EAAC,CAACd,CAAC,EAACA,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACzE,OAAO,EAACyE,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAC5L,EAAE,CAAC,MAAI;IAACyM,CAAC,CAACxD,KAAK,CAACmE,OAAO,CAACC,OAAO,GAACE,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAAC,IAAG,CAACc,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,GAAC5K,CAAC,CAAC6I,CAAC,EAACO,CAAC,IAAE,CAACA,CAAC,CAACyB,aAAa,EAACzB,CAAC,CAAC0B,aAAa,EAAC1B,CAAC,CAAC2B,YAAY,EAAC3B,CAAC,CAAClF,cAAc,CAAC,CAAC;IAAC8G,CAAC,GAAClL,EAAE,CAACmL,GAAG,CAAC,IAAI,CAAC;IAACC,CAAC,GAAClL,CAAC,CAACgL,CAAC,EAAC1Q,CAAC,CAAC8O,CAAC,IAAE4B,CAAC,CAACjF,SAAS,CAACoF,KAAK,CAAC/B,CAAC,EAAC7F,CAAC,CAAC,EAAC,CAACyH,CAAC,EAACzH,CAAC,CAAC,CAAC,CAAC;EAAC7G,EAAE,CAACwO,CAAC,EAAC,CAACR,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,EAAC,MAAI/B,CAAC,CAACuC,OAAO,CAACC,aAAa,CAAC,CAAC,CAAC;EAAC,IAAIC,CAAC,GAACtL,CAAC,CAAC6I,CAAC,EAACA,CAAC,CAAC9C,SAAS,CAACC,iBAAiB,CAAC;IAACuF,CAAC,GAACvL,CAAC,CAAC6I,CAAC,EAACA,CAAC,CAAC9C,SAAS,CAACyF,YAAY,CAAC;IAACC,EAAE,GAAC/Q,CAAC,CAAC,OAAK;MAACgR,IAAI,EAACjB,CAAC,KAAGhI,CAAC,CAACkJ,IAAI;MAAC3D,QAAQ,EAACrC,CAAC;MAACoC,OAAO,EAACtC,CAAC;MAACmG,WAAW,EAACN,CAAC;MAACE,YAAY,EAACD,CAAC;MAACpF,KAAK,EAACwC;IAAC,CAAC,CAAC,EAAC,CAACgB,CAAC,EAAChE,CAAC,EAACgD,CAAC,EAAClD,CAAC,EAAC8F,CAAC,EAACd,CAAC,CAAC,CAAC;IAAC,CAACoB,CAAC,EAACC,EAAE,CAAC,GAAC/J,EAAE,CAAC,CAAC;IAACgK,CAAC,GAACzI,CAAC,KAAG,IAAI,GAAC,CAAC,CAAC,GAAC;MAACmD,GAAG,EAACnD;IAAC,CAAC;IAAC0I,CAAC,GAAC1R,CAAC,CAAC,MAAI;MAAC,IAAGoO,CAAC,KAAG,KAAK,CAAC,EAAC,OAAOE,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACE,CAAC,EAACF,CAAC,CAAC,CAAC;IAACuD,CAAC,GAAC5K,EAAE,CAAC,CAAC;EAAC,OAAOrH,CAAC,CAACiM,aAAa,CAAC6F,EAAE,EAAC;IAAC3F,KAAK,EAAC0F,CAAC;IAACK,KAAK,EAAC;MAACC,OAAO,EAACxB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC7B;IAAE,CAAC;IAAC9B,IAAI,EAAC;MAAC0E,IAAI,EAACjB,CAAC,KAAGhI,CAAC,CAACkJ,IAAI;MAAC3D,QAAQ,EAACrC;IAAC;EAAC,CAAC,EAAC3L,CAAC,CAACiM,aAAa,CAAC3H,EAAE,EAAC,IAAI,EAACtE,CAAC,CAACiM,aAAa,CAAC/C,EAAE,CAACgD,QAAQ,EAAC;IAACC,KAAK,EAACwD;EAAC,CAAC,EAAC3P,CAAC,CAACiM,aAAa,CAACpD,EAAE,CAACqD,QAAQ,EAAC;IAACC,KAAK,EAAC0C;EAAC,CAAC,EAAC7O,CAAC,CAACiM,aAAa,CAACzG,EAAE,EAAC;IAAC2G,KAAK,EAACxF,EAAE,CAAC8J,CAAC,EAAC;MAAC,CAAChI,CAAC,CAACkJ,IAAI,GAAEjM,EAAE,CAACiM,IAAI;MAAC,CAAClJ,CAAC,CAAC2J,MAAM,GAAE1M,EAAE,CAAC0M;IAAM,CAAC;EAAC,CAAC,EAAC7G,CAAC,IAAE,IAAI,IAAEvL,CAAC,CAACiM,aAAa,CAACjH,EAAE,EAAC;IAACgJ,QAAQ,EAACrC,CAAC;IAAC0G,IAAI,EAAC1D,CAAC,IAAE,IAAI,GAAC;MAAC,CAACpD,CAAC,GAAEoD;IAAC,CAAC,GAAC,CAAC,CAAC;IAACf,IAAI,EAAC/C,CAAC;IAACyH,OAAO,EAACN;EAAC,CAAC,CAAC,EAACC,CAAC,CAAC;IAACM,QAAQ,EAACR,CAAC;IAACS,UAAU,EAAC/D,CAAC;IAACzB,IAAI,EAACyE,EAAE;IAACgB,UAAU,EAACjF,EAAE;IAACK,IAAI,EAAC;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAI6E,EAAE,GAAC,OAAO;AAAC,SAASC,EAAEA,CAACtJ,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIuI,CAAC,EAACC,EAAE;EAAC,IAAIvI,CAAC,GAACN,EAAE,CAAC,gBAAgB,CAAC;IAACa,CAAC,GAACV,EAAE,CAAC,gBAAgB,CAAC;IAACY,CAAC,GAAChI,EAAE,CAAC,CAAC;IAACmI,CAAC,GAAC7E,EAAE,CAAC,CAAC;IAAC;MAACwJ,EAAE,EAAC1E,CAAC,GAACD,CAAC,iCAAAV,MAAA,CAA+BO,CAAC,CAAE;MAAC2D,QAAQ,EAAC9C,CAAC;MAAC+H,YAAY,EAACrH,CAAC;MAACyC,QAAQ,EAACxC,CAAC,GAAC1B,CAAC,CAACkE,QAAQ,IAAE,CAAC,CAAC;MAAC6E,SAAS,EAACpH,CAAC,GAAC,CAAC,CAAC;MAAC8E,IAAI,EAAC5E,CAAC,GAAC;IAAW,CAAC,GAACtC,CAAC;IAAJyC,CAAC,GAAA3M,wBAAA,CAAEkK,CAAC,EAAA/J,UAAA;IAAC,CAAC2K,CAAC,CAAC,GAACjE,CAAC,CAACuD,CAAC,EAACwI,CAAC,IAAE,CAACA,CAAC,CAAChB,YAAY,CAAC,CAAC;IAAC1G,CAAC,GAACzJ,EAAE,CAAC,IAAI,CAAC;IAACyN,CAAC,GAAC7K,EAAE,CAAC6G,CAAC,EAACf,CAAC,EAAC1E,EAAE,CAAC,CAAC,EAAC2E,CAAC,CAAC6H,OAAO,CAAC0B,eAAe,CAAC;IAACxE,CAAC,GAAC1L,EAAE,CAACqH,CAAC,CAAC;IAAC,CAACuE,CAAC,EAACC,CAAC,CAAC,GAACzI,CAAC,CAACuD,CAAC,EAACwI,CAAC,IAAE,CAACA,CAAC,CAAClB,aAAa,EAACkB,CAAC,CAACgB,QAAQ,CAAC,CAAC;IAACrE,CAAC,GAAChN,EAAE,CAAC,CAAC;IAACiN,CAAC,GAAC7M,CAAC,CAAC,MAAI;MAACyH,CAAC,CAAC6H,OAAO,CAACzD,QAAQ,CAAC,IAAI,CAAC,EAACpE,CAAC,CAAC8B,KAAK,CAACnB,cAAc,KAAGX,CAAC,CAAC8B,KAAK,CAACnB,cAAc,CAAC8I,SAAS,GAAC,CAAC,CAAC,EAACzJ,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;QAACC,KAAK,EAAC5M,CAAC,CAAC6M;MAAO,CAAC,CAAC;IAAA,CAAC,CAAC;IAACvE,CAAC,GAAClO,CAAC,CAAC,MAAI;MAAC,IAAIqR,CAAC;MAAC,OAAO,OAAOxG,CAAC,IAAE,UAAU,IAAEzB,CAAC,CAACqC,KAAK,KAAG,KAAK,CAAC,GAAC,CAAC4F,CAAC,GAACxG,CAAC,CAACzB,CAAC,CAACqC,KAAK,CAAC,KAAG,IAAI,GAAC4F,CAAC,GAAC,EAAE,GAAC,OAAOjI,CAAC,CAACqC,KAAK,IAAE,QAAQ,GAACrC,CAAC,CAACqC,KAAK,GAAC,EAAE;IAAA,CAAC,EAAC,CAACrC,CAAC,CAACqC,KAAK,EAACZ,CAAC,CAAC,CAAC;EAACrH,EAAE,CAAC,CAAAkP,IAAA,EAAAC,KAAA,KAAe;IAAA,IAAd,CAACtB,CAAC,EAACC,CAAC,CAAC,GAAAoB,IAAA;IAAA,IAAC,CAACnB,CAAC,EAAC7C,CAAC,CAAC,GAAAiE,KAAA;IAAI,IAAG9J,CAAC,CAAC8B,KAAK,CAAC0H,QAAQ,EAAC;IAAO,IAAIxD,CAAC,GAAClF,CAAC,CAACoF,OAAO;IAACF,CAAC,KAAG,CAACH,CAAC,KAAG3G,CAAC,CAACkJ,IAAI,IAAEK,CAAC,KAAGvJ,CAAC,CAAC2J,MAAM,IAAEL,CAAC,KAAGE,CAAC,MAAI1C,CAAC,CAACpD,KAAK,GAAC4F,CAAC,CAAC,EAACuB,qBAAqB,CAAC,MAAI;MAAC,IAAG/J,CAAC,CAAC8B,KAAK,CAAC0H,QAAQ,IAAE,CAACxD,CAAC,IAAE,CAACjB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACiF,aAAa,MAAIhE,CAAC,EAAC;MAAO,IAAG;QAACiE,cAAc,EAACC,CAAC;QAACC,YAAY,EAACC;MAAE,CAAC,GAACpE,CAAC;MAACqE,IAAI,CAACC,GAAG,CAAC,CAACF,EAAE,IAAE,IAAI,GAACA,EAAE,GAAC,CAAC,KAAGF,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,CAAC,CAAC,KAAG,CAAC,IAAEA,CAAC,KAAG,CAAC,IAAElE,CAAC,CAACuE,iBAAiB,CAACvE,CAAC,CAACpD,KAAK,CAACpB,MAAM,EAACwE,CAAC,CAACpD,KAAK,CAACpB,MAAM,CAAC;IAAA,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC6D,CAAC,EAACJ,CAAC,EAACF,CAAC,EAACG,CAAC,CAAC,CAAC,EAACvK,EAAE,CAAC,CAAA6P,KAAA,EAAAC,KAAA,KAAW;IAAA,IAAV,CAACjC,CAAC,CAAC,GAAAgC,KAAA;IAAA,IAAC,CAAC/B,CAAC,CAAC,GAAAgC,KAAA;IAAI,IAAGjC,CAAC,KAAGtJ,CAAC,CAACkJ,IAAI,IAAEK,CAAC,KAAGvJ,CAAC,CAAC2J,MAAM,EAAC;MAAC,IAAG7I,CAAC,CAAC8B,KAAK,CAAC0H,QAAQ,EAAC;MAAO,IAAId,CAAC,GAAC5H,CAAC,CAACoF,OAAO;MAAC,IAAG,CAACwC,CAAC,EAAC;MAAO,IAAI7C,CAAC,GAAC6C,CAAC,CAAC9F,KAAK;QAAC;UAACqH,cAAc,EAACjE,CAAC;UAACmE,YAAY,EAACD,CAAC;UAACQ,kBAAkB,EAACN;QAAE,CAAC,GAAC1B,CAAC;MAACA,CAAC,CAAC9F,KAAK,GAAC,EAAE,EAAC8F,CAAC,CAAC9F,KAAK,GAACiD,CAAC,EAACuE,EAAE,KAAG,IAAI,GAAC1B,CAAC,CAAC6B,iBAAiB,CAACvE,CAAC,EAACkE,CAAC,EAACE,EAAE,CAAC,GAAC1B,CAAC,CAAC6B,iBAAiB,CAACvE,CAAC,EAACkE,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC,CAACjF,CAAC,CAAC,CAAC;EAAC,IAAIK,CAAC,GAACjO,EAAE,CAAC,CAAC,CAAC,CAAC;IAACmO,CAAC,GAACjN,CAAC,CAAC,MAAI;MAAC+M,CAAC,CAACY,OAAO,GAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACP,CAAC,GAACpN,CAAC,CAAC,MAAI;MAAC4M,CAAC,CAACwF,SAAS,CAAC,MAAI;QAACrF,CAAC,CAACY,OAAO,GAAC,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAACN,CAAC,GAACrN,CAAC,CAACiQ,CAAC,IAAE;MAAC,QAAOxI,CAAC,CAAC6H,OAAO,CAAC+C,WAAW,CAAC,CAAC,CAAC,CAAC,EAACpC,CAAC,CAACnF,GAAG;QAAE,KAAKnF,CAAC,CAAC2M,KAAK;UAAC,IAAG7K,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,IAAE9C,CAAC,CAACY,OAAO,EAAC;UAAO,IAAGsC,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAACwC,SAAS,CAACC,iBAAiB,CAACzC,CAAC,CAAC8B,KAAK,CAAC,KAAG,IAAI,EAAC;YAAC9B,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC;YAAC;UAAM;UAAC9H,CAAC,CAAC6H,OAAO,CAACmD,kBAAkB,CAAC,CAAC,EAACzK,CAAC,CAAC8F,IAAI,KAAGjH,CAAC,CAACoH,MAAM,IAAExG,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC;UAAC;QAAM,KAAK5J,CAAC,CAAC+M,SAAS;UAAC,OAAOzC,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC3N,EAAE,CAAC4C,CAAC,CAAC8B,KAAK,CAACwF,aAAa,EAAC;YAAC,CAACpI,CAAC,CAACkJ,IAAI,GAAE,MAAIpI,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;cAACC,KAAK,EAAC5M,CAAC,CAACmO;YAAI,CAAC,CAAC;YAAC,CAAChM,CAAC,CAAC2J,MAAM,GAAE,MAAI7I,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC;UAAC,CAAC,CAAC;QAAC,KAAKjN,CAAC,CAACkN,OAAO;UAAC,OAAO5C,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC3N,EAAE,CAAC4C,CAAC,CAAC8B,KAAK,CAACwF,aAAa,EAAC;YAAC,CAACpI,CAAC,CAACkJ,IAAI,GAAE,MAAIpI,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;cAACC,KAAK,EAAC5M,CAAC,CAACsO;YAAQ,CAAC,CAAC;YAAC,CAACnM,CAAC,CAAC2J,MAAM,GAAE,MAAI;cAACpR,EAAE,CAAC,MAAIuI,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,EAAC5K,CAAC,CAACqC,KAAK,IAAE5C,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;gBAACC,KAAK,EAAC5M,CAAC,CAACuO;cAAI,CAAC,CAAC;YAAA;UAAC,CAAC,CAAC;QAAC,KAAKpN,CAAC,CAACqN,IAAI;UAAC,IAAG/C,CAAC,CAACgD,QAAQ,EAAC;UAAM,OAAOhD,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;YAACC,KAAK,EAAC5M,CAAC,CAAC0O;UAAK,CAAC,CAAC;QAAC,KAAKvN,CAAC,CAACwN,MAAM;UAAC,OAAOlD,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;YAACC,KAAK,EAAC5M,CAAC,CAAC0O;UAAK,CAAC,CAAC;QAAC,KAAKvN,CAAC,CAACyN,GAAG;UAAC,IAAGnD,CAAC,CAACgD,QAAQ,EAAC;UAAM,OAAOhD,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;YAACC,KAAK,EAAC5M,CAAC,CAACuO;UAAI,CAAC,CAAC;QAAC,KAAKpN,CAAC,CAAC0N,QAAQ;UAAC,OAAOpD,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACtC,CAAC,CAACuC,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;YAACC,KAAK,EAAC5M,CAAC,CAACuO;UAAI,CAAC,CAAC;QAAC,KAAKpN,CAAC,CAAC2N,MAAM;UAAC,OAAO7L,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,GAAC,KAAK,CAAC,IAAEI,CAAC,CAACsC,cAAc,CAAC,CAAC,EAAC9K,CAAC,CAAC8B,KAAK,CAACnB,cAAc,IAAE,CAACJ,CAAC,CAACoG,eAAe,CAACT,OAAO,CAACT,MAAM,IAAE+C,CAAC,CAACuC,eAAe,CAAC,CAAC,EAACxK,CAAC,CAAC8F,IAAI,KAAGjH,CAAC,CAACoH,MAAM,IAAEjG,CAAC,CAACqC,KAAK,KAAG,IAAI,IAAEwC,CAAC,CAAC,CAAC,EAACpF,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC,CAAC;QAAC,KAAK5J,CAAC,CAAC4N,GAAG;UAAC,IAAG9L,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,EAAC;UAAO7H,CAAC,CAAC8F,IAAI,KAAGjH,CAAC,CAACoH,MAAM,IAAExG,CAAC,CAAC8B,KAAK,CAACO,iBAAiB,KAAGrD,EAAE,CAAClC,KAAK,IAAEkD,CAAC,CAAC6H,OAAO,CAACmD,kBAAkB,CAAC,CAAC,EAAChL,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAAC3B,CAAC,GAAC5N,CAAC,CAACiQ,CAAC,IAAE;MAAClH,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACkH,CAAC,CAAC,EAACjI,CAAC,CAAC8F,IAAI,KAAGjH,CAAC,CAACoH,MAAM,IAAEgC,CAAC,CAACuD,MAAM,CAACnJ,KAAK,KAAG,EAAE,IAAEwC,CAAC,CAAC,CAAC,EAACpF,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC1E,CAAC,GAAClO,CAAC,CAACiQ,CAAC,IAAE;MAAC,IAAIE,CAAC,EAAC7C,CAAC,EAACG,CAAC;MAAC,IAAIyC,CAAC,GAAC,CAACC,CAAC,GAACF,CAAC,CAACwD,aAAa,KAAG,IAAI,GAACtD,CAAC,GAAC/L,EAAE,CAACsP,IAAI,CAAC/B,CAAC,IAAEA,CAAC,KAAG1B,CAAC,CAAC0D,aAAa,CAAC;MAAC,IAAG,EAAE,CAACrG,CAAC,GAAC7F,CAAC,CAAC8B,KAAK,CAACnB,cAAc,KAAG,IAAI,IAAEkF,CAAC,CAACsG,QAAQ,CAAC1D,CAAC,CAAC,CAAC,IAAE,EAAE,CAACzC,CAAC,GAAChG,CAAC,CAAC8B,KAAK,CAACyF,aAAa,KAAG,IAAI,IAAEvB,CAAC,CAACmG,QAAQ,CAAC1D,CAAC,CAAC,CAAC,IAAEzI,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,EAAC,OAAOI,CAAC,CAACsC,cAAc,CAAC,CAAC,EAACvK,CAAC,CAAC8F,IAAI,KAAGjH,CAAC,CAACoH,MAAM,IAAEjG,CAAC,CAACqC,KAAK,KAAG,IAAI,IAAEwC,CAAC,CAAC,CAAC,EAACpF,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC;IAAA,CAAC,CAAC;IAACpB,CAAC,GAACnO,CAAC,CAACiQ,CAAC,IAAE;MAAC,IAAIE,CAAC,EAAC7C,CAAC,EAACG,CAAC;MAAC,IAAIyC,CAAC,GAAC,CAACC,CAAC,GAACF,CAAC,CAACwD,aAAa,KAAG,IAAI,GAACtD,CAAC,GAAC/L,EAAE,CAACsP,IAAI,CAAC/B,CAAC,IAAEA,CAAC,KAAG1B,CAAC,CAAC0D,aAAa,CAAC;MAAC,CAACrG,CAAC,GAAC7F,CAAC,CAAC8B,KAAK,CAACyF,aAAa,KAAG,IAAI,IAAE1B,CAAC,CAACsG,QAAQ,CAAC1D,CAAC,CAAC,IAAE,CAACzC,CAAC,GAAChG,CAAC,CAAC8B,KAAK,CAACnB,cAAc,KAAG,IAAI,IAAEqF,CAAC,CAACmG,QAAQ,CAAC1D,CAAC,CAAC,IAAElI,CAAC,CAACkE,QAAQ,IAAElE,CAAC,CAACsE,SAAS,IAAE7E,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,IAAEjD,CAAC,CAACiH,SAAS,CAAC,MAAI;QAAC3U,EAAE,CAAC,MAAIuI,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,EAACnL,CAAC,CAAC6H,OAAO,CAACwE,oBAAoB,CAACrN,EAAE,CAAClC,KAAK,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAACsJ,CAAC,GAAC9H,EAAE,CAAC,CAAC;IAAC4I,CAAC,GAAClJ,EAAE,CAAC,CAAC;IAAC;MAACsO,SAAS,EAACnF,CAAC;MAACoF,UAAU,EAACnF;IAAC,CAAC,GAAChR,EAAE,CAAC;MAACkT,SAAS,EAACpH;IAAC,CAAC,CAAC;IAAC;MAACsK,SAAS,EAACnF,CAAC;MAACoF,UAAU,EAAChF;IAAC,CAAC,GAACnR,EAAE,CAAC;MAACoW,UAAU,EAACzK;IAAC,CAAC,CAAC;IAAC0F,CAAC,GAAClL,CAAC,CAACuD,CAAC,EAACwI,CAAC,IAAEA,CAAC,CAAC7H,cAAc,CAAC;IAACoH,CAAC,GAAC5Q,CAAC,CAAC,OAAK;MAACgR,IAAI,EAAClD,CAAC,KAAG/F,CAAC,CAACkJ,IAAI;MAAC3D,QAAQ,EAACxC,CAAC;MAACuC,OAAO,EAACjE,CAAC,CAACiE,OAAO;MAACmI,KAAK,EAACtF,CAAC;MAACsC,KAAK,EAACxC,CAAC;MAACyF,SAAS,EAAC1K;IAAC,CAAC,CAAC,EAAC,CAAC3B,CAAC,EAAC8G,CAAC,EAACF,CAAC,EAACjF,CAAC,EAACD,CAAC,EAAC1B,CAAC,CAACiE,OAAO,CAAC,CAAC;IAACwD,CAAC,GAACpK,EAAE,CAAC;MAACsF,GAAG,EAAC4B,CAAC;MAACS,EAAE,EAAC1E,CAAC;MAACgM,IAAI,EAAC,UAAU;MAAC7F,IAAI,EAAC5E,CAAC;MAAC,eAAe,EAACuF,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACpC,EAAE;MAAC,eAAe,EAACN,CAAC,KAAG/F,CAAC,CAACkJ,IAAI;MAAC,uBAAuB,EAAC3L,CAAC,CAACuD,CAAC,EAACA,CAAC,CAACwC,SAAS,CAACsK,kBAAkB,CAAC;MAAC,iBAAiB,EAAC1G,CAAC;MAAC,kBAAkB,EAACc,CAAC;MAAC,mBAAmB,EAAC,MAAM;MAAC/C,YAAY,EAAC,CAACoE,EAAE,GAAC,CAACD,CAAC,GAACxI,CAAC,CAACqE,YAAY,KAAG,IAAI,GAACmE,CAAC,GAAC/H,CAAC,CAAC4D,YAAY,KAAG,KAAK,CAAC,GAACnC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACzB,CAAC,CAAC4D,YAAY,CAAC,GAAC,IAAI,KAAG,IAAI,GAACoE,EAAE,GAAChI,CAAC,CAAC4D,YAAY;MAACM,QAAQ,EAACxC,CAAC,IAAE,KAAK,CAAC;MAACqH,SAAS,EAACpH,CAAC;MAAC6K,kBAAkB,EAACvH,CAAC;MAACwH,gBAAgB,EAACrH,CAAC;MAACsH,SAAS,EAACrH,CAAC;MAACxB,QAAQ,EAAC+B,CAAC;MAAC+G,OAAO,EAACxG,CAAC;MAACyG,MAAM,EAAC1G;IAAC,CAAC,EAACW,CAAC,EAACK,CAAC,CAAC;EAAC,OAAO3J,EAAE,CAAC,CAAC,CAAC;IAACkL,QAAQ,EAAChB,CAAC;IAACiB,UAAU,EAAC1G,CAAC;IAACkB,IAAI,EAACsE,CAAC;IAACmB,UAAU,EAACC,EAAE;IAAC7E,IAAI,EAAC;EAAgB,CAAC,CAAC;AAAA;AAAC,IAAI8I,EAAE,GAAC,QAAQ;AAAC,SAASC,EAAEA,CAACvN,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,EAAE,CAAC,iBAAiB,CAAC;IAACa,CAAC,GAACV,EAAE,CAAC,iBAAiB,CAAC;IAAC,CAACY,CAAC,EAACG,CAAC,CAAC,GAACrJ,EAAE,CAAC,IAAI,CAAC;IAACsJ,CAAC,GAAC5G,EAAE,CAAC8F,CAAC,EAACa,CAAC,EAACZ,CAAC,CAAC6H,OAAO,CAACyF,gBAAgB,CAAC;IAAChM,CAAC,GAAC7I,EAAE,CAAC,CAAC;IAAC;MAAC8M,EAAE,EAACvD,CAAC,iCAAA9B,MAAA,CAA+BoB,CAAC,CAAE;MAACmD,QAAQ,EAACxC,CAAC,GAAC1B,CAAC,CAACkE,QAAQ,IAAE,CAAC,CAAC;MAAC6E,SAAS,EAACpH,CAAC,GAAC,CAAC;IAAM,CAAC,GAACpC,CAAC;IAAJsC,CAAC,GAAAxM,wBAAA,CAAEkK,CAAC,EAAA9J,UAAA;IAAC,CAACuM,CAAC,EAAC7B,CAAC,EAACI,CAAC,CAAC,GAACrE,CAAC,CAACuD,CAAC,EAACoG,CAAC,IAAE,CAACA,CAAC,CAACkB,aAAa,EAAClB,CAAC,CAACoB,YAAY,EAACpB,CAAC,CAACzF,cAAc,CAAC,CAAC;IAACmE,CAAC,GAACnL,EAAE,CAAC+G,CAAC,CAAC;IAACqE,CAAC,GAACxC,CAAC,KAAGrD,CAAC,CAACkJ,IAAI;EAAC3O,EAAE,CAACsL,CAAC,EAAC;IAACwI,OAAO,EAAC9M,CAAC;IAAC+M,MAAM,EAACzW,CAAC,CAACqP,CAAC,IAAE;MAAC,IAAG3F,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC0L,QAAQ,CAAC/F,CAAC,CAAC2F,MAAM,CAAC,EAAC,OAAOxS,EAAE,CAACkU,MAAM;MAAC,IAAG/M,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACyL,QAAQ,CAAC/F,CAAC,CAAC2F,MAAM,CAAC,EAAC,OAAOxS,EAAE,CAACkU,MAAM;MAAC,IAAIvG,CAAC,GAACd,CAAC,CAAC2F,MAAM,CAAC2B,OAAO,CAAC,sCAAsC,CAAC;MAAC,OAAOxQ,EAAE,CAACyQ,aAAa,CAACzG,CAAC,CAAC,GAAC3N,EAAE,CAACqU,MAAM,CAAC1G,CAAC,CAAC,GAACpG,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACqL,QAAQ,CAAC/F,CAAC,CAAC2F,MAAM,CAAC,GAACxS,EAAE,CAACkU,MAAM,GAAClU,EAAE,CAACsU,KAAK;IAAA,CAAC,EAAC,CAACpN,CAAC,EAACC,CAAC,EAACI,CAAC,CAAC,CAAC;IAACgN,KAAK,EAAC9N,CAAC,CAAC6H,OAAO,CAACC,aAAa;IAACiG,MAAM,EAAC/N,CAAC,CAAC6H,OAAO,CAACmD;EAAkB,CAAC,CAAC;EAAC,IAAI/F,CAAC,GAAC1M,CAAC,CAAC6N,CAAC,IAAE;MAAC,QAAOA,CAAC,CAAC/C,GAAG;QAAE,KAAKnF,CAAC,CAAC8P,KAAK;QAAC,KAAK9P,CAAC,CAAC2M,KAAK;UAACzE,CAAC,CAAC0E,cAAc,CAAC,CAAC,EAAC1E,CAAC,CAAC2E,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAAC2J,MAAM,IAAEpR,EAAE,CAAC,MAAIuI,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,EAACrG,CAAC,CAAC,CAAC;UAAC;QAAO,KAAK5G,CAAC,CAAC+M,SAAS;UAAC7E,CAAC,CAAC0E,cAAc,CAAC,CAAC,EAAC1E,CAAC,CAAC2E,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAAC2J,MAAM,KAAGpR,EAAE,CAAC,MAAIuI,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,EAACnL,CAAC,CAAC8B,KAAK,CAACmE,OAAO,CAACC,OAAO,CAACtD,KAAK,IAAE5C,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;YAACC,KAAK,EAAC5M,CAAC,CAAC0O;UAAK,CAAC,CAAC,CAAC,EAAC3G,CAAC,CAAC,CAAC;UAAC;QAAO,KAAK5G,CAAC,CAACkN,OAAO;UAAChF,CAAC,CAAC0E,cAAc,CAAC,CAAC,EAAC1E,CAAC,CAAC2E,eAAe,CAAC,CAAC,EAAC/K,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAAC2J,MAAM,KAAGpR,EAAE,CAAC,MAAIuI,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,EAACnL,CAAC,CAAC8B,KAAK,CAACmE,OAAO,CAACC,OAAO,CAACtD,KAAK,IAAE5C,CAAC,CAAC6H,OAAO,CAAC6B,UAAU,CAAC;YAACC,KAAK,EAAC5M,CAAC,CAACuO;UAAI,CAAC,CAAC,CAAC,EAACxG,CAAC,CAAC,CAAC;UAAC;QAAO,KAAK5G,CAAC,CAAC2N,MAAM;UAAC,IAAG7L,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,EAAC;UAAOhC,CAAC,CAAC0E,cAAc,CAAC,CAAC,EAAC9K,CAAC,CAAC8B,KAAK,CAACnB,cAAc,IAAE,CAACJ,CAAC,CAACoG,eAAe,CAACT,OAAO,CAACT,MAAM,IAAEW,CAAC,CAAC2E,eAAe,CAAC,CAAC,EAACtT,EAAE,CAAC,MAAIuI,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC,CAAC,EAAChD,CAAC,CAAC,CAAC;UAAC;QAAO;UAAQ;MAAM;IAAC,CAAC,CAAC;IAACI,CAAC,GAAC3M,CAAC,CAAC6N,CAAC,IAAE;MAACA,CAAC,CAAC0E,cAAc,CAAC,CAAC,EAAC,CAACjO,EAAE,CAACuJ,CAAC,CAAC8F,aAAa,CAAC,KAAG9F,CAAC,CAAC6H,MAAM,KAAGvP,EAAE,CAACwP,IAAI,KAAGlO,CAAC,CAAC8B,KAAK,CAACwF,aAAa,KAAGpI,CAAC,CAACkJ,IAAI,GAACpI,CAAC,CAAC6H,OAAO,CAACC,aAAa,CAAC,CAAC,GAAC9H,CAAC,CAAC6H,OAAO,CAACsD,YAAY,CAAC,CAAC,CAAC,EAACrG,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACK,CAAC,GAAC7G,EAAE,CAAC,CAAC0D,CAAC,CAAC,CAAC;IAAC;MAACmM,cAAc,EAAC/I,CAAC;MAACmH,UAAU,EAAClH;IAAC,CAAC,GAACjP,EAAE,CAAC;MAACkT,SAAS,EAACpH;IAAC,CAAC,CAAC;IAAC;MAACsK,SAAS,EAAClH,CAAC;MAACmH,UAAU,EAACjH;IAAC,CAAC,GAAClP,EAAE,CAAC;MAACoW,UAAU,EAACzK;IAAC,CAAC,CAAC;IAAC;MAACmM,OAAO,EAACzI,CAAC;MAAC0I,UAAU,EAACzI;IAAC,CAAC,GAACjO,EAAE,CAAC;MAAC8M,QAAQ,EAACxC;IAAC,CAAC,CAAC;IAACkE,CAAC,GAAChP,CAAC,CAAC,OAAK;MAACgR,IAAI,EAAC5F,CAAC,KAAGrD,CAAC,CAACkJ,IAAI;MAACkG,MAAM,EAAC3I,CAAC,IAAEpD,CAAC,KAAGrD,CAAC,CAACkJ,IAAI;MAAC3D,QAAQ,EAACxC,CAAC;MAACuC,OAAO,EAACjE,CAAC,CAACiE,OAAO;MAAC5B,KAAK,EAACrC,CAAC,CAACqC,KAAK;MAAC+J,KAAK,EAACrH,CAAC;MAACqE,KAAK,EAACvE;IAAC,CAAC,CAAC,EAAC,CAAC7E,CAAC,EAAC+E,CAAC,EAACF,CAAC,EAACO,CAAC,EAAC1D,CAAC,EAACM,CAAC,CAAC,CAAC;IAACkE,CAAC,GAAC7I,EAAE,CAAC;MAACsF,GAAG,EAACrC,CAAC;MAAC0E,EAAE,EAACvD,CAAC;MAACgF,IAAI,EAACnN,EAAE,CAACiG,CAAC,EAACW,CAAC,CAAC;MAAC8N,QAAQ,EAAC,CAAC,CAAC;MAAC,eAAe,EAAC,SAAS;MAAC,eAAe,EAACzN,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACyE,EAAE;MAAC,eAAe,EAAChD,CAAC,KAAGrD,CAAC,CAACkJ,IAAI;MAAC,iBAAiB,EAACjD,CAAC;MAACV,QAAQ,EAACxC,CAAC,IAAE,KAAK,CAAC;MAACqH,SAAS,EAACpH,CAAC;MAACsM,aAAa,EAACtJ,CAAC;MAAC+H,SAAS,EAAChI;IAAC,CAAC,EAACI,CAAC,EAACG,CAAC,EAACI,CAAC,CAAC;EAAC,OAAO9H,EAAE,CAAC,CAAC,CAAC;IAACkL,QAAQ,EAACvC,CAAC;IAACwC,UAAU,EAAC7G,CAAC;IAACqB,IAAI,EAAC0C,CAAC;IAAC+C,UAAU,EAACkE,EAAE;IAAC9I,IAAI,EAAC;EAAiB,CAAC,CAAC;AAAA;AAAC,IAAImK,EAAE,GAAC,KAAK;EAACC,EAAE,GAAClR,EAAE,CAACmR,cAAc,GAACnR,EAAE,CAACoR,MAAM;AAAC,SAASC,EAAEA,CAAC/O,CAAC,EAACC,CAAC,EAAC;EAAC,IAAI2I,CAAC,EAAC7C,CAAC,EAACG,CAAC;EAAC,IAAIhG,CAAC,GAACvH,EAAE,CAAC,CAAC;IAAC;MAAC8M,EAAE,EAAChF,CAAC,kCAAAL,MAAA,CAAgCF,CAAC,CAAE;MAAC0F,IAAI,EAACjF,CAAC,GAAC,CAAC,CAAC;MAACqO,MAAM,EAAClO,CAAC;MAACmO,MAAM,EAAClO,CAAC,GAAC,CAAC,CAAC;MAACmO,KAAK,EAAC1N,CAAC,GAAC,CAAC,CAAC;MAAC2N,UAAU,EAACjN,CAAC,GAAC,CAAC;IAAM,CAAC,GAAClC,CAAC;IAAJmC,CAAC,GAAArM,wBAAA,CAAEkK,CAAC,EAAA7J,UAAA;IAACiM,CAAC,GAACxC,EAAE,CAAC,kBAAkB,CAAC;IAAC0C,CAAC,GAACvC,EAAE,CAAC,kBAAkB,CAAC;IAAC0C,CAAC,GAAChH,EAAE,CAACqF,CAAC,CAAC;EAAC2B,CAAC,KAAG1B,CAAC,GAAC,CAAC,CAAC,CAAC;EAAC,IAAG,CAACH,CAAC,EAACI,CAAC,CAAC,GAAC7F,EAAE,CAACsH,CAAC,CAAC;IAAC,CAACuC,CAAC,EAACC,CAAC,CAAC,GAACxN,EAAE,CAAC,IAAI,CAAC;IAAC0N,CAAC,GAAC9J,EAAE,CAAC,CAAC;IAAC+J,CAAC,GAACjL,EAAE,CAAC8F,CAAC,EAACwC,CAAC,GAAC7B,CAAC,GAAC,IAAI,EAACwB,CAAC,CAAC2F,OAAO,CAACqH,iBAAiB,EAACnK,CAAC,CAAC;IAAC,CAACI,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACE,CAAC,CAAC,GAAC/I,CAAC,CAACyF,CAAC,EAACgI,CAAC,IAAE,CAACA,CAAC,CAAC5C,aAAa,EAAC4C,CAAC,CAAC1C,YAAY,EAAC0C,CAAC,CAAC3C,aAAa,EAAC2C,CAAC,CAACvJ,cAAc,EAACuJ,CAAC,CAAC7H,iBAAiB,CAAC,CAAC;IAACsD,CAAC,GAACtM,EAAE,CAAC+L,CAAC,IAAEC,CAAC,CAAC;IAACO,CAAC,GAACvM,EAAE,CAACiM,CAAC,CAAC;IAACa,CAAC,GAAC9J,EAAE,CAAC,CAAC;IAAC,CAACoK,CAAC,EAACC,CAAC,CAAC,GAACnM,EAAE,CAACyH,CAAC,EAAC8C,CAAC,EAACqB,CAAC,KAAG,IAAI,GAAC,CAACA,CAAC,GAAChK,EAAE,CAACiM,IAAI,MAAIjM,EAAE,CAACiM,IAAI,GAACjD,CAAC,KAAGjG,CAAC,CAACkJ,IAAI,CAAC;EAACnP,EAAE,CAACwN,CAAC,EAACrB,CAAC,EAAClD,CAAC,CAAC2F,OAAO,CAACC,aAAa,CAAC;EAAC,IAAI1B,CAAC,GAAChE,CAAC,CAACuC,UAAU,GAAC,CAAC,CAAC,GAACrD,CAAC,IAAE6D,CAAC,KAAGjG,CAAC,CAACkJ,IAAI;EAACrO,EAAE,CAACqM,CAAC,EAACR,CAAC,CAAC;EAAC,IAAIsB,CAAC,GAAC9E,CAAC,CAACuC,UAAU,GAAC,CAAC,CAAC,GAACrD,CAAC,IAAE6D,CAAC,KAAGjG,CAAC,CAACkJ,IAAI;EAACzP,EAAE,CAACuO,CAAC,EAAC;IAACiI,OAAO,EAACpY,CAAC,CAAC,MAAI,CAACqO,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,EAAC,CAACF,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC;EAAC,CAAC,CAAC,EAACzM,EAAE,CAAC,MAAI;IAAC,IAAIqR,CAAC;IAAC9H,CAAC,CAACuE,eAAe,CAACT,OAAO,CAACT,MAAM,GAAC,CAACyE,CAAC,GAACpK,CAAC,CAAC2F,MAAM,KAAG,IAAI,GAACyE,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC9H,CAAC,CAACuE,eAAe,EAAC7G,CAAC,CAAC2F,MAAM,CAAC,CAAC,EAAC5M,EAAE,CAAC,MAAI;IAACuJ,CAAC,CAACuE,eAAe,CAACT,OAAO,CAACR,IAAI,GAACjF,CAAC;EAAA,CAAC,EAAC,CAAC2B,CAAC,CAACuE,eAAe,EAAClG,CAAC,CAAC,CAAC,EAAChG,EAAE,CAAC0K,CAAC,KAAGjG,CAAC,CAACkJ,IAAI,EAAC;IAACgH,SAAS,EAAC9J,CAAC;IAAC+J,MAAMA,CAACnF,CAAC,EAAC;MAAC,OAAOA,CAAC,CAACoF,YAAY,CAAC,MAAM,CAAC,KAAG,QAAQ,GAACC,UAAU,CAACC,aAAa,GAACtF,CAAC,CAACuF,YAAY,CAAC,MAAM,CAAC,GAACF,UAAU,CAACG,WAAW,GAACH,UAAU,CAACI,aAAa;IAAA,CAAC;IAACC,IAAIA,CAAC1F,CAAC,EAAC;MAACA,CAAC,CAAC2F,YAAY,CAAC,MAAM,EAAC,MAAM,CAAC;IAAA;EAAC,CAAC,CAAC;EAAC,IAAI1I,CAAC,GAAC7I,EAAE,CAAC,CAAC+G,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACE,EAAE,CAAC,CAAC;IAAC6B,CAAC,GAACjQ,CAAC,CAAC,OAAK;MAACgR,IAAI,EAAChD,CAAC,KAAGjG,CAAC,CAACkJ,IAAI;MAAC1E,MAAM,EAAC,KAAK;IAAC,CAAC,CAAC,EAAC,CAACyB,CAAC,CAAC,CAAC;IAACkC,CAAC,GAAC9O,CAAC,CAAC,MAAI;MAAC2J,CAAC,CAAC2F,OAAO,CAACwE,oBAAoB,CAACrN,EAAE,CAACsD,OAAO,CAAC;IAAA,CAAC,CAAC;IAACmF,CAAC,GAAClP,CAAC,CAAC2R,CAAC,IAAE;MAACA,CAAC,CAACY,cAAc,CAAC,CAAC,EAAC5I,CAAC,CAAC2F,OAAO,CAACwE,oBAAoB,CAACrN,EAAE,CAACsD,OAAO,CAAC;IAAA,CAAC,CAAC;IAACqF,CAAC,GAAC/J,EAAE,CAAC2E,CAAC,GAAC0C,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,EAAApP,aAAA;MAAE,iBAAiB,EAACsR,CAAC;MAAC0F,IAAI,EAAC,SAAS;MAAC,sBAAsB,EAACzK,CAAC,CAACiE,IAAI,KAAGjH,CAAC,CAACkH,KAAK,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAACf,EAAE,EAAChF,CAAC;MAAC2C,GAAG,EAACgC,CAAC;MAACrC,KAAK,EAAAhN,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAKoM,CAAC,CAACY,KAAK,GAAI/B,CAAC;QAAC,eAAe,EAACzI,EAAE,CAAC+M,CAAC,EAAC,CAAC,CAAC,CAAC,CAACrC,KAAK;QAAC,gBAAgB,EAAC1K,EAAE,CAACgN,CAAC,EAAC,CAAC,CAAC,CAAC,CAACtC;MAAK,EAAC;MAAC+M,OAAO,EAACtK,CAAC,KAAGxG,EAAE,CAACsD,OAAO,GAAC,KAAK,CAAC,GAAC+E,CAAC;MAAC0I,WAAW,EAACtI;IAAC,GAAIpN,EAAE,CAACqM,CAAC,CAAC,CAAC,CAAC;IAACqB,CAAC,GAACtB,CAAC,IAAEtB,CAAC,KAAGjG,CAAC,CAAC2J,MAAM;IAACb,CAAC,GAACnM,EAAE,CAACkM,CAAC,EAAC,CAACW,CAAC,GAACtG,CAAC,CAAC5B,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACkI,CAAC,CAACpI,OAAO,CAAC;IAAC4H,EAAE,GAACrM,EAAE,CAACkM,CAAC,EAAC3F,CAAC,CAACQ,KAAK,CAAC;IAAC0F,CAAC,GAAC/P,CAAC,CAAC2R,CAAC,IAAE9H,CAAC,CAAC0E,OAAO,CAACoB,EAAE,EAACgC,CAAC,CAAC,CAAC;IAAC3B,EAAE,GAACpR,CAAC,CAAC,MAAI;MAAC,IAAG,CAACiL,CAAC,CAAC5B,OAAO,EAAC,OAAO4B,CAAC;MAAC,IAAG4F,CAAC,KAAG,KAAK,CAAC,EAAC,MAAM,IAAI/H,KAAK,CAAC,mCAAmC,CAAC;MAAC,OAAO+H,CAAC,KAAG5F,CAAC,CAAC5B,OAAO,CAACF,OAAO,GAAAzK,aAAA,CAAAA,aAAA,KAAKuM,CAAC;QAAC5B,OAAO,EAAA3K,aAAA,CAAAA,aAAA,KAAKuM,CAAC,CAAC5B,OAAO;UAACF,OAAO,EAAC0H;QAAC;MAAC,KAAE5F,CAAC;IAAA,CAAC,EAAC,CAACA,CAAC,EAAC4F,CAAC,EAAC,CAACnC,CAAC,GAACzD,CAAC,CAAC5B,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACqF,CAAC,CAACvF,OAAO,CAAC,CAAC;EAAC8B,CAAC,CAAC5B,OAAO,IAAEwP,MAAM,CAACC,MAAM,CAAChO,CAAC,EAAC;IAACsB,QAAQ,EAAC9M,CAAC,CAACiM,aAAa,CAAC/C,EAAE,CAACgD,QAAQ,EAAC;MAACC,KAAK,EAAC2F;IAAE,CAAC,EAAC9R,CAAC,CAACiM,aAAa,CAACrC,EAAE,EAAC;MAACoD,IAAI,EAAC2D;IAAC,CAAC,EAACnF,CAAC,CAACsB,QAAQ,CAAC;EAAC,CAAC,CAAC;EAAC,IAAIiF,CAAC,GAAC1K,EAAE,CAAC,CAAC;IAAC2K,CAAC,GAACtR,CAAC,CAAC,MAAIiL,CAAC,CAACiE,IAAI,KAAGjH,CAAC,CAACkH,KAAK,GAAClE,CAAC,GAAAvM,aAAA,CAAAA,aAAA,KAAKuM,CAAC;MAACwE,UAAU,EAAC0B;IAAC,EAAC,EAAC,CAAClG,CAAC,EAACkG,CAAC,CAAC,CAAC;EAAC,OAAO7R,CAAC,CAACiM,aAAa,CAAC9D,EAAE,EAAC;IAAC2C,OAAO,EAACV,CAAC,GAACf,CAAC,CAAC2F,MAAM,IAAEgB,CAAC,GAAC,CAAC,CAAC;IAACyJ,aAAa,EAACvK;EAAC,CAAC,EAAClP,CAAC,CAACiM,aAAa,CAAC/C,EAAE,CAACgD,QAAQ,EAAC;IAACC,KAAK,EAAC6F;EAAC,CAAC,EAACD,CAAC,CAAC;IAACQ,QAAQ,EAACrB,CAAC;IAACsB,UAAU,EAAApT,aAAA,CAAAA,aAAA,KAAKoM,CAAC;MAACsB,QAAQ,EAAC9M,CAAC,CAACiM,aAAa,CAAC/G,EAAE,EAAC;QAACwU,MAAM,EAACpI;MAAC,CAAC,EAAC,OAAO9F,CAAC,CAACsB,QAAQ,IAAE,UAAU,GAAC,CAACyC,CAAC,GAAC/D,CAAC,CAACsB,QAAQ,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyC,CAAC,CAACxC,IAAI,CAACvB,CAAC,EAACmF,CAAC,CAAC,GAACnF,CAAC,CAACsB,QAAQ;IAAC,EAAC;IAACE,IAAI,EAAC2D,CAAC;IAAC8B,UAAU,EAACuF,EAAE;IAAC2B,QAAQ,EAAC1B,EAAE;IAAC2B,OAAO,EAAC5J,CAAC;IAACnC,IAAI,EAAC;EAAkB,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIgM,EAAE,GAAC,KAAK;AAAC,SAASC,EAAEA,CAACzQ,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIqG,CAAC,EAACc,CAAC,EAACC,CAAC;EAAC,IAAInH,CAAC,GAACH,EAAE,CAAC,iBAAiB,CAAC;IAACU,CAAC,GAACb,EAAE,CAAC,iBAAiB,CAAC;IAACe,CAAC,GAAChI,EAAE,CAAC,CAAC;IAAC;MAAC8M,EAAE,EAAC3E,CAAC,iCAAAV,MAAA,CAA+BO,CAAC,CAAE;MAACmC,KAAK,EAAC/B,CAAC;MAAC4D,QAAQ,EAACnD,CAAC,GAAC,CAAC6F,CAAC,GAAC,CAACD,CAAC,GAAC,CAACd,CAAC,GAACpG,CAAC,CAACQ,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC4F,CAAC,CAAC3B,QAAQ,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyC,CAAC,CAAC1D,IAAI,CAAC4C,CAAC,EAACvF,CAAC,CAAC,KAAG,IAAI,GAACsG,CAAC,GAAC,CAAC,CAAC;MAACqJ,KAAK,EAACxO,CAAC,GAAC;IAAS,CAAC,GAAClC,CAAC;IAAJmC,CAAC,GAAArM,wBAAA,CAAEkK,CAAC,EAAA5J,UAAA;IAAC,CAACgM,CAAC,CAAC,GAACzF,CAAC,CAAC8D,CAAC,EAAC6G,CAAC,IAAE,CAACA,CAAC,CAACI,YAAY,CAAC,CAAC;IAACpF,CAAC,GAACzI,EAAE,CAACuI,CAAC,CAAC;IAACK,CAAC,GAAC9F,CAAC,CAAC8D,CAAC,EAACxJ,CAAC,CAACqQ,CAAC,IAAE7G,CAAC,CAACiC,SAAS,CAACiO,QAAQ,CAACrJ,CAAC,EAACvG,CAAC,EAACD,CAAC,CAAC,EAAC,CAACC,CAAC,EAACD,CAAC,CAAC,CAAC,CAAC;IAACF,CAAC,GAACV,CAAC,CAAC4G,UAAU,CAAC/F,CAAC,CAAC;IAACC,CAAC,GAACzJ,EAAE,CAAC,IAAI,CAAC;IAACyN,CAAC,GAAC/L,EAAE,CAAC;MAAC0L,QAAQ,EAACnD,CAAC;MAACsB,KAAK,EAAC/B,CAAC;MAAC6P,MAAM,EAAC5P,CAAC;MAAC0P,KAAK,EAACxO;IAAC,CAAC,CAAC;IAAC+C,CAAC,GAAC9N,EAAE,CAACmJ,EAAE,CAAC;IAAC6E,CAAC,GAAChL,EAAE,CAAC8F,CAAC,EAACe,CAAC,EAACiE,CAAC,GAACA,CAAC,CAAC4L,cAAc,GAAC,IAAI,CAAC;IAACzL,CAAC,GAAC3M,CAAC,CAAC,MAAI;MAACgI,CAAC,CAACsH,OAAO,CAAC+C,WAAW,CAAC,CAAC,CAAC,CAAC,EAACrK,CAAC,CAACsH,OAAO,CAACzD,QAAQ,CAACvD,CAAC,CAAC;IAAA,CAAC,CAAC;EAAChI,EAAE,CAAC,MAAI0H,CAAC,CAACsH,OAAO,CAAC+I,cAAc,CAAChQ,CAAC,EAACkE,CAAC,CAAC,EAAC,CAACA,CAAC,EAAClE,CAAC,CAAC,CAAC;EAAC,IAAIuE,CAAC,GAAC1I,CAAC,CAAC8D,CAAC,EAACxJ,CAAC,CAACqQ,CAAC,IAAE7G,CAAC,CAACiC,SAAS,CAACqO,oBAAoB,CAACzJ,CAAC,EAACvG,CAAC,EAACD,CAAC,CAAC,EAAC,CAACC,CAAC,EAACD,CAAC,CAAC,CAAC,CAAC;EAAC/H,EAAE,CAAC,MAAI;IAAC,IAAGsM,CAAC,EAAC,OAAOlI,EAAE,CAAC,CAAC,CAAC8M,qBAAqB,CAAC,MAAI;MAAC,IAAI3C,CAAC,EAACC,CAAC;MAAC,CAACA,CAAC,GAAC,CAACD,CAAC,GAACtG,CAAC,CAACoF,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACkB,CAAC,CAAC0J,cAAc,KAAG,IAAI,IAAEzJ,CAAC,CAAC7D,IAAI,CAAC4D,CAAC,EAAC;QAAC2J,KAAK,EAAC;MAAS,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC5L,CAAC,EAACrE,CAAC,CAAC,CAAC;EAAC,IAAIsE,CAAC,GAAC7M,CAAC,CAAC6O,CAAC,IAAE;MAACA,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC6G,MAAM,KAAGvP,EAAE,CAACwP,IAAI,KAAG5M,CAAC,KAAG4D,CAAC,CAAC,CAAC,EAAC5H,EAAE,CAAC,CAAC,IAAEyM,qBAAqB,CAAC,MAAI3H,CAAC,CAAC,CAAC,CAAC,EAACpC,CAAC,CAACqG,IAAI,KAAGjH,CAAC,CAACoH,MAAM,IAAEjG,CAAC,CAACsH,OAAO,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACzC,CAAC,GAAC9M,CAAC,CAAC,MAAI;MAAC,IAAG+I,CAAC,EAAC,OAAOf,CAAC,CAACsH,OAAO,CAAC6B,UAAU,CAAC;QAACC,KAAK,EAAC5M,CAAC,CAAC6M;MAAO,CAAC,CAAC;MAAC,IAAIxC,CAAC,GAACpH,CAAC,CAAC6G,cAAc,CAAChG,CAAC,CAAC;MAACN,CAAC,CAACsH,OAAO,CAAC6B,UAAU,CAAC;QAACC,KAAK,EAAC5M,CAAC,CAACiU,QAAQ;QAACC,GAAG,EAAC7J;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC9B,CAAC,GAACnL,EAAE,CAAC,CAAC;IAACqL,CAAC,GAACjN,CAAC,CAAC6O,CAAC,IAAE9B,CAAC,CAAC4L,MAAM,CAAC9J,CAAC,CAAC,CAAC;IAACzB,CAAC,GAACpN,CAAC,CAAC6O,CAAC,IAAE;MAAC,IAAG,CAAC9B,CAAC,CAAC6L,QAAQ,CAAC/J,CAAC,CAAC,IAAE9F,CAAC,IAAEiB,CAAC,EAAC;MAAO,IAAI8E,CAAC,GAACrH,CAAC,CAAC6G,cAAc,CAAChG,CAAC,CAAC;MAACN,CAAC,CAACsH,OAAO,CAAC6B,UAAU,CAAC;QAACC,KAAK,EAAC5M,CAAC,CAACiU,QAAQ;QAACC,GAAG,EAAC5J;MAAC,CAAC,EAACrI,EAAE,CAACsD,OAAO,CAAC;IAAA,CAAC,CAAC;IAACsD,CAAC,GAACrN,CAAC,CAAC6O,CAAC,IAAE;MAAC9B,CAAC,CAAC6L,QAAQ,CAAC/J,CAAC,CAAC,KAAG9F,CAAC,IAAEiB,CAAC,KAAGvC,CAAC,CAAC2G,eAAe,CAACT,OAAO,CAACR,IAAI,IAAEnF,CAAC,CAACsH,OAAO,CAAC6B,UAAU,CAAC;QAACC,KAAK,EAAC5M,CAAC,CAAC6M;MAAO,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACzD,CAAC,GAAChP,CAAC,CAAC,OAAK;MAACmX,MAAM,EAAC/L,CAAC;MAACoH,KAAK,EAACpH,CAAC;MAAC6O,QAAQ,EAAC1Q,CAAC;MAAC+D,QAAQ,EAACnD;IAAC,CAAC,CAAC,EAAC,CAACiB,CAAC,EAAC7B,CAAC,EAACY,CAAC,CAAC,CAAC;IAACmF,CAAC,GAAC;MAAClB,EAAE,EAAC3E,CAAC;MAACsC,GAAG,EAAC+B,CAAC;MAAC4H,IAAI,EAAC,QAAQ;MAAC0B,QAAQ,EAACjN,CAAC,KAAG,CAAC,CAAC,GAAC,KAAK,CAAC,GAAC,CAAC,CAAC;MAAC,eAAe,EAACA,CAAC,KAAG,CAAC,CAAC,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAAC,eAAe,EAACZ,CAAC;MAAC+D,QAAQ,EAAC,KAAK,CAAC;MAACsL,WAAW,EAAC3K,CAAC;MAAC8H,OAAO,EAAC7H,CAAC;MAACgM,cAAc,EAAC7L,CAAC;MAAC8L,YAAY,EAAC9L,CAAC;MAAC+L,aAAa,EAAC5L,CAAC;MAAC6L,WAAW,EAAC7L,CAAC;MAAC8L,cAAc,EAAC7L,CAAC;MAAC8L,YAAY,EAAC9L;IAAC,CAAC;EAAC,OAAO9H,EAAE,CAAC,CAAC,CAAC;IAACkL,QAAQ,EAACvC,CAAC;IAACwC,UAAU,EAAChH,CAAC;IAACwB,IAAI,EAAC0C,CAAC;IAAC+C,UAAU,EAACoH,EAAE;IAAChM,IAAI,EAAC;EAAiB,CAAC,CAAC;AAAA;AAAC,IAAIqN,EAAE,GAACjU,EAAE,CAACwG,EAAE,CAAC;EAAC0N,EAAE,GAAClU,EAAE,CAAC2P,EAAE,CAAC;EAACwE,EAAE,GAACnU,EAAE,CAAC0L,EAAE,CAAC;EAAC0I,EAAE,GAAC1T,EAAE;EAAC2T,EAAE,GAACrU,EAAE,CAACmR,EAAE,CAAC;EAACmD,EAAE,GAACtU,EAAE,CAAC6S,EAAE,CAAC;EAAC0B,EAAE,GAACjC,MAAM,CAACC,MAAM,CAAC0B,EAAE,EAAC;IAACO,KAAK,EAACL,EAAE;IAACM,MAAM,EAACP,EAAE;IAACzT,KAAK,EAAC2T,EAAE;IAACM,OAAO,EAACL,EAAE;IAACM,MAAM,EAACL;EAAE,CAAC,CAAC;AAAC,SAAOC,EAAE,IAAIK,QAAQ,EAACV,EAAE,IAAIW,cAAc,EAACV,EAAE,IAAIW,aAAa,EAACV,EAAE,IAAIW,aAAa,EAACT,EAAE,IAAIU,cAAc,EAACX,EAAE,IAAIY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}