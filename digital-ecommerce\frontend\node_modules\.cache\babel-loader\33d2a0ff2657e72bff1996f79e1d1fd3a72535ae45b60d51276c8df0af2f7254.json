{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useState,useEffect}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const AdminContext=/*#__PURE__*/createContext();export const useAdmin=()=>{const context=useContext(AdminContext);if(!context){throw new Error('useAdmin must be used within an AdminProvider');}return context;};// Mock admin users for demonstration\nconst mockAdmins=[{id:'admin-1',username:'admin',password:'admin123',// In production, this would be hashed\nemail:'<EMAIL>',firstName:'Admin',lastName:'User',role:'super_admin',permissions:['all'],createdAt:'2024-01-01T00:00:00Z',lastLogin:null},{id:'admin-2',username:'manager',password:'manager123',email:'<EMAIL>',firstName:'Store',lastName:'Manager',role:'manager',permissions:['products','categories','inventory'],createdAt:'2024-01-01T00:00:00Z',lastLogin:null}];export const AdminProvider=_ref=>{let{children}=_ref;const[admin,setAdmin]=useState(null);const[isLoading,setIsLoading]=useState(true);const[isAuthenticated,setIsAuthenticated]=useState(false);// Check for existing admin session on mount\nuseEffect(()=>{const checkAdminAuthStatus=()=>{const token=localStorage.getItem('adminAuthToken');const adminData=localStorage.getItem('adminData');if(token&&adminData){try{const parsedAdmin=JSON.parse(adminData);setAdmin(parsedAdmin);setIsAuthenticated(true);}catch(error){console.error('Error parsing admin data:',error);localStorage.removeItem('adminAuthToken');localStorage.removeItem('adminData');}}setIsLoading(false);};checkAdminAuthStatus();},[]);const adminLogin=async function(username,password){let rememberMe=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;setIsLoading(true);try{console.log('Attempting admin login with:',username,password);// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,1000));// Find admin in mock data\nconst foundAdmin=mockAdmins.find(a=>(a.username===username||a.email===username)&&a.password===password);console.log('Found admin:',foundAdmin);if(!foundAdmin){throw new Error('Invalid username or password');}// Update last login\nconst updatedAdmin=_objectSpread(_objectSpread({},foundAdmin),{},{lastLogin:new Date().toISOString()});// Generate mock token\nconst token=\"admin_token_\".concat(Date.now());// Store auth data\nif(rememberMe){localStorage.setItem('adminAuthToken',token);localStorage.setItem('adminData',JSON.stringify(updatedAdmin));}else{sessionStorage.setItem('adminAuthToken',token);sessionStorage.setItem('adminData',JSON.stringify(updatedAdmin));}setAdmin(updatedAdmin);setIsAuthenticated(true);setIsLoading(false);return{success:true,admin:updatedAdmin};}catch(error){setIsLoading(false);return{success:false,error:error.message};}};const adminLogout=()=>{// Clear stored data\nlocalStorage.removeItem('adminAuthToken');localStorage.removeItem('adminData');sessionStorage.removeItem('adminAuthToken');sessionStorage.removeItem('adminData');setAdmin(null);setIsAuthenticated(false);};const hasPermission=permission=>{if(!admin)return false;if(admin.permissions.includes('all'))return true;return admin.permissions.includes(permission);};const isRole=role=>{return(admin===null||admin===void 0?void 0:admin.role)===role;};const isSuperAdmin=()=>{return(admin===null||admin===void 0?void 0:admin.role)==='super_admin';};const value={admin,isLoading,isAuthenticated,adminLogin,adminLogout,hasPermission,isRole,isSuperAdmin};return/*#__PURE__*/_jsx(AdminContext.Provider,{value:value,children:children});};export default AdminContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsx", "_jsx", "AdminContext", "useAdmin", "context", "Error", "mockAdmins", "id", "username", "password", "email", "firstName", "lastName", "role", "permissions", "createdAt", "lastLogin", "Admin<PERSON><PERSON><PERSON>", "_ref", "children", "admin", "set<PERSON>d<PERSON>", "isLoading", "setIsLoading", "isAuthenticated", "setIsAuthenticated", "checkAdminAuthStatus", "token", "localStorage", "getItem", "adminData", "parsedAdmin", "JSON", "parse", "error", "console", "removeItem", "adminLogin", "rememberMe", "arguments", "length", "undefined", "log", "Promise", "resolve", "setTimeout", "found<PERSON>dmin", "find", "a", "updatedAdmin", "_objectSpread", "Date", "toISOString", "concat", "now", "setItem", "stringify", "sessionStorage", "success", "message", "adminLogout", "hasPermission", "permission", "includes", "isRole", "isSuperAdmin", "value", "Provider"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/AdminContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AdminContext = createContext();\n\nexport const useAdmin = () => {\n  const context = useContext(AdminContext);\n  if (!context) {\n    throw new Error('useAdmin must be used within an AdminProvider');\n  }\n  return context;\n};\n\n// Mock admin users for demonstration\nconst mockAdmins = [\n  {\n    id: 'admin-1',\n    username: 'admin',\n    password: 'admin123', // In production, this would be hashed\n    email: '<EMAIL>',\n    firstName: 'Admin',\n    lastName: 'User',\n    role: 'super_admin',\n    permissions: ['all'],\n    createdAt: '2024-01-01T00:00:00Z',\n    lastLogin: null\n  },\n  {\n    id: 'admin-2',\n    username: 'manager',\n    password: 'manager123',\n    email: '<EMAIL>',\n    firstName: 'Store',\n    lastName: 'Manager',\n    role: 'manager',\n    permissions: ['products', 'categories', 'inventory'],\n    createdAt: '2024-01-01T00:00:00Z',\n    lastLogin: null\n  }\n];\n\nexport const AdminProvider = ({ children }) => {\n  const [admin, setAdmin] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check for existing admin session on mount\n  useEffect(() => {\n    const checkAdminAuthStatus = () => {\n      const token = localStorage.getItem('adminAuthToken');\n      const adminData = localStorage.getItem('adminData');\n      \n      if (token && adminData) {\n        try {\n          const parsedAdmin = JSON.parse(adminData);\n          setAdmin(parsedAdmin);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing admin data:', error);\n          localStorage.removeItem('adminAuthToken');\n          localStorage.removeItem('adminData');\n        }\n      }\n      setIsLoading(false);\n    };\n\n    checkAdminAuthStatus();\n  }, []);\n\n  const adminLogin = async (username, password, rememberMe = false) => {\n    setIsLoading(true);\n\n    try {\n      console.log('Attempting admin login with:', username, password);\n\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Find admin in mock data\n      const foundAdmin = mockAdmins.find(a =>\n        (a.username === username || a.email === username) && a.password === password\n      );\n\n      console.log('Found admin:', foundAdmin);\n\n      if (!foundAdmin) {\n        throw new Error('Invalid username or password');\n      }\n\n      // Update last login\n      const updatedAdmin = {\n        ...foundAdmin,\n        lastLogin: new Date().toISOString()\n      };\n\n      // Generate mock token\n      const token = `admin_token_${Date.now()}`;\n      \n      // Store auth data\n      if (rememberMe) {\n        localStorage.setItem('adminAuthToken', token);\n        localStorage.setItem('adminData', JSON.stringify(updatedAdmin));\n      } else {\n        sessionStorage.setItem('adminAuthToken', token);\n        sessionStorage.setItem('adminData', JSON.stringify(updatedAdmin));\n      }\n\n      setAdmin(updatedAdmin);\n      setIsAuthenticated(true);\n      setIsLoading(false);\n      \n      return { success: true, admin: updatedAdmin };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const adminLogout = () => {\n    // Clear stored data\n    localStorage.removeItem('adminAuthToken');\n    localStorage.removeItem('adminData');\n    sessionStorage.removeItem('adminAuthToken');\n    sessionStorage.removeItem('adminData');\n    \n    setAdmin(null);\n    setIsAuthenticated(false);\n  };\n\n  const hasPermission = (permission) => {\n    if (!admin) return false;\n    if (admin.permissions.includes('all')) return true;\n    return admin.permissions.includes(permission);\n  };\n\n  const isRole = (role) => {\n    return admin?.role === role;\n  };\n\n  const isSuperAdmin = () => {\n    return admin?.role === 'super_admin';\n  };\n\n  const value = {\n    admin,\n    isLoading,\n    isAuthenticated,\n    adminLogin,\n    adminLogout,\n    hasPermission,\n    isRole,\n    isSuperAdmin\n  };\n\n  return (\n    <AdminContext.Provider value={value}>\n      {children}\n    </AdminContext.Provider>\n  );\n};\n\nexport default AdminContext;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9E,KAAM,CAAAC,YAAY,cAAGN,aAAa,CAAC,CAAC,CAEpC,MAAO,MAAM,CAAAO,QAAQ,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,OAAO,CAAGP,UAAU,CAACK,YAAY,CAAC,CACxC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,+CAA+C,CAAC,CAClE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAE,UAAU,CAAG,CACjB,CACEC,EAAE,CAAE,SAAS,CACbC,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,UAAU,CAAE;AACtBC,KAAK,CAAE,mBAAmB,CAC1BC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,MAAM,CAChBC,IAAI,CAAE,aAAa,CACnBC,WAAW,CAAE,CAAC,KAAK,CAAC,CACpBC,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,IACb,CAAC,CACD,CACET,EAAE,CAAE,SAAS,CACbC,QAAQ,CAAE,SAAS,CACnBC,QAAQ,CAAE,YAAY,CACtBC,KAAK,CAAE,qBAAqB,CAC5BC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,SAAS,CACnBC,IAAI,CAAE,SAAS,CACfC,WAAW,CAAE,CAAC,UAAU,CAAE,YAAY,CAAE,WAAW,CAAC,CACpDC,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,IACb,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,aAAa,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACxC,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACwB,SAAS,CAAEC,YAAY,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC0B,eAAe,CAAEC,kBAAkB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2B,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CACpD,KAAM,CAAAC,SAAS,CAAGF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAEnD,GAAIF,KAAK,EAAIG,SAAS,CAAE,CACtB,GAAI,CACF,KAAM,CAAAC,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC,CACzCT,QAAQ,CAACU,WAAW,CAAC,CACrBN,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDN,YAAY,CAACQ,UAAU,CAAC,gBAAgB,CAAC,CACzCR,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC,CACtC,CACF,CACAb,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDG,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,UAAU,CAAG,cAAAA,CAAO7B,QAAQ,CAAEC,QAAQ,CAAyB,IAAvB,CAAA6B,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC9DhB,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACFY,OAAO,CAACO,GAAG,CAAC,8BAA8B,CAAElC,QAAQ,CAAEC,QAAQ,CAAC,CAE/D;AACA,KAAM,IAAI,CAAAkC,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,UAAU,CAAGxC,UAAU,CAACyC,IAAI,CAACC,CAAC,EAClC,CAACA,CAAC,CAACxC,QAAQ,GAAKA,QAAQ,EAAIwC,CAAC,CAACtC,KAAK,GAAKF,QAAQ,GAAKwC,CAAC,CAACvC,QAAQ,GAAKA,QACtE,CAAC,CAED0B,OAAO,CAACO,GAAG,CAAC,cAAc,CAAEI,UAAU,CAAC,CAEvC,GAAI,CAACA,UAAU,CAAE,CACf,KAAM,IAAI,CAAAzC,KAAK,CAAC,8BAA8B,CAAC,CACjD,CAEA;AACA,KAAM,CAAA4C,YAAY,CAAAC,aAAA,CAAAA,aAAA,IACbJ,UAAU,MACb9B,SAAS,CAAE,GAAI,CAAAmC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EACpC,CAED;AACA,KAAM,CAAAzB,KAAK,gBAAA0B,MAAA,CAAkBF,IAAI,CAACG,GAAG,CAAC,CAAC,CAAE,CAEzC;AACA,GAAIhB,UAAU,CAAE,CACdV,YAAY,CAAC2B,OAAO,CAAC,gBAAgB,CAAE5B,KAAK,CAAC,CAC7CC,YAAY,CAAC2B,OAAO,CAAC,WAAW,CAAEvB,IAAI,CAACwB,SAAS,CAACP,YAAY,CAAC,CAAC,CACjE,CAAC,IAAM,CACLQ,cAAc,CAACF,OAAO,CAAC,gBAAgB,CAAE5B,KAAK,CAAC,CAC/C8B,cAAc,CAACF,OAAO,CAAC,WAAW,CAAEvB,IAAI,CAACwB,SAAS,CAACP,YAAY,CAAC,CAAC,CACnE,CAEA5B,QAAQ,CAAC4B,YAAY,CAAC,CACtBxB,kBAAkB,CAAC,IAAI,CAAC,CACxBF,YAAY,CAAC,KAAK,CAAC,CAEnB,MAAO,CAAEmC,OAAO,CAAE,IAAI,CAAEtC,KAAK,CAAE6B,YAAa,CAAC,CAC/C,CAAE,MAAOf,KAAK,CAAE,CACdX,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAEmC,OAAO,CAAE,KAAK,CAAExB,KAAK,CAAEA,KAAK,CAACyB,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB;AACAhC,YAAY,CAACQ,UAAU,CAAC,gBAAgB,CAAC,CACzCR,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC,CACpCqB,cAAc,CAACrB,UAAU,CAAC,gBAAgB,CAAC,CAC3CqB,cAAc,CAACrB,UAAU,CAAC,WAAW,CAAC,CAEtCf,QAAQ,CAAC,IAAI,CAAC,CACdI,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAoC,aAAa,CAAIC,UAAU,EAAK,CACpC,GAAI,CAAC1C,KAAK,CAAE,MAAO,MAAK,CACxB,GAAIA,KAAK,CAACN,WAAW,CAACiD,QAAQ,CAAC,KAAK,CAAC,CAAE,MAAO,KAAI,CAClD,MAAO,CAAA3C,KAAK,CAACN,WAAW,CAACiD,QAAQ,CAACD,UAAU,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAE,MAAM,CAAInD,IAAI,EAAK,CACvB,MAAO,CAAAO,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEP,IAAI,IAAKA,IAAI,CAC7B,CAAC,CAED,KAAM,CAAAoD,YAAY,CAAGA,CAAA,GAAM,CACzB,MAAO,CAAA7C,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEP,IAAI,IAAK,aAAa,CACtC,CAAC,CAED,KAAM,CAAAqD,KAAK,CAAG,CACZ9C,KAAK,CACLE,SAAS,CACTE,eAAe,CACfa,UAAU,CACVuB,WAAW,CACXC,aAAa,CACbG,MAAM,CACNC,YACF,CAAC,CAED,mBACEhE,IAAA,CAACC,YAAY,CAACiE,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAA/C,QAAA,CACjCA,QAAQ,CACY,CAAC,CAE5B,CAAC,CAED,cAAe,CAAAjB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}