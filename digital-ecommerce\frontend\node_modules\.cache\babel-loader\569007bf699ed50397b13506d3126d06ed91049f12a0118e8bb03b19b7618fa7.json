{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\AdminLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, HomeIcon, ShoppingBagIcon, TagIcon, PhotoIcon, ChartBarIcon, CogIcon, ArrowRightOnRectangleIcon, UserIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport ThemeToggle from './ThemeToggle';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminLayout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    admin,\n    adminLogout,\n    hasPermission\n  } = useAdmin();\n  const {\n    getThemeClasses\n  } = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    adminLogout();\n    navigate('/admin/login');\n  };\n  const navigationItems = [{\n    name: 'Dashboard',\n    href: '/admin/dashboard',\n    icon: HomeIcon,\n    permission: null\n  }, {\n    name: 'Products',\n    href: '/admin/products',\n    icon: ShoppingBagIcon,\n    permission: 'products'\n  }, {\n    name: 'Categories',\n    href: '/admin/categories',\n    icon: TagIcon,\n    permission: 'categories'\n  }, {\n    name: 'Inventory',\n    href: '/admin/inventory',\n    icon: ClipboardDocumentListIcon,\n    permission: 'inventory'\n  }, {\n    name: 'Media',\n    href: '/admin/media',\n    icon: PhotoIcon,\n    permission: 'media'\n  }, {\n    name: 'Analytics',\n    href: '/admin/analytics',\n    icon: ChartBarIcon,\n    permission: 'analytics'\n  }, {\n    name: 'Settings',\n    href: '/admin/settings',\n    icon: CogIcon,\n    permission: 'settings'\n  }];\n  const filteredNavItems = navigationItems.filter(item => !item.permission || hasPermission(item.permission));\n  const isActive = path => location.pathname === path;\n  const Sidebar = ({\n    mobile = false\n  }) => {\n    var _admin$role;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex flex-col h-full ${getThemeClasses('bg-white border-r border-gray-200', 'bg-slate-900 border-r border-slate-700')}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-slate-700\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin/dashboard\",\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n              className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-xl font-bold ${getThemeClasses('text-gray-900', 'text-white')}`,\n            children: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 9\n        }, this), mobile && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSidebarOpen(false),\n          className: `p-2 rounded-md ${getThemeClasses('text-gray-400 hover:text-gray-600', 'text-gray-500 hover:text-gray-300')}`,\n          children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-4 py-6 space-y-2\",\n        children: filteredNavItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.href,\n          onClick: mobile ? () => setSidebarOpen(false) : undefined,\n          className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? getThemeClasses('bg-light-orange-100 text-light-orange-700', 'bg-light-orange-900/20 text-light-orange-400') : getThemeClasses('text-gray-700 hover:bg-gray-100', 'text-gray-300 hover:bg-slate-800')}`,\n          children: [/*#__PURE__*/_jsxDEV(item.icon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, item.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 border-t ${getThemeClasses('border-gray-200', 'border-slate-700')}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center space-x-3 p-3 rounded-lg ${getThemeClasses('bg-gray-50', 'bg-slate-800')}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-light-orange-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(UserIcon, {\n              className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm font-medium truncate ${getThemeClasses('text-gray-900', 'text-white')}`,\n              children: [admin === null || admin === void 0 ? void 0 : admin.firstName, \" \", admin === null || admin === void 0 ? void 0 : admin.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs truncate ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n              children: admin === null || admin === void 0 ? void 0 : (_admin$role = admin.role) === null || _admin$role === void 0 ? void 0 : _admin$role.replace('_', ' ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: `w-full mt-3 flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${getThemeClasses('text-red-700 hover:bg-red-50', 'text-red-400 hover:bg-red-900/20')}`,\n          children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Sign Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen ${getThemeClasses('bg-gray-50', 'bg-slate-800')}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n      children: /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: sidebarOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          className: \"fixed inset-0 z-40 lg:hidden\",\n          onClick: () => setSidebarOpen(false),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-black opacity-50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            x: -256\n          },\n          animate: {\n            x: 0\n          },\n          exit: {\n            x: -256\n          },\n          transition: {\n            type: \"spring\",\n            damping: 30,\n            stiffness: 300\n          },\n          className: \"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(Sidebar, {\n            mobile: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:pl-64\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: `sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 border-b ${getThemeClasses('bg-white border-gray-200', 'bg-slate-900 border-slate-700')}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSidebarOpen(true),\n            className: `lg:hidden p-2 rounded-md ${getThemeClasses('text-gray-400 hover:text-gray-600', 'text-gray-500 hover:text-gray-300')}`,\n            children: /*#__PURE__*/_jsxDEV(Bars3Icon, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: `text-2xl font-bold ${getThemeClasses('text-gray-900', 'text-white')}`,\n            children: \"Admin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(ThemeToggle, {\n            size: \"md\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"p-4 sm:p-6 lg:p-8\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLayout, \"EV1jw8iC+smqd40SqNx11456Kwo=\", false, function () {\n  return [useAdmin, useTheme, useLocation, useNavigate];\n});\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "HomeIcon", "ShoppingBagIcon", "TagIcon", "PhotoIcon", "ChartBarIcon", "CogIcon", "ArrowRightOnRectangleIcon", "UserIcon", "ClipboardDocumentListIcon", "useAdmin", "useTheme", "ThemeToggle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminLayout", "children", "_s", "sidebarOpen", "setSidebarOpen", "admin", "adminLogout", "hasPermission", "getThemeClasses", "location", "navigate", "handleLogout", "navigationItems", "name", "href", "icon", "permission", "filteredNavItems", "filter", "item", "isActive", "path", "pathname", "Sidebar", "mobile", "_admin$role", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "undefined", "firstName", "lastName", "role", "replace", "div", "initial", "opacity", "animate", "exit", "x", "transition", "type", "damping", "stiffness", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AdminLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  ShoppingBagIcon,\n  TagIcon,\n  PhotoIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  UserIcon,\n  ClipboardDocumentListIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport ThemeToggle from './ThemeToggle';\n\nconst AdminLayout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { admin, adminLogout, hasPermission } = useAdmin();\n  const { getThemeClasses } = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    adminLogout();\n    navigate('/admin/login');\n  };\n\n  const navigationItems = [\n    {\n      name: 'Dashboard',\n      href: '/admin/dashboard',\n      icon: HomeIcon,\n      permission: null\n    },\n    {\n      name: 'Products',\n      href: '/admin/products',\n      icon: ShoppingBagIcon,\n      permission: 'products'\n    },\n    {\n      name: 'Categories',\n      href: '/admin/categories',\n      icon: TagIcon,\n      permission: 'categories'\n    },\n    {\n      name: 'Inventory',\n      href: '/admin/inventory',\n      icon: ClipboardDocumentListIcon,\n      permission: 'inventory'\n    },\n    {\n      name: 'Media',\n      href: '/admin/media',\n      icon: PhotoIcon,\n      permission: 'media'\n    },\n    {\n      name: 'Analytics',\n      href: '/admin/analytics',\n      icon: ChartBarIcon,\n      permission: 'analytics'\n    },\n    {\n      name: 'Settings',\n      href: '/admin/settings',\n      icon: CogIcon,\n      permission: 'settings'\n    }\n  ];\n\n  const filteredNavItems = navigationItems.filter(item => \n    !item.permission || hasPermission(item.permission)\n  );\n\n  const isActive = (path) => location.pathname === path;\n\n  const Sidebar = ({ mobile = false }) => (\n    <div className={`flex flex-col h-full ${\n      getThemeClasses(\n        'bg-white border-r border-gray-200',\n        'bg-slate-900 border-r border-slate-700'\n      )\n    }`}>\n      {/* Logo */}\n      <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-slate-700\">\n        <Link to=\"/admin/dashboard\" className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center\">\n            <ShoppingBagIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <span className={`text-xl font-bold ${\n            getThemeClasses('text-gray-900', 'text-white')\n          }`}>\n            Admin\n          </span>\n        </Link>\n        {mobile && (\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className={`p-2 rounded-md ${\n              getThemeClasses(\n                'text-gray-400 hover:text-gray-600',\n                'text-gray-500 hover:text-gray-300'\n              )\n            }`}\n          >\n            <XMarkIcon className=\"w-6 h-6\" />\n          </button>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {filteredNavItems.map((item) => (\n          <Link\n            key={item.name}\n            to={item.href}\n            onClick={mobile ? () => setSidebarOpen(false) : undefined}\n            className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${\n              isActive(item.href)\n                ? getThemeClasses(\n                    'bg-light-orange-100 text-light-orange-700',\n                    'bg-light-orange-900/20 text-light-orange-400'\n                  )\n                : getThemeClasses(\n                    'text-gray-700 hover:bg-gray-100',\n                    'text-gray-300 hover:bg-slate-800'\n                  )\n            }`}\n          >\n            <item.icon className=\"w-5 h-5\" />\n            <span>{item.name}</span>\n          </Link>\n        ))}\n      </nav>\n\n      {/* User Info & Logout */}\n      <div className={`p-4 border-t ${\n        getThemeClasses('border-gray-200', 'border-slate-700')\n      }`}>\n        <div className={`flex items-center space-x-3 p-3 rounded-lg ${\n          getThemeClasses('bg-gray-50', 'bg-slate-800')\n        }`}>\n          <div className=\"w-8 h-8 bg-light-orange-500 rounded-full flex items-center justify-center\">\n            <UserIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className={`text-sm font-medium truncate ${\n              getThemeClasses('text-gray-900', 'text-white')\n            }`}>\n              {admin?.firstName} {admin?.lastName}\n            </p>\n            <p className={`text-xs truncate ${\n              getThemeClasses('text-gray-500', 'text-gray-400')\n            }`}>\n              {admin?.role?.replace('_', ' ')}\n            </p>\n          </div>\n        </div>\n        <button\n          onClick={handleLogout}\n          className={`w-full mt-3 flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${\n            getThemeClasses(\n              'text-red-700 hover:bg-red-50',\n              'text-red-400 hover:bg-red-900/20'\n            )\n          }`}\n        >\n          <ArrowRightOnRectangleIcon className=\"w-5 h-5\" />\n          <span>Sign Out</span>\n        </button>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={`min-h-screen ${\n      getThemeClasses(\n        'bg-gray-50',\n        'bg-slate-800'\n      )\n    }`}>\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile Sidebar */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-40 lg:hidden\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <div className=\"absolute inset-0 bg-black opacity-50\" />\n            </motion.div>\n            <motion.div\n              initial={{ x: -256 }}\n              animate={{ x: 0 }}\n              exit={{ x: -256 }}\n              transition={{ type: \"spring\", damping: 30, stiffness: 300 }}\n              className=\"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\"\n            >\n              <Sidebar mobile />\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Main Content */}\n      <div className=\"lg:pl-64\">\n        {/* Top Header */}\n        <header className={`sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 border-b ${\n          getThemeClasses(\n            'bg-white border-gray-200',\n            'bg-slate-900 border-slate-700'\n          )\n        }`}>\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className={`lg:hidden p-2 rounded-md ${\n                getThemeClasses(\n                  'text-gray-400 hover:text-gray-600',\n                  'text-gray-500 hover:text-gray-300'\n                )\n              }`}\n            >\n              <Bars3Icon className=\"w-6 h-6\" />\n            </button>\n            <h1 className={`text-2xl font-bold ${\n              getThemeClasses('text-gray-900', 'text-white')\n            }`}>\n              Admin Dashboard\n            </h1>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <ThemeToggle size=\"md\" />\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"p-4 sm:p-6 lg:p-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,OAAO,EACPC,SAAS,EACTC,YAAY,EACZC,OAAO,EACPC,yBAAyB,EACzBC,QAAQ,EACRC,yBAAyB,QACpB,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAE6B,KAAK;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGd,QAAQ,CAAC,CAAC;EACxD,MAAM;IAAEe;EAAgB,CAAC,GAAGd,QAAQ,CAAC,CAAC;EACtC,MAAMe,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAE9B,MAAMgC,YAAY,GAAGA,CAAA,KAAM;IACzBL,WAAW,CAAC,CAAC;IACbI,QAAQ,CAAC,cAAc,CAAC;EAC1B,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE/B,QAAQ;IACdgC,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE9B,eAAe;IACrB+B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE7B,OAAO;IACb8B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAEvB,yBAAyB;IAC/BwB,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE5B,SAAS;IACf6B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE3B,YAAY;IAClB4B,UAAU,EAAE;EACd,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE1B,OAAO;IACb2B,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGL,eAAe,CAACM,MAAM,CAACC,IAAI,IAClD,CAACA,IAAI,CAACH,UAAU,IAAIT,aAAa,CAACY,IAAI,CAACH,UAAU,CACnD,CAAC;EAED,MAAMI,QAAQ,GAAIC,IAAI,IAAKZ,QAAQ,CAACa,QAAQ,KAAKD,IAAI;EAErD,MAAME,OAAO,GAAGA,CAAC;IAAEC,MAAM,GAAG;EAAM,CAAC;IAAA,IAAAC,WAAA;IAAA,oBACjC5B,OAAA;MAAK6B,SAAS,EAAE,wBACdlB,eAAe,CACb,mCAAmC,EACnC,wCACF,CAAC,EACA;MAAAP,QAAA,gBAEDJ,OAAA;QAAK6B,SAAS,EAAC,4FAA4F;QAAAzB,QAAA,gBACzGJ,OAAA,CAACpB,IAAI;UAACkD,EAAE,EAAC,kBAAkB;UAACD,SAAS,EAAC,6BAA6B;UAAAzB,QAAA,gBACjEJ,OAAA;YAAK6B,SAAS,EAAC,gHAAgH;YAAAzB,QAAA,eAC7HJ,OAAA,CAACZ,eAAe;cAACyC,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNlC,OAAA;YAAM6B,SAAS,EAAE,qBACflB,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;YAAAP,QAAA,EAAC;UAEJ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACNP,MAAM,iBACL3B,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,KAAK,CAAE;UACrCsB,SAAS,EAAE,kBACTlB,eAAe,CACb,mCAAmC,EACnC,mCACF,CAAC,EACA;UAAAP,QAAA,eAEHJ,OAAA,CAACd,SAAS;YAAC2C,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,4BAA4B;QAAAzB,QAAA,EACxCgB,gBAAgB,CAACgB,GAAG,CAAEd,IAAI,iBACzBtB,OAAA,CAACpB,IAAI;UAEHkD,EAAE,EAAER,IAAI,CAACL,IAAK;UACdkB,OAAO,EAAER,MAAM,GAAG,MAAMpB,cAAc,CAAC,KAAK,CAAC,GAAG8B,SAAU;UAC1DR,SAAS,EAAE,uGACTN,QAAQ,CAACD,IAAI,CAACL,IAAI,CAAC,GACfN,eAAe,CACb,2CAA2C,EAC3C,8CACF,CAAC,GACDA,eAAe,CACb,iCAAiC,EACjC,kCACF,CAAC,EACJ;UAAAP,QAAA,gBAEHJ,OAAA,CAACsB,IAAI,CAACJ,IAAI;YAACW,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjClC,OAAA;YAAAI,QAAA,EAAOkB,IAAI,CAACN;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAhBnBZ,IAAI,CAACN,IAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAE,gBACdlB,eAAe,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACrD;QAAAP,QAAA,gBACDJ,OAAA;UAAK6B,SAAS,EAAE,8CACdlB,eAAe,CAAC,YAAY,EAAE,cAAc,CAAC,EAC5C;UAAAP,QAAA,gBACDJ,OAAA;YAAK6B,SAAS,EAAC,2EAA2E;YAAAzB,QAAA,eACxFJ,OAAA,CAACN,QAAQ;cAACmC,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNlC,OAAA;YAAK6B,SAAS,EAAC,gBAAgB;YAAAzB,QAAA,gBAC7BJ,OAAA;cAAG6B,SAAS,EAAE,gCACZlB,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;cAAAP,QAAA,GACAI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8B,SAAS,EAAC,GAAC,EAAC9B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+B,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACJlC,OAAA;cAAG6B,SAAS,EAAE,oBACZlB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;cAAAP,QAAA,EACAI,KAAK,aAALA,KAAK,wBAAAoB,WAAA,GAALpB,KAAK,CAAEgC,IAAI,cAAAZ,WAAA,uBAAXA,WAAA,CAAaa,OAAO,CAAC,GAAG,EAAE,GAAG;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlC,OAAA;UACEmC,OAAO,EAAErB,YAAa;UACtBe,SAAS,EAAE,mHACTlB,eAAe,CACb,8BAA8B,EAC9B,kCACF,CAAC,EACA;UAAAP,QAAA,gBAEHJ,OAAA,CAACP,yBAAyB;YAACoC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDlC,OAAA;YAAAI,QAAA,EAAM;UAAQ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,oBACElC,OAAA;IAAK6B,SAAS,EAAE,gBACdlB,eAAe,CACb,YAAY,EACZ,cACF,CAAC,EACA;IAAAP,QAAA,gBAEDJ,OAAA;MAAK6B,SAAS,EAAC,0DAA0D;MAAAzB,QAAA,eACvEJ,OAAA,CAAC0B,OAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNlC,OAAA,CAAChB,eAAe;MAAAoB,QAAA,EACbE,WAAW,iBACVN,OAAA,CAAAE,SAAA;QAAAE,QAAA,gBACEJ,OAAA,CAACjB,MAAM,CAAC2D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBE,IAAI,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACrBf,SAAS,EAAC,8BAA8B;UACxCM,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,KAAK,CAAE;UAAAH,QAAA,eAErCJ,OAAA;YAAK6B,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACblC,OAAA,CAACjB,MAAM,CAAC2D,GAAG;UACTC,OAAO,EAAE;YAAEI,CAAC,EAAE,CAAC;UAAI,CAAE;UACrBF,OAAO,EAAE;YAAEE,CAAC,EAAE;UAAE,CAAE;UAClBD,IAAI,EAAE;YAAEC,CAAC,EAAE,CAAC;UAAI,CAAE;UAClBC,UAAU,EAAE;YAAEC,IAAI,EAAE,QAAQ;YAAEC,OAAO,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC5DtB,SAAS,EAAC,4CAA4C;UAAAzB,QAAA,eAEtDJ,OAAA,CAAC0B,OAAO;YAACC,MAAM;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,eACb;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBlC,OAAA;MAAK6B,SAAS,EAAC,UAAU;MAAAzB,QAAA,gBAEvBJ,OAAA;QAAQ6B,SAAS,EAAE,0FACjBlB,eAAe,CACb,0BAA0B,EAC1B,+BACF,CAAC,EACA;QAAAP,QAAA,gBACDJ,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAzB,QAAA,gBAC1CJ,OAAA;YACEmC,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,IAAI,CAAE;YACpCsB,SAAS,EAAE,4BACTlB,eAAe,CACb,mCAAmC,EACnC,mCACF,CAAC,EACA;YAAAP,QAAA,eAEHJ,OAAA,CAACf,SAAS;cAAC4C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACTlC,OAAA;YAAI6B,SAAS,EAAE,sBACblB,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;YAAAP,QAAA,EAAC;UAEJ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlC,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAzB,QAAA,eAC1CJ,OAAA,CAACF,WAAW;YAACsD,IAAI,EAAC;UAAI;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGTlC,OAAA;QAAM6B,SAAS,EAAC,mBAAmB;QAAAzB,QAAA,EAChCA;MAAQ;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA/OIF,WAAW;EAAA,QAE+BP,QAAQ,EAC1BC,QAAQ,EACnBhB,WAAW,EACXC,WAAW;AAAA;AAAAuE,EAAA,GALxBlD,WAAW;AAiPjB,eAAeA,WAAW;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}