{"ast": null, "code": "import { useMemo as a } from \"react\";\nfunction e(t, u) {\n  return a(() => {\n    var n;\n    if (t.type) return t.type;\n    let r = (n = t.as) != null ? n : \"button\";\n    if (typeof r == \"string\" && r.toLowerCase() === \"button\" || (u == null ? void 0 : u.tagName) === \"BUTTON\" && !u.hasAttribute(\"type\")) return \"button\";\n  }, [t.type, t.as, u]);\n}\nexport { e as useResolveButtonType };", "map": {"version": 3, "names": ["useMemo", "a", "e", "t", "u", "n", "type", "r", "as", "toLowerCase", "tagName", "hasAttribute", "useResolveButtonType"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-resolve-button-type.js"], "sourcesContent": ["import{useMemo as a}from\"react\";function e(t,u){return a(()=>{var n;if(t.type)return t.type;let r=(n=t.as)!=null?n:\"button\";if(typeof r==\"string\"&&r.toLowerCase()===\"button\"||(u==null?void 0:u.tagName)===\"BUTTON\"&&!u.hasAttribute(\"type\"))return\"button\"},[t.type,t.as,u])}export{e as useResolveButtonType};\n"], "mappings": "AAAA,SAAOA,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOH,CAAC,CAAC,MAAI;IAAC,IAAII,CAAC;IAAC,IAAGF,CAAC,CAACG,IAAI,EAAC,OAAOH,CAAC,CAACG,IAAI;IAAC,IAAIC,CAAC,GAAC,CAACF,CAAC,GAACF,CAAC,CAACK,EAAE,KAAG,IAAI,GAACH,CAAC,GAAC,QAAQ;IAAC,IAAG,OAAOE,CAAC,IAAE,QAAQ,IAAEA,CAAC,CAACE,WAAW,CAAC,CAAC,KAAG,QAAQ,IAAE,CAACL,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACM,OAAO,MAAI,QAAQ,IAAE,CAACN,CAAC,CAACO,YAAY,CAAC,MAAM,CAAC,EAAC,OAAM,QAAQ;EAAA,CAAC,EAAC,CAACR,CAAC,CAACG,IAAI,EAACH,CAAC,CAACK,EAAE,EAACJ,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIU,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}