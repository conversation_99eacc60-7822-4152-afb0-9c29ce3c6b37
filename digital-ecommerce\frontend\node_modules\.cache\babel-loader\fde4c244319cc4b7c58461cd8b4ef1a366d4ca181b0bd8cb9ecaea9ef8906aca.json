{"ast": null, "code": "function u(l) {\n  throw new Error(\"Unexpected object: \" + l);\n}\nvar c = (i => (i[i.First = 0] = \"First\", i[i.Previous = 1] = \"Previous\", i[i.Next = 2] = \"Next\", i[i.Last = 3] = \"Last\", i[i.Specific = 4] = \"Specific\", i[i.Nothing = 5] = \"Nothing\", i))(c || {});\nfunction f(l, n) {\n  let t = n.resolveItems();\n  if (t.length <= 0) return null;\n  let r = n.resolveActiveIndex(),\n    s = r != null ? r : -1;\n  switch (l.focus) {\n    case 0:\n      {\n        for (let e = 0; e < t.length; ++e) if (!n.resolveDisabled(t[e], e, t)) return e;\n        return r;\n      }\n    case 1:\n      {\n        s === -1 && (s = t.length);\n        for (let e = s - 1; e >= 0; --e) if (!n.resolveDisabled(t[e], e, t)) return e;\n        return r;\n      }\n    case 2:\n      {\n        for (let e = s + 1; e < t.length; ++e) if (!n.resolveDisabled(t[e], e, t)) return e;\n        return r;\n      }\n    case 3:\n      {\n        for (let e = t.length - 1; e >= 0; --e) if (!n.resolveDisabled(t[e], e, t)) return e;\n        return r;\n      }\n    case 4:\n      {\n        for (let e = 0; e < t.length; ++e) if (n.resolveId(t[e], e, t) === l.id) return e;\n        return r;\n      }\n    case 5:\n      return null;\n    default:\n      u(l);\n  }\n}\nexport { c as Focus, f as calculateActiveIndex };", "map": {"version": 3, "names": ["u", "l", "Error", "c", "i", "First", "Previous", "Next", "Last", "Specific", "Nothing", "f", "n", "t", "resolveItems", "length", "r", "resolveActiveIndex", "s", "focus", "e", "resolveDisabled", "resolveId", "id", "Focus", "calculateActiveIndex"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/calculate-active-index.js"], "sourcesContent": ["function u(l){throw new Error(\"Unexpected object: \"+l)}var c=(i=>(i[i.First=0]=\"First\",i[i.Previous=1]=\"Previous\",i[i.Next=2]=\"Next\",i[i.Last=3]=\"Last\",i[i.Specific=4]=\"Specific\",i[i.Nothing=5]=\"Nothing\",i))(c||{});function f(l,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),s=r!=null?r:-1;switch(l.focus){case 0:{for(let e=0;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 1:{s===-1&&(s=t.length);for(let e=s-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 2:{for(let e=s+1;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 3:{for(let e=t.length-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 4:{for(let e=0;e<t.length;++e)if(n.resolveId(t[e],e,t)===l.id)return e;return r}case 5:return null;default:u(l)}}export{c as Focus,f as calculateActiveIndex};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAC;EAAC,MAAM,IAAIC,KAAK,CAAC,qBAAqB,GAACD,CAAC,CAAC;AAAA;AAAC,IAAIE,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACD,CAAC,CAACA,CAAC,CAACE,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACF,CAAC,CAACA,CAAC,CAACG,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACH,CAAC,CAACA,CAAC,CAACI,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACJ,CAAC,CAACA,CAAC,CAACK,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACL,CAAC,CAACA,CAAC,CAACM,OAAO,GAAC,CAAC,CAAC,GAAC,SAAS,EAACN,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASQ,CAACA,CAACV,CAAC,EAACW,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,YAAY,CAAC,CAAC;EAAC,IAAGD,CAAC,CAACE,MAAM,IAAE,CAAC,EAAC,OAAO,IAAI;EAAC,IAAIC,CAAC,GAACJ,CAAC,CAACK,kBAAkB,CAAC,CAAC;IAACC,CAAC,GAACF,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,CAAC;EAAC,QAAOf,CAAC,CAACkB,KAAK;IAAE,KAAK,CAAC;MAAC;QAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACP,CAAC,CAACE,MAAM,EAAC,EAAEK,CAAC,EAAC,IAAG,CAACR,CAAC,CAACS,eAAe,CAACR,CAAC,CAACO,CAAC,CAAC,EAACA,CAAC,EAACP,CAAC,CAAC,EAAC,OAAOO,CAAC;QAAC,OAAOJ,CAAC;MAAA;IAAC,KAAK,CAAC;MAAC;QAACE,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAACL,CAAC,CAACE,MAAM,CAAC;QAAC,KAAI,IAAIK,CAAC,GAACF,CAAC,GAAC,CAAC,EAACE,CAAC,IAAE,CAAC,EAAC,EAAEA,CAAC,EAAC,IAAG,CAACR,CAAC,CAACS,eAAe,CAACR,CAAC,CAACO,CAAC,CAAC,EAACA,CAAC,EAACP,CAAC,CAAC,EAAC,OAAOO,CAAC;QAAC,OAAOJ,CAAC;MAAA;IAAC,KAAK,CAAC;MAAC;QAAC,KAAI,IAAII,CAAC,GAACF,CAAC,GAAC,CAAC,EAACE,CAAC,GAACP,CAAC,CAACE,MAAM,EAAC,EAAEK,CAAC,EAAC,IAAG,CAACR,CAAC,CAACS,eAAe,CAACR,CAAC,CAACO,CAAC,CAAC,EAACA,CAAC,EAACP,CAAC,CAAC,EAAC,OAAOO,CAAC;QAAC,OAAOJ,CAAC;MAAA;IAAC,KAAK,CAAC;MAAC;QAAC,KAAI,IAAII,CAAC,GAACP,CAAC,CAACE,MAAM,GAAC,CAAC,EAACK,CAAC,IAAE,CAAC,EAAC,EAAEA,CAAC,EAAC,IAAG,CAACR,CAAC,CAACS,eAAe,CAACR,CAAC,CAACO,CAAC,CAAC,EAACA,CAAC,EAACP,CAAC,CAAC,EAAC,OAAOO,CAAC;QAAC,OAAOJ,CAAC;MAAA;IAAC,KAAK,CAAC;MAAC;QAAC,KAAI,IAAII,CAAC,GAAC,CAAC,EAACA,CAAC,GAACP,CAAC,CAACE,MAAM,EAAC,EAAEK,CAAC,EAAC,IAAGR,CAAC,CAACU,SAAS,CAACT,CAAC,CAACO,CAAC,CAAC,EAACA,CAAC,EAACP,CAAC,CAAC,KAAGZ,CAAC,CAACsB,EAAE,EAAC,OAAOH,CAAC;QAAC,OAAOJ,CAAC;MAAA;IAAC,KAAK,CAAC;MAAC,OAAO,IAAI;IAAC;MAAQhB,CAAC,CAACC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOE,CAAC,IAAIqB,KAAK,EAACb,CAAC,IAAIc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}