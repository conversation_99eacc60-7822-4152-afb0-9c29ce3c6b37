import React, { useState } from 'react';
import { HeartIcon, ShoppingCartIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

const mockWishlistItems = [
  {
    id: 1,
    name: 'Premium Wireless Earbuds',
    price: 159.99,
    originalPrice: 199.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.8,
    reviews: 124,
    inStock: true,
    discount: 20
  },
  {
    id: 2,
    name: 'Smart Fitness Tracker',
    price: 89.99,
    originalPrice: 129.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.5,
    reviews: 89,
    inStock: true,
    discount: 31
  },
  {
    id: 3,
    name: 'Portable Power Bank',
    price: 49.99,
    originalPrice: 69.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.3,
    reviews: 67,
    inStock: false,
    discount: 29
  },
  {
    id: 4,
    name: 'Wireless Charging Pad',
    price: 29.99,
    originalPrice: 39.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.6,
    reviews: 156,
    inStock: true,
    discount: 25
  }
];

const Wishlist = () => {
  const [wishlistItems, setWishlistItems] = useState(mockWishlistItems);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

  const removeFromWishlist = (id) => {
    setWishlistItems(items => items.filter(item => item.id !== id));
  };

  const addToCart = (item) => {
    // Mock add to cart functionality
    console.log('Added to cart:', item.name);
    // You could show a toast notification here
  };

  const renderStars = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    return (
      <div className="flex items-center">
        {[...Array(fullStars)].map((_, i) => (
          <HeartIconSolid key={i} className="w-4 h-4 text-light-orange-400" />
        ))}
        {hasHalfStar && <HeartIconSolid className="w-4 h-4 text-light-orange-200" />}
        {[...Array(5 - Math.ceil(rating))].map((_, i) => (
          <HeartIcon key={i + fullStars} className="w-4 h-4 text-light-orange-200" />
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-white flex items-center">
            <HeartIconSolid className="w-6 h-6 mr-2" />
            My Wishlist ({wishlistItems.length})
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white bg-opacity-20 text-white'
                  : 'text-white hover:bg-white hover:bg-opacity-10'
              }`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list'
                  ? 'bg-white bg-opacity-20 text-white'
                  : 'text-white hover:bg-white hover:bg-opacity-10'
              }`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Wishlist Content */}
      <div className="p-6">
        {wishlistItems.length === 0 ? (
          <div className="text-center py-12">
            <HeartIcon className="w-16 h-16 text-light-orange-300 mx-auto mb-4" />
            <p className="text-light-orange-600 text-lg">Your wishlist is empty</p>
            <p className="text-light-orange-500 text-sm mt-2">Save items you love for later!</p>
          </div>
        ) : (
          <div className={`${
            viewMode === 'grid'
              ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }`}>
            {wishlistItems.map((item) => (
              <div
                key={item.id}
                className={`${
                  viewMode === 'grid'
                    ? 'bg-light-orange-50 rounded-xl border border-light-orange-200 overflow-hidden hover:shadow-lg transition-shadow'
                    : 'flex bg-light-orange-50 rounded-xl border border-light-orange-200 hover:shadow-lg transition-shadow'
                }`}
              >
                {/* Product Image */}
                <div className={`relative ${
                  viewMode === 'grid' ? 'w-full h-48' : 'w-32 h-32 flex-none'
                }`}>
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                  {item.discount > 0 && (
                    <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                      -{item.discount}%
                    </div>
                  )}
                  {!item.inStock && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <span className="text-white font-semibold">Out of Stock</span>
                    </div>
                  )}
                </div>

                {/* Product Details */}
                <div className={`p-4 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>
                  <div>
                    <h3 className="font-semibold text-light-orange-800 mb-2 line-clamp-2">
                      {item.name}
                    </h3>

                    <div className="flex items-center space-x-2 mb-2">
                      {renderStars(item.rating)}
                      <span className="text-sm text-light-orange-600">({item.reviews})</span>
                    </div>

                    <div className="flex items-center space-x-2 mb-3">
                      <span className="text-xl font-bold text-light-orange-700">
                        ${item.price.toFixed(2)}
                      </span>
                      {item.originalPrice > item.price && (
                        <span className="text-sm text-light-orange-500 line-through">
                          ${item.originalPrice.toFixed(2)}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className={`flex ${viewMode === 'grid' ? 'flex-col space-y-2' : 'space-x-2'}`}>
                    <button
                      onClick={() => addToCart(item)}
                      disabled={!item.inStock}
                      className={`flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                        item.inStock
                          ? 'bg-light-orange-500 text-white hover:bg-light-orange-600'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <ShoppingCartIcon className="w-4 h-4" />
                      <span>{item.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                    </button>

                    <div className="flex space-x-2">
                      <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-light-orange-100 text-light-orange-700 rounded-lg hover:bg-light-orange-200 transition-colors">
                        <EyeIcon className="w-4 h-4" />
                        <span>View</span>
                      </button>
                      <button
                        onClick={() => removeFromWishlist(item.id)}
                        className="flex items-center justify-center px-3 py-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Wishlist;
