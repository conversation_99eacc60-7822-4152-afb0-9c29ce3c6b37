import React, { createContext, useContext, useState, useEffect } from 'react';

const AdminContext = createContext();

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

// Mock admin users for demonstration
const mockAdmins = [
  {
    id: 'admin-1',
    username: 'admin',
    password: 'admin123', // In production, this would be hashed
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'super_admin',
    permissions: ['all'],
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: null
  },
  {
    id: 'admin-2',
    username: 'manager',
    password: 'manager123',
    email: '<EMAIL>',
    firstName: 'Store',
    lastName: 'Manager',
    role: 'manager',
    permissions: ['products', 'categories', 'inventory'],
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: null
  }
];

export const AdminProvider = ({ children }) => {
  const [admin, setAdmin] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check for existing admin session on mount
  useEffect(() => {
    const checkAdminAuthStatus = () => {
      const token = localStorage.getItem('adminAuthToken');
      const adminData = localStorage.getItem('adminData');
      
      if (token && adminData) {
        try {
          const parsedAdmin = JSON.parse(adminData);
          setAdmin(parsedAdmin);
          setIsAuthenticated(true);
        } catch (error) {
          console.error('Error parsing admin data:', error);
          localStorage.removeItem('adminAuthToken');
          localStorage.removeItem('adminData');
        }
      }
      setIsLoading(false);
    };

    checkAdminAuthStatus();
  }, []);

  const adminLogin = async (username, password, rememberMe = false) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find admin in mock data
      const foundAdmin = mockAdmins.find(a => 
        (a.username === username || a.email === username) && a.password === password
      );
      
      if (!foundAdmin) {
        throw new Error('Invalid username or password');
      }

      // Update last login
      const updatedAdmin = {
        ...foundAdmin,
        lastLogin: new Date().toISOString()
      };

      // Generate mock token
      const token = `admin_token_${Date.now()}`;
      
      // Store auth data
      if (rememberMe) {
        localStorage.setItem('adminAuthToken', token);
        localStorage.setItem('adminData', JSON.stringify(updatedAdmin));
      } else {
        sessionStorage.setItem('adminAuthToken', token);
        sessionStorage.setItem('adminData', JSON.stringify(updatedAdmin));
      }

      setAdmin(updatedAdmin);
      setIsAuthenticated(true);
      setIsLoading(false);
      
      return { success: true, admin: updatedAdmin };
    } catch (error) {
      setIsLoading(false);
      return { success: false, error: error.message };
    }
  };

  const adminLogout = () => {
    // Clear stored data
    localStorage.removeItem('adminAuthToken');
    localStorage.removeItem('adminData');
    sessionStorage.removeItem('adminAuthToken');
    sessionStorage.removeItem('adminData');
    
    setAdmin(null);
    setIsAuthenticated(false);
  };

  const hasPermission = (permission) => {
    if (!admin) return false;
    if (admin.permissions.includes('all')) return true;
    return admin.permissions.includes(permission);
  };

  const isRole = (role) => {
    return admin?.role === role;
  };

  const isSuperAdmin = () => {
    return admin?.role === 'super_admin';
  };

  const value = {
    admin,
    isLoading,
    isAuthenticated,
    adminLogin,
    adminLogout,
    hasPermission,
    isRole,
    isSuperAdmin
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
};

export default AdminContext;
