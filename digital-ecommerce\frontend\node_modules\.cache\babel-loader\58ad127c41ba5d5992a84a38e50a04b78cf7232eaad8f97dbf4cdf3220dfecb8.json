{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /**\n    * Calls all functions in the order they were chained with the same arguments.\n    */function $ff5963eb1fccf552$export$e08e3b67e392101e(...callbacks) {\n  return (...args) => {\n    for (let callback of callbacks) if (typeof callback === 'function') callback(...args);\n  };\n}\nexport { $ff5963eb1fccf552$export$e08e3b67e392101e as chain };", "map": {"version": 3, "names": ["$ff5963eb1fccf552$export$e08e3b67e392101e", "callbacks", "args", "callback"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\chain.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Calls all functions in the order they were chained with the same arguments.\n */\nexport function chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n"], "mappings": "AAAA;;;;;;;;;;GAAA,CAYA;;MAGO,SAASA,0CAAM,GAAGC,SAAgB;EACvC,OAAO,CAAC,GAAGC,IAAA;IACT,KAAK,IAAIC,QAAA,IAAYF,SAAA,EACnB,IAAI,OAAOE,QAAA,KAAa,YACtBA,QAAA,IAAYD,IAAA;EAGlB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}