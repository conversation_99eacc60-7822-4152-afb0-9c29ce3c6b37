{"ast": null, "code": "import { onDocumentReady as d } from './document-ready.js';\nimport * as u from './dom.js';\nimport { focusableSelector as i } from './focus-management.js';\nlet n = [];\nd(() => {\n  function e(t) {\n    if (!u.isHTMLorSVGElement(t.target) || t.target === document.body || n[0] === t.target) return;\n    let r = t.target;\n    r = r.closest(i), n.unshift(r != null ? r : t.target), n = n.filter(o => o != null && o.isConnected), n.splice(10);\n  }\n  window.addEventListener(\"click\", e, {\n    capture: !0\n  }), window.addEventListener(\"mousedown\", e, {\n    capture: !0\n  }), window.addEventListener(\"focus\", e, {\n    capture: !0\n  }), document.body.addEventListener(\"click\", e, {\n    capture: !0\n  }), document.body.addEventListener(\"mousedown\", e, {\n    capture: !0\n  }), document.body.addEventListener(\"focus\", e, {\n    capture: !0\n  });\n});\nexport { n as history };", "map": {"version": 3, "names": ["onDocumentReady", "d", "u", "focusableSelector", "i", "n", "e", "t", "isHTMLorSVGElement", "target", "document", "body", "r", "closest", "unshift", "filter", "o", "isConnected", "splice", "window", "addEventListener", "capture", "history"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/active-element-history.js"], "sourcesContent": ["import{onDocumentReady as d}from'./document-ready.js';import*as u from'./dom.js';import{focusableSelector as i}from'./focus-management.js';let n=[];d(()=>{function e(t){if(!u.isHTMLorSVGElement(t.target)||t.target===document.body||n[0]===t.target)return;let r=t.target;r=r.closest(i),n.unshift(r!=null?r:t.target),n=n.filter(o=>o!=null&&o.isConnected),n.splice(10)}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{n as history};\n"], "mappings": "AAAA,SAAOA,eAAe,IAAIC,CAAC,QAAK,qBAAqB;AAAC,OAAM,KAAIC,CAAC,MAAK,UAAU;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAAC,EAAE;AAACJ,CAAC,CAAC,MAAI;EAAC,SAASK,CAACA,CAACC,CAAC,EAAC;IAAC,IAAG,CAACL,CAAC,CAACM,kBAAkB,CAACD,CAAC,CAACE,MAAM,CAAC,IAAEF,CAAC,CAACE,MAAM,KAAGC,QAAQ,CAACC,IAAI,IAAEN,CAAC,CAAC,CAAC,CAAC,KAAGE,CAAC,CAACE,MAAM,EAAC;IAAO,IAAIG,CAAC,GAACL,CAAC,CAACE,MAAM;IAACG,CAAC,GAACA,CAAC,CAACC,OAAO,CAACT,CAAC,CAAC,EAACC,CAAC,CAACS,OAAO,CAACF,CAAC,IAAE,IAAI,GAACA,CAAC,GAACL,CAAC,CAACE,MAAM,CAAC,EAACJ,CAAC,GAACA,CAAC,CAACU,MAAM,CAACC,CAAC,IAAEA,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACC,WAAW,CAAC,EAACZ,CAAC,CAACa,MAAM,CAAC,EAAE,CAAC;EAAA;EAACC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAACd,CAAC,EAAC;IAACe,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACF,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAACd,CAAC,EAAC;IAACe,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACF,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAACd,CAAC,EAAC;IAACe,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACX,QAAQ,CAACC,IAAI,CAACS,gBAAgB,CAAC,OAAO,EAACd,CAAC,EAAC;IAACe,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACX,QAAQ,CAACC,IAAI,CAACS,gBAAgB,CAAC,WAAW,EAACd,CAAC,EAAC;IAACe,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACX,QAAQ,CAACC,IAAI,CAACS,gBAAgB,CAAC,OAAO,EAACd,CAAC,EAAC;IAACe,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC;AAAA,CAAC,CAAC;AAAC,SAAOhB,CAAC,IAAIiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}