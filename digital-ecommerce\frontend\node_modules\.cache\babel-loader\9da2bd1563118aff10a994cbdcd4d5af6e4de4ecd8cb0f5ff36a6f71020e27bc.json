{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */function $ab71dadb03a6fb2e$export$622cea445a1c5b7d(element, reverse) {\n  let orientation = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'horizontal';\n  let rect = element.getBoundingClientRect();\n  if (reverse) return orientation === 'horizontal' ? rect.right : rect.bottom;\n  return orientation === 'horizontal' ? rect.left : rect.top;\n}\nexport { $ab71dadb03a6fb2e$export$622cea445a1c5b7d as getOffset };", "map": {"version": 3, "names": ["$ab71dadb03a6fb2e$export$622cea445a1c5b7d", "element", "reverse", "orientation", "arguments", "length", "undefined", "rect", "getBoundingClientRect", "right", "bottom", "left", "top"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\getOffset.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Orientation} from '@react-types/shared';\n\nexport function getOffset(element: HTMLElement, reverse?: boolean, orientation: Orientation = 'horizontal'): number {\n  let rect = element.getBoundingClientRect();\n  if (reverse) {\n    return orientation === 'horizontal' ? rect.right : rect.bottom;\n  }\n  return orientation === 'horizontal' ? rect.left : rect.top;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAcO,SAASA,0CAAUC,OAAoB,EAAEC,OAAiB,EAAyC;EAAA,IAAvCC,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA2B,YAAY;EACxG,IAAIG,IAAA,GAAON,OAAA,CAAQO,qBAAqB;EACxC,IAAIN,OAAA,EACF,OAAOC,WAAA,KAAgB,eAAeI,IAAA,CAAKE,KAAK,GAAGF,IAAA,CAAKG,MAAM;EAEhE,OAAOP,WAAA,KAAgB,eAAeI,IAAA,CAAKI,IAAI,GAAGJ,IAAA,CAAKK,GAAG;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}