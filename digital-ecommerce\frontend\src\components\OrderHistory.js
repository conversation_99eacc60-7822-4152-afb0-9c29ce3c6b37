import React, { useState } from 'react';
import { ClockIcon, CheckCircleIcon, TruckIcon, XCircleIcon, EyeIcon } from '@heroicons/react/24/outline';

const mockOrders = [
  {
    id: 'ORD-001',
    date: '2024-01-15',
    status: 'delivered',
    total: 449.98,
    items: [
      { name: 'Wireless Headphones', quantity: 1, price: 199.99 },
      { name: 'Smart Watch', quantity: 1, price: 249.99 }
    ]
  },
  {
    id: 'ORD-002',
    date: '2024-01-10',
    status: 'shipped',
    total: 129.99,
    items: [
      { name: 'Bluetooth Speaker', quantity: 1, price: 129.99 }
    ]
  },
  {
    id: 'ORD-003',
    date: '2024-01-05',
    status: 'processing',
    total: 89.99,
    items: [
      { name: 'Phone Case', quantity: 2, price: 44.99 }
    ]
  },
  {
    id: 'ORD-004',
    date: '2023-12-28',
    status: 'cancelled',
    total: 299.99,
    items: [
      { name: 'Tablet Stand', quantity: 1, price: 299.99 }
    ]
  }
];

const OrderHistory = () => {
  const [selectedOrder, setSelectedOrder] = useState(null);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'shipped':
        return <TruckIcon className="w-5 h-5 text-blue-500" />;
      case 'processing':
        return <ClockIcon className="w-5 h-5 text-light-orange-500" />;
      case 'cancelled':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'shipped':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'processing':
        return 'bg-light-orange-100 text-light-orange-800 border-light-orange-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4">
        <h2 className="text-xl font-bold text-white flex items-center">
          <ClockIcon className="w-6 h-6 mr-2" />
          Order History
        </h2>
      </div>

      {/* Orders List */}
      <div className="p-6">
        {mockOrders.length === 0 ? (
          <div className="text-center py-12">
            <ClockIcon className="w-16 h-16 text-light-orange-300 mx-auto mb-4" />
            <p className="text-light-orange-600 text-lg">No orders found</p>
            <p className="text-light-orange-500 text-sm mt-2">Your order history will appear here</p>
          </div>
        ) : (
          <div className="space-y-4">
            {mockOrders.map((order) => (
              <div
                key={order.id}
                className="border border-light-orange-200 rounded-lg p-4 hover:shadow-md transition-shadow bg-light-orange-50"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="font-bold text-light-orange-800">#{order.id}</span>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(order.status)}`}
                    >
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(order.status)}
                        <span className="capitalize">{order.status}</span>
                      </div>
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-light-orange-600">{formatDate(order.date)}</p>
                    <p className="font-bold text-light-orange-800">${order.total.toFixed(2)}</p>
                  </div>
                </div>

                <div className="mb-3">
                  <p className="text-sm text-light-orange-700 mb-2">
                    {order.items.length} item{order.items.length > 1 ? 's' : ''}:
                  </p>
                  <div className="space-y-1">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-light-orange-800">
                          {item.quantity}x {item.name}
                        </span>
                        <span className="text-light-orange-600">${item.price.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex justify-between items-center pt-3 border-t border-light-orange-200">
                  <button
                    onClick={() => setSelectedOrder(order)}
                    className="flex items-center space-x-2 text-light-orange-600 hover:text-light-orange-700 transition-colors"
                  >
                    <EyeIcon className="w-4 h-4" />
                    <span className="text-sm">View Details</span>
                  </button>

                  {order.status === 'delivered' && (
                    <button className="bg-light-orange-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-light-orange-600 transition-colors">
                      Reorder
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white">Order Details - #{selectedOrder.id}</h2>
                <button
                  onClick={() => setSelectedOrder(null)}
                  className="text-white hover:text-light-orange-200 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-light-orange-800 mb-2">Order Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-light-orange-600">Order ID:</span>
                      <span className="text-light-orange-800 font-medium">#{selectedOrder.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-light-orange-600">Date:</span>
                      <span className="text-light-orange-800">{formatDate(selectedOrder.date)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-light-orange-600">Status:</span>
                      <span className={`px-2 py-1 rounded text-xs font-semibold ${getStatusColor(selectedOrder.status)}`}>
                        {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-light-orange-800 mb-2">Order Summary</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-light-orange-600">Subtotal:</span>
                      <span className="text-light-orange-800">${selectedOrder.total.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-light-orange-600">Shipping:</span>
                      <span className="text-light-orange-800">Free</span>
                    </div>
                    <div className="flex justify-between font-semibold border-t border-light-orange-200 pt-2">
                      <span className="text-light-orange-800">Total:</span>
                      <span className="text-light-orange-800">${selectedOrder.total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-light-orange-800 mb-3">Items Ordered</h3>
                <div className="space-y-3">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-light-orange-50 rounded-lg border border-light-orange-100">
                      <div>
                        <h4 className="font-medium text-light-orange-800">{item.name}</h4>
                        <p className="text-sm text-light-orange-600">Quantity: {item.quantity}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-light-orange-800">${item.price.toFixed(2)}</p>
                        <p className="text-sm text-light-orange-600">each</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderHistory;
