module.exports = {
  purge: ['./src/**/*.{js,jsx,ts,tsx}', './public/index.html'],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        'light-orange': {
          50: '#FFF8F1',
          100: '#FFF3E0',
          200: '#FFE0B2',
          300: '#FFCC80',
          400: '#FFB74D',
          500: '#FFA726',
          600: '#FB8C00',
          700: '#F57C00',
          800: '#EF6C00',
          900: '#E65100',
        },

      },
      // Custom gradients for dark theme
      backgroundImage: {
        'dark-gradient': 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
        'light-gradient': 'linear-gradient(135deg, #FFF8F1 0%, #ffffff 100%)',
      },
      // Enhanced transitions for theme switching
      transitionProperty: {
        'theme': 'background-color, border-color, color, fill, stroke, opacity, box-shadow, transform',
      },
      transitionDuration: {
        '400': '400ms',
      },
    },
  },
  variants: {
    extend: {
      // Enable dark mode variants for more utilities
      backgroundColor: ['dark'],
      textColor: ['dark'],
      borderColor: ['dark'],
      gradientColorStops: ['dark'],
      boxShadow: ['dark'],
      opacity: ['dark'],
    },
  },
  plugins: [],
}