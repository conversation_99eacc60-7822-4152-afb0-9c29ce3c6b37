import React from 'react';
import { motion } from 'framer-motion';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = ({ className = '', showLabel = false, size = 'md' }) => {
  const { theme, toggleTheme, isLoading } = useTheme();

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  if (isLoading) {
    return (
      <div className={`${sizeClasses[size]} bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ${className}`} />
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <motion.button
        onClick={toggleTheme}
        className={`
          ${sizeClasses[size]} 
          relative overflow-hidden rounded-full 
          bg-gradient-to-r from-light-orange-400 to-light-orange-500
          dark:from-light-orange-500 dark:to-light-orange-600
          hover:from-light-orange-500 hover:to-light-orange-600
          dark:hover:from-light-orange-400 dark:hover:to-light-orange-500
          shadow-lg hover:shadow-xl
          transition-all duration-300 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:ring-offset-2
          dark:focus:ring-light-orange-400 dark:focus:ring-offset-gray-800
          group
        `}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
        title={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
      >
        {/* Background gradient animation */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500"
          initial={false}
          animate={{
            opacity: theme === 'light' ? 1 : 0,
          }}
          transition={{ duration: 0.3 }}
        />
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"
          initial={false}
          animate={{
            opacity: theme === 'dark' ? 1 : 0,
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Icon container */}
        <div className="relative z-10 flex items-center justify-center h-full">
          <motion.div
            key={theme}
            initial={{ rotate: -180, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            exit={{ rotate: 180, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="flex items-center justify-center"
          >
            {theme === 'light' ? (
              <SunIcon 
                className={`${iconSizeClasses[size]} text-white drop-shadow-sm`} 
              />
            ) : (
              <MoonIcon 
                className={`${iconSizeClasses[size]} text-white drop-shadow-sm`} 
              />
            )}
          </motion.div>
        </div>

        {/* Ripple effect */}
        <motion.div
          className="absolute inset-0 bg-white rounded-full"
          initial={{ scale: 0, opacity: 0.3 }}
          animate={{ scale: 0, opacity: 0 }}
          whileTap={{ scale: 1.5, opacity: 0.1 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      {showLabel && (
        <motion.span
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize"
        >
          {theme} mode
        </motion.span>
      )}
    </div>
  );
};

export default ThemeToggle;
