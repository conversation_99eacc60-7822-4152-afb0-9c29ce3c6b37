{"ast": null, "code": "import { useMemo as o, useReducer as h } from \"react\";\nimport { useIsoMorphicEffect as s } from './use-iso-morphic-effect.js';\nfunction f(e) {\n  if (e === null) return {\n    width: 0,\n    height: 0\n  };\n  let {\n    width: t,\n    height: r\n  } = e.getBoundingClientRect();\n  return {\n    width: t,\n    height: r\n  };\n}\nfunction d(e, t = !1) {\n  let [r, u] = h(() => ({}), {}),\n    i = o(() => f(e), [e, r]);\n  return s(() => {\n    if (!e) return;\n    let n = new ResizeObserver(u);\n    return n.observe(e), () => {\n      n.disconnect();\n    };\n  }, [e]), t ? {\n    width: `${i.width}px`,\n    height: `${i.height}px`\n  } : i;\n}\nexport { d as useElementSize };", "map": {"version": 3, "names": ["useMemo", "o", "useReducer", "h", "useIsoMorphicEffect", "s", "f", "e", "width", "height", "t", "r", "getBoundingClientRect", "d", "u", "i", "n", "ResizeObserver", "observe", "disconnect", "useElementSize"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-element-size.js"], "sourcesContent": ["import{useMemo as o,useReducer as h}from\"react\";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(e){if(e===null)return{width:0,height:0};let{width:t,height:r}=e.getBoundingClientRect();return{width:t,height:r}}function d(e,t=!1){let[r,u]=h(()=>({}),{}),i=o(()=>f(e),[e,r]);return s(()=>{if(!e)return;let n=new ResizeObserver(u);return n.observe(e),()=>{n.disconnect()}},[e]),t?{width:`${i.width}px`,height:`${i.height}px`}:i}export{d as useElementSize};\n"], "mappings": "AAAA,SAAOA,OAAO,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAGA,CAAC,KAAG,IAAI,EAAC,OAAM;IAACC,KAAK,EAAC,CAAC;IAACC,MAAM,EAAC;EAAC,CAAC;EAAC,IAAG;IAACD,KAAK,EAACE,CAAC;IAACD,MAAM,EAACE;EAAC,CAAC,GAACJ,CAAC,CAACK,qBAAqB,CAAC,CAAC;EAAC,OAAM;IAACJ,KAAK,EAACE,CAAC;IAACD,MAAM,EAACE;EAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAACN,CAAC,EAACG,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACG,CAAC,CAAC,GAACX,CAAC,CAAC,OAAK,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IAACY,CAAC,GAACd,CAAC,CAAC,MAAIK,CAAC,CAACC,CAAC,CAAC,EAAC,CAACA,CAAC,EAACI,CAAC,CAAC,CAAC;EAAC,OAAON,CAAC,CAAC,MAAI;IAAC,IAAG,CAACE,CAAC,EAAC;IAAO,IAAIS,CAAC,GAAC,IAAIC,cAAc,CAACH,CAAC,CAAC;IAAC,OAAOE,CAAC,CAACE,OAAO,CAACX,CAAC,CAAC,EAAC,MAAI;MAACS,CAAC,CAACG,UAAU,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC,EAAC,CAACZ,CAAC,CAAC,CAAC,EAACG,CAAC,GAAC;IAACF,KAAK,EAAC,GAAGO,CAAC,CAACP,KAAK,IAAI;IAACC,MAAM,EAAC,GAAGM,CAAC,CAACN,MAAM;EAAI,CAAC,GAACM,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIO,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}