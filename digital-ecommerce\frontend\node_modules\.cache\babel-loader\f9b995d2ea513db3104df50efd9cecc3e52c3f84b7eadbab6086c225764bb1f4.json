{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Link,useNavigate}from'react-router-dom';import{motion}from'framer-motion';import{EyeIcon,EyeSlashIcon,UserPlusIcon,CheckCircleIcon,XCircleIcon}from'@heroicons/react/24/outline';import{useUser}from'../contexts/UserContext';import Button from'../components/Button';import Input from'../components/Input';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RegisterPage=()=>{const[formData,setFormData]=useState({firstName:'',lastName:'',email:'',phone:'',password:'',confirmPassword:'',agreeToTerms:false,marketingEmails:false});const[showPassword,setShowPassword]=useState(false);const[showConfirmPassword,setShowConfirmPassword]=useState(false);const[errors,setErrors]=useState({});const[passwordStrength,setPasswordStrength]=useState(0);const{register,isLoading}=useUser();const navigate=useNavigate();const handleInputChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));// Clear error when user starts typing\nif(errors[name]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));}// Calculate password strength\nif(name==='password'){calculatePasswordStrength(value);}};const calculatePasswordStrength=password=>{let strength=0;if(password.length>=8)strength++;if(/[a-z]/.test(password))strength++;if(/[A-Z]/.test(password))strength++;if(/[0-9]/.test(password))strength++;if(/[^A-Za-z0-9]/.test(password))strength++;setPasswordStrength(strength);};const getPasswordStrengthText=()=>{switch(passwordStrength){case 0:case 1:return{text:'Very Weak',color:'text-red-500'};case 2:return{text:'Weak',color:'text-orange-500'};case 3:return{text:'Fair',color:'text-yellow-500'};case 4:return{text:'Good',color:'text-blue-500'};case 5:return{text:'Strong',color:'text-green-500'};default:return{text:'',color:''};}};const validateForm=()=>{const newErrors={};if(!formData.firstName.trim()){newErrors.firstName='First name is required';}if(!formData.lastName.trim()){newErrors.lastName='Last name is required';}if(!formData.email){newErrors.email='Email is required';}else if(!/\\S+@\\S+\\.\\S+/.test(formData.email)){newErrors.email='Please enter a valid email address';}if(!formData.password){newErrors.password='Password is required';}else if(formData.password.length<8){newErrors.password='Password must be at least 8 characters';}if(!formData.confirmPassword){newErrors.confirmPassword='Please confirm your password';}else if(formData.password!==formData.confirmPassword){newErrors.confirmPassword='Passwords do not match';}if(!formData.agreeToTerms){newErrors.agreeToTerms='You must agree to the terms and conditions';}setErrors(newErrors);return Object.keys(newErrors).length===0;};const handleSubmit=async e=>{e.preventDefault();if(!validateForm())return;const result=await register(formData);if(result.success){toast.success(result.message);navigate('/login',{state:{message:'Account created successfully! Please sign in.'}});}else{toast.error(result.error);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.6},className:\"max-w-md w-full space-y-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(motion.div,{initial:{scale:0},animate:{scale:1},transition:{delay:0.2,type:\"spring\",stiffness:200},className:\"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",children:/*#__PURE__*/_jsx(UserPlusIcon,{className:\"h-8 w-8 text-white\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"mt-6 text-3xl font-bold text-gray-900\",children:\"Create your account\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-600\",children:\"Join us and start your shopping journey\"})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:0.3},className:\"bg-white rounded-2xl shadow-xl p-8 space-y-6\",children:[/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsx(Input,{label:\"First Name\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,error:errors.firstName,placeholder:\"John\",required:true}),/*#__PURE__*/_jsx(Input,{label:\"Last Name\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,error:errors.lastName,placeholder:\"Doe\",required:true})]}),/*#__PURE__*/_jsx(Input,{label:\"Email Address\",type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,error:errors.email,placeholder:\"<EMAIL>\",required:true}),/*#__PURE__*/_jsx(Input,{label:\"Phone Number\",type:\"tel\",name:\"phone\",value:formData.phone,onChange:handleInputChange,error:errors.phone,placeholder:\"+****************\",helperText:\"Optional - for order updates\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(Input,{label:\"Password\",type:showPassword?'text':'password',name:\"password\",value:formData.password,onChange:handleInputChange,error:errors.password,placeholder:\"Create a strong password\",required:true}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowPassword(!showPassword),className:\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\",children:showPassword?/*#__PURE__*/_jsx(EyeSlashIcon,{className:\"h-5 w-5\"}):/*#__PURE__*/_jsx(EyeIcon,{className:\"h-5 w-5\"})}),formData.password&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-2 rounded-full transition-all duration-300 \".concat(passwordStrength<=2?'bg-red-500':passwordStrength===3?'bg-yellow-500':passwordStrength===4?'bg-blue-500':'bg-green-500'),style:{width:\"\".concat(passwordStrength/5*100,\"%\")}})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium \".concat(getPasswordStrengthText().color),children:getPasswordStrengthText().text})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(Input,{label:\"Confirm Password\",type:showConfirmPassword?'text':'password',name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleInputChange,error:errors.confirmPassword,placeholder:\"Confirm your password\",required:true}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowConfirmPassword(!showConfirmPassword),className:\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\",children:showConfirmPassword?/*#__PURE__*/_jsx(EyeSlashIcon,{className:\"h-5 w-5\"}):/*#__PURE__*/_jsx(EyeIcon,{className:\"h-5 w-5\"})}),formData.confirmPassword&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute right-10 top-9\",children:formData.password===formData.confirmPassword?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-5 w-5 text-green-500\"}):/*#__PURE__*/_jsx(XCircleIcon,{className:\"h-5 w-5 text-red-500\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-start\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"agreeToTerms\",checked:formData.agreeToTerms,onChange:handleInputChange,className:\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded mt-0.5\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"ml-2 text-sm text-gray-600\",children:[\"I agree to the\",' ',/*#__PURE__*/_jsx(Link,{to:\"/terms\",className:\"text-light-orange-600 hover:text-light-orange-500 font-medium\",children:\"Terms of Service\"}),' ',\"and\",' ',/*#__PURE__*/_jsx(Link,{to:\"/privacy\",className:\"text-light-orange-600 hover:text-light-orange-500 font-medium\",children:\"Privacy Policy\"})]})]}),errors.agreeToTerms&&/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-600\",children:errors.agreeToTerms}),/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"marketingEmails\",checked:formData.marketingEmails,onChange:handleInputChange,className:\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-sm text-gray-600\",children:\"I'd like to receive marketing emails about new products and offers\"})]})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",loading:isLoading,fullWidth:true,size:\"large\",children:\"Create Account\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[\"Already have an account?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"font-medium text-light-orange-600 hover:text-light-orange-500\",children:\"Sign in here\"})]})})]})]})]});};export default RegisterPage;", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "motion", "EyeIcon", "EyeSlashIcon", "UserPlusIcon", "CheckCircleIcon", "XCircleIcon", "useUser", "<PERSON><PERSON>", "Input", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "RegisterPage", "formData", "setFormData", "firstName", "lastName", "email", "phone", "password", "confirmPassword", "agreeToTerms", "marketingEmails", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "errors", "setErrors", "passwordStrength", "setPasswordStrength", "register", "isLoading", "navigate", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "_objectSpread", "calculatePasswordStrength", "strength", "length", "test", "getPasswordStrengthText", "text", "color", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "result", "success", "message", "state", "error", "className", "children", "position", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "stiffness", "onSubmit", "label", "onChange", "placeholder", "required", "helperText", "onClick", "concat", "style", "width", "to", "loading", "fullWidth", "size"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/RegisterPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { \n  EyeIcon, \n  EyeSlashIcon, \n  UserPlusIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst RegisterPage = () => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: '',\n    agreeToTerms: false,\n    marketingEmails: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  \n  const { register, isLoading } = useUser();\n  const navigate = useNavigate();\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n\n    // Calculate password strength\n    if (name === 'password') {\n      calculatePasswordStrength(value);\n    }\n  };\n\n  const calculatePasswordStrength = (password) => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    setPasswordStrength(strength);\n  };\n\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1: return { text: 'Very Weak', color: 'text-red-500' };\n      case 2: return { text: 'Weak', color: 'text-orange-500' };\n      case 3: return { text: 'Fair', color: 'text-yellow-500' };\n      case 4: return { text: 'Good', color: 'text-blue-500' };\n      case 5: return { text: 'Strong', color: 'text-green-500' };\n      default: return { text: '', color: '' };\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    \n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    \n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    \n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters';\n    }\n    \n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    \n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the terms and conditions';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n    \n    const result = await register(formData);\n    \n    if (result.success) {\n      toast.success(result.message);\n      navigate('/login', { \n        state: { message: 'Account created successfully! Please sign in.' }\n      });\n    } else {\n      toast.error(result.error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <Toaster position=\"top-right\" />\n      \n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"max-w-md w-full space-y-8\"\n      >\n        {/* Header */}\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n          >\n            <UserPlusIcon className=\"h-8 w-8 text-white\" />\n          </motion.div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Join us and start your shopping journey\n          </p>\n        </div>\n\n        {/* Registration Form */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n          className=\"bg-white rounded-2xl shadow-xl p-8 space-y-6\"\n        >\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Name Fields */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <Input\n                label=\"First Name\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                error={errors.firstName}\n                placeholder=\"John\"\n                required\n              />\n              <Input\n                label=\"Last Name\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                error={errors.lastName}\n                placeholder=\"Doe\"\n                required\n              />\n            </div>\n\n            {/* Email Field */}\n            <Input\n              label=\"Email Address\"\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              error={errors.email}\n              placeholder=\"<EMAIL>\"\n              required\n            />\n\n            {/* Phone Field */}\n            <Input\n              label=\"Phone Number\"\n              type=\"tel\"\n              name=\"phone\"\n              value={formData.phone}\n              onChange={handleInputChange}\n              error={errors.phone}\n              placeholder=\"+****************\"\n              helperText=\"Optional - for order updates\"\n            />\n\n            {/* Password Field */}\n            <div className=\"relative\">\n              <Input\n                label=\"Password\"\n                type={showPassword ? 'text' : 'password'}\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                error={errors.password}\n                placeholder=\"Create a strong password\"\n                required\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5\" />\n                )}\n              </button>\n              \n              {/* Password Strength Indicator */}\n              {formData.password && (\n                <div className=\"mt-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full transition-all duration-300 ${\n                          passwordStrength <= 2 ? 'bg-red-500' :\n                          passwordStrength === 3 ? 'bg-yellow-500' :\n                          passwordStrength === 4 ? 'bg-blue-500' : 'bg-green-500'\n                        }`}\n                        style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                      />\n                    </div>\n                    <span className={`text-xs font-medium ${getPasswordStrengthText().color}`}>\n                      {getPasswordStrengthText().text}\n                    </span>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Confirm Password Field */}\n            <div className=\"relative\">\n              <Input\n                label=\"Confirm Password\"\n                type={showConfirmPassword ? 'text' : 'password'}\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                error={errors.confirmPassword}\n                placeholder=\"Confirm your password\"\n                required\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                className=\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\"\n              >\n                {showConfirmPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5\" />\n                )}\n              </button>\n              \n              {/* Password Match Indicator */}\n              {formData.confirmPassword && (\n                <div className=\"absolute right-10 top-9\">\n                  {formData.password === formData.confirmPassword ? (\n                    <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />\n                  ) : (\n                    <XCircleIcon className=\"h-5 w-5 text-red-500\" />\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Checkboxes */}\n            <div className=\"space-y-4\">\n              <label className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  name=\"agreeToTerms\"\n                  checked={formData.agreeToTerms}\n                  onChange={handleInputChange}\n                  className=\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded mt-0.5\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600\">\n                  I agree to the{' '}\n                  <Link to=\"/terms\" className=\"text-light-orange-600 hover:text-light-orange-500 font-medium\">\n                    Terms of Service\n                  </Link>{' '}\n                  and{' '}\n                  <Link to=\"/privacy\" className=\"text-light-orange-600 hover:text-light-orange-500 font-medium\">\n                    Privacy Policy\n                  </Link>\n                </span>\n              </label>\n              {errors.agreeToTerms && (\n                <p className=\"text-sm text-red-600\">{errors.agreeToTerms}</p>\n              )}\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  name=\"marketingEmails\"\n                  checked={formData.marketingEmails}\n                  onChange={handleInputChange}\n                  className=\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600\">\n                  I'd like to receive marketing emails about new products and offers\n                </span>\n              </label>\n            </div>\n\n            {/* Submit Button */}\n            <Button\n              type=\"submit\"\n              loading={isLoading}\n              fullWidth\n              size=\"large\"\n            >\n              Create Account\n            </Button>\n          </form>\n\n          {/* Sign In Link */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              Already have an account?{' '}\n              <Link\n                to=\"/login\"\n                className=\"font-medium text-light-orange-600 hover:text-light-orange-500\"\n              >\n                Sign in here\n              </Link>\n            </p>\n          </div>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,OAAO,CACPC,YAAY,CACZC,YAAY,CACZC,eAAe,CACfC,WAAW,KACN,6BAA6B,CACpC,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,CACvCqB,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EAAE,CACnBC,YAAY,CAAE,KAAK,CACnBC,eAAe,CAAE,KACnB,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+B,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACiC,MAAM,CAAEC,SAAS,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxC,KAAM,CAACmC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpC,QAAQ,CAAC,CAAC,CAAC,CAE3D,KAAM,CAAEqC,QAAQ,CAAEC,SAAU,CAAC,CAAG7B,OAAO,CAAC,CAAC,CACzC,KAAM,CAAA8B,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAsC,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGJ,CAAC,CAACK,MAAM,CAC/C1B,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACL,IAAI,EAAGE,IAAI,GAAK,UAAU,CAAGC,OAAO,CAAGF,KAAK,EAC7C,CAAC,CAEH;AACA,GAAIV,MAAM,CAACS,IAAI,CAAC,CAAE,CAChBR,SAAS,CAACa,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAG,EAAE,EAAG,CAAC,CAC9C,CAEA;AACA,GAAIA,IAAI,GAAK,UAAU,CAAE,CACvBO,yBAAyB,CAACN,KAAK,CAAC,CAClC,CACF,CAAC,CAED,KAAM,CAAAM,yBAAyB,CAAIxB,QAAQ,EAAK,CAC9C,GAAI,CAAAyB,QAAQ,CAAG,CAAC,CAChB,GAAIzB,QAAQ,CAAC0B,MAAM,EAAI,CAAC,CAAED,QAAQ,EAAE,CACpC,GAAI,OAAO,CAACE,IAAI,CAAC3B,QAAQ,CAAC,CAAEyB,QAAQ,EAAE,CACtC,GAAI,OAAO,CAACE,IAAI,CAAC3B,QAAQ,CAAC,CAAEyB,QAAQ,EAAE,CACtC,GAAI,OAAO,CAACE,IAAI,CAAC3B,QAAQ,CAAC,CAAEyB,QAAQ,EAAE,CACtC,GAAI,cAAc,CAACE,IAAI,CAAC3B,QAAQ,CAAC,CAAEyB,QAAQ,EAAE,CAC7Cd,mBAAmB,CAACc,QAAQ,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAG,uBAAuB,CAAGA,CAAA,GAAM,CACpC,OAAQlB,gBAAgB,EACtB,IAAK,EAAC,CACN,IAAK,EAAC,CAAE,MAAO,CAAEmB,IAAI,CAAE,WAAW,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC3D,IAAK,EAAC,CAAE,MAAO,CAAED,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,iBAAkB,CAAC,CACzD,IAAK,EAAC,CAAE,MAAO,CAAED,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,iBAAkB,CAAC,CACzD,IAAK,EAAC,CAAE,MAAO,CAAED,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,eAAgB,CAAC,CACvD,IAAK,EAAC,CAAE,MAAO,CAAED,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,gBAAiB,CAAC,CAC1D,QAAS,MAAO,CAAED,IAAI,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAG,CAAC,CACzC,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,SAAS,CAAG,CAAC,CAAC,CAEpB,GAAI,CAACtC,QAAQ,CAACE,SAAS,CAACqC,IAAI,CAAC,CAAC,CAAE,CAC9BD,SAAS,CAACpC,SAAS,CAAG,wBAAwB,CAChD,CAEA,GAAI,CAACF,QAAQ,CAACG,QAAQ,CAACoC,IAAI,CAAC,CAAC,CAAE,CAC7BD,SAAS,CAACnC,QAAQ,CAAG,uBAAuB,CAC9C,CAEA,GAAI,CAACH,QAAQ,CAACI,KAAK,CAAE,CACnBkC,SAAS,CAAClC,KAAK,CAAG,mBAAmB,CACvC,CAAC,IAAM,IAAI,CAAC,cAAc,CAAC6B,IAAI,CAACjC,QAAQ,CAACI,KAAK,CAAC,CAAE,CAC/CkC,SAAS,CAAClC,KAAK,CAAG,oCAAoC,CACxD,CAEA,GAAI,CAACJ,QAAQ,CAACM,QAAQ,CAAE,CACtBgC,SAAS,CAAChC,QAAQ,CAAG,sBAAsB,CAC7C,CAAC,IAAM,IAAIN,QAAQ,CAACM,QAAQ,CAAC0B,MAAM,CAAG,CAAC,CAAE,CACvCM,SAAS,CAAChC,QAAQ,CAAG,wCAAwC,CAC/D,CAEA,GAAI,CAACN,QAAQ,CAACO,eAAe,CAAE,CAC7B+B,SAAS,CAAC/B,eAAe,CAAG,8BAA8B,CAC5D,CAAC,IAAM,IAAIP,QAAQ,CAACM,QAAQ,GAAKN,QAAQ,CAACO,eAAe,CAAE,CACzD+B,SAAS,CAAC/B,eAAe,CAAG,wBAAwB,CACtD,CAEA,GAAI,CAACP,QAAQ,CAACQ,YAAY,CAAE,CAC1B8B,SAAS,CAAC9B,YAAY,CAAG,4CAA4C,CACvE,CAEAO,SAAS,CAACuB,SAAS,CAAC,CACpB,MAAO,CAAAE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACN,MAAM,GAAK,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAU,YAAY,CAAG,KAAO,CAAApB,CAAC,EAAK,CAChCA,CAAC,CAACqB,cAAc,CAAC,CAAC,CAElB,GAAI,CAACN,YAAY,CAAC,CAAC,CAAE,OAErB,KAAM,CAAAO,MAAM,CAAG,KAAM,CAAA1B,QAAQ,CAAClB,QAAQ,CAAC,CAEvC,GAAI4C,MAAM,CAACC,OAAO,CAAE,CAClBpD,KAAK,CAACoD,OAAO,CAACD,MAAM,CAACE,OAAO,CAAC,CAC7B1B,QAAQ,CAAC,QAAQ,CAAE,CACjB2B,KAAK,CAAE,CAAED,OAAO,CAAE,+CAAgD,CACpE,CAAC,CAAC,CACJ,CAAC,IAAM,CACLrD,KAAK,CAACuD,KAAK,CAACJ,MAAM,CAACI,KAAK,CAAC,CAC3B,CACF,CAAC,CAED,mBACElD,KAAA,QAAKmD,SAAS,CAAC,0HAA0H,CAAAC,QAAA,eACvItD,IAAA,CAACF,OAAO,EAACyD,QAAQ,CAAC,WAAW,CAAE,CAAC,cAEhCrD,KAAA,CAACd,MAAM,CAACoE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BT,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eAGrCpD,KAAA,QAAKmD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BtD,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACTC,OAAO,CAAE,CAAEM,KAAK,CAAE,CAAE,CAAE,CACtBH,OAAO,CAAE,CAAEG,KAAK,CAAE,CAAE,CAAE,CACtBF,UAAU,CAAE,CAAEG,KAAK,CAAE,GAAG,CAAEnC,IAAI,CAAE,QAAQ,CAAEoC,SAAS,CAAE,GAAI,CAAE,CAC3DZ,SAAS,CAAC,sIAAsI,CAAAC,QAAA,cAEhJtD,IAAA,CAACT,YAAY,EAAC8D,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACrC,CAAC,cACbrD,IAAA,OAAIqD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,qBAEtD,CAAI,CAAC,cACLtD,IAAA,MAAGqD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yCAE1C,CAAG,CAAC,EACD,CAAC,cAGNpD,KAAA,CAACd,MAAM,CAACoE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,UAAU,CAAE,CAAEG,KAAK,CAAE,GAAI,CAAE,CAC3BX,SAAS,CAAC,8CAA8C,CAAAC,QAAA,eAExDpD,KAAA,SAAMgE,QAAQ,CAAEpB,YAAa,CAACO,SAAS,CAAC,WAAW,CAAAC,QAAA,eAEjDpD,KAAA,QAAKmD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCtD,IAAA,CAACJ,KAAK,EACJuE,KAAK,CAAC,YAAY,CAClBxC,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACE,SAAU,CAC1B8D,QAAQ,CAAE3C,iBAAkB,CAC5B2B,KAAK,CAAElC,MAAM,CAACZ,SAAU,CACxB+D,WAAW,CAAC,MAAM,CAClBC,QAAQ,MACT,CAAC,cACFtE,IAAA,CAACJ,KAAK,EACJuE,KAAK,CAAC,WAAW,CACjBxC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAExB,QAAQ,CAACG,QAAS,CACzB6D,QAAQ,CAAE3C,iBAAkB,CAC5B2B,KAAK,CAAElC,MAAM,CAACX,QAAS,CACvB8D,WAAW,CAAC,KAAK,CACjBC,QAAQ,MACT,CAAC,EACC,CAAC,cAGNtE,IAAA,CAACJ,KAAK,EACJuE,KAAK,CAAC,eAAe,CACrBtC,IAAI,CAAC,OAAO,CACZF,IAAI,CAAC,OAAO,CACZC,KAAK,CAAExB,QAAQ,CAACI,KAAM,CACtB4D,QAAQ,CAAE3C,iBAAkB,CAC5B2B,KAAK,CAAElC,MAAM,CAACV,KAAM,CACpB6D,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,cAGFtE,IAAA,CAACJ,KAAK,EACJuE,KAAK,CAAC,cAAc,CACpBtC,IAAI,CAAC,KAAK,CACVF,IAAI,CAAC,OAAO,CACZC,KAAK,CAAExB,QAAQ,CAACK,KAAM,CACtB2D,QAAQ,CAAE3C,iBAAkB,CAC5B2B,KAAK,CAAElC,MAAM,CAACT,KAAM,CACpB4D,WAAW,CAAC,mBAAmB,CAC/BE,UAAU,CAAC,8BAA8B,CAC1C,CAAC,cAGFrE,KAAA,QAAKmD,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBtD,IAAA,CAACJ,KAAK,EACJuE,KAAK,CAAC,UAAU,CAChBtC,IAAI,CAAEf,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCa,IAAI,CAAC,UAAU,CACfC,KAAK,CAAExB,QAAQ,CAACM,QAAS,CACzB0D,QAAQ,CAAE3C,iBAAkB,CAC5B2B,KAAK,CAAElC,MAAM,CAACR,QAAS,CACvB2D,WAAW,CAAC,0BAA0B,CACtCC,QAAQ,MACT,CAAC,cACFtE,IAAA,WACE6B,IAAI,CAAC,QAAQ,CACb2C,OAAO,CAAEA,CAAA,GAAMzD,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9CuC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,CAEnExC,YAAY,cACXd,IAAA,CAACV,YAAY,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,cAEpCrD,IAAA,CAACX,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAC/B,CACK,CAAC,CAGRjD,QAAQ,CAACM,QAAQ,eAChBV,IAAA,QAAKqD,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBpD,KAAA,QAAKmD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CtD,IAAA,QAAKqD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDtD,IAAA,QACEqD,SAAS,iDAAAoB,MAAA,CACPrD,gBAAgB,EAAI,CAAC,CAAG,YAAY,CACpCA,gBAAgB,GAAK,CAAC,CAAG,eAAe,CACxCA,gBAAgB,GAAK,CAAC,CAAG,aAAa,CAAG,cAAc,CACtD,CACHsD,KAAK,CAAE,CAAEC,KAAK,IAAAF,MAAA,CAAMrD,gBAAgB,CAAG,CAAC,CAAI,GAAG,KAAI,CAAE,CACtD,CAAC,CACC,CAAC,cACNpB,IAAA,SAAMqD,SAAS,wBAAAoB,MAAA,CAAyBnC,uBAAuB,CAAC,CAAC,CAACE,KAAK,CAAG,CAAAc,QAAA,CACvEhB,uBAAuB,CAAC,CAAC,CAACC,IAAI,CAC3B,CAAC,EACJ,CAAC,CACH,CACN,EACE,CAAC,cAGNrC,KAAA,QAAKmD,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBtD,IAAA,CAACJ,KAAK,EACJuE,KAAK,CAAC,kBAAkB,CACxBtC,IAAI,CAAEb,mBAAmB,CAAG,MAAM,CAAG,UAAW,CAChDW,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAExB,QAAQ,CAACO,eAAgB,CAChCyD,QAAQ,CAAE3C,iBAAkB,CAC5B2B,KAAK,CAAElC,MAAM,CAACP,eAAgB,CAC9B0D,WAAW,CAAC,uBAAuB,CACnCC,QAAQ,MACT,CAAC,cACFtE,IAAA,WACE6B,IAAI,CAAC,QAAQ,CACb2C,OAAO,CAAEA,CAAA,GAAMvD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE,CAC5DqC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,CAEnEtC,mBAAmB,cAClBhB,IAAA,CAACV,YAAY,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,cAEpCrD,IAAA,CAACX,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAC/B,CACK,CAAC,CAGRjD,QAAQ,CAACO,eAAe,eACvBX,IAAA,QAAKqD,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClD,QAAQ,CAACM,QAAQ,GAAKN,QAAQ,CAACO,eAAe,cAC7CX,IAAA,CAACR,eAAe,EAAC6D,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAEtDrD,IAAA,CAACP,WAAW,EAAC4D,SAAS,CAAC,sBAAsB,CAAE,CAChD,CACE,CACN,EACE,CAAC,cAGNnD,KAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpD,KAAA,UAAOmD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACjCtD,IAAA,UACE6B,IAAI,CAAC,UAAU,CACfF,IAAI,CAAC,cAAc,CACnBG,OAAO,CAAE1B,QAAQ,CAACQ,YAAa,CAC/BwD,QAAQ,CAAE3C,iBAAkB,CAC5B4B,SAAS,CAAC,0FAA0F,CACrG,CAAC,cACFnD,KAAA,SAAMmD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,gBAC7B,CAAC,GAAG,cAClBtD,IAAA,CAACd,IAAI,EAAC0F,EAAE,CAAC,QAAQ,CAACvB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAAC,kBAE5F,CAAM,CAAC,CAAC,GAAG,CAAC,KACT,CAAC,GAAG,cACPtD,IAAA,CAACd,IAAI,EAAC0F,EAAE,CAAC,UAAU,CAACvB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAAC,gBAE9F,CAAM,CAAC,EACH,CAAC,EACF,CAAC,CACPpC,MAAM,CAACN,YAAY,eAClBZ,IAAA,MAAGqD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEpC,MAAM,CAACN,YAAY,CAAI,CAC7D,cAEDV,KAAA,UAAOmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClCtD,IAAA,UACE6B,IAAI,CAAC,UAAU,CACfF,IAAI,CAAC,iBAAiB,CACtBG,OAAO,CAAE1B,QAAQ,CAACS,eAAgB,CAClCuD,QAAQ,CAAE3C,iBAAkB,CAC5B4B,SAAS,CAAC,mFAAmF,CAC9F,CAAC,cACFrD,IAAA,SAAMqD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,oEAE7C,CAAM,CAAC,EACF,CAAC,EACL,CAAC,cAGNtD,IAAA,CAACL,MAAM,EACLkC,IAAI,CAAC,QAAQ,CACbgD,OAAO,CAAEtD,SAAU,CACnBuD,SAAS,MACTC,IAAI,CAAC,OAAO,CAAAzB,QAAA,CACb,gBAED,CAAQ,CAAC,EACL,CAAC,cAGPtD,IAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BpD,KAAA,MAAGmD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,0BACX,CAAC,GAAG,cAC5BtD,IAAA,CAACd,IAAI,EACH0F,EAAE,CAAC,QAAQ,CACXvB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1E,cAED,CAAM,CAAC,EACN,CAAC,CACD,CAAC,EACI,CAAC,EACH,CAAC,EACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}