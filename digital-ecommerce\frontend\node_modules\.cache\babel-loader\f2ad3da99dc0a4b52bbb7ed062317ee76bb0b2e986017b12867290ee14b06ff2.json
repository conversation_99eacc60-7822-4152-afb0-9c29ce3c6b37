{"ast": null, "code": "function t() {\n  for (var _len = arguments.length, r = new Array(_len), _key = 0; _key < _len; _key++) {\n    r[_key] = arguments[_key];\n  }\n  return Array.from(new Set(r.flatMap(n => typeof n == \"string\" ? n.split(\" \") : []))).filter(Boolean).join(\" \");\n}\nexport { t as classNames };", "map": {"version": 3, "names": ["t", "_len", "arguments", "length", "r", "Array", "_key", "from", "Set", "flatMap", "n", "split", "filter", "Boolean", "join", "classNames"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/class-names.js"], "sourcesContent": ["function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAM;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAFC,CAAC,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAADF,CAAC,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAAE,OAAOD,KAAK,CAACE,IAAI,CAAC,IAAIC,GAAG,CAACJ,CAAC,CAACK,OAAO,CAACC,CAAC,IAAE,OAAOA,CAAC,IAAE,QAAQ,GAACA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,GAAC,EAAE,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAAA;AAAC,SAAOd,CAAC,IAAIe,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}