{"ast": null, "code": "import { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useValueEffect as $1dbecbe27a04f9af$export$14d238f342723f25 } from \"./useValueEffect.mjs\";\nimport { useState as $eKkEp$useState, useRef as $eKkEp$useRef, useEffect as $eKkEp$useEffect, useCallback as $eKkEp$useCallback } from \"react\";\nimport { useSSRSafeId as $eKkEp$useSSRSafeId } from \"@react-aria/ssr\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet $bdb11010cef70236$var$canUseDOM = Boolean(typeof window !== 'undefined' && window.document && window.document.createElement);\nlet $bdb11010cef70236$export$d41a04c74483c6ef = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet $bdb11010cef70236$var$registry;\nif (typeof FinalizationRegistry !== 'undefined') $bdb11010cef70236$var$registry = new FinalizationRegistry(heldValue => {\n  $bdb11010cef70236$export$d41a04c74483c6ef.delete(heldValue);\n});\nfunction $bdb11010cef70236$export$f680877a34711e37(defaultId) {\n  let [value, setValue] = (0, $eKkEp$useState)(defaultId);\n  let nextId = (0, $eKkEp$useRef)(null);\n  let res = (0, $eKkEp$useSSRSafeId)(value);\n  let cleanupRef = (0, $eKkEp$useRef)(null);\n  if ($bdb11010cef70236$var$registry) $bdb11010cef70236$var$registry.register(cleanupRef, res);\n  if ($bdb11010cef70236$var$canUseDOM) {\n    const cacheIdRef = $bdb11010cef70236$export$d41a04c74483c6ef.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) cacheIdRef.push(nextId);else $bdb11010cef70236$export$d41a04c74483c6ef.set(res, [nextId]);\n  }\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if ($bdb11010cef70236$var$registry) $bdb11010cef70236$var$registry.unregister(cleanupRef);\n      $bdb11010cef70236$export$d41a04c74483c6ef.delete(r);\n    };\n  }, [res]);\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  (0, $eKkEp$useEffect)(() => {\n    let newId = nextId.current;\n    if (newId) setValue(newId);\n    return () => {\n      if (newId) nextId.current = null;\n    };\n  });\n  return res;\n}\nfunction $bdb11010cef70236$export$cd8c9cb68f842629(idA, idB) {\n  if (idA === idB) return idA;\n  let setIdsA = $bdb11010cef70236$export$d41a04c74483c6ef.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => ref.current = idB);\n    return idB;\n  }\n  let setIdsB = $bdb11010cef70236$export$d41a04c74483c6ef.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach(ref => ref.current = idA);\n    return idA;\n  }\n  return idB;\n}\nfunction $bdb11010cef70236$export$b4cc09c592e8fdb8(depArray = []) {\n  let id = $bdb11010cef70236$export$f680877a34711e37();\n  let [resolvedId, setResolvedId] = (0, $1dbecbe27a04f9af$export$14d238f342723f25)(id);\n  let updateId = (0, $eKkEp$useCallback)(() => {\n    setResolvedId(function* () {\n      yield id;\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(updateId, [id, updateId, ...depArray]);\n  return resolvedId;\n}\nexport { $bdb11010cef70236$export$d41a04c74483c6ef as idsUpdaterMap, $bdb11010cef70236$export$f680877a34711e37 as useId, $bdb11010cef70236$export$cd8c9cb68f842629 as mergeIds, $bdb11010cef70236$export$b4cc09c592e8fdb8 as useSlotId };", "map": {"version": 3, "names": ["$bdb11010cef70236$var$canUseDOM", "Boolean", "window", "document", "createElement", "$bdb11010cef70236$export$d41a04c74483c6ef", "Map", "$bdb11010cef70236$var$registry", "FinalizationRegistry", "heldValue", "delete", "$bdb11010cef70236$export$f680877a34711e37", "defaultId", "value", "setValue", "$eKkEp$useState", "nextId", "$eKkEp$useRef", "res", "$eKkEp$useSSRSafeId", "cleanupRef", "register", "cacheIdRef", "get", "includes", "push", "set", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "r", "unregister", "$eKkEp$useEffect", "newId", "current", "$bdb11010cef70236$export$cd8c9cb68f842629", "idA", "idB", "setIdsA", "for<PERSON>ach", "ref", "setIdsB", "$bdb11010cef70236$export$b4cc09c592e8fdb8", "dep<PERSON><PERSON><PERSON>", "id", "resolvedId", "setResolvedId", "$1dbecbe27a04f9af$export$14d238f342723f25", "updateId", "$eKkEp$useCallback", "getElementById", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useId.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useSSRSafeId} from '@react-aria/ssr';\nimport {useValueEffect} from './';\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nexport let idsUpdaterMap: Map<string, { current: string | null }[]> = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet registry;\nif (typeof FinalizationRegistry !== 'undefined') {\n  registry = new FinalizationRegistry<string>((heldValue) => {\n    idsUpdaterMap.delete(heldValue);\n  });\n}\n\n/**\n * If a default is not provided, generate an id.\n * @param defaultId - Default component id.\n */\nexport function useId(defaultId?: string): string {\n  let [value, setValue] = useState(defaultId);\n  let nextId = useRef(null);\n\n  let res = useSSRSafeId(value);\n  let cleanupRef = useRef(null);\n\n  if (registry) {\n    registry.register(cleanupRef, res);\n  }\n\n  if (canUseDOM) {\n    const cacheIdRef = idsUpdaterMap.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) {\n      cacheIdRef.push(nextId);\n    } else {\n      idsUpdaterMap.set(res, [nextId]);\n    }\n  }\n\n  useLayoutEffect(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if (registry) {\n        registry.unregister(cleanupRef);\n      }\n      idsUpdaterMap.delete(r);\n    };\n  }, [res]);\n\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  useEffect(() => {\n    let newId = nextId.current;\n    if (newId) { setValue(newId); }\n\n    return () => {\n      if (newId) { nextId.current = null; }\n    };\n  });\n\n  return res;\n}\n\n/**\n * Merges two ids.\n * Different ids will trigger a side-effect and re-render components hooked up with `useId`.\n */\nexport function mergeIds(idA: string, idB: string): string {\n  if (idA === idB) {\n    return idA;\n  }\n\n  let setIdsA = idsUpdaterMap.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => (ref.current = idB));\n    return idB;\n  }\n\n  let setIdsB = idsUpdaterMap.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach((ref) => (ref.current = idA));\n    return idA;\n  }\n\n  return idB;\n}\n\n/**\n * Used to generate an id, and after render, check if that id is rendered so we know\n * if we can use it in places such as labelledby.\n * @param depArray - When to recalculate if the id is in the DOM.\n */\nexport function useSlotId(depArray: ReadonlyArray<any> = []): string {\n  let id = useId();\n  let [resolvedId, setResolvedId] = useValueEffect(id);\n  let updateId = useCallback(() => {\n    setResolvedId(function *() {\n      yield id;\n\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n\n  useLayoutEffect(updateId, [id, updateId, ...depArray]);\n\n  return resolvedId;\n}\n"], "mappings": ";;;;;AAAA;;;;;;;;;;;;AAiBA;AACA,IAAIA,+BAAA,GAAYC,OAAA,CACd,OAAOC,MAAA,KAAW,eAClBA,MAAA,CAAOC,QAAQ,IACfD,MAAA,CAAOC,QAAQ,CAACC,aAAa;AAGxB,IAAIC,yCAAA,GAA2D,IAAIC,GAAA;AAC1E;AACA;AACA;AACA,IAAIC,8BAAA;AACJ,IAAI,OAAOC,oBAAA,KAAyB,aAClCD,8BAAA,GAAW,IAAIC,oBAAA,CAA8BC,SAAA;EAC3CJ,yCAAA,CAAcK,MAAM,CAACD,SAAA;AACvB;AAOK,SAASE,0CAAMC,SAAkB;EACtC,IAAI,CAACC,KAAA,EAAOC,QAAA,CAAS,GAAG,IAAAC,eAAO,EAAEH,SAAA;EACjC,IAAII,MAAA,GAAS,IAAAC,aAAK,EAAE;EAEpB,IAAIC,GAAA,GAAM,IAAAC,mBAAW,EAAEN,KAAA;EACvB,IAAIO,UAAA,GAAa,IAAAH,aAAK,EAAE;EAExB,IAAIV,8BAAA,EACFA,8BAAA,CAASc,QAAQ,CAACD,UAAA,EAAYF,GAAA;EAGhC,IAAIlB,+BAAA,EAAW;IACb,MAAMsB,UAAA,GAAajB,yCAAA,CAAckB,GAAG,CAACL,GAAA;IACrC,IAAII,UAAA,IAAc,CAACA,UAAA,CAAWE,QAAQ,CAACR,MAAA,GACrCM,UAAA,CAAWG,IAAI,CAACT,MAAA,OAEhBX,yCAAA,CAAcqB,GAAG,CAACR,GAAA,EAAK,CAACF,MAAA,CAAO;EAEnC;EAEA,IAAAW,yCAAc,EAAE;IACd,IAAIC,CAAA,GAAIV,GAAA;IACR,OAAO;MACL;MACA;MACA,IAAIX,8BAAA,EACFA,8BAAA,CAASsB,UAAU,CAACT,UAAA;MAEtBf,yCAAA,CAAcK,MAAM,CAACkB,CAAA;IACvB;EACF,GAAG,CAACV,GAAA,CAAI;EAER;EACA;EACA,IAAAY,gBAAQ,EAAE;IACR,IAAIC,KAAA,GAAQf,MAAA,CAAOgB,OAAO;IAC1B,IAAID,KAAA,EAASjB,QAAA,CAASiB,KAAA;IAEtB,OAAO;MACL,IAAIA,KAAA,EAASf,MAAA,CAAOgB,OAAO,GAAG;IAChC;EACF;EAEA,OAAOd,GAAA;AACT;AAMO,SAASe,0CAASC,GAAW,EAAEC,GAAW;EAC/C,IAAID,GAAA,KAAQC,GAAA,EACV,OAAOD,GAAA;EAGT,IAAIE,OAAA,GAAU/B,yCAAA,CAAckB,GAAG,CAACW,GAAA;EAChC,IAAIE,OAAA,EAAS;IACXA,OAAA,CAAQC,OAAO,CAACC,GAAA,IAAQA,GAAA,CAAIN,OAAO,GAAGG,GAAA;IACtC,OAAOA,GAAA;EACT;EAEA,IAAII,OAAA,GAAUlC,yCAAA,CAAckB,GAAG,CAACY,GAAA;EAChC,IAAII,OAAA,EAAS;IACXA,OAAA,CAAQF,OAAO,CAAEC,GAAA,IAASA,GAAA,CAAIN,OAAO,GAAGE,GAAA;IACxC,OAAOA,GAAA;EACT;EAEA,OAAOC,GAAA;AACT;AAOO,SAASK,0CAAUC,QAAA,GAA+B,EAAE;EACzD,IAAIC,EAAA,GAAK/B,yCAAA;EACT,IAAI,CAACgC,UAAA,EAAYC,aAAA,CAAc,GAAG,IAAAC,yCAAa,EAAEH,EAAA;EACjD,IAAII,QAAA,GAAW,IAAAC,kBAAU,EAAE;IACzBH,aAAA,CAAc;MACZ,MAAMF,EAAA;MAEN,MAAMvC,QAAA,CAAS6C,cAAc,CAACN,EAAA,IAAMA,EAAA,GAAKO,SAAA;IAC3C;EACF,GAAG,CAACP,EAAA,EAAIE,aAAA,CAAc;EAEtB,IAAAjB,yCAAc,EAAEmB,QAAA,EAAU,CAACJ,EAAA,EAAII,QAAA,E,GAAaL,QAAA,CAAS;EAErD,OAAOE,UAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}