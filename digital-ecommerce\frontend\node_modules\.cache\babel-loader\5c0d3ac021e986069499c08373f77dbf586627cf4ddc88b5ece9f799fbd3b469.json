{"ast": null, "code": "function _class_apply_descriptor_set(receiver, descriptor, value) {\n  if (descriptor.set) descriptor.set.call(receiver, value);else {\n    if (!descriptor.writable) {\n      // This should only throw in strict mode, but class bodies are\n      // always strict and private fields can only be used inside\n      // class bodies.\n      throw new TypeError(\"attempted to set read only private field\");\n    }\n    descriptor.value = value;\n  }\n}\nexport { _class_apply_descriptor_set as _ };", "map": {"version": 3, "names": ["_class_apply_descriptor_set", "receiver", "descriptor", "value", "set", "call", "writable", "TypeError", "_"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "mappings": "AAAA,SAASA,2BAA2BA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAC9D,IAAID,UAAU,CAACE,GAAG,EAAEF,UAAU,CAACE,GAAG,CAACC,IAAI,CAACJ,QAAQ,EAAEE,KAAK,CAAC,CAAC,KACpD;IACD,IAAI,CAACD,UAAU,CAACI,QAAQ,EAAE;MACtB;MACA;MACA;MACA,MAAM,IAAIC,SAAS,CAAC,0CAA0C,CAAC;IACnE;IACAL,UAAU,CAACC,KAAK,GAAGA,KAAK;EAC5B;AACJ;AACA,SAASH,2BAA2B,IAAIQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}