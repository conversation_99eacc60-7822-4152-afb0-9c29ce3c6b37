{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"features\"];\nimport { forwardRefWithAs as i, useRender as p } from '../utils/render.js';\nlet a = \"span\";\nvar s = (e => (e[e.None = 1] = \"None\", e[e.Focusable = 2] = \"Focusable\", e[e.Hidden = 4] = \"Hidden\", e))(s || {});\nfunction l(t, r) {\n  var n;\n  let {\n      features: d = 1\n    } = t,\n    e = _objectWithoutProperties(t, _excluded),\n    o = {\n      ref: r,\n      \"aria-hidden\": (d & 2) === 2 ? !0 : (n = e[\"aria-hidden\"]) != null ? n : void 0,\n      hidden: (d & 4) === 4 ? !0 : void 0,\n      style: _objectSpread({\n        position: \"fixed\",\n        top: 1,\n        left: 1,\n        width: 1,\n        height: 0,\n        padding: 0,\n        margin: -1,\n        overflow: \"hidden\",\n        clip: \"rect(0, 0, 0, 0)\",\n        whiteSpace: \"nowrap\",\n        borderWidth: \"0\"\n      }, (d & 4) === 4 && (d & 2) !== 2 && {\n        display: \"none\"\n      })\n    };\n  return p()({\n    ourProps: o,\n    theirProps: e,\n    slot: {},\n    defaultTag: a,\n    name: \"Hidden\"\n  });\n}\nlet f = i(l);\nexport { f as Hidden, s as HiddenFeatures };", "map": {"version": 3, "names": ["forwardRefWithAs", "i", "useRender", "p", "a", "s", "e", "None", "Focusable", "Hidden", "l", "t", "r", "n", "features", "d", "_objectWithoutProperties", "_excluded", "o", "ref", "hidden", "style", "_objectSpread", "position", "top", "left", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "borderWidth", "display", "ourProps", "theirProps", "slot", "defaultTag", "name", "f", "HiddenFeatures"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/hidden.js"], "sourcesContent": ["import{forwardRefWithAs as i,useRender as p}from'../utils/render.js';let a=\"span\";var s=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(s||{});function l(t,r){var n;let{features:d=1,...e}=t,o={ref:r,\"aria-hidden\":(d&2)===2?!0:(n=e[\"aria-hidden\"])!=null?n:void 0,hidden:(d&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(d&4)===4&&(d&2)!==2&&{display:\"none\"}}};return p()({ourProps:o,theirProps:e,slot:{},defaultTag:a,name:\"Hidden\"})}let f=i(l);export{f as Hidden,s as HiddenFeatures};\n"], "mappings": ";;;AAAA,SAAOA,gBAAgB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,IAAIC,CAAC,GAAC,MAAM;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACF,CAAC,CAACA,CAAC,CAACG,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACH,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASK,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,IAAG;MAACC,QAAQ,EAACC,CAAC,GAAC;IAAM,CAAC,GAACJ,CAAC;IAAJL,CAAC,GAAAU,wBAAA,CAAEL,CAAC,EAAAM,SAAA;IAACC,CAAC,GAAC;MAACC,GAAG,EAACP,CAAC;MAAC,aAAa,EAAC,CAACG,CAAC,GAAC,CAAC,MAAI,CAAC,GAAC,CAAC,CAAC,GAAC,CAACF,CAAC,GAACP,CAAC,CAAC,aAAa,CAAC,KAAG,IAAI,GAACO,CAAC,GAAC,KAAK,CAAC;MAACO,MAAM,EAAC,CAACL,CAAC,GAAC,CAAC,MAAI,CAAC,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAACM,KAAK,EAAAC,aAAA;QAAEC,QAAQ,EAAC,OAAO;QAACC,GAAG,EAAC,CAAC;QAACC,IAAI,EAAC,CAAC;QAACC,KAAK,EAAC,CAAC;QAACC,MAAM,EAAC,CAAC;QAACC,OAAO,EAAC,CAAC;QAACC,MAAM,EAAC,CAAC,CAAC;QAACC,QAAQ,EAAC,QAAQ;QAACC,IAAI,EAAC,kBAAkB;QAACC,UAAU,EAAC,QAAQ;QAACC,WAAW,EAAC;MAAG,GAAI,CAAClB,CAAC,GAAC,CAAC,MAAI,CAAC,IAAE,CAACA,CAAC,GAAC,CAAC,MAAI,CAAC,IAAE;QAACmB,OAAO,EAAC;MAAM,CAAC;IAAC,CAAC;EAAC,OAAO/B,CAAC,CAAC,CAAC,CAAC;IAACgC,QAAQ,EAACjB,CAAC;IAACkB,UAAU,EAAC9B,CAAC;IAAC+B,IAAI,EAAC,CAAC,CAAC;IAACC,UAAU,EAAClC,CAAC;IAACmC,IAAI,EAAC;EAAQ,CAAC,CAAC;AAAA;AAAC,IAAIC,CAAC,GAACvC,CAAC,CAACS,CAAC,CAAC;AAAC,SAAO8B,CAAC,IAAI/B,MAAM,EAACJ,CAAC,IAAIoC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}