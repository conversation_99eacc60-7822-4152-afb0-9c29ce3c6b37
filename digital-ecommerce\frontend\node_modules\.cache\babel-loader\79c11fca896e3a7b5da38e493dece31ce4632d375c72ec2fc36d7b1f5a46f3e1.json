{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminDashboardPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { ShoppingBagIcon, TagIcon, UserGroupIcon, CurrencyDollarIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon, EyeIcon, PlusIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboardPage = () => {\n  _s();\n  const {\n    admin\n  } = useAdmin();\n  const {\n    products,\n    categories,\n    addProduct\n  } = useProducts();\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalCategories: 0,\n    totalRevenue: 0,\n    totalOrders: 0,\n    recentProducts: [],\n    lowStockProducts: []\n  });\n  useEffect(() => {\n    // Calculate dashboard statistics\n    const totalProducts = products.length;\n    const totalCategories = categories.length;\n    const totalRevenue = products.reduce((sum, product) => sum + product.price * (product.sold || 0), 0);\n    const totalOrders = products.reduce((sum, product) => sum + (product.sold || 0), 0);\n\n    // Get recent products (last 5)\n    const recentProducts = products.slice(-5).reverse();\n\n    // Get low stock products\n    const lowStockProducts = products.filter(product => product.stockCount && product.stockCount < 10).slice(0, 5);\n    setStats({\n      totalProducts,\n      totalCategories,\n      totalRevenue,\n      totalOrders,\n      recentProducts,\n      lowStockProducts\n    });\n  }, []);\n  const StatCard = ({\n    title,\n    value,\n    icon: Icon,\n    trend,\n    trendValue,\n    color = 'blue'\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"p-6 rounded-xl shadow-lg transition-all duration-300 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-600\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), trend && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2\",\n          children: [trend === 'up' ? /*#__PURE__*/_jsxDEV(ArrowTrendingUpIcon, {\n            className: \"w-4 h-4 text-green-500 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(ArrowTrendingDownIcon, {\n            className: \"w-4 h-4 text-red-500 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`,\n            children: trendValue\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-3 rounded-full ${color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' : color === 'green' ? 'bg-green-100 dark:bg-green-900/20' : color === 'yellow' ? 'bg-yellow-100 dark:bg-yellow-900/20' : 'bg-purple-100 dark:bg-purple-900/20'}`,\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          className: `w-6 h-6 ${color === 'blue' ? 'text-blue-600' : color === 'green' ? 'text-green-600' : color === 'yellow' ? 'text-yellow-600' : 'text-purple-600'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: [\"Welcome back, \", admin === null || admin === void 0 ? void 0 : admin.firstName, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Here's what's happening with your store today.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setShowAddProductModal(true),\n          className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Products\",\n          value: stats.totalProducts,\n          icon: ShoppingBagIcon,\n          trend: \"up\",\n          trendValue: \"+12%\",\n          color: \"blue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Categories\",\n          value: stats.totalCategories,\n          icon: TagIcon,\n          color: \"green\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Revenue\",\n          value: `$${stats.totalRevenue.toLocaleString()}`,\n          icon: CurrencyDollarIcon,\n          trend: \"up\",\n          trendValue: \"+8.2%\",\n          color: \"yellow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Orders\",\n          value: stats.totalOrders,\n          icon: UserGroupIcon,\n          trend: \"up\",\n          trendValue: \"+23%\",\n          color: \"purple\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: \"p-6 rounded-xl shadow-lg bg-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${getThemeClasses('text-gray-900', 'text-white')}`,\n              children: \"Recent Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `text-sm ${getThemeClasses('text-light-orange-600 hover:text-light-orange-700', 'text-light-orange-400 hover:text-light-orange-300')}`,\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: stats.recentProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-12 h-12 rounded-lg object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-medium truncate ${getThemeClasses('text-gray-900', 'text-white')}`,\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                  children: [\"$\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `p-2 rounded-lg ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')}`,\n                children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: `p-6 rounded-xl shadow-lg ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${getThemeClasses('text-gray-900', 'text-white')}`,\n              children: \"Low Stock Alert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full\",\n              children: [stats.lowStockProducts.length, \" items\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: stats.lowStockProducts.length > 0 ? stats.lowStockProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: product.name,\n                  className: \"w-10 h-10 rounded-lg object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm font-medium ${getThemeClasses('text-gray-900', 'text-white')}`,\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-red-600\",\n                    children: [product.stockCount, \" left\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors\",\n                children: \"Restock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n              children: \"All products are well stocked!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddProductModal, {\n      isOpen: showAddProductModal,\n      onClose: () => setShowAddProductModal(false),\n      onSubmit: async productData => {\n        const result = await addProduct(productData);\n        if (result.success) {\n          setShowAddProductModal(false);\n          // Show success notification (you can add a toast notification here)\n          console.log('Product added successfully:', result.product);\n        } else {\n          // Show error notification\n          console.error('Failed to add product:', result.error);\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboardPage, \"RCTmrjHdZtzY9C+vrTawGgsqiwY=\", false, function () {\n  return [useAdmin, useProducts];\n});\n_c = AdminDashboardPage;\nexport default AdminDashboardPage;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboardPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "ShoppingBagIcon", "TagIcon", "UserGroupIcon", "CurrencyDollarIcon", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "EyeIcon", "PlusIcon", "useAdmin", "useProducts", "AdminLayout", "AddProductModal", "jsxDEV", "_jsxDEV", "AdminDashboardPage", "_s", "admin", "products", "categories", "addProduct", "showAddProductModal", "setShowAddProductModal", "stats", "setStats", "totalProducts", "totalCategories", "totalRevenue", "totalOrders", "recentProducts", "lowStockProducts", "length", "reduce", "sum", "product", "price", "sold", "slice", "reverse", "filter", "stockCount", "StatCard", "title", "value", "icon", "Icon", "trend", "trendValue", "color", "div", "initial", "opacity", "y", "animate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "button", "whileHover", "scale", "whileTap", "onClick", "toLocaleString", "x", "getThemeClasses", "map", "index", "src", "image", "alt", "name", "id", "isOpen", "onClose", "onSubmit", "productData", "result", "success", "console", "log", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminDashboardPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ShoppingBagIcon,\n  TagIcon,\n  UserGroupIcon,\n  CurrencyDollarIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n  EyeIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\n\nconst AdminDashboardPage = () => {\n  const { admin } = useAdmin();\n  const { products, categories, addProduct } = useProducts();\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalCategories: 0,\n    totalRevenue: 0,\n    totalOrders: 0,\n    recentProducts: [],\n    lowStockProducts: []\n  });\n\n  useEffect(() => {\n    // Calculate dashboard statistics\n    const totalProducts = products.length;\n    const totalCategories = categories.length;\n    const totalRevenue = products.reduce((sum, product) => sum + (product.price * (product.sold || 0)), 0);\n    const totalOrders = products.reduce((sum, product) => sum + (product.sold || 0), 0);\n    \n    // Get recent products (last 5)\n    const recentProducts = products.slice(-5).reverse();\n    \n    // Get low stock products\n    const lowStockProducts = products.filter(product => \n      product.stockCount && product.stockCount < 10\n    ).slice(0, 5);\n\n    setStats({\n      totalProducts,\n      totalCategories,\n      totalRevenue,\n      totalOrders,\n      recentProducts,\n      lowStockProducts\n    });\n  }, []);\n\n  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue' }) => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"p-6 rounded-xl shadow-lg transition-all duration-300 bg-white\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600\">\n            {title}\n          </p>\n          <p className=\"text-3xl font-bold text-gray-900\">\n            {value}\n          </p>\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              {trend === 'up' ? (\n                <ArrowTrendingUpIcon className=\"w-4 h-4 text-green-500 mr-1\" />\n              ) : (\n                <ArrowTrendingDownIcon className=\"w-4 h-4 text-red-500 mr-1\" />\n              )}\n              <span className={`text-sm ${\n                trend === 'up' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trendValue}\n              </span>\n            </div>\n          )}\n        </div>\n        <div className={`p-3 rounded-full ${\n          color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' :\n          color === 'green' ? 'bg-green-100 dark:bg-green-900/20' :\n          color === 'yellow' ? 'bg-yellow-100 dark:bg-yellow-900/20' :\n          'bg-purple-100 dark:bg-purple-900/20'\n        }`}>\n          <Icon className={`w-6 h-6 ${\n            color === 'blue' ? 'text-blue-600' :\n            color === 'green' ? 'text-green-600' :\n            color === 'yellow' ? 'text-yellow-600' :\n            'text-purple-600'\n          }`} />\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-8\">\n        {/* Welcome Section */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Welcome back, {admin?.firstName}!\n            </h1>\n            <p className=\"mt-2 text-gray-600\">\n              Here's what's happening with your store today.\n            </p>\n          </div>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setShowAddProductModal(true)}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <PlusIcon className=\"w-5 h-5\" />\n            <span>Add Product</span>\n          </motion.button>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <StatCard\n            title=\"Total Products\"\n            value={stats.totalProducts}\n            icon={ShoppingBagIcon}\n            trend=\"up\"\n            trendValue=\"+12%\"\n            color=\"blue\"\n          />\n          <StatCard\n            title=\"Categories\"\n            value={stats.totalCategories}\n            icon={TagIcon}\n            color=\"green\"\n          />\n          <StatCard\n            title=\"Total Revenue\"\n            value={`$${stats.totalRevenue.toLocaleString()}`}\n            icon={CurrencyDollarIcon}\n            trend=\"up\"\n            trendValue=\"+8.2%\"\n            color=\"yellow\"\n          />\n          <StatCard\n            title=\"Total Orders\"\n            value={stats.totalOrders}\n            icon={UserGroupIcon}\n            trend=\"up\"\n            trendValue=\"+23%\"\n            color=\"purple\"\n          />\n        </div>\n\n        {/* Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Products */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className=\"p-6 rounded-xl shadow-lg bg-white\"\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className={`text-lg font-semibold ${\n                getThemeClasses('text-gray-900', 'text-white')\n              }`}>\n                Recent Products\n              </h3>\n              <button className={`text-sm ${\n                getThemeClasses('text-light-orange-600 hover:text-light-orange-700', 'text-light-orange-400 hover:text-light-orange-300')\n              }`}>\n                View All\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {stats.recentProducts.map((product, index) => (\n                <div key={product.id} className=\"flex items-center space-x-4\">\n                  <img\n                    src={product.image}\n                    alt={product.name}\n                    className=\"w-12 h-12 rounded-lg object-cover\"\n                  />\n                  <div className=\"flex-1 min-w-0\">\n                    <p className={`text-sm font-medium truncate ${\n                      getThemeClasses('text-gray-900', 'text-white')\n                    }`}>\n                      {product.name}\n                    </p>\n                    <p className={`text-sm ${\n                      getThemeClasses('text-gray-500', 'text-gray-400')\n                    }`}>\n                      ${product.price}\n                    </p>\n                  </div>\n                  <button className={`p-2 rounded-lg ${\n                    getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')\n                  }`}>\n                    <EyeIcon className=\"w-4 h-4 text-gray-400\" />\n                  </button>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Low Stock Alert */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className={`p-6 rounded-xl shadow-lg ${\n              getThemeClasses('bg-white', 'bg-slate-800')\n            }`}\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className={`text-lg font-semibold ${\n                getThemeClasses('text-gray-900', 'text-white')\n              }`}>\n                Low Stock Alert\n              </h3>\n              <span className=\"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full\">\n                {stats.lowStockProducts.length} items\n              </span>\n            </div>\n            <div className=\"space-y-4\">\n              {stats.lowStockProducts.length > 0 ? (\n                stats.lowStockProducts.map((product) => (\n                  <div key={product.id} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <img\n                        src={product.image}\n                        alt={product.name}\n                        className=\"w-10 h-10 rounded-lg object-cover\"\n                      />\n                      <div>\n                        <p className={`text-sm font-medium ${\n                          getThemeClasses('text-gray-900', 'text-white')\n                        }`}>\n                          {product.name}\n                        </p>\n                        <p className=\"text-xs text-red-600\">\n                          {product.stockCount} left\n                        </p>\n                      </div>\n                    </div>\n                    <button className=\"px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors\">\n                      Restock\n                    </button>\n                  </div>\n                ))\n              ) : (\n                <p className={`text-sm ${\n                  getThemeClasses('text-gray-500', 'text-gray-400')\n                }`}>\n                  All products are well stocked!\n                </p>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Add Product Modal */}\n      <AddProductModal\n        isOpen={showAddProductModal}\n        onClose={() => setShowAddProductModal(false)}\n        onSubmit={async (productData) => {\n          const result = await addProduct(productData);\n          if (result.success) {\n            setShowAddProductModal(false);\n            // Show success notification (you can add a toast notification here)\n            console.log('Product added successfully:', result.product);\n          } else {\n            // Show error notification\n            console.error('Failed to add product:', result.error);\n          }\n        }}\n      />\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboardPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,eAAe,EACfC,OAAO,EACPC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,qBAAqB,EACrBC,OAAO,EACPC,QAAQ,QACH,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAM,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAC5B,MAAM;IAAES,QAAQ;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGV,WAAW,CAAC,CAAC;EAC1D,MAAM,CAACW,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC;IACjC2B,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF/B,SAAS,CAAC,MAAM;IACd;IACA,MAAM0B,aAAa,GAAGP,QAAQ,CAACa,MAAM;IACrC,MAAML,eAAe,GAAGP,UAAU,CAACY,MAAM;IACzC,MAAMJ,YAAY,GAAGT,QAAQ,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAIC,OAAO,CAACC,KAAK,IAAID,OAAO,CAACE,IAAI,IAAI,CAAC,CAAE,EAAE,CAAC,CAAC;IACtG,MAAMR,WAAW,GAAGV,QAAQ,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACE,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEnF;IACA,MAAMP,cAAc,GAAGX,QAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;IAEnD;IACA,MAAMR,gBAAgB,GAAGZ,QAAQ,CAACqB,MAAM,CAACL,OAAO,IAC9CA,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,EAC7C,CAAC,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAEbb,QAAQ,CAAC;MACPC,aAAa;MACbC,eAAe;MACfC,YAAY;MACZC,WAAW;MACXC,cAAc;MACdC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI,EAAEC,IAAI;IAAEC,KAAK;IAAEC,UAAU;IAAEC,KAAK,GAAG;EAAO,CAAC,kBAC/ElC,OAAA,CAACd,MAAM,CAACiD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAEzEzC,OAAA;MAAKwC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA;UAAGwC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC7Cb;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ7C,OAAA;UAAGwC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC5CZ;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACHb,KAAK,iBACJhC,OAAA;UAAKwC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GACpCT,KAAK,KAAK,IAAI,gBACbhC,OAAA,CAACT,mBAAmB;YAACiD,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE/D7C,OAAA,CAACR,qBAAqB;YAACgD,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC/D,eACD7C,OAAA;YAAMwC,SAAS,EAAE,WACfR,KAAK,KAAK,IAAI,GAAG,gBAAgB,GAAG,cAAc,EACjD;YAAAS,QAAA,EACAR;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAE,oBACdN,KAAK,KAAK,MAAM,GAAG,iCAAiC,GACpDA,KAAK,KAAK,OAAO,GAAG,mCAAmC,GACvDA,KAAK,KAAK,QAAQ,GAAG,qCAAqC,GAC1D,qCAAqC,EACpC;QAAAO,QAAA,eACDzC,OAAA,CAAC+B,IAAI;UAACS,SAAS,EAAE,WACfN,KAAK,KAAK,MAAM,GAAG,eAAe,GAClCA,KAAK,KAAK,OAAO,GAAG,gBAAgB,GACpCA,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GACtC,iBAAiB;QAChB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,oBACE7C,OAAA,CAACH,WAAW;IAAA4C,QAAA,gBACVzC,OAAA;MAAKwC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBzC,OAAA;QAAKwC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAIwC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,gBACjC,EAACtC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2C,SAAS,EAAC,GAClC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7C,OAAA;YAAGwC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN7C,OAAA,CAACd,MAAM,CAAC6D,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BE,OAAO,EAAEA,CAAA,KAAM3C,sBAAsB,CAAC,IAAI,CAAE;UAC5CgC,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvIzC,OAAA,CAACN,QAAQ;YAAC8C,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChC7C,OAAA;YAAAyC,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGN7C,OAAA;QAAKwC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnEzC,OAAA,CAAC2B,QAAQ;UACPC,KAAK,EAAC,gBAAgB;UACtBC,KAAK,EAAEpB,KAAK,CAACE,aAAc;UAC3BmB,IAAI,EAAE3C,eAAgB;UACtB6C,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,MAAM;UACjBC,KAAK,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACF7C,OAAA,CAAC2B,QAAQ;UACPC,KAAK,EAAC,YAAY;UAClBC,KAAK,EAAEpB,KAAK,CAACG,eAAgB;UAC7BkB,IAAI,EAAE1C,OAAQ;UACd8C,KAAK,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACF7C,OAAA,CAAC2B,QAAQ;UACPC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAE,IAAIpB,KAAK,CAACI,YAAY,CAACuC,cAAc,CAAC,CAAC,EAAG;UACjDtB,IAAI,EAAExC,kBAAmB;UACzB0C,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,OAAO;UAClBC,KAAK,EAAC;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF7C,OAAA,CAAC2B,QAAQ;UACPC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEpB,KAAK,CAACK,WAAY;UACzBgB,IAAI,EAAEzC,aAAc;UACpB2C,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,MAAM;UACjBC,KAAK,EAAC;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN7C,OAAA;QAAKwC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDzC,OAAA,CAACd,MAAM,CAACiD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7CzC,OAAA;YAAKwC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzC,OAAA;cAAIwC,SAAS,EAAE,yBACbc,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;cAAAb,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAQwC,SAAS,EAAE,WACjBc,eAAe,CAAC,mDAAmD,EAAE,mDAAmD,CAAC,EACxH;cAAAb,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhC,KAAK,CAACM,cAAc,CAACwC,GAAG,CAAC,CAACnC,OAAO,EAAEoC,KAAK,kBACvCxD,OAAA;cAAsBwC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC3DzC,OAAA;gBACEyD,GAAG,EAAErC,OAAO,CAACsC,KAAM;gBACnBC,GAAG,EAAEvC,OAAO,CAACwC,IAAK;gBAClBpB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACF7C,OAAA;gBAAKwC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzC,OAAA;kBAAGwC,SAAS,EAAE,gCACZc,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;kBAAAb,QAAA,EACArB,OAAO,CAACwC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACJ7C,OAAA;kBAAGwC,SAAS,EAAE,WACZc,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAb,QAAA,GAAC,GACD,EAACrB,OAAO,CAACC,KAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN7C,OAAA;gBAAQwC,SAAS,EAAE,kBACjBc,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;gBAAAb,QAAA,eACDzC,OAAA,CAACP,OAAO;kBAAC+C,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA,GAtBDzB,OAAO,CAACyC,EAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb7C,OAAA,CAACd,MAAM,CAACiD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAG,CAAE;UAC/Bd,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEgB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,SAAS,EAAE,4BACTc,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;UAAAb,QAAA,gBAEHzC,OAAA;YAAKwC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzC,OAAA;cAAIwC,SAAS,EAAE,yBACbc,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;cAAAb,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAMwC,SAAS,EAAC,uFAAuF;cAAAC,QAAA,GACpGhC,KAAK,CAACO,gBAAgB,CAACC,MAAM,EAAC,QACjC;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhC,KAAK,CAACO,gBAAgB,CAACC,MAAM,GAAG,CAAC,GAChCR,KAAK,CAACO,gBAAgB,CAACuC,GAAG,CAAEnC,OAAO,iBACjCpB,OAAA;cAAsBwC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBACjEzC,OAAA;gBAAKwC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CzC,OAAA;kBACEyD,GAAG,EAAErC,OAAO,CAACsC,KAAM;kBACnBC,GAAG,EAAEvC,OAAO,CAACwC,IAAK;kBAClBpB,SAAS,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACF7C,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAGwC,SAAS,EAAE,uBACZc,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;oBAAAb,QAAA,EACArB,OAAO,CAACwC;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACJ7C,OAAA;oBAAGwC,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAChCrB,OAAO,CAACM,UAAU,EAAC,OACtB;kBAAA;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7C,OAAA;gBAAQwC,SAAS,EAAC,yGAAyG;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GApBDzB,OAAO,CAACyC,EAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBf,CACN,CAAC,gBAEF7C,OAAA;cAAGwC,SAAS,EAAE,WACZc,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;cAAAb,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA,CAACF,eAAe;MACdgE,MAAM,EAAEvD,mBAAoB;MAC5BwD,OAAO,EAAEA,CAAA,KAAMvD,sBAAsB,CAAC,KAAK,CAAE;MAC7CwD,QAAQ,EAAE,MAAOC,WAAW,IAAK;QAC/B,MAAMC,MAAM,GAAG,MAAM5D,UAAU,CAAC2D,WAAW,CAAC;QAC5C,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClB3D,sBAAsB,CAAC,KAAK,CAAC;UAC7B;UACA4D,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,MAAM,CAAC9C,OAAO,CAAC;QAC5D,CAAC,MAAM;UACL;UACAgD,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEJ,MAAM,CAACI,KAAK,CAAC;QACvD;MACF;IAAE;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAC3C,EAAA,CA1QID,kBAAkB;EAAA,QACJN,QAAQ,EACmBC,WAAW;AAAA;AAAA2E,EAAA,GAFpDtE,kBAAkB;AA4QxB,eAAeA,kBAAkB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}