{"ast": null, "code": "import { version as $iulvE$version } from \"react\";\nfunction $cdc5a6778b766db2$export$a9d04c5684123369(value) {\n  const pieces = (0, $iulvE$version).split('.');\n  const major = parseInt(pieces[0], 10);\n  if (major >= 19) return value;\n  // compatibility with React < 19\n  return value ? 'true' : undefined;\n}\nexport { $cdc5a6778b766db2$export$a9d04c5684123369 as inertValue };", "map": {"version": 3, "names": ["$cdc5a6778b766db2$export$a9d04c5684123369", "value", "pieces", "$iulvE$version", "split", "major", "parseInt", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\inertValue.ts"], "sourcesContent": ["import {version} from 'react';\n\nexport function inertValue(value?: boolean): string | boolean | undefined {\n  const pieces = version.split('.');\n  const major = parseInt(pieces[0], 10);\n  if (major >= 19) {\n    return value;\n  }\n  // compatibility with React < 19\n  return value ? 'true' : undefined;\n}\n"], "mappings": ";AAEO,SAASA,0CAAWC,KAAe;EACxC,MAAMC,MAAA,GAAS,IAAAC,cAAM,EAAEC,KAAK,CAAC;EAC7B,MAAMC,KAAA,GAAQC,QAAA,CAASJ,MAAM,CAAC,EAAE,EAAE;EAClC,IAAIG,KAAA,IAAS,IACX,OAAOJ,KAAA;EAET;EACA,OAAOA,KAAA,GAAQ,SAASM,SAAA;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}