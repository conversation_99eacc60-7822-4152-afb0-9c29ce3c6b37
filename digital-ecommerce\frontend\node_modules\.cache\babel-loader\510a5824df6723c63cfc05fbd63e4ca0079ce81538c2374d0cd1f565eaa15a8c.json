{"ast": null, "code": "import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\nexport { TYPEABLE_SELECTOR, activeElement, contains, getDocument, getPlatform, getTarget, getUserAgent, isAndroid, isEventTargetWithin, isJSDOM, isMac, isMouseLikePointerType, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, stopEvent };", "map": {"version": 3, "names": ["isShadowRoot", "isHTMLElement", "activeElement", "doc", "_activeElement", "shadowRoot", "contains", "parent", "child", "rootNode", "getRootNode", "next", "parentNode", "host", "getPlatform", "uaData", "navigator", "userAgentData", "platform", "getUserAgent", "Array", "isArray", "brands", "map", "_ref", "brand", "version", "join", "userAgent", "isVirtualClick", "event", "mozInputSource", "isTrusted", "isAndroid", "pointerType", "type", "buttons", "detail", "isVirtualPointerEvent", "isJSDOM", "width", "height", "pressure", "<PERSON><PERSON><PERSON><PERSON>", "test", "vendor", "re", "isMac", "toLowerCase", "startsWith", "maxTouchPoints", "includes", "isMouseLikePointerType", "strict", "values", "push", "undefined", "isReactEvent", "isRootElement", "element", "matches", "getDocument", "node", "ownerDocument", "document", "isEventTargetWithin", "<PERSON><PERSON><PERSON>", "e", "target", "get<PERSON><PERSON><PERSON>", "TYPEABLE_SELECTOR", "isTypeableElement", "stopEvent", "preventDefault", "stopPropagation", "isTypeableCombobox", "getAttribute"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@floating-ui/react/dist/floating-ui.react.utils.mjs"], "sourcesContent": ["import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\n\nexport { TYPEABLE_SELECTOR, activeElement, contains, getDocument, getPlatform, getTarget, getUserAgent, isAndroid, isEventTargetWithin, isJSDOM, isMac, isMouseLikePointerType, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, stopEvent };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAQ,wBAAwB;AAEpE,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1B,IAAID,aAAa,GAAGC,GAAG,CAACD,aAAa;EACrC,OAAO,CAAC,CAACE,cAAc,GAAGF,aAAa,KAAK,IAAI,IAAI,CAACE,cAAc,GAAGA,cAAc,CAACC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACF,aAAa,KAAK,IAAI,EAAE;IACzJ,IAAIE,cAAc;IAClBF,aAAa,GAAGA,aAAa,CAACG,UAAU,CAACH,aAAa;EACxD;EACA,OAAOA,aAAa;AACtB;AACA,SAASI,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/B,IAAI,CAACD,MAAM,IAAI,CAACC,KAAK,EAAE;IACrB,OAAO,KAAK;EACd;EACA,MAAMC,QAAQ,GAAGD,KAAK,CAACE,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,KAAK,CAACE,WAAW,CAAC,CAAC;;EAEzE;EACA,IAAIH,MAAM,CAACD,QAAQ,CAACE,KAAK,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;;EAEA;EACA,IAAIC,QAAQ,IAAIT,YAAY,CAACS,QAAQ,CAAC,EAAE;IACtC,IAAIE,IAAI,GAAGH,KAAK;IAChB,OAAOG,IAAI,EAAE;MACX,IAAIJ,MAAM,KAAKI,IAAI,EAAE;QACnB,OAAO,IAAI;MACb;MACA;MACAA,IAAI,GAAGA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACE,IAAI;IACrC;EACF;;EAEA;EACA,OAAO,KAAK;AACd;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,MAAM,GAAGC,SAAS,CAACC,aAAa;EACtC,IAAIF,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,QAAQ,EAAE;IACrC,OAAOH,MAAM,CAACG,QAAQ;EACxB;EACA,OAAOF,SAAS,CAACE,QAAQ;AAC3B;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB,MAAMJ,MAAM,GAAGC,SAAS,CAACC,aAAa;EACtC,IAAIF,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACN,MAAM,CAACO,MAAM,CAAC,EAAE;IAC1C,OAAOP,MAAM,CAACO,MAAM,CAACC,GAAG,CAACC,IAAI,IAAI;MAC/B,IAAI;QACFC,KAAK;QACLC;MACF,CAAC,GAAGF,IAAI;MACR,OAAOC,KAAK,GAAG,GAAG,GAAGC,OAAO;IAC9B,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd;EACA,OAAOX,SAAS,CAACY,SAAS;AAC5B;;AAEA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B;EACA;EACA,IAAIA,KAAK,CAACC,cAAc,KAAK,CAAC,IAAID,KAAK,CAACE,SAAS,EAAE;IACjD,OAAO,IAAI;EACb;EACA,IAAIC,SAAS,CAAC,CAAC,IAAIH,KAAK,CAACI,WAAW,EAAE;IACpC,OAAOJ,KAAK,CAACK,IAAI,KAAK,OAAO,IAAIL,KAAK,CAACM,OAAO,KAAK,CAAC;EACtD;EACA,OAAON,KAAK,CAACO,MAAM,KAAK,CAAC,IAAI,CAACP,KAAK,CAACI,WAAW;AACjD;AACA,SAASI,qBAAqBA,CAACR,KAAK,EAAE;EACpC,IAAIS,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK;EAC3B,OAAO,CAACN,SAAS,CAAC,CAAC,IAAIH,KAAK,CAACU,KAAK,KAAK,CAAC,IAAIV,KAAK,CAACW,MAAM,KAAK,CAAC,IAAIR,SAAS,CAAC,CAAC,IAAIH,KAAK,CAACU,KAAK,KAAK,CAAC,IAAIV,KAAK,CAACW,MAAM,KAAK,CAAC,IAAIX,KAAK,CAACY,QAAQ,KAAK,CAAC,IAAIZ,KAAK,CAACO,MAAM,KAAK,CAAC,IAAIP,KAAK,CAACI,WAAW,KAAK,OAAO;EACvM;EACAJ,KAAK,CAACU,KAAK,GAAG,CAAC,IAAIV,KAAK,CAACW,MAAM,GAAG,CAAC,IAAIX,KAAK,CAACY,QAAQ,KAAK,CAAC,IAAIZ,KAAK,CAACO,MAAM,KAAK,CAAC,IAAIP,KAAK,CAACI,WAAW,KAAK,OAAO;AACpH;AACA,SAASS,QAAQA,CAAA,EAAG;EAClB;EACA,OAAO,QAAQ,CAACC,IAAI,CAAC5B,SAAS,CAAC6B,MAAM,CAAC;AACxC;AACA,SAASZ,SAASA,CAAA,EAAG;EACnB,MAAMa,EAAE,GAAG,UAAU;EACrB,OAAOA,EAAE,CAACF,IAAI,CAAC9B,WAAW,CAAC,CAAC,CAAC,IAAIgC,EAAE,CAACF,IAAI,CAACzB,YAAY,CAAC,CAAC,CAAC;AAC1D;AACA,SAAS4B,KAAKA,CAAA,EAAG;EACf,OAAOjC,WAAW,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,KAAK,CAAC,IAAI,CAACjC,SAAS,CAACkC,cAAc;AACnF;AACA,SAASX,OAAOA,CAAA,EAAG;EACjB,OAAOpB,YAAY,CAAC,CAAC,CAACgC,QAAQ,CAAC,QAAQ,CAAC;AAC1C;AACA,SAASC,sBAAsBA,CAAClB,WAAW,EAAEmB,MAAM,EAAE;EACnD;EACA;EACA,MAAMC,MAAM,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;EAC/B,IAAI,CAACD,MAAM,EAAE;IACXC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAEC,SAAS,CAAC;EAC5B;EACA,OAAOF,MAAM,CAACH,QAAQ,CAACjB,WAAW,CAAC;AACrC;AACA,SAASuB,YAAYA,CAAC3B,KAAK,EAAE;EAC3B,OAAO,aAAa,IAAIA,KAAK;AAC/B;AACA,SAAS4B,aAAaA,CAACC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,OAAO,CAAC,WAAW,CAAC;AACrC;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,OAAO,CAACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACC,aAAa,KAAKC,QAAQ;AACjE;AACA,SAASC,mBAAmBA,CAACnC,KAAK,EAAEgC,IAAI,EAAE;EACxC,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,KAAK;EACd;EACA,IAAI,cAAc,IAAIhC,KAAK,EAAE;IAC3B,OAAOA,KAAK,CAACoC,YAAY,CAAC,CAAC,CAACf,QAAQ,CAACW,IAAI,CAAC;EAC5C;;EAEA;EACA,MAAMK,CAAC,GAAGrC,KAAK;EACf,OAAOqC,CAAC,CAACC,MAAM,IAAI,IAAI,IAAIN,IAAI,CAACxD,QAAQ,CAAC6D,CAAC,CAACC,MAAM,CAAC;AACpD;AACA,SAASC,SAASA,CAACvC,KAAK,EAAE;EACxB,IAAI,cAAc,IAAIA,KAAK,EAAE;IAC3B,OAAOA,KAAK,CAACoC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA;EACA;EACA,OAAOpC,KAAK,CAACsC,MAAM;AACrB;AACA,MAAME,iBAAiB,GAAG,6CAA6C,GAAG,2EAA2E;AACrJ,SAASC,iBAAiBA,CAACZ,OAAO,EAAE;EAClC,OAAO1D,aAAa,CAAC0D,OAAO,CAAC,IAAIA,OAAO,CAACC,OAAO,CAACU,iBAAiB,CAAC;AACrE;AACA,SAASE,SAASA,CAAC1C,KAAK,EAAE;EACxBA,KAAK,CAAC2C,cAAc,CAAC,CAAC;EACtB3C,KAAK,CAAC4C,eAAe,CAAC,CAAC;AACzB;AACA,SAASC,kBAAkBA,CAAChB,OAAO,EAAE;EACnC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;EAC1B,OAAOA,OAAO,CAACiB,YAAY,CAAC,MAAM,CAAC,KAAK,UAAU,IAAIL,iBAAiB,CAACZ,OAAO,CAAC;AAClF;AAEA,SAASW,iBAAiB,EAAEpE,aAAa,EAAEI,QAAQ,EAAEuD,WAAW,EAAE/C,WAAW,EAAEuD,SAAS,EAAElD,YAAY,EAAEc,SAAS,EAAEgC,mBAAmB,EAAE1B,OAAO,EAAEQ,KAAK,EAAEK,sBAAsB,EAAEK,YAAY,EAAEC,aAAa,EAAEf,QAAQ,EAAEgC,kBAAkB,EAAEJ,iBAAiB,EAAE1C,cAAc,EAAES,qBAAqB,EAAEkC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}