{"ast": null, "code": "function a(o, r) {\n  let t = o(),\n    n = new Set();\n  return {\n    getSnapshot() {\n      return t;\n    },\n    subscribe(e) {\n      return n.add(e), () => n.delete(e);\n    },\n    dispatch(e) {\n      for (var _len = arguments.length, s = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        s[_key - 1] = arguments[_key];\n      }\n      let i = r[e].call(t, ...s);\n      i && (t = i, n.forEach(c => c()));\n    }\n  };\n}\nexport { a as createStore };", "map": {"version": 3, "names": ["a", "o", "r", "t", "n", "Set", "getSnapshot", "subscribe", "e", "add", "delete", "dispatch", "_len", "arguments", "length", "s", "Array", "_key", "i", "call", "for<PERSON>ach", "c", "createStore"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/store.js"], "sourcesContent": ["function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACF,CAAC,CAAC,CAAC;IAACG,CAAC,GAAC,IAAIC,GAAG,CAAD,CAAC;EAAC,OAAM;IAACC,WAAWA,CAAA,EAAE;MAAC,OAAOH,CAAC;IAAA,CAAC;IAACI,SAASA,CAACC,CAAC,EAAC;MAAC,OAAOJ,CAAC,CAACK,GAAG,CAACD,CAAC,CAAC,EAAC,MAAIJ,CAAC,CAACM,MAAM,CAACF,CAAC,CAAC;IAAA,CAAC;IAACG,QAAQA,CAACH,CAAC,EAAM;MAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAFC,CAAC,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAADF,CAAC,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAAE,IAAIC,CAAC,GAAChB,CAAC,CAACM,CAAC,CAAC,CAACW,IAAI,CAAChB,CAAC,EAAC,GAAGY,CAAC,CAAC;MAACG,CAAC,KAAGf,CAAC,GAACe,CAAC,EAACd,CAAC,CAACgB,OAAO,CAACC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOrB,CAAC,IAAIsB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}