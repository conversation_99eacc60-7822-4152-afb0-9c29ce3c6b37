{"ast": null, "code": "import { useEvent as $e9faafb641e167db$export$90fc3a17d93f704c } from \"./useEvent.mjs\";\nimport { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useRef as $hDRkU$useRef, useCallback as $hDRkU$useCallback } from \"react\";\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $26f7f3da73fcd9d6$export$7717c92ee915373e(props, ref) {\n  let {\n    isLoading: isLoading,\n    onLoadMore: onLoadMore,\n    scrollOffset = 1,\n    items: items\n  } = props;\n  // Handle scrolling, and call onLoadMore when nearing the bottom.\n  let isLoadingRef = (0, $hDRkU$useRef)(isLoading);\n  let prevProps = (0, $hDRkU$useRef)(props);\n  let onScroll = (0, $hDRkU$useCallback)(() => {\n    if (ref.current && !isLoadingRef.current && onLoadMore) {\n      let shouldLoadMore = ref.current.scrollHeight - ref.current.scrollTop - ref.current.clientHeight < ref.current.clientHeight * scrollOffset;\n      if (shouldLoadMore) {\n        isLoadingRef.current = true;\n        onLoadMore();\n      }\n    }\n  }, [onLoadMore, ref, scrollOffset]);\n  let lastItems = (0, $hDRkU$useRef)(items);\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    // Only update isLoadingRef if props object actually changed,\n    // not if a local state change occurred.\n    if (props !== prevProps.current) {\n      isLoadingRef.current = isLoading;\n      prevProps.current = props;\n    }\n    // TODO: Eventually this hook will move back into RAC during which we will accept the collection as a option to this hook.\n    // We will only load more if the collection has changed after the last load to prevent multiple onLoadMore from being called\n    // while the data from the last onLoadMore is being processed by RAC collection.\n    let shouldLoadMore = (ref === null || ref === void 0 ? void 0 : ref.current) && !isLoadingRef.current && onLoadMore && (!items || items !== lastItems.current) && ref.current.clientHeight === ref.current.scrollHeight;\n    if (shouldLoadMore) {\n      isLoadingRef.current = true;\n      onLoadMore === null || onLoadMore === void 0 ? void 0 : onLoadMore();\n    }\n    lastItems.current = items;\n  }, [isLoading, onLoadMore, props, ref, items]);\n  // TODO: maybe this should still just return scroll props?\n  // Test against case where the ref isn't defined when this is called\n  // Think this was a problem when trying to attach to the scrollable body of the table in OnLoadMoreTableBodyScroll\n  (0, $e9faafb641e167db$export$90fc3a17d93f704c)(ref, 'scroll', onScroll);\n}\nexport { $26f7f3da73fcd9d6$export$7717c92ee915373e as useLoadMore };", "map": {"version": 3, "names": ["$26f7f3da73fcd9d6$export$7717c92ee915373e", "props", "ref", "isLoading", "onLoadMore", "scrollOffset", "items", "isLoadingRef", "$hDRkU$useRef", "prevProps", "onScroll", "$hDRkU$useCallback", "current", "shouldLoadMore", "scrollHeight", "scrollTop", "clientHeight", "lastItems", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "$e9faafb641e167db$export$90fc3a17d93f704c"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useLoadMore.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject, useCallback, useRef} from 'react';\nimport {useEvent} from './useEvent';\n\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport interface LoadMoreProps {\n  /** Whether data is currently being loaded. */\n  isLoading?: boolean,\n  /** Handler that is called when more items should be loaded, e.g. while scrolling near the bottom.  */\n  onLoadMore?: () => void,\n  /**\n   * The amount of offset from the bottom of your scrollable region that should trigger load more.\n   * Uses a percentage value relative to the scroll body's client height. Load more is then triggered\n   * when your current scroll position's distance from the bottom of the currently loaded list of items is less than\n   * or equal to the provided value. (e.g. 1 = 100% of the scroll region's height).\n   * @default 1\n   */\n  scrollOffset?: number,\n  /** The data currently loaded. */\n  items?: any\n}\n\nexport function useLoadMore(props: LoadMoreProps, ref: RefObject<HTMLElement | null>): void {\n  let {isLoading, onLoadMore, scrollOffset = 1, items} = props;\n\n  // Handle scrolling, and call onLoadMore when nearing the bottom.\n  let isLoadingRef = useRef(isLoading);\n  let prevProps = useRef(props);\n  let onScroll = useCallback(() => {\n    if (ref.current && !isLoadingRef.current && onLoadMore) {\n      let shouldLoadMore = ref.current.scrollHeight - ref.current.scrollTop - ref.current.clientHeight < ref.current.clientHeight * scrollOffset;\n\n      if (shouldLoadMore) {\n        isLoadingRef.current = true;\n        onLoadMore();\n      }\n    }\n  }, [onLoadMore, ref, scrollOffset]);\n\n  let lastItems = useRef(items);\n  useLayoutEffect(() => {\n    // Only update isLoadingRef if props object actually changed,\n    // not if a local state change occurred.\n    if (props !== prevProps.current) {\n      isLoadingRef.current = isLoading;\n      prevProps.current = props;\n    }\n\n    // TODO: Eventually this hook will move back into RAC during which we will accept the collection as a option to this hook.\n    // We will only load more if the collection has changed after the last load to prevent multiple onLoadMore from being called\n    // while the data from the last onLoadMore is being processed by RAC collection.\n    let shouldLoadMore = ref?.current\n      && !isLoadingRef.current\n      && onLoadMore\n      && (!items || items !== lastItems.current)\n      && ref.current.clientHeight === ref.current.scrollHeight;\n\n    if (shouldLoadMore) {\n      isLoadingRef.current = true;\n      onLoadMore?.();\n    }\n\n    lastItems.current = items;\n  }, [isLoading, onLoadMore, props, ref, items]);\n\n  // TODO: maybe this should still just return scroll props?\n  // Test against case where the ref isn't defined when this is called\n  // Think this was a problem when trying to attach to the scrollable body of the table in OnLoadMoreTableBodyScroll\n  useEvent(ref, 'scroll', onScroll);\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAkCO,SAASA,0CAAYC,KAAoB,EAAEC,GAAkC;EAClF,IAAI;IAAAC,SAAA,EAACA,SAAS;IAAAC,UAAA,EAAEA,UAAU;IAAEC,YAAA,GAAe;IAAAC,KAAA,EAAGA;EAAK,CAAC,GAAGL,KAAA;EAEvD;EACA,IAAIM,YAAA,GAAe,IAAAC,aAAK,EAAEL,SAAA;EAC1B,IAAIM,SAAA,GAAY,IAAAD,aAAK,EAAEP,KAAA;EACvB,IAAIS,QAAA,GAAW,IAAAC,kBAAU,EAAE;IACzB,IAAIT,GAAA,CAAIU,OAAO,IAAI,CAACL,YAAA,CAAaK,OAAO,IAAIR,UAAA,EAAY;MACtD,IAAIS,cAAA,GAAiBX,GAAA,CAAIU,OAAO,CAACE,YAAY,GAAGZ,GAAA,CAAIU,OAAO,CAACG,SAAS,GAAGb,GAAA,CAAIU,OAAO,CAACI,YAAY,GAAGd,GAAA,CAAIU,OAAO,CAACI,YAAY,GAAGX,YAAA;MAE9H,IAAIQ,cAAA,EAAgB;QAClBN,YAAA,CAAaK,OAAO,GAAG;QACvBR,UAAA;MACF;IACF;EACF,GAAG,CAACA,UAAA,EAAYF,GAAA,EAAKG,YAAA,CAAa;EAElC,IAAIY,SAAA,GAAY,IAAAT,aAAK,EAAEF,KAAA;EACvB,IAAAY,yCAAc,EAAE;IACd;IACA;IACA,IAAIjB,KAAA,KAAUQ,SAAA,CAAUG,OAAO,EAAE;MAC/BL,YAAA,CAAaK,OAAO,GAAGT,SAAA;MACvBM,SAAA,CAAUG,OAAO,GAAGX,KAAA;IACtB;IAEA;IACA;IACA;IACA,IAAIY,cAAA,GAAiB,CAAAX,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAKU,OAAO,KAC5B,CAACL,YAAA,CAAaK,OAAO,IACrBR,UAAA,KACC,CAACE,KAAA,IAASA,KAAA,KAAUW,SAAA,CAAUL,OAAO,CAAD,IACrCV,GAAA,CAAIU,OAAO,CAACI,YAAY,KAAKd,GAAA,CAAIU,OAAO,CAACE,YAAY;IAE1D,IAAID,cAAA,EAAgB;MAClBN,YAAA,CAAaK,OAAO,GAAG;MACvBR,UAAA,aAAAA,UAAA,uBAAAA,UAAA;IACF;IAEAa,SAAA,CAAUL,OAAO,GAAGN,KAAA;EACtB,GAAG,CAACH,SAAA,EAAWC,UAAA,EAAYH,KAAA,EAAOC,GAAA,EAAKI,KAAA,CAAM;EAE7C;EACA;EACA;EACA,IAAAa,yCAAO,EAAEjB,GAAA,EAAK,UAAUQ,QAAA;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}