{"ast": null, "code": "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */let $f4e2df6bd15f8569$var$_tableNestedRows = false;\nlet $f4e2df6bd15f8569$var$_shadowDOM = false;\nfunction $f4e2df6bd15f8569$export$d9d8a0f82de49530() {\n  $f4e2df6bd15f8569$var$_tableNestedRows = true;\n}\nfunction $f4e2df6bd15f8569$export$1b00cb14a96194e6() {\n  return $f4e2df6bd15f8569$var$_tableNestedRows;\n}\nfunction $f4e2df6bd15f8569$export$12b151d9882e9985() {\n  $f4e2df6bd15f8569$var$_shadowDOM = true;\n}\nfunction $f4e2df6bd15f8569$export$98658e8c59125e6a() {\n  return $f4e2df6bd15f8569$var$_shadowDOM;\n}\nexport { $f4e2df6bd15f8569$export$d9d8a0f82de49530 as enableTableNestedRows, $f4e2df6bd15f8569$export$1b00cb14a96194e6 as tableNestedRows, $f4e2df6bd15f8569$export$12b151d9882e9985 as enableShadowDOM, $f4e2df6bd15f8569$export$98658e8c59125e6a as shadowDOM };", "map": {"version": 3, "names": ["$f4e2df6bd15f8569$var$_tableNestedRows", "$f4e2df6bd15f8569$var$_shadowDOM", "$f4e2df6bd15f8569$export$d9d8a0f82de49530", "$f4e2df6bd15f8569$export$1b00cb14a96194e6", "$f4e2df6bd15f8569$export$12b151d9882e9985", "$f4e2df6bd15f8569$export$98658e8c59125e6a"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-stately\\flags\\dist\\packages\\@react-stately\\flags\\src\\index.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAYA,IAAIA,sCAAA,GAAmB;AACvB,IAAIC,gCAAA,GAAa;AAEV,SAASC,0CAAA;EACdF,sCAAA,GAAmB;AACrB;AAEO,SAASG,0CAAA;EACd,OAAOH,sCAAA;AACT;AAEO,SAASI,0CAAA;EACdH,gCAAA,GAAa;AACf;AAEO,SAASI,0CAAA;EACd,OAAOJ,gCAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}