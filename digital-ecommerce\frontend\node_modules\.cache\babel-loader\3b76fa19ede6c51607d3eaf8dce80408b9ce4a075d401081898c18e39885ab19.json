{"ast": null, "code": "import { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { flushSync as $jJMAe$flushSync } from \"react-dom\";\nimport { useState as $jJMAe$useState, useCallback as $jJMAe$useCallback } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $d3f049242431219c$export$6d3443f2c48bfc20(ref) {\n  let isReady = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  let [isEntering, setEntering] = (0, $jJMAe$useState)(true);\n  let isAnimationReady = isEntering && isReady;\n  // There are two cases for entry animations:\n  // 1. CSS @keyframes. The `animation` property is set during the isEntering state, and it is removed after the animation finishes.\n  // 2. CSS transitions. The initial styles are applied during the isEntering state, and removed immediately, causing the transition to occur.\n  //\n  // In the second case, cancel any transitions that were triggered prior to the isEntering = false state (when the transition is supposed to start).\n  // This can happen when isReady starts as false (e.g. popovers prior to placement calculation).\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    if (isAnimationReady && ref.current && 'getAnimations' in ref.current) {\n      for (let animation of ref.current.getAnimations()) if (animation instanceof CSSTransition) animation.cancel();\n    }\n  }, [ref, isAnimationReady]);\n  $d3f049242431219c$var$useAnimation(ref, isAnimationReady, (0, $jJMAe$useCallback)(() => setEntering(false), []));\n  return isAnimationReady;\n}\nfunction $d3f049242431219c$export$45fda7c47f93fd48(ref, isOpen) {\n  let [exitState, setExitState] = (0, $jJMAe$useState)(isOpen ? 'open' : 'closed');\n  switch (exitState) {\n    case 'open':\n      // If isOpen becomes false, set the state to exiting.\n      if (!isOpen) setExitState('exiting');\n      break;\n    case 'closed':\n    case 'exiting':\n      // If we are exiting and isOpen becomes true, the animation was interrupted.\n      // Reset the state to open.\n      if (isOpen) setExitState('open');\n      break;\n  }\n  let isExiting = exitState === 'exiting';\n  $d3f049242431219c$var$useAnimation(ref, isExiting, (0, $jJMAe$useCallback)(() => {\n    // Set the state to closed, which will cause the element to be unmounted.\n    setExitState(state => state === 'exiting' ? 'closed' : state);\n  }, []));\n  return isExiting;\n}\nfunction $d3f049242431219c$var$useAnimation(ref, isActive, onEnd) {\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    if (isActive && ref.current) {\n      if (!('getAnimations' in ref.current)) {\n        // JSDOM\n        onEnd();\n        return;\n      }\n      let animations = ref.current.getAnimations();\n      if (animations.length === 0) {\n        onEnd();\n        return;\n      }\n      let canceled = false;\n      Promise.all(animations.map(a => a.finished)).then(() => {\n        if (!canceled) (0, $jJMAe$flushSync)(() => {\n          onEnd();\n        });\n      }).catch(() => {});\n      return () => {\n        canceled = true;\n      };\n    }\n  }, [ref, isActive, onEnd]);\n}\nexport { $d3f049242431219c$export$6d3443f2c48bfc20 as useEnterAnimation, $d3f049242431219c$export$45fda7c47f93fd48 as useExitAnimation };", "map": {"version": 3, "names": ["$d3f049242431219c$export$6d3443f2c48bfc20", "ref", "isReady", "arguments", "length", "undefined", "isEntering", "setEntering", "$jJMAe$useState", "isAnimationReady", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "current", "animation", "getAnimations", "CSSTransition", "cancel", "$d3f049242431219c$var$useAnimation", "$jJMAe$useCallback", "$d3f049242431219c$export$45fda7c47f93fd48", "isOpen", "exitState", "setExitState", "isExiting", "state", "isActive", "onEnd", "animations", "canceled", "Promise", "all", "map", "a", "finished", "then", "$jJMAe$flushSync", "catch"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\animation.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {flushSync} from 'react-dom';\nimport {RefObject, useCallback, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEnterAnimation(ref: RefObject<HTMLElement | null>, isReady: boolean = true): boolean {\n  let [isEntering, setEntering] = useState(true);\n  let isAnimationReady = isEntering && isReady;\n\n  // There are two cases for entry animations:\n  // 1. CSS @keyframes. The `animation` property is set during the isEntering state, and it is removed after the animation finishes.\n  // 2. CSS transitions. The initial styles are applied during the isEntering state, and removed immediately, causing the transition to occur.\n  //\n  // In the second case, cancel any transitions that were triggered prior to the isEntering = false state (when the transition is supposed to start).\n  // This can happen when isReady starts as false (e.g. popovers prior to placement calculation).\n  useLayoutEffect(() => {\n    if (isAnimationReady && ref.current && 'getAnimations' in ref.current) {\n      for (let animation of ref.current.getAnimations()) {\n        if (animation instanceof CSSTransition) {\n          animation.cancel();\n        }\n      }\n    }\n  }, [ref, isAnimationReady]);\n\n  useAnimation(ref, isAnimationReady, useCallback(() => setEntering(false), []));\n  return isAnimationReady;\n}\n\nexport function useExitAnimation(ref: RefObject<HTMLElement | null>, isOpen: boolean): boolean {\n  let [exitState, setExitState] = useState<'closed' | 'open' | 'exiting'>(isOpen ? 'open' : 'closed');\n\n  switch (exitState) {\n    case 'open':\n      // If isOpen becomes false, set the state to exiting.\n      if (!isOpen) {\n        setExitState('exiting');\n      }\n      break;\n    case 'closed':\n    case 'exiting':\n      // If we are exiting and isOpen becomes true, the animation was interrupted.\n      // Reset the state to open.\n      if (isOpen) {\n        setExitState('open');\n      }\n      break;\n  }\n\n  let isExiting = exitState === 'exiting';\n  useAnimation(\n    ref,\n    isExiting,\n    useCallback(() => {\n      // Set the state to closed, which will cause the element to be unmounted.\n      setExitState(state => state === 'exiting' ? 'closed' : state);\n    }, [])\n  );\n\n  return isExiting;\n}\n\nfunction useAnimation(ref: RefObject<HTMLElement | null>, isActive: boolean, onEnd: () => void): void {\n  useLayoutEffect(() => {\n    if (isActive && ref.current) {\n      if (!('getAnimations' in ref.current)) {\n        // JSDOM\n        onEnd();\n        return;\n      }\n\n      let animations = ref.current.getAnimations();\n      if (animations.length === 0) {\n        onEnd();\n        return;\n      }\n\n      let canceled = false;\n      Promise.all(animations.map(a => a.finished)).then(() => {\n        if (!canceled) {\n          flushSync(() => {\n            onEnd();\n          });\n        }\n      }).catch(() => {});\n\n      return () => {\n        canceled = true;\n      };\n    }\n  }, [ref, isActive, onEnd]);\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAgBO,SAASA,0CAAkBC,GAAkC,EAAyB;EAAA,IAAvBC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAmB,IAAI;EAC3F,IAAI,CAACG,UAAA,EAAYC,WAAA,CAAY,GAAG,IAAAC,eAAO,EAAE;EACzC,IAAIC,gBAAA,GAAmBH,UAAA,IAAcJ,OAAA;EAErC;EACA;EACA;EACA;EACA;EACA;EACA,IAAAQ,yCAAc,EAAE;IACd,IAAID,gBAAA,IAAoBR,GAAA,CAAIU,OAAO,IAAI,mBAAmBV,GAAA,CAAIU,OAAO,EAAE;MACrE,KAAK,IAAIC,SAAA,IAAaX,GAAA,CAAIU,OAAO,CAACE,aAAa,IAC7C,IAAID,SAAA,YAAqBE,aAAA,EACvBF,SAAA,CAAUG,MAAM;IAGtB;EACF,GAAG,CAACd,GAAA,EAAKQ,gBAAA,CAAiB;EAE1BO,kCAAA,CAAaf,GAAA,EAAKQ,gBAAA,EAAkB,IAAAQ,kBAAU,EAAE,MAAMV,WAAA,CAAY,QAAQ,EAAE;EAC5E,OAAOE,gBAAA;AACT;AAEO,SAASS,0CAAiBjB,GAAkC,EAAEkB,MAAe;EAClF,IAAI,CAACC,SAAA,EAAWC,YAAA,CAAa,GAAG,IAAAb,eAAO,EAAiCW,MAAA,GAAS,SAAS;EAE1F,QAAQC,SAAA;IACN,KAAK;MACH;MACA,IAAI,CAACD,MAAA,EACHE,YAAA,CAAa;MAEf;IACF,KAAK;IACL,KAAK;MACH;MACA;MACA,IAAIF,MAAA,EACFE,YAAA,CAAa;MAEf;EACJ;EAEA,IAAIC,SAAA,GAAYF,SAAA,KAAc;EAC9BJ,kCAAA,CACEf,GAAA,EACAqB,SAAA,EACA,IAAAL,kBAAU,EAAE;IACV;IACAI,YAAA,CAAaE,KAAA,IAASA,KAAA,KAAU,YAAY,WAAWA,KAAA;EACzD,GAAG,EAAE;EAGP,OAAOD,SAAA;AACT;AAEA,SAASN,mCAAaf,GAAkC,EAAEuB,QAAiB,EAAEC,KAAiB;EAC5F,IAAAf,yCAAc,EAAE;IACd,IAAIc,QAAA,IAAYvB,GAAA,CAAIU,OAAO,EAAE;MAC3B,IAAI,EAAE,mBAAmBV,GAAA,CAAIU,OAAO,CAAD,EAAI;QACrC;QACAc,KAAA;QACA;MACF;MAEA,IAAIC,UAAA,GAAazB,GAAA,CAAIU,OAAO,CAACE,aAAa;MAC1C,IAAIa,UAAA,CAAWtB,MAAM,KAAK,GAAG;QAC3BqB,KAAA;QACA;MACF;MAEA,IAAIE,QAAA,GAAW;MACfC,OAAA,CAAQC,GAAG,CAACH,UAAA,CAAWI,GAAG,CAACC,CAAA,IAAKA,CAAA,CAAEC,QAAQ,GAAGC,IAAI,CAAC;QAChD,IAAI,CAACN,QAAA,EACH,IAAAO,gBAAQ,EAAE;UACRT,KAAA;QACF;MAEJ,GAAGU,KAAK,CAAC,OAAO;MAEhB,OAAO;QACLR,QAAA,GAAW;MACb;IACF;EACF,GAAG,CAAC1B,GAAA,EAAKuB,QAAA,EAAUC,KAAA,CAAM;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}