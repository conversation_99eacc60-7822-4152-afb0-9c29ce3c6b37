import React, { useState } from 'react';
import { GlobeAltIcon, CheckIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

const languages = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸'
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷'
  },
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪'
  },
  {
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    flag: '🇮🇹'
  },
  {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇵🇹'
  },
  {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺'
  },
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳'
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵'
  },
  {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷'
  }
];

const MultiLanguageSupport = () => {
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredLanguages = languages.filter(lang =>
    lang.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lang.nativeName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLanguageSelect = (language) => {
    setSelectedLanguage(language);
    setIsOpen(false);
    setSearchTerm('');
    // Here you would typically trigger a language change in your app
    console.log('Language changed to:', language.code);
  };

  return (
    <div className="relative">
      {/* Language Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-light-orange-100 text-light-orange-700 rounded-lg hover:bg-light-orange-200 transition-colors shadow-md"
      >
        <GlobeAltIcon className="w-5 h-5" />
        <span className="text-2xl">{selectedLanguage.flag}</span>
        <span className="font-medium hidden sm:block">{selectedLanguage.name}</span>
        <ChevronDownIcon className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Language Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-4 py-3 rounded-t-xl">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white flex items-center">
                <GlobeAltIcon className="w-5 h-5 mr-2" />
                Select Language
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-light-orange-200 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="p-4 border-b border-light-orange-100">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 text-light-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search languages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors text-sm"
              />
            </div>
          </div>

          {/* Language List */}
          <div className="max-h-64 overflow-y-auto">
            {filteredLanguages.length === 0 ? (
              <div className="p-4 text-center text-light-orange-600">
                <GlobeAltIcon className="w-8 h-8 mx-auto mb-2 text-light-orange-300" />
                <p>No languages found</p>
              </div>
            ) : (
              <div className="py-2">
                {filteredLanguages.map((language) => (
                  <button
                    key={language.code}
                    onClick={() => handleLanguageSelect(language)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 hover:bg-light-orange-50 transition-colors ${
                      selectedLanguage.code === language.code ? 'bg-light-orange-100' : ''
                    }`}
                  >
                    <span className="text-2xl">{language.flag}</span>
                    <div className="flex-1 text-left">
                      <div className="font-medium text-light-orange-800">{language.name}</div>
                      <div className="text-sm text-light-orange-600">{language.nativeName}</div>
                    </div>
                    {selectedLanguage.code === language.code && (
                      <CheckIcon className="w-5 h-5 text-light-orange-500" />
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-light-orange-100 bg-light-orange-50">
            <div className="flex items-center justify-between text-sm">
              <span className="text-light-orange-600">
                {filteredLanguages.length} language{filteredLanguages.length !== 1 ? 's' : ''} available
              </span>
              <div className="flex items-center space-x-1 text-light-orange-500">
                <span>Powered by</span>
                <GlobeAltIcon className="w-4 h-4" />
                <span className="font-medium">Translation API</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Language Change Indicator */}
      <div className="absolute -top-1 -right-1">
        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
      </div>
    </div>
  );
};

export default MultiLanguageSupport;
