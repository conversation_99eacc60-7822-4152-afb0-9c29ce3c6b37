{"ast": null, "code": "\"use client\";\n\nimport o, { use<PERSON>emo as b } from \"react\";\nimport { useResolvedTag as E } from '../../hooks/use-resolved-tag.js';\nimport { useSyncRefs as P } from '../../hooks/use-sync-refs.js';\nimport { Disabled<PERSON>rovider as u, useDisabled as g } from '../../internal/disabled.js';\nimport { forwardRefWithAs as D, useRender as A } from '../../utils/render.js';\nimport { useLabels as L } from '../label/label.js';\nlet d = \"fieldset\";\nfunction _(t, a) {\n  var s;\n  let i = g(),\n    {\n      disabled: e = i || !1,\n      ...p\n    } = t,\n    [n, T] = E((s = t.as) != null ? s : d),\n    l = P(a, T),\n    [r, f] = L(),\n    m = b(() => ({\n      disabled: e\n    }), [e]),\n    y = n === \"fieldset\" ? {\n      ref: l,\n      \"aria-labelledby\": r,\n      disabled: e || void 0\n    } : {\n      ref: l,\n      role: \"group\",\n      \"aria-labelledby\": r,\n      \"aria-disabled\": e || void 0\n    },\n    F = A();\n  return o.createElement(u, {\n    value: e\n  }, o.createElement(f, null, F({\n    ourProps: y,\n    theirProps: p,\n    slot: m,\n    defaultTag: d,\n    name: \"Fieldset\"\n  })));\n}\nlet G = D(_);\nexport { G as Fieldset };", "map": {"version": 3, "names": ["o", "useMemo", "b", "useResolvedTag", "E", "useSyncRefs", "P", "Disable<PERSON><PERSON><PERSON><PERSON>", "u", "useDisabled", "g", "forwardRefWithAs", "D", "useRender", "A", "useLabels", "L", "d", "_", "t", "a", "s", "i", "disabled", "e", "p", "n", "T", "as", "l", "r", "f", "m", "y", "ref", "role", "F", "createElement", "value", "ourProps", "theirProps", "slot", "defaultTag", "name", "G", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/fieldset/fieldset.js"], "sourcesContent": ["\"use client\";import o,{use<PERSON>emo as b}from\"react\";import{useResolvedTag as E}from'../../hooks/use-resolved-tag.js';import{useSyncRefs as P}from'../../hooks/use-sync-refs.js';import{Disabled<PERSON>rovider as u,useDisabled as g}from'../../internal/disabled.js';import{forwardRefWithAs as D,useRender as A}from'../../utils/render.js';import{useLabels as L}from'../label/label.js';let d=\"fieldset\";function _(t,a){var s;let i=g(),{disabled:e=i||!1,...p}=t,[n,T]=E((s=t.as)!=null?s:d),l=P(a,T),[r,f]=L(),m=b(()=>({disabled:e}),[e]),y=n===\"fieldset\"?{ref:l,\"aria-labelledby\":r,disabled:e||void 0}:{ref:l,role:\"group\",\"aria-labelledby\":r,\"aria-disabled\":e||void 0},F=A();return o.createElement(u,{value:e},o.createElement(f,null,F({ourProps:y,theirProps:p,slot:m,defaultTag:d,name:\"Fieldset\"})))}let G=D(_);export{G as Fieldset};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAOA,CAAC,IAAEC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAAC,UAAU;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,IAAIC,CAAC,GAACZ,CAAC,CAAC,CAAC;IAAC;MAACa,QAAQ,EAACC,CAAC,GAACF,CAAC,IAAE,CAAC,CAAC;MAAC,GAAGG;IAAC,CAAC,GAACN,CAAC;IAAC,CAACO,CAAC,EAACC,CAAC,CAAC,GAACvB,CAAC,CAAC,CAACiB,CAAC,GAACF,CAAC,CAACS,EAAE,KAAG,IAAI,GAACP,CAAC,GAACJ,CAAC,CAAC;IAACY,CAAC,GAACvB,CAAC,CAACc,CAAC,EAACO,CAAC,CAAC;IAAC,CAACG,CAAC,EAACC,CAAC,CAAC,GAACf,CAAC,CAAC,CAAC;IAACgB,CAAC,GAAC9B,CAAC,CAAC,OAAK;MAACqB,QAAQ,EAACC;IAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;IAACS,CAAC,GAACP,CAAC,KAAG,UAAU,GAAC;MAACQ,GAAG,EAACL,CAAC;MAAC,iBAAiB,EAACC,CAAC;MAACP,QAAQ,EAACC,CAAC,IAAE,KAAK;IAAC,CAAC,GAAC;MAACU,GAAG,EAACL,CAAC;MAACM,IAAI,EAAC,OAAO;MAAC,iBAAiB,EAACL,CAAC;MAAC,eAAe,EAACN,CAAC,IAAE,KAAK;IAAC,CAAC;IAACY,CAAC,GAACtB,CAAC,CAAC,CAAC;EAAC,OAAOd,CAAC,CAACqC,aAAa,CAAC7B,CAAC,EAAC;IAAC8B,KAAK,EAACd;EAAC,CAAC,EAACxB,CAAC,CAACqC,aAAa,CAACN,CAAC,EAAC,IAAI,EAACK,CAAC,CAAC;IAACG,QAAQ,EAACN,CAAC;IAACO,UAAU,EAACf,CAAC;IAACgB,IAAI,EAACT,CAAC;IAACU,UAAU,EAACzB,CAAC;IAAC0B,IAAI,EAAC;EAAU,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAChC,CAAC,CAACM,CAAC,CAAC;AAAC,SAAO0B,CAAC,IAAIC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}