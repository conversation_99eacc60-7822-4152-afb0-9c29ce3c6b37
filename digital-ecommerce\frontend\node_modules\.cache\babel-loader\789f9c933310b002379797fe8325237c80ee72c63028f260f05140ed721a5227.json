{"ast": null, "code": "import { useEffectEvent as $8ae05eaa5c114e9c$export$7f54fc3180508a52 } from \"./useEffectEvent.mjs\";\nimport { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useState as $fCAlL$useState, useRef as $fCAlL$useRef } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $1dbecbe27a04f9af$export$14d238f342723f25(defaultValue) {\n  let [value, setValue] = (0, $fCAlL$useState)(defaultValue);\n  let effect = (0, $fCAlL$useRef)(null);\n  // Store the function in a ref so we can always access the current version\n  // which has the proper `value` in scope.\n  let nextRef = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(() => {\n    if (!effect.current) return;\n    // Run the generator to the next yield.\n    let newValue = effect.current.next();\n    // If the generator is done, reset the effect.\n    if (newValue.done) {\n      effect.current = null;\n      return;\n    }\n    // If the value is the same as the current value,\n    // then continue to the next yield. Otherwise,\n    // set the value in state and wait for the next layout effect.\n    if (value === newValue.value) nextRef();else setValue(newValue.value);\n  });\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    // If there is an effect currently running, continue to the next yield.\n    if (effect.current) nextRef();\n  });\n  let queue = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(fn => {\n    effect.current = fn(value);\n    nextRef();\n  });\n  return [value, queue];\n}\nexport { $1dbecbe27a04f9af$export$14d238f342723f25 as useValueEffect };", "map": {"version": 3, "names": ["$1dbecbe27a04f9af$export$14d238f342723f25", "defaultValue", "value", "setValue", "$fCAlL$useState", "effect", "$fCAlL$useRef", "nextRef", "$8ae05eaa5c114e9c$export$7f54fc3180508a52", "current", "newValue", "next", "done", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "queue", "fn"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useValueEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Dispatch, MutableRefObject, useRef, useState} from 'react';\nimport {useEffectEvent, useLayoutEffect} from './';\n\ntype SetValueAction<S> = (prev: S) => Generator<any, void, unknown>;\n\n// This hook works like `useState`, but when setting the value, you pass a generator function\n// that can yield multiple values. Each yielded value updates the state and waits for the next\n// layout effect, then continues the generator. This allows sequential updates to state to be\n// written linearly.\nexport function useValueEffect<S>(defaultValue: S | (() => S)): [S, Dispatch<SetValueAction<S>>] {\n  let [value, setValue] = useState(defaultValue);\n  let effect: MutableRefObject<Generator<S> | null> = useRef<Generator<S> | null>(null);\n\n  // Store the function in a ref so we can always access the current version\n  // which has the proper `value` in scope.\n  let nextRef = useEffectEvent(() => {\n    if (!effect.current) {\n      return;\n    }\n    // Run the generator to the next yield.\n    let newValue = effect.current.next();\n\n    // If the generator is done, reset the effect.\n    if (newValue.done) {\n      effect.current = null;\n      return;\n    }\n\n    // If the value is the same as the current value,\n    // then continue to the next yield. Otherwise,\n    // set the value in state and wait for the next layout effect.\n    if (value === newValue.value) {\n      nextRef();\n    } else {\n      setValue(newValue.value);\n    }\n  });\n\n  useLayoutEffect(() => {\n    // If there is an effect currently running, continue to the next yield.\n    if (effect.current) {\n      nextRef();\n    }\n  });\n\n  let queue = useEffectEvent(fn => {\n    effect.current = fn(value);\n    nextRef();\n  });\n\n  return [value, queue];\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAqBO,SAASA,0CAAkBC,YAA2B;EAC3D,IAAI,CAACC,KAAA,EAAOC,QAAA,CAAS,GAAG,IAAAC,eAAO,EAAEH,YAAA;EACjC,IAAII,MAAA,GAAgD,IAAAC,aAAK,EAAuB;EAEhF;EACA;EACA,IAAIC,OAAA,GAAU,IAAAC,yCAAa,EAAE;IAC3B,IAAI,CAACH,MAAA,CAAOI,OAAO,EACjB;IAEF;IACA,IAAIC,QAAA,GAAWL,MAAA,CAAOI,OAAO,CAACE,IAAI;IAElC;IACA,IAAID,QAAA,CAASE,IAAI,EAAE;MACjBP,MAAA,CAAOI,OAAO,GAAG;MACjB;IACF;IAEA;IACA;IACA;IACA,IAAIP,KAAA,KAAUQ,QAAA,CAASR,KAAK,EAC1BK,OAAA,QAEAJ,QAAA,CAASO,QAAA,CAASR,KAAK;EAE3B;EAEA,IAAAW,yCAAc,EAAE;IACd;IACA,IAAIR,MAAA,CAAOI,OAAO,EAChBF,OAAA;EAEJ;EAEA,IAAIO,KAAA,GAAQ,IAAAN,yCAAa,EAAEO,EAAA;IACzBV,MAAA,CAAOI,OAAO,GAAGM,EAAA,CAAGb,KAAA;IACpBK,OAAA;EACF;EAEA,OAAO,CAACL,KAAA,EAAOY,KAAA,CAAM;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}