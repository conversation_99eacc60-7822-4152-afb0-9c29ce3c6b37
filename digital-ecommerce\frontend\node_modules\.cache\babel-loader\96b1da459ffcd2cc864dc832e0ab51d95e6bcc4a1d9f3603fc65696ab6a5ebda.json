{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */function $7215afc6de606d6b$export$de79e2c695e052f3(element) {\n  if ($7215afc6de606d6b$var$supportsPreventScroll()) element.focus({\n    preventScroll: true\n  });else {\n    let scrollableElements = $7215afc6de606d6b$var$getScrollableElements(element);\n    element.focus();\n    $7215afc6de606d6b$var$restoreScrollPosition(scrollableElements);\n  }\n}\nlet $7215afc6de606d6b$var$supportsPreventScrollCached = null;\nfunction $7215afc6de606d6b$var$supportsPreventScroll() {\n  if ($7215afc6de606d6b$var$supportsPreventScrollCached == null) {\n    $7215afc6de606d6b$var$supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          $7215afc6de606d6b$var$supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch (_unused) {\n      // Ignore\n    }\n  }\n  return $7215afc6de606d6b$var$supportsPreventScrollCached;\n}\nfunction $7215afc6de606d6b$var$getScrollableElements(element) {\n  let parent = element.parentNode;\n  let scrollableElements = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (parent.offsetHeight < parent.scrollHeight || parent.offsetWidth < parent.scrollWidth) scrollableElements.push({\n      element: parent,\n      scrollTop: parent.scrollTop,\n      scrollLeft: parent.scrollLeft\n    });\n    parent = parent.parentNode;\n  }\n  if (rootScrollingElement instanceof HTMLElement) scrollableElements.push({\n    element: rootScrollingElement,\n    scrollTop: rootScrollingElement.scrollTop,\n    scrollLeft: rootScrollingElement.scrollLeft\n  });\n  return scrollableElements;\n}\nfunction $7215afc6de606d6b$var$restoreScrollPosition(scrollableElements) {\n  for (let {\n    element: element,\n    scrollTop: scrollTop,\n    scrollLeft: scrollLeft\n  } of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\nexport { $7215afc6de606d6b$export$de79e2c695e052f3 as focusWithoutScrolling };", "map": {"version": 3, "names": ["$7215afc6de606d6b$export$de79e2c695e052f3", "element", "$7215afc6de606d6b$var$supportsPreventScroll", "focus", "preventScroll", "scrollableElements", "$7215afc6de606d6b$var$getScrollableElements", "$7215afc6de606d6b$var$restoreScrollPosition", "$7215afc6de606d6b$var$supportsPreventScrollCached", "focusElem", "document", "createElement", "_unused", "parent", "parentNode", "rootScrollingElement", "scrollingElement", "documentElement", "HTMLElement", "offsetHeight", "scrollHeight", "offsetWidth", "scrollWidth", "push", "scrollTop", "scrollLeft"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\focusWithoutScrolling.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;GA8BO,SAASA,0CAAsBC,OAAyB;EAC7D,IAAIC,2CAAA,IACFD,OAAA,CAAQE,KAAK,CAAC;IAACC,aAAA,EAAe;EAAI,QAC7B;IACL,IAAIC,kBAAA,GAAqBC,2CAAA,CAAsBL,OAAA;IAC/CA,OAAA,CAAQE,KAAK;IACbI,2CAAA,CAAsBF,kBAAA;EACxB;AACF;AAEA,IAAIG,iDAAA,GAA8C;AAClD,SAASN,4CAAA;EACP,IAAIM,iDAAA,IAA+B,MAAM;IACvCA,iDAAA,GAA8B;IAC9B,IAAI;MACF,IAAIC,SAAA,GAAYC,QAAA,CAASC,aAAa,CAAC;MACvCF,SAAA,CAAUN,KAAK,CAAC;QACd,IAAIC,cAAA,EAAgB;UAClBI,iDAAA,GAA8B;UAC9B,OAAO;QACT;MACF;IACF,EAAE,OAAAI,OAAA,EAAM;MACN;IAAA;EAEJ;EAEA,OAAOJ,iDAAA;AACT;AAEA,SAASF,4CAAsBL,OAAyB;EACtD,IAAIY,MAAA,GAASZ,OAAA,CAAQa,UAAU;EAC/B,IAAIT,kBAAA,GAA0C,EAAE;EAChD,IAAIU,oBAAA,GAAuBL,QAAA,CAASM,gBAAgB,IAAIN,QAAA,CAASO,eAAe;EAEhF,OAAOJ,MAAA,YAAkBK,WAAA,IAAeL,MAAA,KAAWE,oBAAA,EAAsB;IACvE,IACEF,MAAA,CAAOM,YAAY,GAAGN,MAAA,CAAOO,YAAY,IACzCP,MAAA,CAAOQ,WAAW,GAAGR,MAAA,CAAOS,WAAW,EAEvCjB,kBAAA,CAAmBkB,IAAI,CAAC;MACtBtB,OAAA,EAASY,MAAA;MACTW,SAAA,EAAWX,MAAA,CAAOW,SAAS;MAC3BC,UAAA,EAAYZ,MAAA,CAAOY;IACrB;IAEFZ,MAAA,GAASA,MAAA,CAAOC,UAAU;EAC5B;EAEA,IAAIC,oBAAA,YAAgCG,WAAA,EAClCb,kBAAA,CAAmBkB,IAAI,CAAC;IACtBtB,OAAA,EAASc,oBAAA;IACTS,SAAA,EAAWT,oBAAA,CAAqBS,SAAS;IACzCC,UAAA,EAAYV,oBAAA,CAAqBU;EACnC;EAGF,OAAOpB,kBAAA;AACT;AAEA,SAASE,4CAAsBF,kBAAuC;EACpE,KAAK,IAAI;IAAAJ,OAAA,EAACA,OAAO;IAAAuB,SAAA,EAAEA,SAAS;IAAAC,UAAA,EAAEA;EAAU,CAAC,IAAIpB,kBAAA,EAAoB;IAC/DJ,OAAA,CAAQuB,SAAS,GAAGA,SAAA;IACpBvB,OAAA,CAAQwB,UAAU,GAAGA,UAAA;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}