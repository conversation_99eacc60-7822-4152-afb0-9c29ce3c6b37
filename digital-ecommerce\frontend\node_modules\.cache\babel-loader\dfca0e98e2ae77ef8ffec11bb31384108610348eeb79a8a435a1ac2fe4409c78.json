{"ast": null, "code": "import { disposables as M } from '../utils/disposables.js';\nimport { getOwnerDocument as b } from '../utils/owner.js';\nimport { useIsTopLayer as L } from './use-is-top-layer.js';\nimport { useIsoMorphicEffect as T } from './use-iso-morphic-effect.js';\nlet f = new Map(),\n  u = new Map();\nfunction h(t) {\n  var e;\n  let r = (e = u.get(t)) != null ? e : 0;\n  return u.set(t, r + 1), r !== 0 ? () => m(t) : (f.set(t, {\n    \"aria-hidden\": t.getAttribute(\"aria-hidden\"),\n    inert: t.inert\n  }), t.setAttribute(\"aria-hidden\", \"true\"), t.inert = !0, () => m(t));\n}\nfunction m(t) {\n  var i;\n  let r = (i = u.get(t)) != null ? i : 1;\n  if (r === 1 ? u.delete(t) : u.set(t, r - 1), r !== 1) return;\n  let e = f.get(t);\n  e && (e[\"aria-hidden\"] === null ? t.removeAttribute(\"aria-hidden\") : t.setAttribute(\"aria-hidden\", e[\"aria-hidden\"]), t.inert = e.inert, f.delete(t));\n}\nfunction y(t) {\n  let {\n    allowed: r,\n    disallowed: e\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let i = L(t, \"inert-others\");\n  T(() => {\n    var d, c;\n    if (!i) return;\n    let a = M();\n    for (let n of (d = e == null ? void 0 : e()) != null ? d : []) n && a.add(h(n));\n    let s = (c = r == null ? void 0 : r()) != null ? c : [];\n    for (let n of s) {\n      if (!n) continue;\n      let l = b(n);\n      if (!l) continue;\n      let o = n.parentElement;\n      for (; o && o !== l.body;) {\n        for (let p of o.children) s.some(E => p.contains(E)) || a.add(h(p));\n        o = o.parentElement;\n      }\n    }\n    return a.dispose;\n  }, [i, r, e]);\n}\nexport { y as useInertOthers };", "map": {"version": 3, "names": ["disposables", "M", "getOwnerDocument", "b", "useIsTopLayer", "L", "useIsoMorphicEffect", "T", "f", "Map", "u", "h", "t", "e", "r", "get", "set", "m", "getAttribute", "inert", "setAttribute", "i", "delete", "removeAttribute", "y", "allowed", "disallowed", "arguments", "length", "undefined", "d", "c", "a", "n", "add", "s", "l", "o", "parentElement", "body", "p", "children", "some", "E", "contains", "dispose", "useInertOthers"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-inert-others.js"], "sourcesContent": ["import{disposables as M}from'../utils/disposables.js';import{getOwnerDocument as b}from'../utils/owner.js';import{useIsTopLayer as L}from'./use-is-top-layer.js';import{useIsoMorphicEffect as T}from'./use-iso-morphic-effect.js';let f=new Map,u=new Map;function h(t){var e;let r=(e=u.get(t))!=null?e:0;return u.set(t,r+1),r!==0?()=>m(t):(f.set(t,{\"aria-hidden\":t.getAttribute(\"aria-hidden\"),inert:t.inert}),t.setAttribute(\"aria-hidden\",\"true\"),t.inert=!0,()=>m(t))}function m(t){var i;let r=(i=u.get(t))!=null?i:1;if(r===1?u.delete(t):u.set(t,r-1),r!==1)return;let e=f.get(t);e&&(e[\"aria-hidden\"]===null?t.removeAttribute(\"aria-hidden\"):t.setAttribute(\"aria-hidden\",e[\"aria-hidden\"]),t.inert=e.inert,f.delete(t))}function y(t,{allowed:r,disallowed:e}={}){let i=L(t,\"inert-others\");T(()=>{var d,c;if(!i)return;let a=M();for(let n of(d=e==null?void 0:e())!=null?d:[])n&&a.add(h(n));let s=(c=r==null?void 0:r())!=null?c:[];for(let n of s){if(!n)continue;let l=b(n);if(!l)continue;let o=n.parentElement;for(;o&&o!==l.body;){for(let p of o.children)s.some(E=>p.contains(E))||a.add(h(p));o=o.parentElement}}return a.dispose},[i,r,e])}export{y as useInertOthers};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,IAAIC,CAAC,GAAC,IAAIC,GAAG,CAAD,CAAC;EAACC,CAAC,GAAC,IAAID,GAAG,CAAD,CAAC;AAAC,SAASE,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,IAAIC,CAAC,GAAC,CAACD,CAAC,GAACH,CAAC,CAACK,GAAG,CAACH,CAAC,CAAC,KAAG,IAAI,GAACC,CAAC,GAAC,CAAC;EAAC,OAAOH,CAAC,CAACM,GAAG,CAACJ,CAAC,EAACE,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,KAAG,CAAC,GAAC,MAAIG,CAAC,CAACL,CAAC,CAAC,IAAEJ,CAAC,CAACQ,GAAG,CAACJ,CAAC,EAAC;IAAC,aAAa,EAACA,CAAC,CAACM,YAAY,CAAC,aAAa,CAAC;IAACC,KAAK,EAACP,CAAC,CAACO;EAAK,CAAC,CAAC,EAACP,CAAC,CAACQ,YAAY,CAAC,aAAa,EAAC,MAAM,CAAC,EAACR,CAAC,CAACO,KAAK,GAAC,CAAC,CAAC,EAAC,MAAIF,CAAC,CAACL,CAAC,CAAC,CAAC;AAAA;AAAC,SAASK,CAACA,CAACL,CAAC,EAAC;EAAC,IAAIS,CAAC;EAAC,IAAIP,CAAC,GAAC,CAACO,CAAC,GAACX,CAAC,CAACK,GAAG,CAACH,CAAC,CAAC,KAAG,IAAI,GAACS,CAAC,GAAC,CAAC;EAAC,IAAGP,CAAC,KAAG,CAAC,GAACJ,CAAC,CAACY,MAAM,CAACV,CAAC,CAAC,GAACF,CAAC,CAACM,GAAG,CAACJ,CAAC,EAACE,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,KAAG,CAAC,EAAC;EAAO,IAAID,CAAC,GAACL,CAAC,CAACO,GAAG,CAACH,CAAC,CAAC;EAACC,CAAC,KAAGA,CAAC,CAAC,aAAa,CAAC,KAAG,IAAI,GAACD,CAAC,CAACW,eAAe,CAAC,aAAa,CAAC,GAACX,CAAC,CAACQ,YAAY,CAAC,aAAa,EAACP,CAAC,CAAC,aAAa,CAAC,CAAC,EAACD,CAAC,CAACO,KAAK,GAACN,CAAC,CAACM,KAAK,EAACX,CAAC,CAACc,MAAM,CAACV,CAAC,CAAC,CAAC;AAAA;AAAC,SAASY,CAACA,CAACZ,CAAC,EAA6B;EAAA,IAA5B;IAACa,OAAO,EAACX,CAAC;IAACY,UAAU,EAACb;EAAC,CAAC,GAAAc,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,CAAC,CAAC;EAAE,IAAIN,CAAC,GAAChB,CAAC,CAACO,CAAC,EAAC,cAAc,CAAC;EAACL,CAAC,CAAC,MAAI;IAAC,IAAIuB,CAAC,EAACC,CAAC;IAAC,IAAG,CAACV,CAAC,EAAC;IAAO,IAAIW,CAAC,GAAC/B,CAAC,CAAC,CAAC;IAAC,KAAI,IAAIgC,CAAC,IAAG,CAACH,CAAC,GAACjB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC,CAAC,KAAG,IAAI,GAACiB,CAAC,GAAC,EAAE,EAACG,CAAC,IAAED,CAAC,CAACE,GAAG,CAACvB,CAAC,CAACsB,CAAC,CAAC,CAAC;IAAC,IAAIE,CAAC,GAAC,CAACJ,CAAC,GAACjB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC,CAAC,KAAG,IAAI,GAACiB,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIE,CAAC,IAAIE,CAAC,EAAC;MAAC,IAAG,CAACF,CAAC,EAAC;MAAS,IAAIG,CAAC,GAACjC,CAAC,CAAC8B,CAAC,CAAC;MAAC,IAAG,CAACG,CAAC,EAAC;MAAS,IAAIC,CAAC,GAACJ,CAAC,CAACK,aAAa;MAAC,OAAKD,CAAC,IAAEA,CAAC,KAAGD,CAAC,CAACG,IAAI,GAAE;QAAC,KAAI,IAAIC,CAAC,IAAIH,CAAC,CAACI,QAAQ,EAACN,CAAC,CAACO,IAAI,CAACC,CAAC,IAAEH,CAAC,CAACI,QAAQ,CAACD,CAAC,CAAC,CAAC,IAAEX,CAAC,CAACE,GAAG,CAACvB,CAAC,CAAC6B,CAAC,CAAC,CAAC;QAACH,CAAC,GAACA,CAAC,CAACC,aAAa;MAAA;IAAC;IAAC,OAAON,CAAC,CAACa,OAAO;EAAA,CAAC,EAAC,CAACxB,CAAC,EAACP,CAAC,EAACD,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOW,CAAC,IAAIsB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}