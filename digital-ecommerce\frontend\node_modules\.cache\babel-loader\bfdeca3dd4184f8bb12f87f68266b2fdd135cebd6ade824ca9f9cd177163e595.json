{"ast": null, "code": "function u(r, n, ...a) {\n  if (r in n) {\n    let e = n[r];\n    return typeof e == \"function\" ? e(...a) : e;\n  }\n  let t = new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e => `\"${e}\"`).join(\", \")}.`);\n  throw Error.captureStackTrace && Error.captureStackTrace(t, u), t;\n}\nexport { u as match };", "map": {"version": 3, "names": ["u", "r", "n", "a", "e", "t", "Error", "Object", "keys", "map", "join", "captureStackTrace", "match"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/match.js"], "sourcesContent": ["function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC,GAAGC,CAAC,EAAC;EAAC,IAAGF,CAAC,IAAIC,CAAC,EAAC;IAAC,IAAIE,CAAC,GAACF,CAAC,CAACD,CAAC,CAAC;IAAC,OAAO,OAAOG,CAAC,IAAE,UAAU,GAACA,CAAC,CAAC,GAAGD,CAAC,CAAC,GAACC,CAAC;EAAA;EAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,oBAAoBL,CAAC,iEAAiEM,MAAM,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,GAAG,CAACL,CAAC,IAAE,IAAIA,CAAC,GAAG,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;EAAC,MAAMJ,KAAK,CAACK,iBAAiB,IAAEL,KAAK,CAACK,iBAAiB,CAACN,CAAC,EAACL,CAAC,CAAC,EAACK,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIY,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}