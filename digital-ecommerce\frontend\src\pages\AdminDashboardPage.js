import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ShoppingBagIcon,
  TagIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { useAdmin } from '../contexts/AdminContext';
import { useProducts } from '../contexts/ProductContext';
import AdminLayout from '../components/AdminLayout';
import AddProductModal from '../components/AddProductModal';

const AdminDashboardPage = () => {
  const { admin } = useAdmin();
  const { products, categories, addProduct } = useProducts();
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCategories: 0,
    totalRevenue: 0,
    totalOrders: 0,
    recentProducts: [],
    lowStockProducts: []
  });

  useEffect(() => {
    // Calculate dashboard statistics
    const totalProducts = products.length;
    const totalCategories = categories.length;
    const totalRevenue = products.reduce((sum, product) => sum + (product.price * (product.sold || 0)), 0);
    const totalOrders = products.reduce((sum, product) => sum + (product.sold || 0), 0);
    
    // Get recent products (last 5)
    const recentProducts = products.slice(-5).reverse();
    
    // Get low stock products
    const lowStockProducts = products.filter(product => 
      product.stockCount && product.stockCount < 10
    ).slice(0, 5);

    setStats({
      totalProducts,
      totalCategories,
      totalRevenue,
      totalOrders,
      recentProducts,
      lowStockProducts
    });
  }, []);

  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue' }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-xl shadow-lg transition-all duration-300 ${
        getThemeClasses('bg-white', 'bg-slate-800')
      }`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className={`text-sm font-medium ${
            getThemeClasses('text-gray-600', 'text-gray-400')
          }`}>
            {title}
          </p>
          <p className={`text-3xl font-bold ${
            getThemeClasses('text-gray-900', 'text-white')
          }`}>
            {value}
          </p>
          {trend && (
            <div className="flex items-center mt-2">
              {trend === 'up' ? (
                <ArrowTrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <ArrowTrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${
                trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {trendValue}
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${
          color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' :
          color === 'green' ? 'bg-green-100 dark:bg-green-900/20' :
          color === 'yellow' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
          'bg-purple-100 dark:bg-purple-900/20'
        }`}>
          <Icon className={`w-6 h-6 ${
            color === 'blue' ? 'text-blue-600' :
            color === 'green' ? 'text-green-600' :
            color === 'yellow' ? 'text-yellow-600' :
            'text-purple-600'
          }`} />
        </div>
      </div>
    </motion.div>
  );

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-3xl font-bold ${
              getThemeClasses('text-gray-900', 'text-white')
            }`}>
              Welcome back, {admin?.firstName}!
            </h1>
            <p className={`mt-2 ${
              getThemeClasses('text-gray-600', 'text-gray-400')
            }`}>
              Here's what's happening with your store today.
            </p>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowAddProductModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors"
          >
            <PlusIcon className="w-5 h-5" />
            <span>Add Product</span>
          </motion.button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Products"
            value={stats.totalProducts}
            icon={ShoppingBagIcon}
            trend="up"
            trendValue="+12%"
            color="blue"
          />
          <StatCard
            title="Categories"
            value={stats.totalCategories}
            icon={TagIcon}
            color="green"
          />
          <StatCard
            title="Total Revenue"
            value={`$${stats.totalRevenue.toLocaleString()}`}
            icon={CurrencyDollarIcon}
            trend="up"
            trendValue="+8.2%"
            color="yellow"
          />
          <StatCard
            title="Total Orders"
            value={stats.totalOrders}
            icon={UserGroupIcon}
            trend="up"
            trendValue="+23%"
            color="purple"
          />
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Products */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className={`p-6 rounded-xl shadow-lg ${
              getThemeClasses('bg-white', 'bg-slate-800')
            }`}
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                getThemeClasses('text-gray-900', 'text-white')
              }`}>
                Recent Products
              </h3>
              <button className={`text-sm ${
                getThemeClasses('text-light-orange-600 hover:text-light-orange-700', 'text-light-orange-400 hover:text-light-orange-300')
              }`}>
                View All
              </button>
            </div>
            <div className="space-y-4">
              {stats.recentProducts.map((product, index) => (
                <div key={product.id} className="flex items-center space-x-4">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium truncate ${
                      getThemeClasses('text-gray-900', 'text-white')
                    }`}>
                      {product.name}
                    </p>
                    <p className={`text-sm ${
                      getThemeClasses('text-gray-500', 'text-gray-400')
                    }`}>
                      ${product.price}
                    </p>
                  </div>
                  <button className={`p-2 rounded-lg ${
                    getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
                  }`}>
                    <EyeIcon className="w-4 h-4 text-gray-400" />
                  </button>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Low Stock Alert */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className={`p-6 rounded-xl shadow-lg ${
              getThemeClasses('bg-white', 'bg-slate-800')
            }`}
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                getThemeClasses('text-gray-900', 'text-white')
              }`}>
                Low Stock Alert
              </h3>
              <span className="px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full">
                {stats.lowStockProducts.length} items
              </span>
            </div>
            <div className="space-y-4">
              {stats.lowStockProducts.length > 0 ? (
                stats.lowStockProducts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-10 h-10 rounded-lg object-cover"
                      />
                      <div>
                        <p className={`text-sm font-medium ${
                          getThemeClasses('text-gray-900', 'text-white')
                        }`}>
                          {product.name}
                        </p>
                        <p className="text-xs text-red-600">
                          {product.stockCount} left
                        </p>
                      </div>
                    </div>
                    <button className="px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors">
                      Restock
                    </button>
                  </div>
                ))
              ) : (
                <p className={`text-sm ${
                  getThemeClasses('text-gray-500', 'text-gray-400')
                }`}>
                  All products are well stocked!
                </p>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Add Product Modal */}
      <AddProductModal
        isOpen={showAddProductModal}
        onClose={() => setShowAddProductModal(false)}
        onSubmit={async (productData) => {
          const result = await addProduct(productData);
          if (result.success) {
            setShowAddProductModal(false);
            // Show success notification (you can add a toast notification here)
            console.log('Product added successfully:', result.product);
          } else {
            // Show error notification
            console.error('Failed to add product:', result.error);
          }
        }}
      />
    </AdminLayout>
  );
};

export default AdminDashboardPage;
