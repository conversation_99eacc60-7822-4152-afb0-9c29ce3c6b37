{"ast": null, "code": "import React,{useState,useMemo}from'react';import{motion,AnimatePresence}from'framer-motion';import{useSearchParams,Link}from'react-router-dom';import{FunnelIcon,Squares2X2Icon,ListBulletIcon,StarIcon,HeartIcon,ShoppingBagIcon,AdjustmentsHorizontalIcon,ChevronRightIcon,HomeIcon,// TagIcon,\nClockIcon,TruckIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{StarIcon as StarIconSolid,HeartIcon as HeartIconSolid}from'@heroicons/react/24/solid';import{categories,products,getProductsByCategory}from'../data/products';import{useCart}from'../components/ShoppingCart';import{useUser}from'../contexts/UserContext';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProductsPage=()=>{var _subcategories$find;const[searchParams,setSearchParams]=useSearchParams();const[viewMode,setViewMode]=useState('grid');const[sortBy,setSortBy]=useState('featured');const[selectedCategory,setSelectedCategory]=useState(searchParams.get('category')||'all');const[selectedSubcategory,setSelectedSubcategory]=useState(searchParams.get('subcategory')||'all');const[productType,setProductType]=useState('all');// all, physical, digital\nconst[priceRange,setPriceRange]=useState([0,1000]);const[selectedRating,setSelectedRating]=useState(0);const[showFilters,setShowFilters]=useState(false);const{addToCart}=useCart();const{addToWishlist,removeFromWishlist,isInWishlist,isAuthenticated}=useUser();const handleAddToCart=product=>{addToCart(product);toast.success(\"\".concat(product.name,\" added to cart!\"),{duration:3000,position:'top-right'});};const handleWishlistToggle=product=>{if(!isAuthenticated){toast.error('Please sign in to add items to your wishlist');return;}if(isInWishlist(product.id)){removeFromWishlist(product.id);toast.success('Removed from wishlist');}else{addToWishlist(product.id);toast.success('Added to wishlist');}};// Get current category data\nconst currentCategory=categories.find(cat=>cat.id===selectedCategory);const subcategories=currentCategory?[{id:'all',name:'All '+currentCategory.name,count:getProductsByCategory(selectedCategory).length},...currentCategory.subcategories.map(sub=>({id:sub,name:sub.split('-').map(word=>word.charAt(0).toUpperCase()+word.slice(1)).join(' '),count:products.filter(p=>p.subcategory===sub).length}))]:[];const productTypeOptions=[{id:'all',name:'All Products',count:products.length},{id:'physical',name:'Physical Products',count:products.filter(p=>p.type==='physical').length},{id:'digital',name:'Digital Products',count:products.filter(p=>p.type==='digital').length}];const sortOptions=[{value:'featured',label:'Featured'},{value:'price-low',label:'Price: Low to High'},{value:'price-high',label:'Price: High to Low'},{value:'rating',label:'Highest Rated'},{value:'newest',label:'Newest First'},{value:'name',label:'Name: A to Z'},{value:'popularity',label:'Most Popular'}];const filteredAndSortedProducts=useMemo(()=>{let filtered=products.filter(product=>{const categoryMatch=selectedCategory==='all'||product.category===selectedCategory;const subcategoryMatch=selectedSubcategory==='all'||product.subcategory===selectedSubcategory;const typeMatch=productType==='all'||product.type===productType;const priceMatch=product.price>=priceRange[0]&&product.price<=priceRange[1];const ratingMatch=selectedRating===0||product.rating>=selectedRating;return categoryMatch&&subcategoryMatch&&typeMatch&&priceMatch&&ratingMatch;});// Sort products\nswitch(sortBy){case'price-low':filtered.sort((a,b)=>a.price-b.price);break;case'price-high':filtered.sort((a,b)=>b.price-a.price);break;case'rating':filtered.sort((a,b)=>b.rating-a.rating);break;case'newest':filtered.sort((a,b)=>b.id.localeCompare(a.id));break;case'name':filtered.sort((a,b)=>a.name.localeCompare(b.name));break;default:// Featured - keep original order\nbreak;}return filtered;},[selectedCategory,selectedSubcategory,productType,priceRange,selectedRating,sortBy]);const ProductCard=_ref=>{var _product$shipping;let{product,index}=_ref;return/*#__PURE__*/_jsxs(motion.div,{layout:true,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3,delay:index*0.05},className:\"rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 bg-white \".concat(viewMode==='list'?'flex':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative \".concat(viewMode==='list'?'w-48 flex-shrink-0':''),children:[/*#__PURE__*/_jsx(\"img\",{src:product.images?product.images[0]:product.image,alt:product.name,className:\"w-full object-cover group-hover:scale-105 transition-transform duration-300 \".concat(viewMode==='list'?'h-48':'h-64')}),product.badge&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-1 rounded-full text-sm font-semibold text-white \".concat(product.type==='digital'?'bg-blue-500':'bg-light-orange-500'),children:product.badge})}),product.type==='digital'&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4 mt-8\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",children:\"Digital\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},onClick:()=>handleWishlistToggle(product),className:\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg hover:bg-opacity-100 transition-all\",children:isInWishlist(product.id)?/*#__PURE__*/_jsx(HeartIconSolid,{className:\"w-5 h-5 text-red-500\"}):/*#__PURE__*/_jsx(HeartIcon,{className:\"w-5 h-5 text-gray-600 hover:text-red-500\"})})}),!product.inStock&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-semibold\",children:\"Out of Stock\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 \".concat(viewMode==='list'?'flex-1 flex flex-col justify-between':''),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2 \".concat(viewMode==='list'?'text-xl':'text-lg'),children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\")\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-light-orange-600\",children:[\"$\",product.price]}),product.originalPrice&&product.originalPrice>product.price&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]}),product.type==='digital'?/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-green-600 font-medium\",children:\"Instant Delivery\"})]}),product.platforms&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Platforms:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-800\",children:product.platforms.join(', ')})]})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[product.colors&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Colors:\"}),product.colors.slice(0,3).map((color,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer \".concat(color==='black'||color==='Black'?'bg-black':color==='white'||color==='White'?'bg-white':color==='blue'||color==='Blue'?'bg-blue-500':color==='red'||color==='Red'?'bg-red-500':color==='silver'||color==='Silver'?'bg-gray-400':color==='gold'||color==='Gold'?'bg-yellow-400':'bg-gray-300')},index)),product.colors.length>3&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[\"+\",product.colors.length-3]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(TruckIcon,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-blue-600\",children:(_product$shipping=product.shipping)!==null&&_product$shipping!==void 0&&_product$shipping.free?'Free Shipping':'Shipping Available'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-4 h-4 \".concat(product.inStock?'text-green-600':'text-red-600')}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm \".concat(product.inStock?'text-green-600':'text-red-600'),children:[product.inStock?'In Stock':'Out of Stock',product.stockCount&&product.inStock&&\" (\".concat(product.stockCount,\" available)\")]})]})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>handleAddToCart(product),disabled:!product.inStock,className:\"w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 \".concat(product.inStock?'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700':'bg-gray-300 text-gray-500 cursor-not-allowed'),children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:product.inStock?'Add to Cart':'Out of Stock'})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsx(\"div\",{className:\"border-b bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",children:/*#__PURE__*/_jsxs(\"nav\",{className:\"flex items-center space-x-2 text-sm\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\",children:[/*#__PURE__*/_jsx(HomeIcon,{className:\"w-4 h-4 mr-1\"}),\"Home\"]}),/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Products\"}),selectedCategory!=='all'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-600 font-medium\",children:currentCategory===null||currentCategory===void 0?void 0:currentCategory.name})]}),selectedSubcategory!=='all'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-600 font-medium\",children:(_subcategories$find=subcategories.find(sub=>sub.id===selectedSubcategory))===null||_subcategories$find===void 0?void 0:_subcategories$find.name})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl lg:text-5xl font-bold text-white mb-4\",children:selectedCategory==='all'?'All Products':(currentCategory===null||currentCategory===void 0?void 0:currentCategory.name)||'Products'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-light-orange-100 max-w-2xl mx-auto\",children:selectedCategory==='all'?'Discover our amazing collection of premium products':(currentCategory===null||currentCategory===void 0?void 0:currentCategory.description)||'Explore our curated selection'})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"border-b bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-4 justify-center\",children:[{id:'all',name:'All Products',icon:'🛍️'},...categories].map(category=>/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>{setSelectedCategory(category.id);setSelectedSubcategory('all');setSearchParams({category:category.id});},className:\"flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all \".concat(selectedCategory===category.id?'bg-light-orange-500 text-white shadow-lg':'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg\",children:category.icon}),/*#__PURE__*/_jsx(\"span\",{children:category.name})]},category.id))})})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:w-64 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-2xl shadow-lg p-6 sticky top-24 bg-white\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Filters\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowFilters(!showFilters),className:\"lg:hidden p-2 text-gray-600\",children:/*#__PURE__*/_jsx(AdjustmentsHorizontalIcon,{className:\"w-5 h-5\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6 \".concat(showFilters?'block':'hidden lg:block'),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Product Type\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:productTypeOptions.map(type=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setProductType(type.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(productType===type.id?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:type.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm\",children:[\"(\",type.count,\")\"]})]})},type.id))})]}),selectedCategory!=='all'&&subcategories.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:[currentCategory===null||currentCategory===void 0?void 0:currentCategory.name,\" Categories\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:subcategories.map(subcategory=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedSubcategory(subcategory.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(selectedSubcategory===subcategory.id?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:subcategory.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm\",children:[\"(\",subcategory.count,\")\"]})]})},subcategory.id))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:[\"Price Range: $\",priceRange[0],\" - $\",priceRange[1]]}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"0\",max:\"1000\",value:priceRange[1],onChange:e=>setPriceRange([priceRange[0],parseInt(e.target.value)]),className:\"w-full\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Minimum Rating\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:[4,3,2,1,0].map(rating=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setSelectedRating(rating),className:\"flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors \".concat(selectedRating===rating?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 \".concat(i<rating?'text-yellow-400':'text-gray-300')},i))}),/*#__PURE__*/_jsx(\"span\",{children:rating>0?\"\".concat(rating,\"+ Stars\"):'All Ratings'})]},rating))})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Showing \",filteredAndSortedProducts.length,\" of \",products.length,\" products\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"select\",{value:sortBy,onChange:e=>setSortBy(e.target.value),className:\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\",children:sortOptions.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex bg-gray-100 rounded-lg p-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('grid'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='grid'?'bg-white text-light-orange-600 shadow-sm':'text-gray-600'),children:/*#__PURE__*/_jsx(Squares2X2Icon,{className:\"w-5 h-5\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('list'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='list'?'bg-white text-light-orange-600 shadow-sm':'text-gray-600'),children:/*#__PURE__*/_jsx(ListBulletIcon,{className:\"w-5 h-5\"})})]})]})]})}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"\".concat(viewMode==='grid'?'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8':'space-y-6'),children:filteredAndSortedProducts.map((product,index)=>/*#__PURE__*/_jsx(ProductCard,{product:product,index:index},product.id))},\"\".concat(viewMode,\"-\").concat(selectedCategory,\"-\").concat(sortBy))}),filteredAndSortedProducts.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-400 mb-4\",children:/*#__PURE__*/_jsx(FunnelIcon,{className:\"w-16 h-16 mx-auto\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:\"No products found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Try adjusting your filters to see more results.\"})]})]})]})})]});};export default ProductsPage;", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "useSearchParams", "Link", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "StarIcon", "HeartIcon", "ShoppingBagIcon", "AdjustmentsHorizontalIcon", "ChevronRightIcon", "HomeIcon", "ClockIcon", "TruckIcon", "CheckCircleIcon", "StarIconSolid", "HeartIconSolid", "categories", "products", "getProductsByCategory", "useCart", "useUser", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProductsPage", "_subcategories$find", "searchParams", "setSearchParams", "viewMode", "setViewMode", "sortBy", "setSortBy", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "get", "selectedSubcategory", "setSelectedSubcategory", "productType", "setProductType", "priceRange", "setPriceRange", "selectedRating", "setSelectedRating", "showFilters", "setShowFilters", "addToCart", "addToWishlist", "removeFromWishlist", "isInWishlist", "isAuthenticated", "handleAddToCart", "product", "success", "concat", "name", "duration", "position", "handleWishlistToggle", "error", "id", "currentCategory", "find", "cat", "subcategories", "count", "length", "map", "sub", "split", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "filter", "p", "subcategory", "productTypeOptions", "type", "sortOptions", "value", "label", "filteredAndSortedProducts", "filtered", "categoryMatch", "category", "subcategoryMatch", "typeMatch", "priceMatch", "price", "ratingMatch", "rating", "sort", "a", "b", "localeCompare", "ProductCard", "_ref", "_product$shipping", "index", "div", "layout", "initial", "opacity", "y", "animate", "exit", "transition", "delay", "className", "children", "src", "images", "image", "alt", "badge", "button", "whileHover", "scale", "whileTap", "onClick", "inStock", "Array", "_", "i", "Math", "floor", "reviews", "originalPrice", "platforms", "colors", "color", "shipping", "free", "stockCount", "disabled", "to", "description", "icon", "min", "max", "onChange", "e", "parseInt", "target", "option", "mode"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/ProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport {\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon,\n  StarIcon,\n  HeartIcon,\n  ShoppingBagIcon,\n  AdjustmentsHorizontalIcon,\n  ChevronRightIcon,\n  HomeIcon,\n  // TagIcon,\n  ClockIcon,\n  TruckIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid, HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';\nimport { categories, products, getProductsByCategory } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst ProductsPage = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [sortBy, setSortBy] = useState('featured');\n  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');\n  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || 'all');\n  const [productType, setProductType] = useState('all'); // all, physical, digital\n  const [priceRange, setPriceRange] = useState([0, 1000]);\n  const [selectedRating, setSelectedRating] = useState(0);\n  const [showFilters, setShowFilters] = useState(false);\n  const { addToCart } = useCart();\n  const { addToWishlist, removeFromWishlist, isInWishlist, isAuthenticated } = useUser();\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  };\n\n  const handleWishlistToggle = (product) => {\n    if (!isAuthenticated) {\n      toast.error('Please sign in to add items to your wishlist');\n      return;\n    }\n\n    if (isInWishlist(product.id)) {\n      removeFromWishlist(product.id);\n      toast.success('Removed from wishlist');\n    } else {\n      addToWishlist(product.id);\n      toast.success('Added to wishlist');\n    }\n  };\n\n  // Get current category data\n  const currentCategory = categories.find(cat => cat.id === selectedCategory);\n  const subcategories = currentCategory ? [\n    { id: 'all', name: 'All ' + currentCategory.name, count: getProductsByCategory(selectedCategory).length },\n    ...currentCategory.subcategories.map(sub => ({\n      id: sub,\n      name: sub.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),\n      count: products.filter(p => p.subcategory === sub).length\n    }))\n  ] : [];\n\n  const productTypeOptions = [\n    { id: 'all', name: 'All Products', count: products.length },\n    { id: 'physical', name: 'Physical Products', count: products.filter(p => p.type === 'physical').length },\n    { id: 'digital', name: 'Digital Products', count: products.filter(p => p.type === 'digital').length }\n  ];\n\n  const sortOptions = [\n    { value: 'featured', label: 'Featured' },\n    { value: 'price-low', label: 'Price: Low to High' },\n    { value: 'price-high', label: 'Price: High to Low' },\n    { value: 'rating', label: 'Highest Rated' },\n    { value: 'newest', label: 'Newest First' },\n    { value: 'name', label: 'Name: A to Z' },\n    { value: 'popularity', label: 'Most Popular' }\n  ];\n\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n      const subcategoryMatch = selectedSubcategory === 'all' || product.subcategory === selectedSubcategory;\n      const typeMatch = productType === 'all' || product.type === productType;\n      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1];\n      const ratingMatch = selectedRating === 0 || product.rating >= selectedRating;\n\n      return categoryMatch && subcategoryMatch && typeMatch && priceMatch && ratingMatch;\n    });\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      case 'newest':\n        filtered.sort((a, b) => b.id.localeCompare(a.id));\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n\n    return filtered;\n  }, [selectedCategory, selectedSubcategory, productType, priceRange, selectedRating, sortBy]);\n\n  const ProductCard = ({ product, index }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3, delay: index * 0.05 }}\n      className={`rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 bg-white ${viewMode === 'list' ? 'flex' : ''}`}\n    >\n      <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>\n        <img\n          src={product.images ? product.images[0] : product.image}\n          alt={product.name}\n          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${\n            viewMode === 'list' ? 'h-48' : 'h-64'\n          }`}\n        />\n        {product.badge && (\n          <div className=\"absolute top-4 left-4\">\n            <span className={`px-3 py-1 rounded-full text-sm font-semibold text-white ${\n              product.type === 'digital' ? 'bg-blue-500' : 'bg-light-orange-500'\n            }`}>\n              {product.badge}\n            </span>\n          </div>\n        )}\n        {product.type === 'digital' && (\n          <div className=\"absolute top-4 left-4 mt-8\">\n            <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n              Digital\n            </span>\n          </div>\n        )}\n        <div className=\"absolute top-4 right-4\">\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={() => handleWishlistToggle(product)}\n            className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg hover:bg-opacity-100 transition-all\"\n          >\n            {isInWishlist(product.id) ? (\n              <HeartIconSolid className=\"w-5 h-5 text-red-500\" />\n            ) : (\n              <HeartIcon className=\"w-5 h-5 text-gray-600 hover:text-red-500\" />\n            )}\n          </motion.button>\n        </div>\n        {!product.inStock && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-semibold\">Out of Stock</span>\n          </div>\n        )}\n      </div>\n\n      <div className={`p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>\n        <div>\n          <h3 className={`font-semibold text-gray-900 mb-2 ${\n            viewMode === 'list' ? 'text-xl' : 'text-lg'\n          }`}>\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex\">\n              {[...Array(5)].map((_, i) => (\n                i < Math.floor(product.rating) ? (\n                  <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                ) : (\n                  <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                )\n              ))}\n            </div>\n            <span className=\"text-sm text-gray-600 ml-2\">\n              {product.rating} ({product.reviews})\n            </span>\n          </div>\n\n          <div className=\"flex items-center space-x-2 mb-3\">\n            <span className=\"text-2xl font-bold text-light-orange-600\">\n              ${product.price}\n            </span>\n            {product.originalPrice && product.originalPrice > product.price && (\n              <span className=\"text-lg text-gray-500 line-through\">\n                ${product.originalPrice}\n              </span>\n            )}\n          </div>\n\n          {/* Product Type Specific Info */}\n          {product.type === 'digital' ? (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <ClockIcon className=\"w-4 h-4 text-green-600\" />\n                <span className=\"text-sm text-green-600 font-medium\">Instant Delivery</span>\n              </div>\n              {product.platforms && (\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-600\">Platforms:</span>\n                  <span className=\"text-sm text-gray-800\">{product.platforms.join(', ')}</span>\n                </div>\n              )}\n            </div>\n          ) : (\n            <>\n              {/* Color Options for Physical Products */}\n              {product.colors && (\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <span className=\"text-sm text-gray-600\">Colors:</span>\n                  {product.colors.slice(0, 3).map((color, index) => (\n                    <div\n                      key={index}\n                      className={`w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer ${\n                        color === 'black' || color === 'Black' ? 'bg-black' :\n                        color === 'white' || color === 'White' ? 'bg-white' :\n                        color === 'blue' || color === 'Blue' ? 'bg-blue-500' :\n                        color === 'red' || color === 'Red' ? 'bg-red-500' :\n                        color === 'silver' || color === 'Silver' ? 'bg-gray-400' :\n                        color === 'gold' || color === 'Gold' ? 'bg-yellow-400' :\n                        'bg-gray-300'\n                      }`}\n                    />\n                  ))}\n                  {product.colors.length > 3 && (\n                    <span className=\"text-sm text-gray-500\">+{product.colors.length - 3}</span>\n                  )}\n                </div>\n              )}\n              {/* Shipping Info */}\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TruckIcon className=\"w-4 h-4 text-blue-600\" />\n                <span className=\"text-sm text-blue-600\">\n                  {product.shipping?.free ? 'Free Shipping' : 'Shipping Available'}\n                </span>\n              </div>\n            </>\n          )}\n\n          {/* Stock Status */}\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <CheckCircleIcon className={`w-4 h-4 ${product.inStock ? 'text-green-600' : 'text-red-600'}`} />\n            <span className={`text-sm ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>\n              {product.inStock ? 'In Stock' : 'Out of Stock'}\n              {product.stockCount && product.inStock && ` (${product.stockCount} available)`}\n            </span>\n          </div>\n        </div>\n\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          onClick={() => handleAddToCart(product)}\n          disabled={!product.inStock}\n          className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${\n            product.inStock\n              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700'\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n          }`}\n        >\n          <ShoppingBagIcon className=\"w-5 h-5\" />\n          <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      {/* Breadcrumb */}\n      <div className=\"border-b bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <nav className=\"flex items-center space-x-2 text-sm\">\n            <Link to=\"/\" className=\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\">\n              <HomeIcon className=\"w-4 h-4 mr-1\" />\n              Home\n            </Link>\n            <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n            <span className=\"text-gray-600\">Products</span>\n            {selectedCategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {currentCategory?.name}\n                </span>\n              </>\n            )}\n            {selectedSubcategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {subcategories.find(sub => sub.id === selectedSubcategory)?.name}\n                </span>\n              </>\n            )}\n          </nav>\n        </div>\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-4\">\n              {selectedCategory === 'all' ? 'All Products' : currentCategory?.name || 'Products'}\n            </h1>\n            <p className=\"text-xl text-light-orange-100 max-w-2xl mx-auto\">\n              {selectedCategory === 'all'\n                ? 'Discover our amazing collection of premium products'\n                : currentCategory?.description || 'Explore our curated selection'\n              }\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Category Navigation */}\n      <div className=\"border-b bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            {[{ id: 'all', name: 'All Products', icon: '🛍️' }, ...categories].map((category) => (\n              <motion.button\n                key={category.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => {\n                  setSelectedCategory(category.id);\n                  setSelectedSubcategory('all');\n                  setSearchParams({ category: category.id });\n                }}\n                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${\n                  selectedCategory === category.id\n                    ? 'bg-light-orange-500 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'\n                }`}\n              >\n                <span className=\"text-lg\">{category.icon}</span>\n                <span>{category.name}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar Filters */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"rounded-2xl shadow-lg p-6 sticky top-24 bg-white\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n                <button\n                  onClick={() => setShowFilters(!showFilters)}\n                  className=\"lg:hidden p-2 text-gray-600\"\n                >\n                  <AdjustmentsHorizontalIcon className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n                {/* Product Type */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Product Type</h4>\n                  <div className=\"space-y-2\">\n                    {productTypeOptions.map(type => (\n                      <button\n                        key={type.id}\n                        onClick={() => setProductType(type.id)}\n                        className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                          productType === type.id\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex justify-between\">\n                          <span>{type.name}</span>\n                          <span className=\"text-sm\">({type.count})</span>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Subcategories */}\n                {selectedCategory !== 'all' && subcategories.length > 0 && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">\n                      {currentCategory?.name} Categories\n                    </h4>\n                    <div className=\"space-y-2\">\n                      {subcategories.map(subcategory => (\n                        <button\n                          key={subcategory.id}\n                          onClick={() => setSelectedSubcategory(subcategory.id)}\n                          className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                            selectedSubcategory === subcategory.id\n                              ? 'bg-light-orange-100 text-light-orange-700'\n                              : 'text-gray-600 hover:bg-gray-100'\n                          }`}\n                        >\n                          <div className=\"flex justify-between\">\n                            <span>{subcategory.name}</span>\n                            <span className=\"text-sm\">({subcategory.count})</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Price Range */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">\n                    Price Range: ${priceRange[0]} - ${priceRange[1]}\n                  </h4>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1000\"\n                    value={priceRange[1]}\n                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}\n                    className=\"w-full\"\n                  />\n                </div>\n\n                {/* Rating Filter */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Minimum Rating</h4>\n                  <div className=\"space-y-2\">\n                    {[4, 3, 2, 1, 0].map(rating => (\n                      <button\n                        key={rating}\n                        onClick={() => setSelectedRating(rating)}\n                        className={`flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors ${\n                          selectedRating === rating\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex\">\n                          {[...Array(5)].map((_, i) => (\n                            <StarIconSolid\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < rating ? 'text-yellow-400' : 'text-gray-300'\n                              }`}\n                            />\n                          ))}\n                        </div>\n                        <span>{rating > 0 ? `${rating}+ Stars` : 'All Ratings'}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {/* Toolbar */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 mb-8\">\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n                <div>\n                  <p className=\"text-gray-600\">\n                    Showing {filteredAndSortedProducts.length} of {products.length} products\n                  </p>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  {/* Sort Dropdown */}\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\"\n                  >\n                    {sortOptions.map(option => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n\n                  {/* View Mode Toggle */}\n                  <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                    <button\n                      onClick={() => setViewMode('grid')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'grid'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <Squares2X2Icon className=\"w-5 h-5\" />\n                    </button>\n                    <button\n                      onClick={() => setViewMode('list')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'list'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <ListBulletIcon className=\"w-5 h-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Products Grid */}\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={`${viewMode}-${selectedCategory}-${sortBy}`}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className={`${\n                  viewMode === 'grid'\n                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8'\n                    : 'space-y-6'\n                }`}\n              >\n                {filteredAndSortedProducts.map((product, index) => (\n                  <ProductCard key={product.id} product={product} index={index} />\n                ))}\n              </motion.div>\n            </AnimatePresence>\n\n            {filteredAndSortedProducts.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <FunnelIcon className=\"w-16 h-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No products found</h3>\n                <p className=\"text-gray-600\">Try adjusting your filters to see more results.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,OAAO,KAAQ,OAAO,CAChD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,eAAe,CAAEC,IAAI,KAAQ,kBAAkB,CACxD,OACEC,UAAU,CACVC,cAAc,CACdC,cAAc,CACdC,QAAQ,CACRC,SAAS,CACTC,eAAe,CACfC,yBAAyB,CACzBC,gBAAgB,CAChBC,QAAQ,CACR;AACAC,SAAS,CACTC,SAAS,CACTC,eAAe,KACV,6BAA6B,CACpC,OAASR,QAAQ,GAAI,CAAAS,aAAa,CAAER,SAAS,GAAI,CAAAS,cAAc,KAAQ,2BAA2B,CAClG,OAASC,UAAU,CAAEC,QAAQ,CAAEC,qBAAqB,KAAQ,kBAAkB,CAC9E,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CACzB,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGhC,eAAe,CAAC,CAAC,CACzD,KAAM,CAACiC,QAAQ,CAAEC,WAAW,CAAC,CAAGtC,QAAQ,CAAC,MAAM,CAAC,CAChD,KAAM,CAACuC,MAAM,CAAEC,SAAS,CAAC,CAAGxC,QAAQ,CAAC,UAAU,CAAC,CAChD,KAAM,CAACyC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1C,QAAQ,CAACmC,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAI,KAAK,CAAC,CAC/F,KAAM,CAACC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG7C,QAAQ,CAACmC,YAAY,CAACQ,GAAG,CAAC,aAAa,CAAC,EAAI,KAAK,CAAC,CACxG,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAE;AACvD,KAAM,CAACgD,UAAU,CAAEC,aAAa,CAAC,CAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,CAAC,CACvD,KAAM,CAACkD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnD,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAACoD,WAAW,CAAEC,cAAc,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAEsD,SAAU,CAAC,CAAG/B,OAAO,CAAC,CAAC,CAC/B,KAAM,CAAEgC,aAAa,CAAEC,kBAAkB,CAAEC,YAAY,CAAEC,eAAgB,CAAC,CAAGlC,OAAO,CAAC,CAAC,CAEtF,KAAM,CAAAmC,eAAe,CAAIC,OAAO,EAAK,CACnCN,SAAS,CAACM,OAAO,CAAC,CAClBnC,KAAK,CAACoC,OAAO,IAAAC,MAAA,CAAIF,OAAO,CAACG,IAAI,oBAAmB,CAC9CC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,WACZ,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIN,OAAO,EAAK,CACxC,GAAI,CAACF,eAAe,CAAE,CACpBjC,KAAK,CAAC0C,KAAK,CAAC,8CAA8C,CAAC,CAC3D,OACF,CAEA,GAAIV,YAAY,CAACG,OAAO,CAACQ,EAAE,CAAC,CAAE,CAC5BZ,kBAAkB,CAACI,OAAO,CAACQ,EAAE,CAAC,CAC9B3C,KAAK,CAACoC,OAAO,CAAC,uBAAuB,CAAC,CACxC,CAAC,IAAM,CACLN,aAAa,CAACK,OAAO,CAACQ,EAAE,CAAC,CACzB3C,KAAK,CAACoC,OAAO,CAAC,mBAAmB,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,eAAe,CAAGjD,UAAU,CAACkD,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACH,EAAE,GAAK3B,gBAAgB,CAAC,CAC3E,KAAM,CAAA+B,aAAa,CAAGH,eAAe,CAAG,CACtC,CAAED,EAAE,CAAE,KAAK,CAAEL,IAAI,CAAE,MAAM,CAAGM,eAAe,CAACN,IAAI,CAAEU,KAAK,CAAEnD,qBAAqB,CAACmB,gBAAgB,CAAC,CAACiC,MAAO,CAAC,CACzG,GAAGL,eAAe,CAACG,aAAa,CAACG,GAAG,CAACC,GAAG,GAAK,CAC3CR,EAAE,CAAEQ,GAAG,CACPb,IAAI,CAAEa,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACF,GAAG,CAACG,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACxFT,KAAK,CAAEpD,QAAQ,CAAC8D,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,WAAW,GAAKT,GAAG,CAAC,CAACF,MACrD,CAAC,CAAC,CAAC,CACJ,CAAG,EAAE,CAEN,KAAM,CAAAY,kBAAkB,CAAG,CACzB,CAAElB,EAAE,CAAE,KAAK,CAAEL,IAAI,CAAE,cAAc,CAAEU,KAAK,CAAEpD,QAAQ,CAACqD,MAAO,CAAC,CAC3D,CAAEN,EAAE,CAAE,UAAU,CAAEL,IAAI,CAAE,mBAAmB,CAAEU,KAAK,CAAEpD,QAAQ,CAAC8D,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACG,IAAI,GAAK,UAAU,CAAC,CAACb,MAAO,CAAC,CACxG,CAAEN,EAAE,CAAE,SAAS,CAAEL,IAAI,CAAE,kBAAkB,CAAEU,KAAK,CAAEpD,QAAQ,CAAC8D,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACG,IAAI,GAAK,SAAS,CAAC,CAACb,MAAO,CAAC,CACtG,CAED,KAAM,CAAAc,WAAW,CAAG,CAClB,CAAEC,KAAK,CAAE,UAAU,CAAEC,KAAK,CAAE,UAAW,CAAC,CACxC,CAAED,KAAK,CAAE,WAAW,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACnD,CAAED,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACpD,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,eAAgB,CAAC,CAC3C,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC1C,CAAED,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,cAAe,CAAC,CACxC,CAAED,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC/C,CAED,KAAM,CAAAC,yBAAyB,CAAG1F,OAAO,CAAC,IAAM,CAC9C,GAAI,CAAA2F,QAAQ,CAAGvE,QAAQ,CAAC8D,MAAM,CAACvB,OAAO,EAAI,CACxC,KAAM,CAAAiC,aAAa,CAAGpD,gBAAgB,GAAK,KAAK,EAAImB,OAAO,CAACkC,QAAQ,GAAKrD,gBAAgB,CACzF,KAAM,CAAAsD,gBAAgB,CAAGnD,mBAAmB,GAAK,KAAK,EAAIgB,OAAO,CAACyB,WAAW,GAAKzC,mBAAmB,CACrG,KAAM,CAAAoD,SAAS,CAAGlD,WAAW,GAAK,KAAK,EAAIc,OAAO,CAAC2B,IAAI,GAAKzC,WAAW,CACvE,KAAM,CAAAmD,UAAU,CAAGrC,OAAO,CAACsC,KAAK,EAAIlD,UAAU,CAAC,CAAC,CAAC,EAAIY,OAAO,CAACsC,KAAK,EAAIlD,UAAU,CAAC,CAAC,CAAC,CACnF,KAAM,CAAAmD,WAAW,CAAGjD,cAAc,GAAK,CAAC,EAAIU,OAAO,CAACwC,MAAM,EAAIlD,cAAc,CAE5E,MAAO,CAAA2C,aAAa,EAAIE,gBAAgB,EAAIC,SAAS,EAAIC,UAAU,EAAIE,WAAW,CACpF,CAAC,CAAC,CAEF;AACA,OAAQ5D,MAAM,EACZ,IAAK,WAAW,CACdqD,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACJ,KAAK,CAAGK,CAAC,CAACL,KAAK,CAAC,CAC1C,MACF,IAAK,YAAY,CACfN,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACL,KAAK,CAAGI,CAAC,CAACJ,KAAK,CAAC,CAC1C,MACF,IAAK,QAAQ,CACXN,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACH,MAAM,CAAGE,CAAC,CAACF,MAAM,CAAC,CAC5C,MACF,IAAK,QAAQ,CACXR,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACnC,EAAE,CAACoC,aAAa,CAACF,CAAC,CAAClC,EAAE,CAAC,CAAC,CACjD,MACF,IAAK,MAAM,CACTwB,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACvC,IAAI,CAACyC,aAAa,CAACD,CAAC,CAACxC,IAAI,CAAC,CAAC,CACrD,MACF,QACE;AACA,MACJ,CAEA,MAAO,CAAA6B,QAAQ,CACjB,CAAC,CAAE,CAACnD,gBAAgB,CAAEG,mBAAmB,CAAEE,WAAW,CAAEE,UAAU,CAAEE,cAAc,CAAEX,MAAM,CAAC,CAAC,CAE5F,KAAM,CAAAkE,WAAW,CAAGC,IAAA,OAAAC,iBAAA,IAAC,CAAE/C,OAAO,CAAEgD,KAAM,CAAC,CAAAF,IAAA,oBACrC5E,KAAA,CAAC5B,MAAM,CAAC2G,GAAG,EACTC,MAAM,MACNC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEpD,QAAQ,CAAE,GAAG,CAAEqD,KAAK,CAAET,KAAK,CAAG,IAAK,CAAE,CACnDU,SAAS,oHAAAxD,MAAA,CAAqHzB,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,EAAE,CAAG,CAAAkF,QAAA,eAElKzF,KAAA,QAAKwF,SAAS,aAAAxD,MAAA,CAAczB,QAAQ,GAAK,MAAM,CAAG,oBAAoB,CAAG,EAAE,CAAG,CAAAkF,QAAA,eAC5E3F,IAAA,QACE4F,GAAG,CAAE5D,OAAO,CAAC6D,MAAM,CAAG7D,OAAO,CAAC6D,MAAM,CAAC,CAAC,CAAC,CAAG7D,OAAO,CAAC8D,KAAM,CACxDC,GAAG,CAAE/D,OAAO,CAACG,IAAK,CAClBuD,SAAS,gFAAAxD,MAAA,CACPzB,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CACpC,CACJ,CAAC,CACDuB,OAAO,CAACgE,KAAK,eACZhG,IAAA,QAAK0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpC3F,IAAA,SAAM0F,SAAS,4DAAAxD,MAAA,CACbF,OAAO,CAAC2B,IAAI,GAAK,SAAS,CAAG,aAAa,CAAG,qBAAqB,CACjE,CAAAgC,QAAA,CACA3D,OAAO,CAACgE,KAAK,CACV,CAAC,CACJ,CACN,CACAhE,OAAO,CAAC2B,IAAI,GAAK,SAAS,eACzB3D,IAAA,QAAK0F,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzC3F,IAAA,SAAM0F,SAAS,CAAC,iEAAiE,CAAAC,QAAA,CAAC,SAElF,CAAM,CAAC,CACJ,CACN,cACD3F,IAAA,QAAK0F,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC3F,IAAA,CAAC1B,MAAM,CAAC2H,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CACzBE,OAAO,CAAEA,CAAA,GAAM/D,oBAAoB,CAACN,OAAO,CAAE,CAC7C0D,SAAS,CAAC,uFAAuF,CAAAC,QAAA,CAEhG9D,YAAY,CAACG,OAAO,CAACQ,EAAE,CAAC,cACvBxC,IAAA,CAACT,cAAc,EAACmG,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAEnD1F,IAAA,CAAClB,SAAS,EAAC4G,SAAS,CAAC,0CAA0C,CAAE,CAClE,CACY,CAAC,CACb,CAAC,CACL,CAAC1D,OAAO,CAACsE,OAAO,eACftG,IAAA,QAAK0F,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvF3F,IAAA,SAAM0F,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,CAC3D,CACN,EACE,CAAC,cAENzF,KAAA,QAAKwF,SAAS,QAAAxD,MAAA,CAASzB,QAAQ,GAAK,MAAM,CAAG,sCAAsC,CAAG,EAAE,CAAG,CAAAkF,QAAA,eACzFzF,KAAA,QAAAyF,QAAA,eACE3F,IAAA,OAAI0F,SAAS,qCAAAxD,MAAA,CACXzB,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CAC1C,CAAAkF,QAAA,CACA3D,OAAO,CAACG,IAAI,CACX,CAAC,cAELjC,KAAA,QAAKwF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3F,IAAA,QAAK0F,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGY,KAAK,CAAC,CAAC,CAAC,CAAC,CAACxD,GAAG,CAAC,CAACyD,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAAC3E,OAAO,CAACwC,MAAM,CAAC,cAC5BxE,IAAA,CAACV,aAAa,EAASoG,SAAS,CAAC,yBAAyB,EAAtCe,CAAwC,CAAC,cAE7DzG,IAAA,CAACnB,QAAQ,EAAS6G,SAAS,CAAC,uBAAuB,EAApCe,CAAsC,CAExD,CAAC,CACC,CAAC,cACNvG,KAAA,SAAMwF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzC3D,OAAO,CAACwC,MAAM,CAAC,IAAE,CAACxC,OAAO,CAAC4E,OAAO,CAAC,GACrC,EAAM,CAAC,EACJ,CAAC,cAEN1G,KAAA,QAAKwF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CzF,KAAA,SAAMwF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,GACxD,CAAC3D,OAAO,CAACsC,KAAK,EACX,CAAC,CACNtC,OAAO,CAAC6E,aAAa,EAAI7E,OAAO,CAAC6E,aAAa,CAAG7E,OAAO,CAACsC,KAAK,eAC7DpE,KAAA,SAAMwF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAAC3D,OAAO,CAAC6E,aAAa,EACnB,CACP,EACE,CAAC,CAGL7E,OAAO,CAAC2B,IAAI,GAAK,SAAS,cACzBzD,KAAA,QAAKwF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzF,KAAA,QAAKwF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C3F,IAAA,CAACb,SAAS,EAACuG,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAChD1F,IAAA,SAAM0F,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACzE,CAAC,CACL3D,OAAO,CAAC8E,SAAS,eAChB5G,KAAA,QAAKwF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3F,IAAA,SAAM0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzD3F,IAAA,SAAM0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE3D,OAAO,CAAC8E,SAAS,CAACxD,IAAI,CAAC,IAAI,CAAC,CAAO,CAAC,EAC1E,CACN,EACE,CAAC,cAENpD,KAAA,CAAAE,SAAA,EAAAuF,QAAA,EAEG3D,OAAO,CAAC+E,MAAM,eACb7G,KAAA,QAAKwF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C3F,IAAA,SAAM0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,CACrD3D,OAAO,CAAC+E,MAAM,CAAC1D,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACN,GAAG,CAAC,CAACiE,KAAK,CAAEhC,KAAK,gBAC3ChF,IAAA,QAEE0F,SAAS,iEAAAxD,MAAA,CACP8E,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,OAAO,CAAG,UAAU,CACnDA,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,OAAO,CAAG,UAAU,CACnDA,KAAK,GAAK,MAAM,EAAIA,KAAK,GAAK,MAAM,CAAG,aAAa,CACpDA,KAAK,GAAK,KAAK,EAAIA,KAAK,GAAK,KAAK,CAAG,YAAY,CACjDA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,QAAQ,CAAG,aAAa,CACxDA,KAAK,GAAK,MAAM,EAAIA,KAAK,GAAK,MAAM,CAAG,eAAe,CACtD,aAAa,CACZ,EATEhC,KAUN,CACF,CAAC,CACDhD,OAAO,CAAC+E,MAAM,CAACjE,MAAM,CAAG,CAAC,eACxB5C,KAAA,SAAMwF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,GAAC,CAAC3D,OAAO,CAAC+E,MAAM,CAACjE,MAAM,CAAG,CAAC,EAAO,CAC3E,EACE,CACN,cAED5C,KAAA,QAAKwF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C3F,IAAA,CAACZ,SAAS,EAACsG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/C1F,IAAA,SAAM0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpC,CAAAZ,iBAAA,CAAA/C,OAAO,CAACiF,QAAQ,UAAAlC,iBAAA,WAAhBA,iBAAA,CAAkBmC,IAAI,CAAG,eAAe,CAAG,oBAAoB,CAC5D,CAAC,EACJ,CAAC,EACN,CACH,cAGDhH,KAAA,QAAKwF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C3F,IAAA,CAACX,eAAe,EAACqG,SAAS,YAAAxD,MAAA,CAAaF,OAAO,CAACsE,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAE,CAAC,cAChGpG,KAAA,SAAMwF,SAAS,YAAAxD,MAAA,CAAaF,OAAO,CAACsE,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAAX,QAAA,EAC/E3D,OAAO,CAACsE,OAAO,CAAG,UAAU,CAAG,cAAc,CAC7CtE,OAAO,CAACmF,UAAU,EAAInF,OAAO,CAACsE,OAAO,OAAApE,MAAA,CAASF,OAAO,CAACmF,UAAU,eAAa,EAC1E,CAAC,EACJ,CAAC,EACH,CAAC,cAENjH,KAAA,CAAC5B,MAAM,CAAC2H,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAMtE,eAAe,CAACC,OAAO,CAAE,CACxCoF,QAAQ,CAAE,CAACpF,OAAO,CAACsE,OAAQ,CAC3BZ,SAAS,gHAAAxD,MAAA,CACPF,OAAO,CAACsE,OAAO,CACX,6HAA6H,CAC7H,8CAA8C,CACjD,CAAAX,QAAA,eAEH3F,IAAA,CAACjB,eAAe,EAAC2G,SAAS,CAAC,SAAS,CAAE,CAAC,cACvC1F,IAAA,SAAA2F,QAAA,CAAO3D,OAAO,CAACsE,OAAO,CAAG,aAAa,CAAG,cAAc,CAAO,CAAC,EAClD,CAAC,EACb,CAAC,EACI,CAAC,EACd,CAED,mBACEpG,KAAA,QAAKwF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC3F,IAAA,CAACF,OAAO,EAACuC,QAAQ,CAAC,WAAW,CAAE,CAAC,cAEhCrC,IAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC3F,IAAA,QAAK0F,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DzF,KAAA,QAAKwF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDzF,KAAA,CAACzB,IAAI,EAAC4I,EAAE,CAAC,GAAG,CAAC3B,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eACpG3F,IAAA,CAACd,QAAQ,EAACwG,SAAS,CAAC,cAAc,CAAE,CAAC,OAEvC,EAAM,CAAC,cACP1F,IAAA,CAACf,gBAAgB,EAACyG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtD1F,IAAA,SAAM0F,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,CAC9C9E,gBAAgB,GAAK,KAAK,eACzBX,KAAA,CAAAE,SAAA,EAAAuF,QAAA,eACE3F,IAAA,CAACf,gBAAgB,EAACyG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtD1F,IAAA,SAAM0F,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChDlD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEN,IAAI,CAClB,CAAC,EACP,CACH,CACAnB,mBAAmB,GAAK,KAAK,eAC5Bd,KAAA,CAAAE,SAAA,EAAAuF,QAAA,eACE3F,IAAA,CAACf,gBAAgB,EAACyG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtD1F,IAAA,SAAM0F,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAArF,mBAAA,CAChDsC,aAAa,CAACF,IAAI,CAACM,GAAG,EAAIA,GAAG,CAACR,EAAE,GAAKxB,mBAAmB,CAAC,UAAAV,mBAAA,iBAAzDA,mBAAA,CAA2D6B,IAAI,CAC5D,CAAC,EACP,CACH,EACE,CAAC,CACH,CAAC,CACH,CAAC,cAGNnC,IAAA,QAAK0F,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E3F,IAAA,QAAK0F,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDzF,KAAA,CAAC5B,MAAM,CAAC2G,GAAG,EACTE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAEvB3F,IAAA,OAAI0F,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAC3D9E,gBAAgB,GAAK,KAAK,CAAG,cAAc,CAAG,CAAA4B,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEN,IAAI,GAAI,UAAU,CAChF,CAAC,cACLnC,IAAA,MAAG0F,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAC3D9E,gBAAgB,GAAK,KAAK,CACvB,qDAAqD,CACrD,CAAA4B,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE6E,WAAW,GAAI,+BAA+B,CAElE,CAAC,EACM,CAAC,CACV,CAAC,CACH,CAAC,cAGNtH,IAAA,QAAK0F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC3F,IAAA,QAAK0F,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D3F,IAAA,QAAK0F,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CACjD,CAAC,CAAEnD,EAAE,CAAE,KAAK,CAAEL,IAAI,CAAE,cAAc,CAAEoF,IAAI,CAAE,KAAM,CAAC,CAAE,GAAG/H,UAAU,CAAC,CAACuD,GAAG,CAAEmB,QAAQ,eAC9EhE,KAAA,CAAC5B,MAAM,CAAC2H,MAAM,EAEZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAM,CACbvF,mBAAmB,CAACoD,QAAQ,CAAC1B,EAAE,CAAC,CAChCvB,sBAAsB,CAAC,KAAK,CAAC,CAC7BT,eAAe,CAAC,CAAE0D,QAAQ,CAAEA,QAAQ,CAAC1B,EAAG,CAAC,CAAC,CAC5C,CAAE,CACFkD,SAAS,kFAAAxD,MAAA,CACPrB,gBAAgB,GAAKqD,QAAQ,CAAC1B,EAAE,CAC5B,0CAA0C,CAC1C,iFAAiF,CACpF,CAAAmD,QAAA,eAEH3F,IAAA,SAAM0F,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEzB,QAAQ,CAACqD,IAAI,CAAO,CAAC,cAChDvH,IAAA,SAAA2F,QAAA,CAAOzB,QAAQ,CAAC/B,IAAI,CAAO,CAAC,GAfvB+B,QAAQ,CAAC1B,EAgBD,CAChB,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAENxC,IAAA,QAAK0F,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DzF,KAAA,QAAKwF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE9C3F,IAAA,QAAK0F,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCzF,KAAA,QAAKwF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DzF,KAAA,QAAKwF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD3F,IAAA,OAAI0F,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAChE3F,IAAA,WACEqG,OAAO,CAAEA,CAAA,GAAM5E,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CkE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAEvC3F,IAAA,CAAChB,yBAAyB,EAAC0G,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3C,CAAC,EACN,CAAC,cAENxF,KAAA,QAAKwF,SAAS,cAAAxD,MAAA,CAAeV,WAAW,CAAG,OAAO,CAAG,iBAAiB,CAAG,CAAAmE,QAAA,eAEvEzF,KAAA,QAAAyF,QAAA,eACE3F,IAAA,OAAI0F,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAChE3F,IAAA,QAAK0F,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBjC,kBAAkB,CAACX,GAAG,CAACY,IAAI,eAC1B3D,IAAA,WAEEqG,OAAO,CAAEA,CAAA,GAAMlF,cAAc,CAACwC,IAAI,CAACnB,EAAE,CAAE,CACvCkD,SAAS,4DAAAxD,MAAA,CACPhB,WAAW,GAAKyC,IAAI,CAACnB,EAAE,CACnB,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAmD,QAAA,cAEHzF,KAAA,QAAKwF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC3F,IAAA,SAAA2F,QAAA,CAAOhC,IAAI,CAACxB,IAAI,CAAO,CAAC,cACxBjC,KAAA,SAAMwF,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,GAAC,CAAChC,IAAI,CAACd,KAAK,CAAC,GAAC,EAAM,CAAC,EAC5C,CAAC,EAXDc,IAAI,CAACnB,EAYJ,CACT,CAAC,CACC,CAAC,EACH,CAAC,CAGL3B,gBAAgB,GAAK,KAAK,EAAI+B,aAAa,CAACE,MAAM,CAAG,CAAC,eACrD5C,KAAA,QAAAyF,QAAA,eACEzF,KAAA,OAAIwF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAC3ClD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEN,IAAI,CAAC,aACzB,EAAI,CAAC,cACLnC,IAAA,QAAK0F,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB/C,aAAa,CAACG,GAAG,CAACU,WAAW,eAC5BzD,IAAA,WAEEqG,OAAO,CAAEA,CAAA,GAAMpF,sBAAsB,CAACwC,WAAW,CAACjB,EAAE,CAAE,CACtDkD,SAAS,4DAAAxD,MAAA,CACPlB,mBAAmB,GAAKyC,WAAW,CAACjB,EAAE,CAClC,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAmD,QAAA,cAEHzF,KAAA,QAAKwF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC3F,IAAA,SAAA2F,QAAA,CAAOlC,WAAW,CAACtB,IAAI,CAAO,CAAC,cAC/BjC,KAAA,SAAMwF,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,GAAC,CAAClC,WAAW,CAACZ,KAAK,CAAC,GAAC,EAAM,CAAC,EACnD,CAAC,EAXDY,WAAW,CAACjB,EAYX,CACT,CAAC,CACC,CAAC,EACH,CACN,cAGDtC,KAAA,QAAAyF,QAAA,eACEzF,KAAA,OAAIwF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAAC,gBAC/B,CAACvE,UAAU,CAAC,CAAC,CAAC,CAAC,MAAI,CAACA,UAAU,CAAC,CAAC,CAAC,EAC7C,CAAC,cACLpB,IAAA,UACE2D,IAAI,CAAC,OAAO,CACZ6D,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,MAAM,CACV5D,KAAK,CAAEzC,UAAU,CAAC,CAAC,CAAE,CACrBsG,QAAQ,CAAGC,CAAC,EAAKtG,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC,CAAEwG,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAChE,KAAK,CAAC,CAAC,CAAE,CAC1E6B,SAAS,CAAC,QAAQ,CACnB,CAAC,EACC,CAAC,cAGNxF,KAAA,QAAAyF,QAAA,eACE3F,IAAA,OAAI0F,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAClE3F,IAAA,QAAK0F,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC5C,GAAG,CAACyB,MAAM,eACzBtE,KAAA,WAEEmG,OAAO,CAAEA,CAAA,GAAM9E,iBAAiB,CAACiD,MAAM,CAAE,CACzCkB,SAAS,8EAAAxD,MAAA,CACPZ,cAAc,GAAKkD,MAAM,CACrB,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAmB,QAAA,eAEH3F,IAAA,QAAK0F,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGY,KAAK,CAAC,CAAC,CAAC,CAAC,CAACxD,GAAG,CAAC,CAACyD,CAAC,CAAEC,CAAC,gBACtBzG,IAAA,CAACV,aAAa,EAEZoG,SAAS,YAAAxD,MAAA,CACPuE,CAAC,CAAGjC,MAAM,CAAG,iBAAiB,CAAG,eAAe,CAC/C,EAHEiC,CAIN,CACF,CAAC,CACC,CAAC,cACNzG,IAAA,SAAA2F,QAAA,CAAOnB,MAAM,CAAG,CAAC,IAAAtC,MAAA,CAAMsC,MAAM,YAAY,aAAa,CAAO,CAAC,GAlBzDA,MAmBC,CACT,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNtE,KAAA,QAAKwF,SAAS,CAAC,QAAQ,CAAAC,QAAA,eAErB3F,IAAA,QAAK0F,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDzF,KAAA,QAAKwF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,eAC1F3F,IAAA,QAAA2F,QAAA,cACEzF,KAAA,MAAGwF,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,UACnB,CAAC5B,yBAAyB,CAACjB,MAAM,CAAC,MAAI,CAACrD,QAAQ,CAACqD,MAAM,CAAC,WACjE,EAAG,CAAC,CACD,CAAC,cAEN5C,KAAA,QAAKwF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAE1C3F,IAAA,WACE6D,KAAK,CAAElD,MAAO,CACd+G,QAAQ,CAAGC,CAAC,EAAK/G,SAAS,CAAC+G,CAAC,CAACE,MAAM,CAAChE,KAAK,CAAE,CAC3C6B,SAAS,CAAC,sFAAsF,CAAAC,QAAA,CAE/F/B,WAAW,CAACb,GAAG,CAAC+E,MAAM,eACrB9H,IAAA,WAA2B6D,KAAK,CAAEiE,MAAM,CAACjE,KAAM,CAAA8B,QAAA,CAC5CmC,MAAM,CAAChE,KAAK,EADFgE,MAAM,CAACjE,KAEZ,CACT,CAAC,CACI,CAAC,cAGT3D,KAAA,QAAKwF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C3F,IAAA,WACEqG,OAAO,CAAEA,CAAA,GAAM3F,WAAW,CAAC,MAAM,CAAE,CACnCgF,SAAS,qCAAAxD,MAAA,CACPzB,QAAQ,GAAK,MAAM,CACf,0CAA0C,CAC1C,eAAe,CAClB,CAAAkF,QAAA,cAEH3F,IAAA,CAACrB,cAAc,EAAC+G,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,cACT1F,IAAA,WACEqG,OAAO,CAAEA,CAAA,GAAM3F,WAAW,CAAC,MAAM,CAAE,CACnCgF,SAAS,qCAAAxD,MAAA,CACPzB,QAAQ,GAAK,MAAM,CACf,0CAA0C,CAC1C,eAAe,CAClB,CAAAkF,QAAA,cAEH3F,IAAA,CAACpB,cAAc,EAAC8G,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN1F,IAAA,CAACzB,eAAe,EAACwJ,IAAI,CAAC,MAAM,CAAApC,QAAA,cAC1B3F,IAAA,CAAC1B,MAAM,CAAC2G,GAAG,EAETE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAE,CAAE,CACrBM,SAAS,IAAAxD,MAAA,CACPzB,QAAQ,GAAK,MAAM,CACf,sDAAsD,CACtD,WAAW,CACd,CAAAkF,QAAA,CAEF5B,yBAAyB,CAAChB,GAAG,CAAC,CAACf,OAAO,CAAEgD,KAAK,gBAC5ChF,IAAA,CAAC6E,WAAW,EAAkB7C,OAAO,CAAEA,OAAQ,CAACgD,KAAK,CAAEA,KAAM,EAA3ChD,OAAO,CAACQ,EAAqC,CAChE,CAAC,KAAAN,MAAA,CAZMzB,QAAQ,MAAAyB,MAAA,CAAIrB,gBAAgB,MAAAqB,MAAA,CAAIvB,MAAM,CAapC,CAAC,CACE,CAAC,CAEjBoD,yBAAyB,CAACjB,MAAM,GAAK,CAAC,eACrC5C,KAAA,QAAKwF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3F,IAAA,QAAK0F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjC3F,IAAA,CAACtB,UAAU,EAACgH,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACzC,CAAC,cACN1F,IAAA,OAAI0F,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC/E3F,IAAA,MAAG0F,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}