import React, { useState } from 'react';
import { StarIcon, ChatBubbleLeftIcon, HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const mockReviews = [
  {
    id: 1,
    user: {
      name: '<PERSON>',
      avatar: 'https://via.placeholder.com/40',
      verified: true
    },
    rating: 5,
    title: 'Excellent quality and fast delivery!',
    comment: 'I absolutely love these headphones. The sound quality is amazing and they are very comfortable to wear for long periods. Highly recommend!',
    date: '2024-01-15',
    helpful: 12,
    notHelpful: 1,
    productName: 'Wireless Headphones'
  },
  {
    id: 2,
    user: {
      name: '<PERSON>',
      avatar: 'https://via.placeholder.com/40',
      verified: false
    },
    rating: 4,
    title: 'Good value for money',
    comment: 'The smart watch works well and has all the features I need. Battery life could be better, but overall satisfied with the purchase.',
    date: '2024-01-12',
    helpful: 8,
    notHelpful: 2,
    productName: 'Smart Watch'
  },
  {
    id: 3,
    user: {
      name: '<PERSON>',
      avatar: 'https://via.placeholder.com/40',
      verified: true
    },
    rating: 5,
    title: 'Perfect for outdoor activities',
    comment: 'This Bluetooth speaker is waterproof and the sound quality is incredible. Perfect for beach trips and pool parties!',
    date: '2024-01-10',
    helpful: 15,
    notHelpful: 0,
    productName: 'Bluetooth Speaker'
  },
  {
    id: 4,
    user: {
      name: 'David Wilson',
      avatar: 'https://via.placeholder.com/40',
      verified: true
    },
    rating: 3,
    title: 'Average product',
    comment: 'The product is okay but not exceptional. It does what it promises but there are better alternatives in the market.',
    date: '2024-01-08',
    helpful: 5,
    notHelpful: 3,
    productName: 'Phone Case'
  }
];

const ProductReviews = () => {
  const [reviews, setReviews] = useState(mockReviews);
  const [showWriteReview, setShowWriteReview] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 5,
    title: '',
    comment: ''
  });

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: (reviews.filter(review => review.rating === rating).length / reviews.length) * 100
  }));

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating, size = 'w-5 h-5') => {
    return [...Array(5)].map((_, i) => (
      i < rating ? (
        <StarIconSolid key={i} className={`${size} text-light-orange-400`} />
      ) : (
        <StarIcon key={i} className={`${size} text-light-orange-200`} />
      )
    ));
  };

  const handleSubmitReview = (e) => {
    e.preventDefault();
    const review = {
      id: reviews.length + 1,
      user: {
        name: 'You',
        avatar: 'https://via.placeholder.com/40',
        verified: false
      },
      rating: newReview.rating,
      title: newReview.title,
      comment: newReview.comment,
      date: new Date().toISOString().split('T')[0],
      helpful: 0,
      notHelpful: 0,
      productName: 'Current Product'
    };

    setReviews([review, ...reviews]);
    setNewReview({ rating: 5, title: '', comment: '' });
    setShowWriteReview(false);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-white flex items-center">
            <ChatBubbleLeftIcon className="w-6 h-6 mr-2" />
            Customer Reviews
          </h2>
          <button
            onClick={() => setShowWriteReview(true)}
            className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition-colors text-sm font-medium"
          >
            Write Review
          </button>
        </div>
      </div>

      {/* Reviews Summary */}
      <div className="p-6 border-b border-light-orange-100">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start space-x-2 mb-2">
              <span className="text-3xl font-bold text-light-orange-800">
                {averageRating.toFixed(1)}
              </span>
              <div className="flex">
                {renderStars(Math.round(averageRating))}
              </div>
            </div>
            <p className="text-light-orange-600">
              Based on {reviews.length} review{reviews.length !== 1 ? 's' : ''}
            </p>
          </div>

          <div className="space-y-2">
            {ratingDistribution.map(({ rating, count, percentage }) => (
              <div key={rating} className="flex items-center space-x-3">
                <div className="flex items-center space-x-1 w-12">
                  <span className="text-sm text-light-orange-700">{rating}</span>
                  <StarIconSolid className="w-4 h-4 text-light-orange-400" />
                </div>
                <div className="flex-1 bg-light-orange-100 rounded-full h-2">
                  <div
                    className="bg-light-orange-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <span className="text-sm text-light-orange-600 w-8">{count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="p-6">
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-b border-light-orange-100 pb-6 last:border-b-0">
              <div className="flex items-start space-x-4">
                <img
                  src={review.user.avatar}
                  alt={review.user.name}
                  className="w-12 h-12 rounded-full border-2 border-light-orange-200"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-light-orange-800">{review.user.name}</h4>
                    {review.user.verified && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full border border-green-200">
                        Verified Purchase
                      </span>
                    )}
                  </div>

                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex">
                      {renderStars(review.rating, 'w-4 h-4')}
                    </div>
                    <span className="text-sm text-light-orange-600">{formatDate(review.date)}</span>
                  </div>

                  <h5 className="font-medium text-light-orange-800 mb-2">{review.title}</h5>
                  <p className="text-light-orange-700 mb-3">{review.comment}</p>

                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-light-orange-600">For: {review.productName}</span>
                    <div className="flex items-center space-x-2">
                      <button className="flex items-center space-x-1 text-light-orange-600 hover:text-light-orange-700 transition-colors">
                        <HandThumbUpIcon className="w-4 h-4" />
                        <span>Helpful ({review.helpful})</span>
                      </button>
                      <button className="flex items-center space-x-1 text-light-orange-600 hover:text-light-orange-700 transition-colors">
                        <HandThumbDownIcon className="w-4 h-4" />
                        <span>({review.notHelpful})</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Write Review Modal */}
      {showWriteReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4">
            <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white">Write a Review</h2>
                <button
                  onClick={() => setShowWriteReview(false)}
                  className="text-white hover:text-light-orange-200 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <form onSubmit={handleSubmitReview} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-semibold text-light-orange-800 mb-2">
                  Rating
                </label>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <button
                      key={rating}
                      type="button"
                      onClick={() => setNewReview({...newReview, rating})}
                      className="focus:outline-none"
                    >
                      {rating <= newReview.rating ? (
                        <StarIconSolid className="w-8 h-8 text-light-orange-400 hover:text-light-orange-500 transition-colors" />
                      ) : (
                        <StarIcon className="w-8 h-8 text-light-orange-200 hover:text-light-orange-300 transition-colors" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-light-orange-800 mb-2">
                  Review Title
                </label>
                <input
                  type="text"
                  value={newReview.title}
                  onChange={(e) => setNewReview({...newReview, title: e.target.value})}
                  className="w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors"
                  placeholder="Summarize your experience"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-light-orange-800 mb-2">
                  Your Review
                </label>
                <textarea
                  value={newReview.comment}
                  onChange={(e) => setNewReview({...newReview, comment: e.target.value})}
                  rows={4}
                  className="w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors resize-none"
                  placeholder="Tell others about your experience with this product"
                  required
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowWriteReview(false)}
                  className="flex-1 px-6 py-3 border border-light-orange-300 text-light-orange-700 rounded-lg hover:bg-light-orange-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold"
                >
                  Submit Review
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductReviews;
