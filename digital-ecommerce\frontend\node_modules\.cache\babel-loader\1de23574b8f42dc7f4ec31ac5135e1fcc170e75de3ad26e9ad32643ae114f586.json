{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminProductsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MagnifyingGlassIcon, FunnelIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminProductsPage = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAdmin();\n  const {\n    products,\n    categories,\n    addProduct\n  } = useProducts();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      var _product$description;\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || ((_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.toLowerCase().includes(searchQuery.toLowerCase()));\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n    return filtered;\n  }, [searchQuery, selectedCategory, selectedType, sortBy]);\n  const handleSelectProduct = productId => {\n    setSelectedProducts(prev => prev.includes(productId) ? prev.filter(id => id !== productId) : [...prev, productId]);\n  };\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n  const ProductCard = ({\n    product\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    layout: true,\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.9\n    },\n    className: `p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl ${getThemeClasses('bg-white', 'bg-slate-800')} ${selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: product.image,\n        alt: product.name,\n        className: \"w-full h-48 object-cover rounded-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 left-2\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: selectedProducts.includes(product.id),\n          onChange: () => handleSelectProduct(product.id),\n          className: \"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 text-xs font-medium rounded-full ${product.inStock ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'}`,\n          children: product.inStock ? 'In Stock' : 'Out of Stock'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `font-semibold truncate ${getThemeClasses('text-gray-900', 'text-white')}`,\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `text-sm mt-1 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n        children: product.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-bold text-light-orange-600\",\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), product.stockCount && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n          children: [\"Stock: \", product.stockCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-slate-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')}`,\n          children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')}`,\n          children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n            className: \"w-4 h-4 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')}`,\n          children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full ${product.type === 'digital' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'}`,\n        children: product.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n  const ProductRow = ({\n    product\n  }) => /*#__PURE__*/_jsxDEV(motion.tr, {\n    layout: true,\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    className: `transition-colors ${getThemeClasses('hover:bg-gray-50', 'hover:bg-slate-700')} ${selectedProducts.includes(product.id) ? 'bg-light-orange-50 dark:bg-light-orange-900/10' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"checkbox\",\n        checked: selectedProducts.includes(product.id),\n        onChange: () => handleSelectProduct(product.id),\n        className: \"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.image,\n          alt: product.name,\n          className: \"w-12 h-12 rounded-lg object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: `font-medium ${getThemeClasses('text-gray-900', 'text-white')}`,\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-sm ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n            children: product.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-lg font-semibold text-light-orange-600\",\n        children: [\"$\", product.price]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `px-2 py-1 text-xs font-medium rounded-full ${product.inStock ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'}`,\n        children: product.inStock ? `${product.stockCount || 'In Stock'}` : 'Out of Stock'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `px-2 py-1 text-xs font-medium rounded-full ${product.type === 'digital' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'}`,\n        children: product.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')}`,\n          children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')}`,\n          children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n            className: \"w-4 h-4 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')}`,\n          children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: `text-3xl font-bold ${getThemeClasses('text-gray-900', 'text-white')}`,\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `mt-2 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n            children: \"Manage your product catalog\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setShowAddProductModal(true),\n          className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-6 rounded-xl shadow-lg ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search products...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: `w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${getThemeClasses('border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500', 'border-slate-600 bg-slate-700 text-white placeholder-gray-400 focus:border-light-orange-400 focus:ring-light-orange-400')}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: `flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')}`,\n              children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 bg-gray-100 dark:bg-slate-700 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('grid'),\n                className: `p-2 rounded-md transition-colors ${viewMode === 'grid' ? 'bg-white dark:bg-slate-600 shadow-sm' : 'hover:bg-gray-200 dark:hover:bg-slate-600'}`,\n                children: /*#__PURE__*/_jsxDEV(Squares2X2Icon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('list'),\n                className: `p-2 rounded-md transition-colors ${viewMode === 'list' ? 'bg-white dark:bg-slate-600 shadow-sm' : 'hover:bg-gray-200 dark:hover:bg-slate-600'}`,\n                children: /*#__PURE__*/_jsxDEV(ListBulletIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showFilters && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: 'auto'\n            },\n            exit: {\n              opacity: 0,\n              height: 0\n            },\n            className: \"mt-4 pt-4 border-t border-gray-200 dark:border-slate-700\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedCategory,\n                onChange: e => setSelectedCategory(e.target.value),\n                className: `px-3 py-2 rounded-lg border transition-colors ${getThemeClasses('border-gray-300 bg-white text-gray-900', 'border-slate-600 bg-slate-700 text-white')}`,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedType,\n                onChange: e => setSelectedType(e.target.value),\n                className: `px-3 py-2 rounded-lg border transition-colors ${getThemeClasses('border-gray-300 bg-white text-gray-900', 'border-slate-600 bg-slate-700 text-white')}`,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"physical\",\n                  children: \"Physical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"digital\",\n                  children: \"Digital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: `px-3 py-2 rounded-lg border transition-colors ${getThemeClasses('border-gray-300 bg-white text-gray-900', 'border-slate-600 bg-slate-700 text-white')}`,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"name\",\n                  children: \"Sort by Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"price\",\n                  children: \"Sort by Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"stock\",\n                  children: \"Sort by Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"category\",\n                  children: \"Sort by Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n          children: [\"Showing \", filteredProducts.length, \" of \", products.length, \" products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), selectedProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n            children: [selectedProducts.length, \" selected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors\",\n            children: \"Delete Selected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), viewMode === 'grid' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `rounded-xl shadow-lg overflow-hidden ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200 dark:divide-slate-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: getThemeClasses('bg-gray-50', 'bg-slate-700'),\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                  onChange: handleSelectAll,\n                  className: \"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: `px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: `px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                children: \"Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: `px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                children: \"Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: `px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: `px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${getThemeClasses('text-gray-500', 'text-gray-400')}`,\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: `divide-y ${getThemeClasses('divide-gray-200', 'divide-slate-700')}`,\n            children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductRow, {\n                product: product\n              }, product.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddProductModal, {\n      isOpen: showAddProductModal,\n      onClose: () => setShowAddProductModal(false),\n      onSubmit: async productData => {\n        const result = await addProduct(productData);\n        if (result.success) {\n          setShowAddProductModal(false);\n          // Show success notification\n          console.log('Product added successfully:', result.product);\n        } else {\n          // Show error notification\n          console.error('Failed to add product:', result.error);\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminProductsPage, \"yTQhRIQmoAJUo9pdYW4Uw+PXIrU=\", false, function () {\n  return [useAdmin, useProducts];\n});\n_c = AdminProductsPage;\nexport default AdminProductsPage;\nvar _c;\n$RefreshReg$(_c, \"AdminProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "useAdmin", "useProducts", "AdminLayout", "AddProductModal", "jsxDEV", "_jsxDEV", "AdminProductsPage", "_s", "hasPermission", "products", "categories", "addProduct", "viewMode", "setViewMode", "showAddProductModal", "setShowAddProductModal", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedType", "setSelectedType", "sortBy", "setSortBy", "showFilters", "setShowFilters", "selectedProducts", "setSelectedProducts", "filteredProducts", "filtered", "filter", "product", "_product$description", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesType", "type", "sort", "a", "b", "localeCompare", "price", "stockCount", "handleSelectProduct", "productId", "prev", "id", "handleSelectAll", "length", "map", "p", "ProductCard", "div", "layout", "initial", "opacity", "scale", "animate", "exit", "className", "getThemeClasses", "children", "src", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "checked", "onChange", "inStock", "ProductRow", "tr", "button", "whileHover", "whileTap", "onClick", "placeholder", "value", "e", "target", "height", "isOpen", "onClose", "onSubmit", "productData", "result", "success", "console", "log", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\n\nconst AdminProductsPage = () => {\n  const { hasPermission } = useAdmin();\n  const { products, categories, addProduct } = useProducts();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           product.description?.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      \n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n\n    return filtered;\n  }, [searchQuery, selectedCategory, selectedType, sortBy]);\n\n  const handleSelectProduct = (productId) => {\n    setSelectedProducts(prev => \n      prev.includes(productId) \n        ? prev.filter(id => id !== productId)\n        : [...prev, productId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n\n  const ProductCard = ({ product }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className={`p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl ${\n        getThemeClasses('bg-white', 'bg-slate-800')\n      } ${selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''}`}\n    >\n      <div className=\"relative\">\n        <img\n          src={product.image}\n          alt={product.name}\n          className=\"w-full h-48 object-cover rounded-lg\"\n        />\n        <div className=\"absolute top-2 left-2\">\n          <input\n            type=\"checkbox\"\n            checked={selectedProducts.includes(product.id)}\n            onChange={() => handleSelectProduct(product.id)}\n            className=\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n          />\n        </div>\n        <div className=\"absolute top-2 right-2\">\n          <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n            product.inStock \n              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'\n              : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'\n          }`}>\n            {product.inStock ? 'In Stock' : 'Out of Stock'}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"mt-4\">\n        <h3 className={`font-semibold truncate ${\n          getThemeClasses('text-gray-900', 'text-white')\n        }`}>\n          {product.name}\n        </h3>\n        <p className={`text-sm mt-1 ${\n          getThemeClasses('text-gray-600', 'text-gray-400')\n        }`}>\n          {product.category}\n        </p>\n        <div className=\"flex items-center justify-between mt-3\">\n          <span className=\"text-lg font-bold text-light-orange-600\">\n            ${product.price}\n          </span>\n          {product.stockCount && (\n            <span className={`text-sm ${\n              getThemeClasses('text-gray-500', 'text-gray-400')\n            }`}>\n              Stock: {product.stockCount}\n            </span>\n          )}\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-slate-700\">\n        <div className=\"flex space-x-2\">\n          <button className={`p-2 rounded-lg transition-colors ${\n            getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')\n          }`}>\n            <EyeIcon className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          {hasPermission('products') && (\n            <button className={`p-2 rounded-lg transition-colors ${\n              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')\n            }`}>\n              <PencilIcon className=\"w-4 h-4 text-blue-500\" />\n            </button>\n          )}\n          {hasPermission('products') && (\n            <button className={`p-2 rounded-lg transition-colors ${\n              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')\n            }`}>\n              <TrashIcon className=\"w-4 h-4 text-red-500\" />\n            </button>\n          )}\n        </div>\n        <span className={`text-xs px-2 py-1 rounded-full ${\n          product.type === 'digital' \n            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'\n            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'\n        }`}>\n          {product.type}\n        </span>\n      </div>\n    </motion.div>\n  );\n\n  const ProductRow = ({ product }) => (\n    <motion.tr\n      layout\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className={`transition-colors ${\n        getThemeClasses('hover:bg-gray-50', 'hover:bg-slate-700')\n      } ${selectedProducts.includes(product.id) ? 'bg-light-orange-50 dark:bg-light-orange-900/10' : ''}`}\n    >\n      <td className=\"px-6 py-4\">\n        <input\n          type=\"checkbox\"\n          checked={selectedProducts.includes(product.id)}\n          onChange={() => handleSelectProduct(product.id)}\n          className=\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n        />\n      </td>\n      <td className=\"px-6 py-4\">\n        <div className=\"flex items-center space-x-3\">\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-12 h-12 rounded-lg object-cover\"\n          />\n          <div>\n            <p className={`font-medium ${\n              getThemeClasses('text-gray-900', 'text-white')\n            }`}>\n              {product.name}\n            </p>\n            <p className={`text-sm ${\n              getThemeClasses('text-gray-500', 'text-gray-400')\n            }`}>\n              {product.category}\n            </p>\n          </div>\n        </div>\n      </td>\n      <td className=\"px-6 py-4\">\n        <span className=\"text-lg font-semibold text-light-orange-600\">\n          ${product.price}\n        </span>\n      </td>\n      <td className=\"px-6 py-4\">\n        <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n          product.inStock \n            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'\n            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'\n        }`}>\n          {product.inStock ? `${product.stockCount || 'In Stock'}` : 'Out of Stock'}\n        </span>\n      </td>\n      <td className=\"px-6 py-4\">\n        <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n          product.type === 'digital' \n            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'\n            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'\n        }`}>\n          {product.type}\n        </span>\n      </td>\n      <td className=\"px-6 py-4\">\n        <div className=\"flex space-x-2\">\n          <button className={`p-2 rounded-lg transition-colors ${\n            getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')\n          }`}>\n            <EyeIcon className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          {hasPermission('products') && (\n            <button className={`p-2 rounded-lg transition-colors ${\n              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')\n            }`}>\n              <PencilIcon className=\"w-4 h-4 text-blue-500\" />\n            </button>\n          )}\n          {hasPermission('products') && (\n            <button className={`p-2 rounded-lg transition-colors ${\n              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')\n            }`}>\n              <TrashIcon className=\"w-4 h-4 text-red-500\" />\n            </button>\n          )}\n        </div>\n      </td>\n    </motion.tr>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className={`text-3xl font-bold ${\n              getThemeClasses('text-gray-900', 'text-white')\n            }`}>\n              Products\n            </h1>\n            <p className={`mt-2 ${\n              getThemeClasses('text-gray-600', 'text-gray-400')\n            }`}>\n              Manage your product catalog\n            </p>\n          </div>\n          {hasPermission('products') && (\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowAddProductModal(true)}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n            >\n              <PlusIcon className=\"w-5 h-5\" />\n              <span>Add Product</span>\n            </motion.button>\n          )}\n        </div>\n\n        {/* Toolbar */}\n        <div className={`p-6 rounded-xl shadow-lg ${\n          getThemeClasses('bg-white', 'bg-slate-800')\n        }`}>\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n            {/* Search */}\n            <div className=\"relative flex-1 max-w-md\">\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${\n                  getThemeClasses(\n                    'border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500',\n                    'border-slate-600 bg-slate-700 text-white placeholder-gray-400 focus:border-light-orange-400 focus:ring-light-orange-400'\n                  )\n                }`}\n              />\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Filters */}\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${\n                  getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')\n                }`}\n              >\n                <FunnelIcon className=\"w-5 h-5\" />\n                <span>Filters</span>\n              </button>\n\n              {/* View Mode */}\n              <div className=\"flex items-center space-x-1 bg-gray-100 dark:bg-slate-700 rounded-lg p-1\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'grid' \n                      ? 'bg-white dark:bg-slate-600 shadow-sm' \n                      : 'hover:bg-gray-200 dark:hover:bg-slate-600'\n                  }`}\n                >\n                  <Squares2X2Icon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'list' \n                      ? 'bg-white dark:bg-slate-600 shadow-sm' \n                      : 'hover:bg-gray-200 dark:hover:bg-slate-600'\n                  }`}\n                >\n                  <ListBulletIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters Panel */}\n          <AnimatePresence>\n            {showFilters && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-4 pt-4 border-t border-gray-200 dark:border-slate-700\"\n              >\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className={`px-3 py-2 rounded-lg border transition-colors ${\n                      getThemeClasses(\n                        'border-gray-300 bg-white text-gray-900',\n                        'border-slate-600 bg-slate-700 text-white'\n                      )\n                    }`}\n                  >\n                    <option value=\"all\">All Categories</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n\n                  <select\n                    value={selectedType}\n                    onChange={(e) => setSelectedType(e.target.value)}\n                    className={`px-3 py-2 rounded-lg border transition-colors ${\n                      getThemeClasses(\n                        'border-gray-300 bg-white text-gray-900',\n                        'border-slate-600 bg-slate-700 text-white'\n                      )\n                    }`}\n                  >\n                    <option value=\"all\">All Types</option>\n                    <option value=\"physical\">Physical</option>\n                    <option value=\"digital\">Digital</option>\n                  </select>\n\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className={`px-3 py-2 rounded-lg border transition-colors ${\n                      getThemeClasses(\n                        'border-gray-300 bg-white text-gray-900',\n                        'border-slate-600 bg-slate-700 text-white'\n                      )\n                    }`}\n                  >\n                    <option value=\"name\">Sort by Name</option>\n                    <option value=\"price\">Sort by Price</option>\n                    <option value=\"stock\">Sort by Stock</option>\n                    <option value=\"category\">Sort by Category</option>\n                  </select>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Results Info */}\n        <div className=\"flex items-center justify-between\">\n          <p className={`text-sm ${\n            getThemeClasses('text-gray-600', 'text-gray-400')\n          }`}>\n            Showing {filteredProducts.length} of {products.length} products\n          </p>\n          {selectedProducts.length > 0 && (\n            <div className=\"flex items-center space-x-4\">\n              <span className={`text-sm ${\n                getThemeClasses('text-gray-600', 'text-gray-400')\n              }`}>\n                {selectedProducts.length} selected\n              </span>\n              <button className=\"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors\">\n                Delete Selected\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Products Display */}\n        {viewMode === 'grid' ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            <AnimatePresence>\n              {filteredProducts.map(product => (\n                <ProductCard key={product.id} product={product} />\n              ))}\n            </AnimatePresence>\n          </div>\n        ) : (\n          <div className={`rounded-xl shadow-lg overflow-hidden ${\n            getThemeClasses('bg-white', 'bg-slate-800')\n          }`}>\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-slate-700\">\n              <thead className={getThemeClasses('bg-gray-50', 'bg-slate-700')}>\n                <tr>\n                  <th className=\"px-6 py-3 text-left\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}\n                      onChange={handleSelectAll}\n                      className=\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n                    />\n                  </th>\n                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${\n                    getThemeClasses('text-gray-500', 'text-gray-400')\n                  }`}>\n                    Product\n                  </th>\n                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${\n                    getThemeClasses('text-gray-500', 'text-gray-400')\n                  }`}>\n                    Price\n                  </th>\n                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${\n                    getThemeClasses('text-gray-500', 'text-gray-400')\n                  }`}>\n                    Stock\n                  </th>\n                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${\n                    getThemeClasses('text-gray-500', 'text-gray-400')\n                  }`}>\n                    Type\n                  </th>\n                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${\n                    getThemeClasses('text-gray-500', 'text-gray-400')\n                  }`}>\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className={`divide-y ${\n                getThemeClasses('divide-gray-200', 'divide-slate-700')\n              }`}>\n                <AnimatePresence>\n                  {filteredProducts.map(product => (\n                    <ProductRow key={product.id} product={product} />\n                  ))}\n                </AnimatePresence>\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* Add Product Modal */}\n      <AddProductModal\n        isOpen={showAddProductModal}\n        onClose={() => setShowAddProductModal(false)}\n        onSubmit={async (productData) => {\n          const result = await addProduct(productData);\n          if (result.success) {\n            setShowAddProductModal(false);\n            // Show success notification\n            console.log('Product added successfully:', result.product);\n          } else {\n            // Show error notification\n            console.error('Failed to add product:', result.error);\n          }\n        }}\n      />\n    </AdminLayout>\n  );\n};\n\nexport default AdminProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,UAAU,EACVC,cAAc,EACdC,cAAc,QACT,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAc,CAAC,GAAGR,QAAQ,CAAC,CAAC;EACpC,MAAM;IAAES,QAAQ;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGV,WAAW,CAAC,CAAC;EAC1D,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAAC0B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAMwC,gBAAgB,GAAGvC,OAAO,CAAC,MAAM;IACrC,IAAIwC,QAAQ,GAAGpB,QAAQ,CAACqB,MAAM,CAACC,OAAO,IAAI;MAAA,IAAAC,oBAAA;MACxC,MAAMC,aAAa,GAAGF,OAAO,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,WAAW,CAACmB,WAAW,CAAC,CAAC,CAAC,MAAAH,oBAAA,GAC/DD,OAAO,CAACM,WAAW,cAAAL,oBAAA,uBAAnBA,oBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,WAAW,CAACmB,WAAW,CAAC,CAAC,CAAC;MAC3F,MAAMG,eAAe,GAAGpB,gBAAgB,KAAK,KAAK,IAAIa,OAAO,CAACQ,QAAQ,KAAKrB,gBAAgB;MAC3F,MAAMsB,WAAW,GAAGpB,YAAY,KAAK,KAAK,IAAIW,OAAO,CAACU,IAAI,KAAKrB,YAAY;MAE3E,OAAOa,aAAa,IAAIK,eAAe,IAAIE,WAAW;IACxD,CAAC,CAAC;;IAEF;IACAX,QAAQ,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,QAAQtB,MAAM;QACZ,KAAK,MAAM;UACT,OAAOqB,CAAC,CAACT,IAAI,CAACW,aAAa,CAACD,CAAC,CAACV,IAAI,CAAC;QACrC,KAAK,OAAO;UACV,OAAOS,CAAC,CAACG,KAAK,GAAGF,CAAC,CAACE,KAAK;QAC1B,KAAK,OAAO;UACV,OAAO,CAACF,CAAC,CAACG,UAAU,IAAI,CAAC,KAAKJ,CAAC,CAACI,UAAU,IAAI,CAAC,CAAC;QAClD,KAAK,UAAU;UACb,OAAOJ,CAAC,CAACJ,QAAQ,CAACM,aAAa,CAACD,CAAC,CAACL,QAAQ,CAAC;QAC7C;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEF,OAAOV,QAAQ;EACjB,CAAC,EAAE,CAACb,WAAW,EAAEE,gBAAgB,EAAEE,YAAY,EAAEE,MAAM,CAAC,CAAC;EAEzD,MAAM0B,mBAAmB,GAAIC,SAAS,IAAK;IACzCtB,mBAAmB,CAACuB,IAAI,IACtBA,IAAI,CAACd,QAAQ,CAACa,SAAS,CAAC,GACpBC,IAAI,CAACpB,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAKF,SAAS,CAAC,GACnC,CAAC,GAAGC,IAAI,EAAED,SAAS,CACzB,CAAC;EACH,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI1B,gBAAgB,CAAC2B,MAAM,KAAKzB,gBAAgB,CAACyB,MAAM,EAAE;MACvD1B,mBAAmB,CAAC,EAAE,CAAC;IACzB,CAAC,MAAM;MACLA,mBAAmB,CAACC,gBAAgB,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAC;IAAEzB;EAAQ,CAAC,kBAC9B1B,OAAA,CAACf,MAAM,CAACmE,GAAG;IACTC,MAAM;IACNC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACpCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IAClCE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACjCG,SAAS,EAAE,wEACTC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,IACzCvC,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAC,GAAG,8BAA8B,GAAG,EAAE,EAAG;IAAAe,QAAA,gBAElF7D,OAAA;MAAK2D,SAAS,EAAC,UAAU;MAAAE,QAAA,gBACvB7D,OAAA;QACE8D,GAAG,EAAEpC,OAAO,CAACqC,KAAM;QACnBC,GAAG,EAAEtC,OAAO,CAACG,IAAK;QAClB8B,SAAS,EAAC;MAAqC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACFpE,OAAA;QAAK2D,SAAS,EAAC,uBAAuB;QAAAE,QAAA,eACpC7D,OAAA;UACEoC,IAAI,EAAC,UAAU;UACfiC,OAAO,EAAEhD,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAE;UAC/CwB,QAAQ,EAAEA,CAAA,KAAM3B,mBAAmB,CAACjB,OAAO,CAACoB,EAAE,CAAE;UAChDa,SAAS,EAAC;QAA4F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpE,OAAA;QAAK2D,SAAS,EAAC,wBAAwB;QAAAE,QAAA,eACrC7D,OAAA;UAAM2D,SAAS,EAAE,8CACfjC,OAAO,CAAC6C,OAAO,GACX,sEAAsE,GACtE,8DAA8D,EACjE;UAAAV,QAAA,EACAnC,OAAO,CAAC6C,OAAO,GAAG,UAAU,GAAG;QAAc;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpE,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAE,QAAA,gBACnB7D,OAAA;QAAI2D,SAAS,EAAE,0BACbC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;QAAAC,QAAA,EACAnC,OAAO,CAACG;MAAI;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACLpE,OAAA;QAAG2D,SAAS,EAAE,gBACZC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;QAAAC,QAAA,EACAnC,OAAO,CAACQ;MAAQ;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACJpE,OAAA;QAAK2D,SAAS,EAAC,wCAAwC;QAAAE,QAAA,gBACrD7D,OAAA;UAAM2D,SAAS,EAAC,yCAAyC;UAAAE,QAAA,GAAC,GACvD,EAACnC,OAAO,CAACe,KAAK;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EACN1C,OAAO,CAACgB,UAAU,iBACjB1C,OAAA;UAAM2D,SAAS,EAAE,WACfC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;UAAAC,QAAA,GAAC,SACK,EAACnC,OAAO,CAACgB,UAAU;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpE,OAAA;MAAK2D,SAAS,EAAC,4FAA4F;MAAAE,QAAA,gBACzG7D,OAAA;QAAK2D,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B7D,OAAA;UAAQ2D,SAAS,EAAE,oCACjBC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;UAAAC,QAAA,eACD7D,OAAA,CAACV,OAAO;YAACqE,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRjE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UAAQ2D,SAAS,EAAE,oCACjBC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;UAAAC,QAAA,eACD7D,OAAA,CAACZ,UAAU;YAACuE,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACT,EACAjE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UAAQ2D,SAAS,EAAE,oCACjBC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;UAAAC,QAAA,eACD7D,OAAA,CAACX,SAAS;YAACsE,SAAS,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNpE,OAAA;QAAM2D,SAAS,EAAE,kCACfjC,OAAO,CAACU,IAAI,KAAK,SAAS,GACtB,kEAAkE,GAClE,kEAAkE,EACrE;QAAAyB,QAAA,EACAnC,OAAO,CAACU;MAAI;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMI,UAAU,GAAGA,CAAC;IAAE9C;EAAQ,CAAC,kBAC7B1B,OAAA,CAACf,MAAM,CAACwF,EAAE;IACRpB,MAAM;IACNC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACxBE,OAAO,EAAE;MAAEF,OAAO,EAAE;IAAE,CAAE;IACxBG,IAAI,EAAE;MAAEH,OAAO,EAAE;IAAE,CAAE;IACrBI,SAAS,EAAE,qBACTC,eAAe,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,IACvDvC,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAC,GAAG,gDAAgD,GAAG,EAAE,EAAG;IAAAe,QAAA,gBAEpG7D,OAAA;MAAI2D,SAAS,EAAC,WAAW;MAAAE,QAAA,eACvB7D,OAAA;QACEoC,IAAI,EAAC,UAAU;QACfiC,OAAO,EAAEhD,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAE;QAC/CwB,QAAQ,EAAEA,CAAA,KAAM3B,mBAAmB,CAACjB,OAAO,CAACoB,EAAE,CAAE;QAChDa,SAAS,EAAC;MAA4F;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACLpE,OAAA;MAAI2D,SAAS,EAAC,WAAW;MAAAE,QAAA,eACvB7D,OAAA;QAAK2D,SAAS,EAAC,6BAA6B;QAAAE,QAAA,gBAC1C7D,OAAA;UACE8D,GAAG,EAAEpC,OAAO,CAACqC,KAAM;UACnBC,GAAG,EAAEtC,OAAO,CAACG,IAAK;UAClB8B,SAAS,EAAC;QAAmC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACFpE,OAAA;UAAA6D,QAAA,gBACE7D,OAAA;YAAG2D,SAAS,EAAE,eACZC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;YAAAC,QAAA,EACAnC,OAAO,CAACG;UAAI;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACJpE,OAAA;YAAG2D,SAAS,EAAE,WACZC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;YAAAC,QAAA,EACAnC,OAAO,CAACQ;UAAQ;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACLpE,OAAA;MAAI2D,SAAS,EAAC,WAAW;MAAAE,QAAA,eACvB7D,OAAA;QAAM2D,SAAS,EAAC,6CAA6C;QAAAE,QAAA,GAAC,GAC3D,EAACnC,OAAO,CAACe,KAAK;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACLpE,OAAA;MAAI2D,SAAS,EAAC,WAAW;MAAAE,QAAA,eACvB7D,OAAA;QAAM2D,SAAS,EAAE,8CACfjC,OAAO,CAAC6C,OAAO,GACX,sEAAsE,GACtE,8DAA8D,EACjE;QAAAV,QAAA,EACAnC,OAAO,CAAC6C,OAAO,GAAG,GAAG7C,OAAO,CAACgB,UAAU,IAAI,UAAU,EAAE,GAAG;MAAc;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACLpE,OAAA;MAAI2D,SAAS,EAAC,WAAW;MAAAE,QAAA,eACvB7D,OAAA;QAAM2D,SAAS,EAAE,8CACfjC,OAAO,CAACU,IAAI,KAAK,SAAS,GACtB,kEAAkE,GAClE,kEAAkE,EACrE;QAAAyB,QAAA,EACAnC,OAAO,CAACU;MAAI;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACLpE,OAAA;MAAI2D,SAAS,EAAC,WAAW;MAAAE,QAAA,eACvB7D,OAAA;QAAK2D,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B7D,OAAA;UAAQ2D,SAAS,EAAE,oCACjBC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;UAAAC,QAAA,eACD7D,OAAA,CAACV,OAAO;YAACqE,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRjE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UAAQ2D,SAAS,EAAE,oCACjBC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;UAAAC,QAAA,eACD7D,OAAA,CAACZ,UAAU;YAACuE,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACT,EACAjE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UAAQ2D,SAAS,EAAE,oCACjBC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;UAAAC,QAAA,eACD7D,OAAA,CAACX,SAAS;YAACsE,SAAS,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACZ;EAED,oBACEpE,OAAA,CAACH,WAAW;IAAAgE,QAAA,gBACV7D,OAAA;MAAK2D,SAAS,EAAC,WAAW;MAAAE,QAAA,gBAExB7D,OAAA;QAAK2D,SAAS,EAAC,mCAAmC;QAAAE,QAAA,gBAChD7D,OAAA;UAAA6D,QAAA,gBACE7D,OAAA;YAAI2D,SAAS,EAAE,sBACbC,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;YAAAC,QAAA,EAAC;UAEJ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpE,OAAA;YAAG2D,SAAS,EAAE,QACZC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;YAAAC,QAAA,EAAC;UAEJ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLjE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA,CAACf,MAAM,CAACyF,MAAM;UACZC,UAAU,EAAE;YAAEnB,KAAK,EAAE;UAAK,CAAE;UAC5BoB,QAAQ,EAAE;YAAEpB,KAAK,EAAE;UAAK,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAMnE,sBAAsB,CAAC,IAAI,CAAE;UAC5CiD,SAAS,EAAC,6HAA6H;UAAAE,QAAA,gBAEvI7D,OAAA,CAACb,QAAQ;YAACwE,SAAS,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCpE,OAAA;YAAA6D,QAAA,EAAM;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpE,OAAA;QAAK2D,SAAS,EAAE,4BACdC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;QAAAC,QAAA,gBACD7D,OAAA;UAAK2D,SAAS,EAAC,qFAAqF;UAAAE,QAAA,gBAElG7D,OAAA;YAAK2D,SAAS,EAAC,0BAA0B;YAAAE,QAAA,gBACvC7D,OAAA,CAACT,mBAAmB;cAACoE,SAAS,EAAC;YAA0E;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GpE,OAAA;cACEoC,IAAI,EAAC,MAAM;cACX0C,WAAW,EAAC,oBAAoB;cAChCC,KAAK,EAAEpE,WAAY;cACnB2D,QAAQ,EAAGU,CAAC,IAAKpE,cAAc,CAACoE,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;cAChDpB,SAAS,EAAE,8DACTC,eAAe,CACb,uHAAuH,EACvH,yHACF,CAAC;YACA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNpE,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAE,QAAA,gBAE1C7D,OAAA;cACE6E,OAAO,EAAEA,CAAA,KAAMzD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CwC,SAAS,EAAE,sEACTC,eAAe,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EACzD;cAAAC,QAAA,gBAEH7D,OAAA,CAACR,UAAU;gBAACmE,SAAS,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCpE,OAAA;gBAAA6D,QAAA,EAAM;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGTpE,OAAA;cAAK2D,SAAS,EAAC,0EAA0E;cAAAE,QAAA,gBACvF7D,OAAA;gBACE6E,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,MAAM,CAAE;gBACnCmD,SAAS,EAAE,oCACTpD,QAAQ,KAAK,MAAM,GACf,sCAAsC,GACtC,2CAA2C,EAC9C;gBAAAsD,QAAA,eAEH7D,OAAA,CAACP,cAAc;kBAACkE,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACTpE,OAAA;gBACE6E,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,MAAM,CAAE;gBACnCmD,SAAS,EAAE,oCACTpD,QAAQ,KAAK,MAAM,GACf,sCAAsC,GACtC,2CAA2C,EAC9C;gBAAAsD,QAAA,eAEH7D,OAAA,CAACN,cAAc;kBAACiE,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpE,OAAA,CAACd,eAAe;UAAA2E,QAAA,EACb1C,WAAW,iBACVnB,OAAA,CAACf,MAAM,CAACmE,GAAG;YACTE,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE2B,MAAM,EAAE;YAAE,CAAE;YACnCzB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAE2B,MAAM,EAAE;YAAO,CAAE;YACxCxB,IAAI,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAE2B,MAAM,EAAE;YAAE,CAAE;YAChCvB,SAAS,EAAC,0DAA0D;YAAAE,QAAA,eAEpE7D,OAAA;cAAK2D,SAAS,EAAC,uCAAuC;cAAAE,QAAA,gBACpD7D,OAAA;gBACE+E,KAAK,EAAElE,gBAAiB;gBACxByD,QAAQ,EAAGU,CAAC,IAAKlE,mBAAmB,CAACkE,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBACrDpB,SAAS,EAAE,iDACTC,eAAe,CACb,wCAAwC,EACxC,0CACF,CAAC,EACA;gBAAAC,QAAA,gBAEH7D,OAAA;kBAAQ+E,KAAK,EAAC,KAAK;kBAAAlB,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1C/D,UAAU,CAAC4C,GAAG,CAACf,QAAQ,iBACtBlC,OAAA;kBAA0B+E,KAAK,EAAE7C,QAAQ,CAACY,EAAG;kBAAAe,QAAA,EAC1C3B,QAAQ,CAACL;gBAAI,GADHK,QAAQ,CAACY,EAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAETpE,OAAA;gBACE+E,KAAK,EAAEhE,YAAa;gBACpBuD,QAAQ,EAAGU,CAAC,IAAKhE,eAAe,CAACgE,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBACjDpB,SAAS,EAAE,iDACTC,eAAe,CACb,wCAAwC,EACxC,0CACF,CAAC,EACA;gBAAAC,QAAA,gBAEH7D,OAAA;kBAAQ+E,KAAK,EAAC,KAAK;kBAAAlB,QAAA,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCpE,OAAA;kBAAQ+E,KAAK,EAAC,UAAU;kBAAAlB,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpE,OAAA;kBAAQ+E,KAAK,EAAC,SAAS;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAETpE,OAAA;gBACE+E,KAAK,EAAE9D,MAAO;gBACdqD,QAAQ,EAAGU,CAAC,IAAK9D,SAAS,CAAC8D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBAC3CpB,SAAS,EAAE,iDACTC,eAAe,CACb,wCAAwC,EACxC,0CACF,CAAC,EACA;gBAAAC,QAAA,gBAEH7D,OAAA;kBAAQ+E,KAAK,EAAC,MAAM;kBAAAlB,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpE,OAAA;kBAAQ+E,KAAK,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CpE,OAAA;kBAAQ+E,KAAK,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CpE,OAAA;kBAAQ+E,KAAK,EAAC,UAAU;kBAAAlB,QAAA,EAAC;gBAAgB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGNpE,OAAA;QAAK2D,SAAS,EAAC,mCAAmC;QAAAE,QAAA,gBAChD7D,OAAA;UAAG2D,SAAS,EAAE,WACZC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;UAAAC,QAAA,GAAC,UACM,EAACtC,gBAAgB,CAACyB,MAAM,EAAC,MAAI,EAAC5C,QAAQ,CAAC4C,MAAM,EAAC,WACxD;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACH/C,gBAAgB,CAAC2B,MAAM,GAAG,CAAC,iBAC1BhD,OAAA;UAAK2D,SAAS,EAAC,6BAA6B;UAAAE,QAAA,gBAC1C7D,OAAA;YAAM2D,SAAS,EAAE,WACfC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;YAAAC,QAAA,GACAxC,gBAAgB,CAAC2B,MAAM,EAAC,WAC3B;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPpE,OAAA;YAAQ2D,SAAS,EAAC,uFAAuF;YAAAE,QAAA,EAAC;UAE1G;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL7D,QAAQ,KAAK,MAAM,gBAClBP,OAAA;QAAK2D,SAAS,EAAC,qEAAqE;QAAAE,QAAA,eAClF7D,OAAA,CAACd,eAAe;UAAA2E,QAAA,EACbtC,gBAAgB,CAAC0B,GAAG,CAACvB,OAAO,iBAC3B1B,OAAA,CAACmD,WAAW;YAAkBzB,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAACoB,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,gBAENpE,OAAA;QAAK2D,SAAS,EAAE,wCACdC,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;QAAAC,QAAA,eACD7D,OAAA;UAAO2D,SAAS,EAAC,2DAA2D;UAAAE,QAAA,gBAC1E7D,OAAA;YAAO2D,SAAS,EAAEC,eAAe,CAAC,YAAY,EAAE,cAAc,CAAE;YAAAC,QAAA,eAC9D7D,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAI2D,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,eACjC7D,OAAA;kBACEoC,IAAI,EAAC,UAAU;kBACfiC,OAAO,EAAEhD,gBAAgB,CAAC2B,MAAM,KAAKzB,gBAAgB,CAACyB,MAAM,IAAIzB,gBAAgB,CAACyB,MAAM,GAAG,CAAE;kBAC5FsB,QAAQ,EAAEvB,eAAgB;kBAC1BY,SAAS,EAAC;gBAA4F;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLpE,OAAA;gBAAI2D,SAAS,EAAE,oEACbC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;gBAAAC,QAAA,EAAC;cAEJ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpE,OAAA;gBAAI2D,SAAS,EAAE,oEACbC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;gBAAAC,QAAA,EAAC;cAEJ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpE,OAAA;gBAAI2D,SAAS,EAAE,oEACbC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;gBAAAC,QAAA,EAAC;cAEJ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpE,OAAA;gBAAI2D,SAAS,EAAE,oEACbC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;gBAAAC,QAAA,EAAC;cAEJ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpE,OAAA;gBAAI2D,SAAS,EAAE,oEACbC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;gBAAAC,QAAA,EAAC;cAEJ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpE,OAAA;YAAO2D,SAAS,EAAE,YAChBC,eAAe,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACrD;YAAAC,QAAA,eACD7D,OAAA,CAACd,eAAe;cAAA2E,QAAA,EACbtC,gBAAgB,CAAC0B,GAAG,CAACvB,OAAO,iBAC3B1B,OAAA,CAACwE,UAAU;gBAAkB9C,OAAO,EAAEA;cAAQ,GAA7BA,OAAO,CAACoB,EAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqB,CACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpE,OAAA,CAACF,eAAe;MACdqF,MAAM,EAAE1E,mBAAoB;MAC5B2E,OAAO,EAAEA,CAAA,KAAM1E,sBAAsB,CAAC,KAAK,CAAE;MAC7C2E,QAAQ,EAAE,MAAOC,WAAW,IAAK;QAC/B,MAAMC,MAAM,GAAG,MAAMjF,UAAU,CAACgF,WAAW,CAAC;QAC5C,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClB9E,sBAAsB,CAAC,KAAK,CAAC;UAC7B;UACA+E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,MAAM,CAAC7D,OAAO,CAAC;QAC5D,CAAC,MAAM;UACL;UACA+D,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEJ,MAAM,CAACI,KAAK,CAAC;QACvD;MACF;IAAE;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAClE,EAAA,CAjfID,iBAAiB;EAAA,QACKN,QAAQ,EACWC,WAAW;AAAA;AAAAgG,EAAA,GAFpD3F,iBAAiB;AAmfvB,eAAeA,iBAAiB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}