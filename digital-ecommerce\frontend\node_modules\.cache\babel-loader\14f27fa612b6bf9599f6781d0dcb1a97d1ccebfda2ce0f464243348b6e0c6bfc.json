{"ast": null, "code": "import { useSyntheticBlurEvent as $8a9cb279dc87e130$export$715c682d09d639cc } from \"./utils.mjs\";\nimport { useCallback as $hf0lj$useCallback } from \"react\";\nimport { getOwnerDocument as $hf0lj$getOwnerDocument, getActiveElement as $hf0lj$getActiveElement, getEventTarget as $hf0lj$getEventTarget } from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nfunction $a1ea59d68270f0dd$export$f8168d8dd8fd66e6(props) {\n  let {\n    isDisabled: isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange: onFocusChange\n  } = props;\n  const onBlur = (0, $hf0lj$useCallback)(e => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) onBlurProp(e);\n      if (onFocusChange) onFocusChange(false);\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n  const onSyntheticFocus = (0, $8a9cb279dc87e130$export$715c682d09d639cc)(onBlur);\n  const onFocus = (0, $hf0lj$useCallback)(e => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = (0, $hf0lj$getOwnerDocument)(e.target);\n    const activeElement = ownerDocument ? (0, $hf0lj$getActiveElement)(ownerDocument) : (0, $hf0lj$getActiveElement)();\n    if (e.target === e.currentTarget && activeElement === (0, $hf0lj$getEventTarget)(e.nativeEvent)) {\n      if (onFocusProp) onFocusProp(e);\n      if (onFocusChange) onFocusChange(true);\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n  return {\n    focusProps: {\n      onFocus: !isDisabled && (onFocusProp || onFocusChange || onBlurProp) ? onFocus : undefined,\n      onBlur: !isDisabled && (onBlurProp || onFocusChange) ? onBlur : undefined\n    }\n  };\n}\nexport { $a1ea59d68270f0dd$export$f8168d8dd8fd66e6 as useFocus };", "map": {"version": 3, "names": ["$a1ea59d68270f0dd$export$f8168d8dd8fd66e6", "props", "isDisabled", "onFocus", "onFocusProp", "onBlur", "onBlurProp", "onFocusChange", "$hf0lj$useCallback", "e", "target", "currentTarget", "onSyntheticFocus", "$8a9cb279dc87e130$export$715c682d09d639cc", "ownerDocument", "$hf0lj$getOwnerDocument", "activeElement", "$hf0lj$getActiveElement", "$hf0lj$getEventTarget", "nativeEvent", "focusProps", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useFocus.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;;AAqBO,SAASA,0CAA6DC,KAAyB;EACpG,IAAI;IAAAC,UAAA,EACFA,UAAU;IACVC,OAAA,EAASC,WAAW;IACpBC,MAAA,EAAQC,UAAU;IAAAC,aAAA,EAClBA;EAAa,CACd,GAAGN,KAAA;EAEJ,MAAMI,MAAA,GAAuC,IAAAG,kBAAU,EAAGC,CAAA;IACxD,IAAIA,CAAA,CAAEC,MAAM,KAAKD,CAAA,CAAEE,aAAa,EAAE;MAChC,IAAIL,UAAA,EACFA,UAAA,CAAWG,CAAA;MAGb,IAAIF,aAAA,EACFA,aAAA,CAAc;MAGhB,OAAO;IACT;EACF,GAAG,CAACD,UAAA,EAAYC,aAAA,CAAc;EAG9B,MAAMK,gBAAA,GAAmB,IAAAC,yCAAoB,EAAUR,MAAA;EAEvD,MAAMF,OAAA,GAAyC,IAAAK,kBAAU,EAAGC,CAAA;IAC1D;IACA;IAEA,MAAMK,aAAA,GAAgB,IAAAC,uBAAe,EAAEN,CAAA,CAAEC,MAAM;IAC/C,MAAMM,aAAA,GAAgBF,aAAA,GAAgB,IAAAG,uBAAe,EAAEH,aAAA,IAAiB,IAAAG,uBAAe;IACvF,IAAIR,CAAA,CAAEC,MAAM,KAAKD,CAAA,CAAEE,aAAa,IAAIK,aAAA,KAAkB,IAAAE,qBAAa,EAAET,CAAA,CAAEU,WAAW,GAAG;MACnF,IAAIf,WAAA,EACFA,WAAA,CAAYK,CAAA;MAGd,IAAIF,aAAA,EACFA,aAAA,CAAc;MAGhBK,gBAAA,CAAiBH,CAAA;IACnB;EACF,GAAG,CAACF,aAAA,EAAeH,WAAA,EAAaQ,gBAAA,CAAiB;EAEjD,OAAO;IACLQ,UAAA,EAAY;MACVjB,OAAA,EAAS,CAAED,UAAA,KAAeE,WAAA,IAAeG,aAAA,IAAiBD,UAAS,IAAMH,OAAA,GAAUkB,SAAA;MACnFhB,MAAA,EAAQ,CAAEH,UAAA,KAAeI,UAAA,IAAcC,aAAY,IAAMF,MAAA,GAASgB;IACpE;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}