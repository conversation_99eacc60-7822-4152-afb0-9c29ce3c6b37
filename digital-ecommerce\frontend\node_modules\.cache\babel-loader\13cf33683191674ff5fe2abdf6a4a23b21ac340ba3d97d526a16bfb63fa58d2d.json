{"ast": null, "code": "import { useCallback as r, useState as b } from \"react\";\nfunction c(u = 0) {\n  let [t, l] = b(u),\n    g = r(e => l(e), [t]),\n    s = r(e => l(a => a | e), [t]),\n    m = r(e => (t & e) === e, [t]),\n    n = r(e => l(a => a & ~e), [l]),\n    F = r(e => l(a => a ^ e), [l]);\n  return {\n    flags: t,\n    setFlag: g,\n    addFlag: s,\n    hasFlag: m,\n    removeFlag: n,\n    toggleFlag: F\n  };\n}\nexport { c as useFlags };", "map": {"version": 3, "names": ["useCallback", "r", "useState", "b", "c", "u", "t", "l", "g", "e", "s", "a", "m", "n", "F", "flags", "setFlag", "addFlag", "hasFlag", "removeFlag", "toggleFlag", "useFlags"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-flags.js"], "sourcesContent": ["import{useCallback as r,useState as b}from\"react\";function c(u=0){let[t,l]=b(u),g=r(e=>l(e),[t]),s=r(e=>l(a=>a|e),[t]),m=r(e=>(t&e)===e,[t]),n=r(e=>l(a=>a&~e),[l]),F=r(e=>l(a=>a^e),[l]);return{flags:t,setFlag:g,addFlag:s,hasFlag:m,removeFlag:n,toggleFlag:F}}export{c as useFlags};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,GAAC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACJ,CAAC,CAACE,CAAC,CAAC;IAACG,CAAC,GAACP,CAAC,CAACQ,CAAC,IAAEF,CAAC,CAACE,CAAC,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;IAACI,CAAC,GAACT,CAAC,CAACQ,CAAC,IAAEF,CAAC,CAACI,CAAC,IAAEA,CAAC,GAACF,CAAC,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;IAACM,CAAC,GAACX,CAAC,CAACQ,CAAC,IAAE,CAACH,CAAC,GAACG,CAAC,MAAIA,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;IAACO,CAAC,GAACZ,CAAC,CAACQ,CAAC,IAAEF,CAAC,CAACI,CAAC,IAAEA,CAAC,GAAC,CAACF,CAAC,CAAC,EAAC,CAACF,CAAC,CAAC,CAAC;IAACO,CAAC,GAACb,CAAC,CAACQ,CAAC,IAAEF,CAAC,CAACI,CAAC,IAAEA,CAAC,GAACF,CAAC,CAAC,EAAC,CAACF,CAAC,CAAC,CAAC;EAAC,OAAM;IAACQ,KAAK,EAACT,CAAC;IAACU,OAAO,EAACR,CAAC;IAACS,OAAO,EAACP,CAAC;IAACQ,OAAO,EAACN,CAAC;IAACO,UAAU,EAACN,CAAC;IAACO,UAAU,EAACN;EAAC,CAAC;AAAA;AAAC,SAAOV,CAAC,IAAIiB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}