/* Global Styles for E-Commerce Store */

/* Root Variables for Light Orange Theme */
:root {
  --light-orange-50: #FFF3E0;
  --light-orange-100: #FFE0B2;
  --light-orange-200: #FFCC80;
  --light-orange-300: #FFB74D;
  --light-orange-400: #FFA726;
  --light-orange-500: #FF9800;
  --light-orange-600: #FB8C00;
  --light-orange-700: #F57C00;
  --light-orange-800: #EF6C00;
  --light-orange-900: #E65100;

  /* Light Theme Variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1f2937;
  --text-secondary: #374151;
  --text-tertiary: #6b7280;
  --text-muted: #9ca3af;
  --border-color: #e5e7eb;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --gradient-bg: linear-gradient(135deg, #FFF8F1 0%, #ffffff 100%);
}

/* Dark Theme Variables */
.dark {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --gradient-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Global Dark Mode Styles - Focus on backgrounds, not text */
.dark body {
  background: var(--gradient-bg);
}

/* Keep text colors consistent but ensure they work on dark backgrounds */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #ffffff;
}

.dark p {
  color: #e2e8f0;
}

.dark a {
  color: #cbd5e1;
}

.dark a:hover {
  color: #ffffff;
}

/* Ensure good contrast for all text on dark backgrounds */
.dark .text-gray-900 {
  color: #ffffff !important;
}

.dark .text-gray-800 {
  color: #f1f5f9 !important;
}

.dark .text-gray-700 {
  color: #e2e8f0 !important;
}

.dark .text-gray-600 {
  color: #cbd5e1 !important;
}

.dark .text-gray-500 {
  color: #94a3b8 !important;
}

/* Base Styles */
* {
  box-sizing: border-box;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--gradient-bg);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  line-height: 1.6;
  min-height: 100vh;
  transition: background 0.4s ease, color 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--light-orange-400);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--light-orange-500);
}

/* Dark theme scrollbar adjustments */
.dark ::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--light-orange-600);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--light-orange-500);
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--light-orange-400);
  outline-offset: 2px;
}

/* Button Focus */
button:focus {
  outline: 2px solid var(--light-orange-400);
  outline-offset: 2px;
}

/* Input Focus */
input:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--light-orange-300);
  border-color: var(--light-orange-400);
}

/* Custom Range Slider Styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--light-orange-500);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: var(--light-orange-500);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, var(--light-orange-600), var(--light-orange-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-light-orange {
  box-shadow: 0 4px 6px -1px rgba(255, 152, 0, 0.1), 0 2px 4px -1px rgba(255, 152, 0, 0.06);
}

.shadow-light-orange-lg {
  box-shadow: 0 10px 15px -3px rgba(255, 152, 0, 0.1), 0 4px 6px -2px rgba(255, 152, 0, 0.05);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--light-orange-400);
  }
  50% {
    box-shadow: 0 0 20px var(--light-orange-400), 0 0 30px var(--light-orange-400);
  }
}

/* Loading Spinner */
.spinner {
  border: 3px solid var(--light-orange-200);
  border-top: 3px solid var(--light-orange-500);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design Utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Mobile First Responsive Breakpoints */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2.5rem;
  }
}

/* Mobile Specific Styles */
@media (max-width: 767px) {
  /* Hide desktop-only elements */
  .hidden-mobile {
    display: none !important;
  }

  /* Stack elements vertically on mobile */
  .mobile-stack {
    flex-direction: column !important;
  }

  /* Full width on mobile */
  .mobile-full {
    width: 100% !important;
  }

  /* Smaller text on mobile */
  .mobile-text-sm {
    font-size: 0.875rem !important;
  }

  /* Reduced padding on mobile */
  .mobile-p-2 {
    padding: 0.5rem !important;
  }

  /* Smaller margins on mobile */
  .mobile-m-2 {
    margin: 0.5rem !important;
  }
}

/* Tablet Specific Styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-hidden {
    display: none !important;
  }
}

/* Desktop Specific Styles */
@media (min-width: 1024px) {
  .desktop-only {
    display: block !important;
  }
}

/* Print Styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }

  .no-print {
    display: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --light-orange-500: #FF6600;
    --light-orange-600: #E55A00;
    --light-orange-700: #CC5200;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --gradient-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
}

/* Theme Toggle Animation */
.theme-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark theme specific utility classes */
.dark .bg-theme-primary {
  background-color: var(--bg-primary);
}

.dark .bg-theme-secondary {
  background-color: var(--bg-secondary);
}

.dark .text-theme-primary {
  color: var(--text-primary);
}

.dark .text-theme-secondary {
  color: var(--text-secondary);
}

.dark .border-theme {
  border-color: var(--border-color);
}

/* Enhanced shadow for dark theme */
.dark .shadow-theme {
  box-shadow: 0 4px 6px -1px var(--shadow-color), 0 2px 4px -1px var(--shadow-color);
}

/* Legacy Browser Support */
.App {
  text-align: center;
  padding: 20px;
}

.App-header {
  background-color: #fff;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}
