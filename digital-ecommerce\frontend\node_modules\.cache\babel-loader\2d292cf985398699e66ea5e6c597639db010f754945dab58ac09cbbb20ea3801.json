{"ast": null, "code": "import { getInteractionModality as $507fabe10e71c6fb$export$630ff653c5ada6a9 } from \"./useFocusVisible.mjs\";\nimport { getOwnerDocument as $k50bp$getOwnerDocument, getActiveElement as $k50bp$getActiveElement, runAfterTransition as $k50bp$runAfterTransition, focusWithoutScrolling as $k50bp$focusWithoutScrolling } from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $3ad3f6e1647bc98d$export$80f3e147d781571c(element) {\n  // If the user is interacting with a virtual cursor, e.g. screen reader, then\n  // wait until after any animated transitions that are currently occurring on\n  // the page before shifting focus. This avoids issues with VoiceOver on iOS\n  // causing the page to scroll when moving focus if the element is transitioning\n  // from off the screen.\n  const ownerDocument = (0, $k50bp$getOwnerDocument)(element);\n  const activeElement = (0, $k50bp$getActiveElement)(ownerDocument);\n  if ((0, $507fabe10e71c6fb$export$630ff653c5ada6a9)() === 'virtual') {\n    let lastFocusedElement = activeElement;\n    (0, $k50bp$runAfterTransition)(() => {\n      // If focus did not move and the element is still in the document, focus it.\n      if ((0, $k50bp$getActiveElement)(ownerDocument) === lastFocusedElement && element.isConnected) (0, $k50bp$focusWithoutScrolling)(element);\n    });\n  } else (0, $k50bp$focusWithoutScrolling)(element);\n}\nexport { $3ad3f6e1647bc98d$export$80f3e147d781571c as focusSafely };", "map": {"version": 3, "names": ["$3ad3f6e1647bc98d$export$80f3e147d781571c", "element", "ownerDocument", "$k50bp$getOwnerDocument", "activeElement", "$k50bp$getActiveElement", "$507fabe10e71c6fb$export$630ff653c5ada6a9", "lastFocusedElement", "$k50bp$runAfterTransition", "isConnected", "$k50bp$focusWithoutScrolling"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\focusSafely.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {\n  focusWithoutScrolling,\n  getActiveElement,\n  getOwnerDocument,\n  runAfterTransition\n} from '@react-aria/utils';\nimport {getInteractionModality} from './useFocusVisible';\n\n/**\n * A utility function that focuses an element while avoiding undesired side effects such\n * as page scrolling and screen reader issues with CSS transitions.\n */\nexport function focusSafely(element: FocusableElement): void {\n  // If the user is interacting with a virtual cursor, e.g. screen reader, then\n  // wait until after any animated transitions that are currently occurring on\n  // the page before shifting focus. This avoids issues with VoiceOver on iOS\n  // causing the page to scroll when moving focus if the element is transitioning\n  // from off the screen.\n  const ownerDocument = getOwnerDocument(element);\n  const activeElement = getActiveElement(ownerDocument);\n  if (getInteractionModality() === 'virtual') {\n    let lastFocusedElement = activeElement;\n    runAfterTransition(() => {\n      // If focus did not move and the element is still in the document, focus it.\n      if (getActiveElement(ownerDocument) === lastFocusedElement && element.isConnected) {\n        focusWithoutScrolling(element);\n      }\n    });\n  } else {\n    focusWithoutScrolling(element);\n  }\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAyBO,SAASA,0CAAYC,OAAyB;EACnD;EACA;EACA;EACA;EACA;EACA,MAAMC,aAAA,GAAgB,IAAAC,uBAAe,EAAEF,OAAA;EACvC,MAAMG,aAAA,GAAgB,IAAAC,uBAAe,EAAEH,aAAA;EACvC,IAAI,IAAAI,yCAAqB,QAAQ,WAAW;IAC1C,IAAIC,kBAAA,GAAqBH,aAAA;IACzB,IAAAI,yBAAiB,EAAE;MACjB;MACA,IAAI,IAAAH,uBAAe,EAAEH,aAAA,MAAmBK,kBAAA,IAAsBN,OAAA,CAAQQ,WAAW,EAC/E,IAAAC,4BAAoB,EAAET,OAAA;IAE1B;EACF,OACE,IAAAS,4BAAoB,EAAET,OAAA;AAE1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}