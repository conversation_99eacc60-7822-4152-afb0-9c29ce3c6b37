{"ast": null, "code": "import { isShadowRoot as $431fbd86ca7dc216$export$af51f0f06c0f328a } from \"./domHelpers.mjs\";\nimport { shadowDOM as $lcSu5$shadowDOM } from \"@react-stately/flags\";\n\n// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nfunction $d4ee10de306f2510$export$4282f70798064fe0(node, otherNode) {\n  if (!(0, $lcSu5$shadowDOM)()) return otherNode && node ? node.contains(otherNode) : false;\n  if (!node || !otherNode) return false;\n  let currentNode = otherNode;\n  while (currentNode !== null) {\n    if (currentNode === node) return true;\n    if (currentNode.tagName === 'SLOT' && currentNode.assignedSlot)\n      // Element is slotted\n      currentNode = currentNode.assignedSlot.parentNode;else if ((0, $431fbd86ca7dc216$export$af51f0f06c0f328a)(currentNode))\n      // Element is in shadow root\n      currentNode = currentNode.host;else currentNode = currentNode.parentNode;\n  }\n  return false;\n}\nconst $d4ee10de306f2510$export$cd4e5573fbe2b576 = function () {\n  let doc = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  var _activeElement_shadowRoot;\n  if (!(0, $lcSu5$shadowDOM)()) return doc.activeElement;\n  let activeElement = doc.activeElement;\n  while (activeElement && 'shadowRoot' in activeElement && ((_activeElement_shadowRoot = activeElement.shadowRoot) === null || _activeElement_shadowRoot === void 0 ? void 0 : _activeElement_shadowRoot.activeElement)) activeElement = activeElement.shadowRoot.activeElement;\n  return activeElement;\n};\nfunction $d4ee10de306f2510$export$e58f029f0fbfdb29(event) {\n  if ((0, $lcSu5$shadowDOM)() && event.target.shadowRoot) {\n    if (event.composedPath) return event.composedPath()[0];\n  }\n  return event.target;\n}\nexport { $d4ee10de306f2510$export$4282f70798064fe0 as nodeContains, $d4ee10de306f2510$export$cd4e5573fbe2b576 as getActiveElement, $d4ee10de306f2510$export$e58f029f0fbfdb29 as getEventTarget };", "map": {"version": 3, "names": ["$d4ee10de306f2510$export$4282f70798064fe0", "node", "otherNode", "$lcSu5$shadowDOM", "contains", "currentNode", "tagName", "assignedSlot", "parentNode", "$431fbd86ca7dc216$export$af51f0f06c0f328a", "host", "$d4ee10de306f2510$export$cd4e5573fbe2b576", "doc", "arguments", "length", "undefined", "document", "_activeElement_shadowRoot", "activeElement", "shadowRoot", "$d4ee10de306f2510$export$e58f029f0fbfdb29", "event", "target", "<PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\shadowdom\\DOMFunctions.ts"], "sourcesContent": ["// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n"], "mappings": ";;;AAAA;;AAQO,SAASA,0CACdC,IAA6B,EAC7BC,SAAkC;EAElC,IAAI,CAAC,IAAAC,gBAAQ,KACX,OAAOD,SAAA,IAAaD,IAAA,GAAOA,IAAA,CAAKG,QAAQ,CAACF,SAAA,IAAa;EAGxD,IAAI,CAACD,IAAA,IAAQ,CAACC,SAAA,EACZ,OAAO;EAGT,IAAIG,WAAA,GAAqDH,SAAA;EAEzD,OAAOG,WAAA,KAAgB,MAAM;IAC3B,IAAIA,WAAA,KAAgBJ,IAAA,EAClB,OAAO;IAGT,IAAII,WAAC,CAAgCC,OAAO,KAAK,UAC/CD,WAAC,CAAgCE,YAAY;MAC7C;MACAF,WAAA,GAAcA,WAAC,CAAgCE,YAAY,CAAEC,UAAU,MAClE,IAAI,IAAAC,yCAAW,EAAEJ,WAAA;MACtB;MACAA,WAAA,GAAcA,WAAA,CAAYK,IAAI,MAE9BL,WAAA,GAAcA,WAAA,CAAYG,UAAU;EAExC;EAEA,OAAO;AACT;AAKO,MAAMG,yCAAA,GAAmB,SAAAA,CAAA,EAAyB;EAAA,IAAxBC,GAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAgBG,QAAQ;MAOvDC,yBAAA;EANA,IAAI,CAAC,IAAAd,gBAAQ,KACX,OAAOS,GAAA,CAAIM,aAAa;EAE1B,IAAIA,aAAA,GAAgCN,GAAA,CAAIM,aAAa;EAErD,OAAOA,aAAA,IAAiB,gBAAgBA,aAAA,MACxCD,yBAAA,GAAAC,aAAA,CAAcC,UAAU,cAAxBF,yBAAA,uBAAAA,yBAAA,CAA0BC,aAAa,GACrCA,aAAA,GAAgBA,aAAA,CAAcC,UAAU,CAACD,aAAa;EAGxD,OAAOA,aAAA;AACT;AAKO,SAASE,0CAAgCC,KAAQ;EACtD,IAAI,IAAAlB,gBAAQ,OAAOkB,KAAC,CAAMC,MAAM,CAAiBH,UAAU,EAAE;IAC3D,IAAIE,KAAA,CAAME,YAAY,EACpB,OAAOF,KAAA,CAAME,YAAY,EAAE,CAAC,EAAE;EAElC;EACA,OAAOF,KAAA,CAAMC,MAAM;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}