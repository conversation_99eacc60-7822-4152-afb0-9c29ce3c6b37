{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ResetPasswordPage from './pages/ResetPasswordPage';\nimport AccountPage from './pages/AccountPage';\nimport WishlistPage from './pages/WishlistPage';\nimport ThemeTestPage from './pages/ThemeTestPage';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport { HelpPage, ReturnsPage, ShippingPage, TrackOrderPage, PrivacyPage, TermsPage, CookiesPage, OrdersPage } from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(UserProvider, {\n      children: /*#__PURE__*/_jsxDEV(CartProvider, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white dark:bg-gradient-to-br dark:from-slate-900 dark:to-slate-800 transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              mode: \"wait\",\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -20\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 53,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/products\",\n                  element: /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -20\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 63,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/digital-products\",\n                  element: /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -20\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DigitalProductsPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 73,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/about\",\n                  element: /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -20\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 83,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 77,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/contact\",\n                  element: /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -20\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 93,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/checkout\",\n                  element: /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -20\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 103,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/help\",\n                  element: /*#__PURE__*/_jsxDEV(HelpPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 42\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/returns\",\n                  element: /*#__PURE__*/_jsxDEV(ReturnsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/shipping\",\n                  element: /*#__PURE__*/_jsxDEV(ShippingPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 46\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/track\",\n                  element: /*#__PURE__*/_jsxDEV(TrackOrderPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 43\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/orders\",\n                  element: /*#__PURE__*/_jsxDEV(OrdersPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 44\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/privacy\",\n                  element: /*#__PURE__*/_jsxDEV(PrivacyPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/terms\",\n                  element: /*#__PURE__*/_jsxDEV(TermsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 43\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/cookies\",\n                  element: /*#__PURE__*/_jsxDEV(CookiesPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    requireAuth: false,\n                    children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 116,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/register\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    requireAuth: false,\n                    children: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/reset-password\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    requireAuth: false,\n                    children: /*#__PURE__*/_jsxDEV(ResetPasswordPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/account\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(AccountPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/wishlist\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(WishlistPage, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 13\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n              className: \"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"col-span-1 md:col-span-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"w-6 h-6 text-white\",\n                          fill: \"none\",\n                          stroke: \"currentColor\",\n                          viewBox: \"0 0 24 24\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 151,\n                            columnNumber: 23\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 150,\n                          columnNumber: 21\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 149,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"ShopHub\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 154,\n                        columnNumber: 19\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-300 mb-4 max-w-md\",\n                      children: \"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-4\",\n                      children: [/*#__PURE__*/_jsxDEV(MultiLanguageSupport, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 161,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(EmailNotifications, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 162,\n                        columnNumber: 19\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 15\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold mb-4\",\n                      children: \"Quick Links\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"space-y-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Home\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 170,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 170,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/products\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Products\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 171,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 171,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/digital-products\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Digital Products\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 172,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/about\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"About Us\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 173,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 173,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/contact\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Contact\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 174,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 19\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 15\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold mb-4\",\n                      children: \"Customer Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"space-y-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/help\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Help Center\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 182,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/returns\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Returns\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 183,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/shipping\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Shipping Info\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 184,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          to: \"/track\",\n                          className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                          children: \"Track Order\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 185,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 19\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 15\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-400 text-sm\",\n                    children: \"\\xA9 2024 ShopHub. All rights reserved.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 15\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-6 mt-4 md:mt-0\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/privacy\",\n                      className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                      children: \"Privacy Policy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/terms\",\n                      className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                      children: \"Terms of Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/cookies\",\n                      className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                      children: \"Cookie Policy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 15\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 13\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "Navigation", "CartProvider", "UserProvider", "ThemeProvider", "HomePage", "ProductsPage", "DigitalProductsPage", "AboutPage", "ContactPage", "CheckoutPage", "LoginPage", "RegisterPage", "ResetPasswordPage", "AccountPage", "WishlistPage", "ThemeTestPage", "ProtectedRoute", "HelpPage", "ReturnsPage", "ShippingPage", "TrackOrderPage", "PrivacyPage", "TermsPage", "CookiesPage", "OrdersPage", "MultiLanguageSupport", "EmailNotifications", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "requireAuth", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ResetPasswordPage from './pages/ResetPasswordPage';\nimport AccountPage from './pages/AccountPage';\nimport WishlistPage from './pages/WishlistPage';\nimport ThemeTestPage from './pages/ThemeTestPage';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport {\n  HelpPage,\n  ReturnsPage,\n  ShippingPage,\n  TrackOrderPage,\n  PrivacyPage,\n  TermsPage,\n  CookiesPage,\n  OrdersPage\n} from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <UserProvider>\n        <CartProvider>\n          <Router>\n          <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white dark:bg-gradient-to-br dark:from-slate-900 dark:to-slate-800 transition-all duration-300\">\n          <Navigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n            <Route path=\"/checkout\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <CheckoutPage />\n              </motion.div>\n            } />\n            <Route path=\"/help\" element={<HelpPage />} />\n            <Route path=\"/returns\" element={<ReturnsPage />} />\n            <Route path=\"/shipping\" element={<ShippingPage />} />\n            <Route path=\"/track\" element={<TrackOrderPage />} />\n            <Route path=\"/orders\" element={<OrdersPage />} />\n            <Route path=\"/privacy\" element={<PrivacyPage />} />\n            <Route path=\"/terms\" element={<TermsPage />} />\n            <Route path=\"/cookies\" element={<CookiesPage />} />\n            <Route path=\"/login\" element={\n              <ProtectedRoute requireAuth={false}>\n                <LoginPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/register\" element={\n              <ProtectedRoute requireAuth={false}>\n                <RegisterPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/reset-password\" element={\n              <ProtectedRoute requireAuth={false}>\n                <ResetPasswordPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/account\" element={\n              <ProtectedRoute>\n                <AccountPage />\n              </ProtectedRoute>\n            } />\n            <Route path=\"/wishlist\" element={\n              <ProtectedRoute>\n                <WishlistPage />\n              </ProtectedRoute>\n            } />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/help\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</Link></li>\n                  <li><Link to=\"/returns\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</Link></li>\n                  <li><Link to=\"/shipping\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</Link></li>\n                  <li><Link to=\"/track\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</Link></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <Link to=\"/privacy\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</Link>\n                <Link to=\"/terms\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</Link>\n                <Link to=\"/cookies\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</Link>\n              </div>\n            </div>\n          </div>\n        </footer>\n        </div>\n          </Router>\n        </CartProvider>\n      </UserProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SACEC,QAAQ,EACRC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,QACL,yBAAyB;AAChC,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACzB,aAAa;IAAA2B,QAAA,eACZF,OAAA,CAAC1B,YAAY;MAAA4B,QAAA,eACXF,OAAA,CAAC3B,YAAY;QAAA6B,QAAA,eACXF,OAAA,CAAClC,MAAM;UAAAoC,QAAA,eACPF,OAAA;YAAKG,SAAS,EAAC,uJAAuJ;YAAAD,QAAA,gBACtKF,OAAA,CAAC5B,UAAU;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhBP,OAAA,CAAC7B,eAAe;cAACqC,IAAI,EAAC,MAAM;cAAAN,QAAA,eAC1BF,OAAA,CAACjC,MAAM;gBAAAmC,QAAA,gBACLF,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,GAAG;kBAACC,OAAO,eACrBV,OAAA,CAAC9B,MAAM,CAACyC,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BE,IAAI,EAAE;sBAAEH,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BG,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,eAE9BF,OAAA,CAACxB,QAAQ;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,WAAW;kBAACC,OAAO,eAC7BV,OAAA,CAAC9B,MAAM,CAACyC,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BE,IAAI,EAAE;sBAAEH,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BG,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,eAE9BF,OAAA,CAACvB,YAAY;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,mBAAmB;kBAACC,OAAO,eACrCV,OAAA,CAAC9B,MAAM,CAACyC,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BE,IAAI,EAAE;sBAAEH,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BG,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,eAE9BF,OAAA,CAACtB,mBAAmB;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAC1BV,OAAA,CAAC9B,MAAM,CAACyC,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BE,IAAI,EAAE;sBAAEH,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BG,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,eAE9BF,OAAA,CAACrB,SAAS;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,UAAU;kBAACC,OAAO,eAC5BV,OAAA,CAAC9B,MAAM,CAACyC,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BE,IAAI,EAAE;sBAAEH,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BG,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,eAE9BF,OAAA,CAACpB,WAAW;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,WAAW;kBAACC,OAAO,eAC7BV,OAAA,CAAC9B,MAAM,CAACyC,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BE,IAAI,EAAE;sBAAEH,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7BG,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,eAE9BF,OAAA,CAACnB,YAAY;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,OAAO;kBAACC,OAAO,eAAEV,OAAA,CAACX,QAAQ;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7CP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,UAAU;kBAACC,OAAO,eAAEV,OAAA,CAACV,WAAW;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAEV,OAAA,CAACT,YAAY;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEV,OAAA,CAACR,cAAc;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,SAAS;kBAACC,OAAO,eAAEV,OAAA,CAACJ,UAAU;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,UAAU;kBAACC,OAAO,eAAEV,OAAA,CAACP,WAAW;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEV,OAAA,CAACN,SAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,UAAU;kBAACC,OAAO,eAAEV,OAAA,CAACL,WAAW;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAC1BV,OAAA,CAACZ,cAAc;oBAAC+B,WAAW,EAAE,KAAM;oBAAAjB,QAAA,eACjCF,OAAA,CAAClB,SAAS;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,WAAW;kBAACC,OAAO,eAC7BV,OAAA,CAACZ,cAAc;oBAAC+B,WAAW,EAAE,KAAM;oBAAAjB,QAAA,eACjCF,OAAA,CAACjB,YAAY;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eACnCV,OAAA,CAACZ,cAAc;oBAAC+B,WAAW,EAAE,KAAM;oBAAAjB,QAAA,eACjCF,OAAA,CAAChB,iBAAiB;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,UAAU;kBAACC,OAAO,eAC5BV,OAAA,CAACZ,cAAc;oBAAAc,QAAA,eACbF,OAAA,CAACf,WAAW;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJP,OAAA,CAAChC,KAAK;kBAACyC,IAAI,EAAC,WAAW;kBAACC,OAAO,eAC7BV,OAAA,CAACZ,cAAc;oBAAAc,QAAA,eACbF,OAAA,CAACd,YAAY;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGlBP,OAAA;cAAQG,SAAS,EAAC,uDAAuD;cAAAD,QAAA,eACvEF,OAAA;gBAAKG,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,gBAC3DF,OAAA;kBAAKG,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBAEpDF,OAAA;oBAAKG,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,gBACvCF,OAAA;sBAAKG,SAAS,EAAC,kCAAkC;sBAAAD,QAAA,gBAC/CF,OAAA;wBAAKG,SAAS,EAAC,oHAAoH;wBAAAD,QAAA,eACjIF,OAAA;0BAAKG,SAAS,EAAC,oBAAoB;0BAACiB,IAAI,EAAC,MAAM;0BAACC,MAAM,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAApB,QAAA,eACvFF,OAAA;4BAAMuB,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,WAAW,EAAE,CAAE;4BAACC,CAAC,EAAC;0BAA4C;4BAAAtB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNP,OAAA;wBAAMG,SAAS,EAAC,oBAAoB;wBAAAD,QAAA,EAAC;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACNP,OAAA;sBAAGG,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAG3C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJP,OAAA;sBAAKG,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,gBAC7BF,OAAA,CAACH,oBAAoB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxBP,OAAA,CAACF,kBAAkB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNP,OAAA;oBAAAE,QAAA,gBACEF,OAAA;sBAAIG,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3DP,OAAA;sBAAIG,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACvBF,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,GAAG;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAI;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzGP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,WAAW;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrHP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,mBAAmB;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAgB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrIP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,QAAQ;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClHP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,UAAU;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGNP,OAAA;oBAAAE,QAAA,gBACEF,OAAA;sBAAIG,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,EAAC;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChEP,OAAA;sBAAIG,SAAS,EAAC,WAAW;sBAAAD,QAAA,gBACvBF,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,OAAO;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACpHP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,UAAU;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnHP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,WAAW;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1HP,OAAA;wBAAAE,QAAA,eAAIF,OAAA,CAAC/B,IAAI;0BAAC0D,EAAE,EAAC,QAAQ;0BAACxB,SAAS,EAAC,6DAA6D;0BAAAD,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENP,OAAA;kBAAKG,SAAS,EAAC,2FAA2F;kBAAAD,QAAA,gBACxGF,OAAA;oBAAGG,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAErC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJP,OAAA;oBAAKG,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1CF,OAAA,CAAC/B,IAAI;sBAAC0D,EAAE,EAAC,UAAU;sBAACxB,SAAS,EAAC,qEAAqE;sBAAAD,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzHP,OAAA,CAAC/B,IAAI;sBAAC0D,EAAE,EAAC,QAAQ;sBAACxB,SAAS,EAAC,qEAAqE;sBAAAD,QAAA,EAAC;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzHP,OAAA,CAAC/B,IAAI;sBAAC0D,EAAE,EAAC,UAAU;sBAACxB,SAAS,EAAC,qEAAqE;sBAAAD,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACqB,EAAA,GA7KQ3B,GAAG;AA+KZ,eAAeA,GAAG;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}