{"ast": null, "code": "import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useAdmin}from'../contexts/AdminContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminProtectedRoute=_ref=>{let{children,requiredPermission=null,requiredRole=null}=_ref;const{isAuthenticated,isLoading,hasPermission,isRole}=useAdmin();const location=useLocation();// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"})});}// Redirect to admin login if not authenticated\nif(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/login\",state:{from:location},replace:true});}// Check for required permission\nif(requiredPermission&&!hasPermission(requiredPermission)){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 dark:text-white mb-4\",children:\"Access Denied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 dark:text-gray-400\",children:\"You don't have permission to access this resource.\"})]})});}// Check for required role\nif(requiredRole&&!isRole(requiredRole)){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 dark:text-white mb-4\",children:\"Access Denied\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 dark:text-gray-400\",children:\"You don't have the required role to access this resource.\"})]})});}return children;};export default AdminProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAdmin", "jsx", "_jsx", "jsxs", "_jsxs", "AdminProtectedRoute", "_ref", "children", "requiredPermission", "requiredRole", "isAuthenticated", "isLoading", "hasPermission", "isRole", "location", "className", "to", "state", "from", "replace"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AdminProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdmin } from '../contexts/AdminContext';\n\nconst AdminProtectedRoute = ({ children, requiredPermission = null, requiredRole = null }) => {\n  const { isAuthenticated, isLoading, hasPermission, isRole } = useAdmin();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"></div>\n      </div>\n    );\n  }\n\n  // Redirect to admin login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/admin/login\" state={{ from: location }} replace />;\n  }\n\n  // Check for required permission\n  if (requiredPermission && !hasPermission(requiredPermission)) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            You don't have permission to access this resource.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  // Check for required role\n  if (requiredRole && !isRole(requiredRole)) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            You don't have the required role to access this resource.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default AdminProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,QAAQ,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAAkE,IAAjE,CAAEC,QAAQ,CAAEC,kBAAkB,CAAG,IAAI,CAAEC,YAAY,CAAG,IAAK,CAAC,CAAAH,IAAA,CACvF,KAAM,CAAEI,eAAe,CAAEC,SAAS,CAAEC,aAAa,CAAEC,MAAO,CAAC,CAAGb,QAAQ,CAAC,CAAC,CACxE,KAAM,CAAAc,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAE9B;AACA,GAAIY,SAAS,CAAE,CACb,mBACET,IAAA,QAAKa,SAAS,CAAC,+CAA+C,CAAAR,QAAA,cAC5DL,IAAA,QAAKa,SAAS,CAAC,wEAAwE,CAAM,CAAC,CAC3F,CAAC,CAEV,CAEA;AACA,GAAI,CAACL,eAAe,CAAE,CACpB,mBAAOR,IAAA,CAACJ,QAAQ,EAACkB,EAAE,CAAC,cAAc,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAEJ,QAAS,CAAE,CAACK,OAAO,MAAE,CAAC,CAC1E,CAEA;AACA,GAAIX,kBAAkB,EAAI,CAACI,aAAa,CAACJ,kBAAkB,CAAC,CAAE,CAC5D,mBACEN,IAAA,QAAKa,SAAS,CAAC,+CAA+C,CAAAR,QAAA,cAC5DH,KAAA,QAAKW,SAAS,CAAC,aAAa,CAAAR,QAAA,eAC1BL,IAAA,OAAIa,SAAS,CAAC,uDAAuD,CAAAR,QAAA,CAAC,eAEtE,CAAI,CAAC,cACLL,IAAA,MAAGa,SAAS,CAAC,kCAAkC,CAAAR,QAAA,CAAC,oDAEhD,CAAG,CAAC,EACD,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIE,YAAY,EAAI,CAACI,MAAM,CAACJ,YAAY,CAAC,CAAE,CACzC,mBACEP,IAAA,QAAKa,SAAS,CAAC,+CAA+C,CAAAR,QAAA,cAC5DH,KAAA,QAAKW,SAAS,CAAC,aAAa,CAAAR,QAAA,eAC1BL,IAAA,OAAIa,SAAS,CAAC,uDAAuD,CAAAR,QAAA,CAAC,eAEtE,CAAI,CAAC,cACLL,IAAA,MAAGa,SAAS,CAAC,kCAAkC,CAAAR,QAAA,CAAC,2DAEhD,CAAG,CAAC,EACD,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}