{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\contexts\\\\AdminContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminContext = /*#__PURE__*/createContext();\nexport const useAdmin = () => {\n  _s();\n  const context = useContext(AdminContext);\n  if (!context) {\n    throw new Error('useAdmin must be used within an AdminProvider');\n  }\n  return context;\n};\n\n// Mock admin users for demonstration\n_s(useAdmin, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst mockAdmins = [{\n  id: 'admin-1',\n  username: 'admin',\n  password: 'admin123',\n  // In production, this would be hashed\n  email: '<EMAIL>',\n  firstName: 'Admin',\n  lastName: 'User',\n  role: 'super_admin',\n  permissions: ['all'],\n  createdAt: '2024-01-01T00:00:00Z',\n  lastLogin: null\n}, {\n  id: 'admin-2',\n  username: 'manager',\n  password: 'manager123',\n  email: '<EMAIL>',\n  firstName: 'Store',\n  lastName: 'Manager',\n  role: 'manager',\n  permissions: ['products', 'categories', 'inventory'],\n  createdAt: '2024-01-01T00:00:00Z',\n  lastLogin: null\n}];\nexport const AdminProvider = ({\n  children\n}) => {\n  _s2();\n  const [admin, setAdmin] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check for existing admin session on mount\n  useEffect(() => {\n    const checkAdminAuthStatus = () => {\n      const token = localStorage.getItem('adminAuthToken');\n      const adminData = localStorage.getItem('adminData');\n      if (token && adminData) {\n        try {\n          const parsedAdmin = JSON.parse(adminData);\n          setAdmin(parsedAdmin);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing admin data:', error);\n          localStorage.removeItem('adminAuthToken');\n          localStorage.removeItem('adminData');\n        }\n      }\n      setIsLoading(false);\n    };\n    checkAdminAuthStatus();\n  }, []);\n  const adminLogin = async (username, password, rememberMe = false) => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Find admin in mock data\n      const foundAdmin = mockAdmins.find(a => (a.username === username || a.email === username) && a.password === password);\n      if (!foundAdmin) {\n        throw new Error('Invalid username or password');\n      }\n\n      // Update last login\n      const updatedAdmin = {\n        ...foundAdmin,\n        lastLogin: new Date().toISOString()\n      };\n\n      // Generate mock token\n      const token = `admin_token_${Date.now()}`;\n\n      // Store auth data\n      if (rememberMe) {\n        localStorage.setItem('adminAuthToken', token);\n        localStorage.setItem('adminData', JSON.stringify(updatedAdmin));\n      } else {\n        sessionStorage.setItem('adminAuthToken', token);\n        sessionStorage.setItem('adminData', JSON.stringify(updatedAdmin));\n      }\n      setAdmin(updatedAdmin);\n      setIsAuthenticated(true);\n      setIsLoading(false);\n      return {\n        success: true,\n        admin: updatedAdmin\n      };\n    } catch (error) {\n      setIsLoading(false);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const adminLogout = () => {\n    // Clear stored data\n    localStorage.removeItem('adminAuthToken');\n    localStorage.removeItem('adminData');\n    sessionStorage.removeItem('adminAuthToken');\n    sessionStorage.removeItem('adminData');\n    setAdmin(null);\n    setIsAuthenticated(false);\n  };\n  const hasPermission = permission => {\n    if (!admin) return false;\n    if (admin.permissions.includes('all')) return true;\n    return admin.permissions.includes(permission);\n  };\n  const isRole = role => {\n    return (admin === null || admin === void 0 ? void 0 : admin.role) === role;\n  };\n  const isSuperAdmin = () => {\n    return (admin === null || admin === void 0 ? void 0 : admin.role) === 'super_admin';\n  };\n  const value = {\n    admin,\n    isLoading,\n    isAuthenticated,\n    adminLogin,\n    adminLogout,\n    hasPermission,\n    isRole,\n    isSuperAdmin\n  };\n  return /*#__PURE__*/_jsxDEV(AdminContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s2(AdminProvider, \"UhzfoO7G+kKrqSrc1fmJAMHiyv8=\");\n_c = AdminProvider;\nexport default AdminContext;\nvar _c;\n$RefreshReg$(_c, \"AdminProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AdminContext", "useAdmin", "_s", "context", "Error", "mockAdmins", "id", "username", "password", "email", "firstName", "lastName", "role", "permissions", "createdAt", "lastLogin", "Admin<PERSON><PERSON><PERSON>", "children", "_s2", "admin", "set<PERSON>d<PERSON>", "isLoading", "setIsLoading", "isAuthenticated", "setIsAuthenticated", "checkAdminAuthStatus", "token", "localStorage", "getItem", "adminData", "parsedAdmin", "JSON", "parse", "error", "console", "removeItem", "adminLogin", "rememberMe", "Promise", "resolve", "setTimeout", "found<PERSON>dmin", "find", "a", "updatedAdmin", "Date", "toISOString", "now", "setItem", "stringify", "sessionStorage", "success", "message", "adminLogout", "hasPermission", "permission", "includes", "isRole", "isSuperAdmin", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/AdminContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AdminContext = createContext();\n\nexport const useAdmin = () => {\n  const context = useContext(AdminContext);\n  if (!context) {\n    throw new Error('useAdmin must be used within an AdminProvider');\n  }\n  return context;\n};\n\n// Mock admin users for demonstration\nconst mockAdmins = [\n  {\n    id: 'admin-1',\n    username: 'admin',\n    password: 'admin123', // In production, this would be hashed\n    email: '<EMAIL>',\n    firstName: 'Admin',\n    lastName: 'User',\n    role: 'super_admin',\n    permissions: ['all'],\n    createdAt: '2024-01-01T00:00:00Z',\n    lastLogin: null\n  },\n  {\n    id: 'admin-2',\n    username: 'manager',\n    password: 'manager123',\n    email: '<EMAIL>',\n    firstName: 'Store',\n    lastName: 'Manager',\n    role: 'manager',\n    permissions: ['products', 'categories', 'inventory'],\n    createdAt: '2024-01-01T00:00:00Z',\n    lastLogin: null\n  }\n];\n\nexport const AdminProvider = ({ children }) => {\n  const [admin, setAdmin] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check for existing admin session on mount\n  useEffect(() => {\n    const checkAdminAuthStatus = () => {\n      const token = localStorage.getItem('adminAuthToken');\n      const adminData = localStorage.getItem('adminData');\n      \n      if (token && adminData) {\n        try {\n          const parsedAdmin = JSON.parse(adminData);\n          setAdmin(parsedAdmin);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing admin data:', error);\n          localStorage.removeItem('adminAuthToken');\n          localStorage.removeItem('adminData');\n        }\n      }\n      setIsLoading(false);\n    };\n\n    checkAdminAuthStatus();\n  }, []);\n\n  const adminLogin = async (username, password, rememberMe = false) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Find admin in mock data\n      const foundAdmin = mockAdmins.find(a => \n        (a.username === username || a.email === username) && a.password === password\n      );\n      \n      if (!foundAdmin) {\n        throw new Error('Invalid username or password');\n      }\n\n      // Update last login\n      const updatedAdmin = {\n        ...foundAdmin,\n        lastLogin: new Date().toISOString()\n      };\n\n      // Generate mock token\n      const token = `admin_token_${Date.now()}`;\n      \n      // Store auth data\n      if (rememberMe) {\n        localStorage.setItem('adminAuthToken', token);\n        localStorage.setItem('adminData', JSON.stringify(updatedAdmin));\n      } else {\n        sessionStorage.setItem('adminAuthToken', token);\n        sessionStorage.setItem('adminData', JSON.stringify(updatedAdmin));\n      }\n\n      setAdmin(updatedAdmin);\n      setIsAuthenticated(true);\n      setIsLoading(false);\n      \n      return { success: true, admin: updatedAdmin };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const adminLogout = () => {\n    // Clear stored data\n    localStorage.removeItem('adminAuthToken');\n    localStorage.removeItem('adminData');\n    sessionStorage.removeItem('adminAuthToken');\n    sessionStorage.removeItem('adminData');\n    \n    setAdmin(null);\n    setIsAuthenticated(false);\n  };\n\n  const hasPermission = (permission) => {\n    if (!admin) return false;\n    if (admin.permissions.includes('all')) return true;\n    return admin.permissions.includes(permission);\n  };\n\n  const isRole = (role) => {\n    return admin?.role === role;\n  };\n\n  const isSuperAdmin = () => {\n    return admin?.role === 'super_admin';\n  };\n\n  const value = {\n    admin,\n    isLoading,\n    isAuthenticated,\n    adminLogin,\n    adminLogout,\n    hasPermission,\n    isRole,\n    isSuperAdmin\n  };\n\n  return (\n    <AdminContext.Provider value={value}>\n      {children}\n    </AdminContext.Provider>\n  );\n};\n\nexport default AdminContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,QAAQ;AASrB,MAAMI,UAAU,GAAG,CACjB;EACEC,EAAE,EAAE,SAAS;EACbC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,UAAU;EAAE;EACtBC,KAAK,EAAE,mBAAmB;EAC1BC,SAAS,EAAE,OAAO;EAClBC,QAAQ,EAAE,MAAM;EAChBC,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,CAAC,KAAK,CAAC;EACpBC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,EACD;EACET,EAAE,EAAE,SAAS;EACbC,QAAQ,EAAE,SAAS;EACnBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE,qBAAqB;EAC5BC,SAAS,EAAE,OAAO;EAClBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC;EACpDC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE;AACb,CAAC,CACF;AAED,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4B,oBAAoB,GAAGA,CAAA,KAAM;MACjC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MACpD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAEnD,IAAIF,KAAK,IAAIG,SAAS,EAAE;QACtB,IAAI;UACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC;UACzCT,QAAQ,CAACU,WAAW,CAAC;UACrBN,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAC,CAAC,OAAOS,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDN,YAAY,CAACQ,UAAU,CAAC,gBAAgB,CAAC;UACzCR,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;QACtC;MACF;MACAb,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDG,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,UAAU,GAAG,MAAAA,CAAO7B,QAAQ,EAAEC,QAAQ,EAAE6B,UAAU,GAAG,KAAK,KAAK;IACnEf,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,UAAU,GAAGpC,UAAU,CAACqC,IAAI,CAACC,CAAC,IAClC,CAACA,CAAC,CAACpC,QAAQ,KAAKA,QAAQ,IAAIoC,CAAC,CAAClC,KAAK,KAAKF,QAAQ,KAAKoC,CAAC,CAACnC,QAAQ,KAAKA,QACtE,CAAC;MAED,IAAI,CAACiC,UAAU,EAAE;QACf,MAAM,IAAIrC,KAAK,CAAC,8BAA8B,CAAC;MACjD;;MAEA;MACA,MAAMwC,YAAY,GAAG;QACnB,GAAGH,UAAU;QACb1B,SAAS,EAAE,IAAI8B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAMpB,KAAK,GAAG,eAAemB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE;;MAEzC;MACA,IAAIV,UAAU,EAAE;QACdV,YAAY,CAACqB,OAAO,CAAC,gBAAgB,EAAEtB,KAAK,CAAC;QAC7CC,YAAY,CAACqB,OAAO,CAAC,WAAW,EAAEjB,IAAI,CAACkB,SAAS,CAACL,YAAY,CAAC,CAAC;MACjE,CAAC,MAAM;QACLM,cAAc,CAACF,OAAO,CAAC,gBAAgB,EAAEtB,KAAK,CAAC;QAC/CwB,cAAc,CAACF,OAAO,CAAC,WAAW,EAAEjB,IAAI,CAACkB,SAAS,CAACL,YAAY,CAAC,CAAC;MACnE;MAEAxB,QAAQ,CAACwB,YAAY,CAAC;MACtBpB,kBAAkB,CAAC,IAAI,CAAC;MACxBF,YAAY,CAAC,KAAK,CAAC;MAEnB,OAAO;QAAE6B,OAAO,EAAE,IAAI;QAAEhC,KAAK,EAAEyB;MAAa,CAAC;IAC/C,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdX,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE6B,OAAO,EAAE,KAAK;QAAElB,KAAK,EAAEA,KAAK,CAACmB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA1B,YAAY,CAACQ,UAAU,CAAC,gBAAgB,CAAC;IACzCR,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;IACpCe,cAAc,CAACf,UAAU,CAAC,gBAAgB,CAAC;IAC3Ce,cAAc,CAACf,UAAU,CAAC,WAAW,CAAC;IAEtCf,QAAQ,CAAC,IAAI,CAAC;IACdI,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAM8B,aAAa,GAAIC,UAAU,IAAK;IACpC,IAAI,CAACpC,KAAK,EAAE,OAAO,KAAK;IACxB,IAAIA,KAAK,CAACN,WAAW,CAAC2C,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;IAClD,OAAOrC,KAAK,CAACN,WAAW,CAAC2C,QAAQ,CAACD,UAAU,CAAC;EAC/C,CAAC;EAED,MAAME,MAAM,GAAI7C,IAAI,IAAK;IACvB,OAAO,CAAAO,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEP,IAAI,MAAKA,IAAI;EAC7B,CAAC;EAED,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO,CAAAvC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEP,IAAI,MAAK,aAAa;EACtC,CAAC;EAED,MAAM+C,KAAK,GAAG;IACZxC,KAAK;IACLE,SAAS;IACTE,eAAe;IACfa,UAAU;IACViB,WAAW;IACXC,aAAa;IACbG,MAAM;IACNC;EACF,CAAC;EAED,oBACE3D,OAAA,CAACC,YAAY,CAAC4D,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA1C,QAAA,EACjCA;EAAQ;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAC9C,GAAA,CAlHWF,aAAa;AAAAiD,EAAA,GAAbjD,aAAa;AAoH1B,eAAehB,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}