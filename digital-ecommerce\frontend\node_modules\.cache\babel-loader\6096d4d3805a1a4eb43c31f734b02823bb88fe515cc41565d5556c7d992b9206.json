{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /**\n    * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n    */function $9446cca9a3875146$export$7d15b64cf5a3a4c4(value, min = -Infinity, max = Infinity) {\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\nfunction $9446cca9a3875146$export$e1a7b8e69ef6c52f(value, step) {\n  let roundedValue = value;\n  let stepString = step.toString();\n  let pointIndex = stepString.indexOf('.');\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\nfunction $9446cca9a3875146$export$cb6e0bb50bc19463(value, min, max, step) {\n  min = Number(min);\n  max = Number(max);\n  let remainder = (value - (isNaN(min) ? 0 : min)) % step;\n  let snappedValue = $9446cca9a3875146$export$e1a7b8e69ef6c52f(Math.abs(remainder) * 2 >= step ? value + Math.sign(remainder) * (step - Math.abs(remainder)) : value - remainder, step);\n  if (!isNaN(min)) {\n    if (snappedValue < min) snappedValue = min;else if (!isNaN(max) && snappedValue > max) snappedValue = min + Math.floor($9446cca9a3875146$export$e1a7b8e69ef6c52f((max - min) / step, step)) * step;\n  } else if (!isNaN(max) && snappedValue > max) snappedValue = Math.floor($9446cca9a3875146$export$e1a7b8e69ef6c52f(max / step, step)) * step;\n  // correct floating point behavior by rounding to step precision\n  snappedValue = $9446cca9a3875146$export$e1a7b8e69ef6c52f(snappedValue, step);\n  return snappedValue;\n}\nfunction $9446cca9a3875146$export$b6268554fba451f(value, digits, base = 10) {\n  const pow = Math.pow(base, digits);\n  return Math.round(value * pow) / pow;\n}\nexport { $9446cca9a3875146$export$7d15b64cf5a3a4c4 as clamp, $9446cca9a3875146$export$e1a7b8e69ef6c52f as roundToStepPrecision, $9446cca9a3875146$export$cb6e0bb50bc19463 as snapValueToStep, $9446cca9a3875146$export$b6268554fba451f as toFixedNumber };", "map": {"version": 3, "names": ["$9446cca9a3875146$export$7d15b64cf5a3a4c4", "value", "min", "Infinity", "max", "newValue", "Math", "$9446cca9a3875146$export$e1a7b8e69ef6c52f", "step", "roundedValue", "stepString", "toString", "pointIndex", "indexOf", "precision", "length", "pow", "round", "$9446cca9a3875146$export$cb6e0bb50bc19463", "Number", "remainder", "isNaN", "snappedValue", "abs", "sign", "floor", "$9446cca9a3875146$export$b6268554fba451f", "digits", "base"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-stately\\utils\\dist\\packages\\@react-stately\\utils\\src\\number.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n */\nexport function clamp(value: number, min: number = -Infinity, max: number = Infinity): number {\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\n\nexport function roundToStepPrecision(value: number, step: number): number {\n  let roundedValue = value;\n  let stepString = step.toString();\n  let pointIndex = stepString.indexOf('.');\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\n\nexport function snapValueToStep(value: number, min: number | undefined, max: number | undefined, step: number): number {\n  min = Number(min);\n  max = Number(max);\n  let remainder = ((value - (isNaN(min) ? 0 : min)) % step);\n  let snappedValue = roundToStepPrecision(Math.abs(remainder) * 2 >= step\n    ? value + Math.sign(remainder) * (step - Math.abs(remainder))\n    : value - remainder, step);\n\n  if (!isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min;\n    } else if (!isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor(roundToStepPrecision((max - min) / step, step)) * step;\n    }\n  } else if (!isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(roundToStepPrecision(max / step, step)) * step;\n  }\n\n  // correct floating point behavior by rounding to step precision\n  snappedValue = roundToStepPrecision(snappedValue, step);\n\n  return snappedValue;\n}\n\n/* Takes a value and rounds off to the number of digits. */\nexport function toFixedNumber(value: number, digits: number, base: number = 10): number {\n  const pow = Math.pow(base, digits);\n\n  return Math.round(value * pow) / pow;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAAA,CAYA;;MAGO,SAASA,0CAAMC,KAAa,EAAEC,GAAA,GAAc,CAACC,QAAQ,EAAEC,GAAA,GAAcD,QAAQ;EAClF,IAAIE,QAAA,GAAWC,IAAA,CAAKJ,GAAG,CAACI,IAAA,CAAKF,GAAG,CAACH,KAAA,EAAOC,GAAA,GAAME,GAAA;EAC9C,OAAOC,QAAA;AACT;AAEO,SAASE,0CAAqBN,KAAa,EAAEO,IAAY;EAC9D,IAAIC,YAAA,GAAeR,KAAA;EACnB,IAAIS,UAAA,GAAaF,IAAA,CAAKG,QAAQ;EAC9B,IAAIC,UAAA,GAAaF,UAAA,CAAWG,OAAO,CAAC;EACpC,IAAIC,SAAA,GAAYF,UAAA,IAAc,IAAIF,UAAA,CAAWK,MAAM,GAAGH,UAAA,GAAa;EACnE,IAAIE,SAAA,GAAY,GAAG;IACjB,IAAIE,GAAA,GAAMV,IAAA,CAAKU,GAAG,CAAC,IAAIF,SAAA;IACvBL,YAAA,GAAeH,IAAA,CAAKW,KAAK,CAACR,YAAA,GAAeO,GAAA,IAAOA,GAAA;EAClD;EACA,OAAOP,YAAA;AACT;AAEO,SAASS,0CAAgBjB,KAAa,EAAEC,GAAuB,EAAEE,GAAuB,EAAEI,IAAY;EAC3GN,GAAA,GAAMiB,MAAA,CAAOjB,GAAA;EACbE,GAAA,GAAMe,MAAA,CAAOf,GAAA;EACb,IAAIgB,SAAA,GAAa,CAACnB,KAAA,IAASoB,KAAA,CAAMnB,GAAA,IAAO,IAAIA,GAAE,CAAC,IAAKM,IAAA;EACpD,IAAIc,YAAA,GAAef,yCAAA,CAAqBD,IAAA,CAAKiB,GAAG,CAACH,SAAA,IAAa,KAAKZ,IAAA,GAC/DP,KAAA,GAAQK,IAAA,CAAKkB,IAAI,CAACJ,SAAA,KAAcZ,IAAA,GAAOF,IAAA,CAAKiB,GAAG,CAACH,SAAA,CAAS,IACzDnB,KAAA,GAAQmB,SAAA,EAAWZ,IAAA;EAEvB,IAAI,CAACa,KAAA,CAAMnB,GAAA,GAAM;IACf,IAAIoB,YAAA,GAAepB,GAAA,EACjBoB,YAAA,GAAepB,GAAA,MACV,IAAI,CAACmB,KAAA,CAAMjB,GAAA,KAAQkB,YAAA,GAAelB,GAAA,EACvCkB,YAAA,GAAepB,GAAA,GAAMI,IAAA,CAAKmB,KAAK,CAAClB,yCAAA,CAAqB,CAACH,GAAA,GAAMF,GAAE,IAAKM,IAAA,EAAMA,IAAA,KAASA,IAAA;EAEtF,OAAO,IAAI,CAACa,KAAA,CAAMjB,GAAA,KAAQkB,YAAA,GAAelB,GAAA,EACvCkB,YAAA,GAAehB,IAAA,CAAKmB,KAAK,CAAClB,yCAAA,CAAqBH,GAAA,GAAMI,IAAA,EAAMA,IAAA,KAASA,IAAA;EAGtE;EACAc,YAAA,GAAef,yCAAA,CAAqBe,YAAA,EAAcd,IAAA;EAElD,OAAOc,YAAA;AACT;AAGO,SAASI,yCAAczB,KAAa,EAAE0B,MAAc,EAAEC,IAAA,GAAe,EAAE;EAC5E,MAAMZ,GAAA,GAAMV,IAAA,CAAKU,GAAG,CAACY,IAAA,EAAMD,MAAA;EAE3B,OAAOrB,IAAA,CAAKW,KAAK,CAAChB,KAAA,GAAQe,GAAA,IAAOA,GAAA;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}