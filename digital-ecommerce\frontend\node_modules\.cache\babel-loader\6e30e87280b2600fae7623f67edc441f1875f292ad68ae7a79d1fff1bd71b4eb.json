{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import{XMarkIcon,PhotoIcon,PlusIcon,TrashIcon,ArrowUpTrayIcon}from'@heroicons/react/24/outline';import{categories}from'../data/products';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddProductModal=_ref=>{var _steps;let{isOpen,onClose,onSubmit}=_ref;const[currentStep,setCurrentStep]=useState(1);const[formData,setFormData]=useState({name:'',description:'',shortDescription:'',price:'',discountPrice:'',currency:'USD',category:'',subcategory:'',type:'physical',stockCount:'',sku:'',tags:[],keywords:'',isActive:true,isFeatured:false,specifications:{},images:[]});const[errors,setErrors]=useState({});const[isSubmitting,setIsSubmitting]=useState(false);const[dragActive,setDragActive]=useState(false);const[newTag,setNewTag]=useState('');// Auto-generate SKU when product name changes\nuseEffect(()=>{if(formData.name&&!formData.sku){const sku=formData.name.toUpperCase().replace(/[^A-Z0-9]/g,'').substring(0,8)+'-'+Date.now().toString().slice(-4);setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{sku}));}},[formData.name]);const steps=[{id:1,name:'Basic Info',description:'Product name, description, and category'},{id:2,name:'Pricing',description:'Price, discounts, and currency'},{id:3,name:'Images',description:'Product photos and media'},{id:4,name:'Details',description:'Stock, SKU, and specifications'},{id:5,name:'Settings',description:'Tags, keywords, and publication'}];const validateStep=step=>{const newErrors={};switch(step){case 1:if(!formData.name.trim())newErrors.name='Product name is required';if(formData.name.length>100)newErrors.name='Product name must be less than 100 characters';if(!formData.description.trim())newErrors.description='Description is required';if(formData.description.length>2000)newErrors.description='Description must be less than 2000 characters';if(!formData.category)newErrors.category='Category is required';break;case 2:if(!formData.price)newErrors.price='Price is required';if(isNaN(formData.price)||parseFloat(formData.price)<=0)newErrors.price='Price must be a positive number';if(formData.discountPrice&&(isNaN(formData.discountPrice)||parseFloat(formData.discountPrice)<=0)){newErrors.discountPrice='Discount price must be a positive number';}if(formData.discountPrice&&parseFloat(formData.discountPrice)>=parseFloat(formData.price)){newErrors.discountPrice='Discount price must be less than regular price';}break;case 3:if(formData.images.length===0)newErrors.images='At least one product image is required';break;case 4:if(formData.type==='physical'&&(!formData.stockCount||isNaN(formData.stockCount)||parseInt(formData.stockCount)<0)){newErrors.stockCount='Stock count must be a non-negative number for physical products';}if(!formData.sku.trim())newErrors.sku='SKU is required';break;}setErrors(newErrors);return Object.keys(newErrors).length===0;};const handleNext=()=>{if(validateStep(currentStep)){setCurrentStep(prev=>Math.min(prev+1,steps.length));}};const handlePrev=()=>{setCurrentStep(prev=>Math.max(prev-1,1));};const handleInputChange=(field,value)=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));if(errors[field]){setErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:''}));}};const handleImageUpload=files=>{const newImages=Array.from(files).map(file=>({id:Date.now()+Math.random(),file,url:URL.createObjectURL(file),name:file.name,size:file.size}));setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{images:[...prev.images,...newImages]}));};const handleDrag=e=>{e.preventDefault();e.stopPropagation();if(e.type==='dragenter'||e.type==='dragover'){setDragActive(true);}else if(e.type==='dragleave'){setDragActive(false);}};const handleDrop=e=>{e.preventDefault();e.stopPropagation();setDragActive(false);if(e.dataTransfer.files&&e.dataTransfer.files[0]){handleImageUpload(e.dataTransfer.files);}};const removeImage=imageId=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{images:prev.images.filter(img=>img.id!==imageId)}));};const moveImage=(fromIndex,toIndex)=>{const newImages=[...formData.images];const[removed]=newImages.splice(fromIndex,1);newImages.splice(toIndex,0,removed);setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{images:newImages}));};const addTag=()=>{if(newTag.trim()&&!formData.tags.includes(newTag.trim())){setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{tags:[...prev.tags,newTag.trim()]}));setNewTag('');}};const removeTag=tagToRemove=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{tags:prev.tags.filter(tag=>tag!==tagToRemove)}));};const handleSubmit=async()=>{if(!validateStep(currentStep))return;setIsSubmitting(true);try{await onSubmit(formData);onClose();// Reset form\nsetFormData({name:'',description:'',shortDescription:'',price:'',discountPrice:'',currency:'USD',category:'',subcategory:'',type:'physical',stockCount:'',sku:'',tags:[],keywords:'',isActive:true,isFeatured:false,specifications:{},images:[]});setCurrentStep(1);setErrors({});}catch(error){console.error('Error creating product:',error);}finally{setIsSubmitting(false);}};const selectedCategory=categories.find(cat=>cat.id===formData.category);const renderStepContent=()=>{var _selectedCategory$sub,_categories$find;switch(currentStep){case 1:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Product Name *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.name,onChange:e=>handleInputChange('name',e.target.value),className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.name?'border-red-500':'border-gray-300'),placeholder:\"Enter product name\"}),errors.name&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.name})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Short Description\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.shortDescription,onChange:e=>handleInputChange('shortDescription',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",placeholder:\"Brief product description\",maxLength:150}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-xs text-gray-500\",children:[formData.shortDescription.length,\"/150 characters\"]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Description *\"}),/*#__PURE__*/_jsx(\"textarea\",{value:formData.description,onChange:e=>handleInputChange('description',e.target.value),rows:4,className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.description?'border-red-500':'border-gray-300'),placeholder:\"Detailed product description\",maxLength:2000}),errors.description&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.description}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-xs text-gray-500\",children:[formData.description.length,\"/2000 characters\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Category *\"}),/*#__PURE__*/_jsxs(\"select\",{value:formData.category,onChange:e=>handleInputChange('category',e.target.value),className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.category?'border-red-500':'border-gray-300'),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a category\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.id,children:category.name},category.id))]}),errors.category&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.category})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Subcategory\"}),/*#__PURE__*/_jsxs(\"select\",{value:formData.subcategory,onChange:e=>handleInputChange('subcategory',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",disabled:!(selectedCategory!==null&&selectedCategory!==void 0&&selectedCategory.subcategories),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a subcategory\"}),selectedCategory===null||selectedCategory===void 0?void 0:(_selectedCategory$sub=selectedCategory.subcategories)===null||_selectedCategory$sub===void 0?void 0:_selectedCategory$sub.map(sub=>/*#__PURE__*/_jsx(\"option\",{value:sub,children:sub.replace('-',' ').replace(/\\b\\w/g,l=>l.toUpperCase())},sub))]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Product Type\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-4\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",value:\"physical\",checked:formData.type==='physical',onChange:e=>handleInputChange('type',e.target.value),className:\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"}),\"Physical Product\"]}),/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",value:\"digital\",checked:formData.type==='digital',onChange:e=>handleInputChange('type',e.target.value),className:\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"}),\"Digital Product\"]})]})]})]});case 2:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:col-span-2\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:[\"Price * (\",formData.currency,\")\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",min:\"0\",value:formData.price,onChange:e=>handleInputChange('price',e.target.value),className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.price?'border-red-500':'border-gray-300'),placeholder:\"0.00\"}),errors.price&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.price})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Currency\"}),/*#__PURE__*/_jsxs(\"select\",{value:formData.currency,onChange:e=>handleInputChange('currency',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"USD\",children:\"USD ($)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"EUR\",children:\"EUR (\\u20AC)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"GBP\",children:\"GBP (\\xA3)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CAD\",children:\"CAD (C$)\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:[\"Discount Price (\",formData.currency,\")\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",min:\"0\",value:formData.discountPrice,onChange:e=>handleInputChange('discountPrice',e.target.value),className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.discountPrice?'border-red-500':'border-gray-300'),placeholder:\"0.00 (optional)\"}),errors.discountPrice&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.discountPrice}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-xs text-gray-500\",children:\"Leave empty if no discount\"})]}),formData.price&&formData.discountPrice&&/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 bg-green-50 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-800\",children:\"Discount Amount:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-green-800\",children:[formData.currency,\" \",(parseFloat(formData.price)-parseFloat(formData.discountPrice)).toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mt-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-800\",children:\"Discount Percentage:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-green-800\",children:[((parseFloat(formData.price)-parseFloat(formData.discountPrice))/parseFloat(formData.price)*100).toFixed(1),\"%\"]})]})]})]});case 3:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Product Images *\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-2 border-dashed rounded-lg p-6 text-center transition-colors \".concat(dragActive?'border-light-orange-500 bg-light-orange-50':errors.images?'border-red-500 bg-red-50':'border-gray-300 hover:border-light-orange-400'),onDragEnter:handleDrag,onDragLeave:handleDrag,onDragOver:handleDrag,onDrop:handleDrop,children:[/*#__PURE__*/_jsx(ArrowUpTrayIcon,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"file-upload\",className:\"cursor-pointer\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mt-2 block text-sm font-medium text-gray-900\",children:\"Drop images here or click to upload\"}),/*#__PURE__*/_jsx(\"span\",{className:\"mt-1 block text-xs text-gray-500\",children:\"PNG, JPG, GIF up to 10MB each\"})]}),/*#__PURE__*/_jsx(\"input\",{id:\"file-upload\",name:\"file-upload\",type:\"file\",className:\"sr-only\",multiple:true,accept:\"image/*\",onChange:e=>handleImageUpload(e.target.files)})]})]}),errors.images&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.images})]}),formData.images.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-sm font-medium text-gray-700 mb-3\",children:[\"Uploaded Images (\",formData.images.length,\")\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 md:grid-cols-3 gap-4\",children:formData.images.map((image,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsx(\"img\",{src:image.url,alt:image.name,className:\"w-full h-32 object-cover rounded-lg border border-gray-200\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>removeImage(image.id),className:\"p-2 bg-red-500 text-white rounded-full hover:bg-red-600\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4\"})})}),index===0&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded\",children:\"Main\"})]},image.id))}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-xs text-gray-500\",children:\"The first image will be used as the main product image. Drag to reorder.\"})]})]});case 4:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"SKU (Stock Keeping Unit) *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.sku,onChange:e=>handleInputChange('sku',e.target.value.toUpperCase()),className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.sku?'border-red-500':'border-gray-300'),placeholder:\"AUTO-GENERATED\"}),errors.sku&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.sku}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-xs text-gray-500\",children:\"Unique identifier for this product\"})]}),formData.type==='physical'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Stock Quantity *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",min:\"0\",value:formData.stockCount,onChange:e=>handleInputChange('stockCount',e.target.value),className:\"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 \".concat(errors.stockCount?'border-red-500':'border-gray-300'),placeholder:\"0\"}),errors.stockCount&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-600\",children:errors.stockCount})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Product Specifications\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Specification name (e.g., Weight)\",className:\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Value (e.g., 1.5 kg)\",className:\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"})]}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",className:\"flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Add Specification\"})]})]})]})]});case 5:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Product Tags\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-2 mb-3\",children:formData.tags.map((tag,index)=>/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800\",children:[tag,/*#__PURE__*/_jsx(\"button\",{onClick:()=>removeTag(tag),className:\"ml-2 text-light-orange-600 hover:text-light-orange-800\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-3 h-3\"})})]},index))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:newTag,onChange:e=>setNewTag(e.target.value),onKeyPress:e=>e.key==='Enter'&&(e.preventDefault(),addTag()),className:\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",placeholder:\"Add a tag\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:addTag,className:\"px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600\",children:\"Add\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Keywords (for search)\"}),/*#__PURE__*/_jsx(\"textarea\",{value:formData.keywords,onChange:e=>handleInputChange('keywords',e.target.value),rows:3,className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\",placeholder:\"Enter keywords separated by commas\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-xs text-gray-500\",children:\"Help customers find this product\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"isActive\",checked:formData.isActive,onChange:e=>handleInputChange('isActive',e.target.checked),className:\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"isActive\",className:\"text-sm font-medium text-gray-700\",children:\"Publish product (make it visible to customers)\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"isFeatured\",checked:formData.isFeatured,onChange:e=>handleInputChange('isFeatured',e.target.checked),className:\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"isFeatured\",className:\"text-sm font-medium text-gray-700\",children:\"Feature this product (show in featured sections)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-blue-800 mb-2\",children:\"Product Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-blue-700 space-y-1\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Name:\"}),\" \",formData.name||'Not set']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Price:\"}),\" \",formData.currency,\" \",formData.price||'0.00']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Category:\"}),\" \",((_categories$find=categories.find(c=>c.id===formData.category))===null||_categories$find===void 0?void 0:_categories$find.name)||'Not set']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Type:\"}),\" \",formData.type]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Images:\"}),\" \",formData.images.length,\" uploaded\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Status:\"}),\" \",formData.isActive?'Active':'Draft']})]})]})]});default:return/*#__PURE__*/_jsxs(\"div\",{children:[\"Step content for step \",currentStep]});}};if(!isOpen)return null;return/*#__PURE__*/_jsx(AnimatePresence,{children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",onClick:onClose,children:/*#__PURE__*/_jsxs(motion.div,{initial:{scale:0.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0.9,opacity:0},onClick:e=>e.stopPropagation(),className:\"w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-6 border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Add New Product\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600 mt-1\",children:[\"Step \",currentStep,\" of \",steps.length,\": \",(_steps=steps[currentStep-1])===null||_steps===void 0?void 0:_steps.description]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-6 h-6\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between\",children:steps.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium \".concat(currentStep>step.id?'bg-green-500 text-white':currentStep===step.id?'bg-light-orange-500 text-white':'bg-gray-200 text-gray-600'),children:currentStep>step.id?'✓':step.id}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-sm font-medium \".concat(currentStep>=step.id?'text-gray-900':'text-gray-500'),children:step.name}),index<steps.length-1&&/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-0.5 mx-4 \".concat(currentStep>step.id?'bg-green-500':'bg-gray-200')})]},step.id))})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6 max-h-96 overflow-y-auto\",children:renderStepContent()}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handlePrev,disabled:currentStep===1,className:\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",children:\"Previous\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",children:\"Cancel\"}),currentStep<steps.length?/*#__PURE__*/_jsx(\"button\",{onClick:handleNext,className:\"px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600\",children:\"Next\"}):/*#__PURE__*/_jsx(\"button\",{onClick:handleSubmit,disabled:isSubmitting,className:\"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\",children:isSubmitting?'Creating...':'Create Product'})]})]})]})})});};export default AddProductModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PhotoIcon", "PlusIcon", "TrashIcon", "ArrowUpTrayIcon", "categories", "jsx", "_jsx", "jsxs", "_jsxs", "AddProductModal", "_ref", "_steps", "isOpen", "onClose", "onSubmit", "currentStep", "setCurrentStep", "formData", "setFormData", "name", "description", "shortDescription", "price", "discountPrice", "currency", "category", "subcategory", "type", "stockCount", "sku", "tags", "keywords", "isActive", "isFeatured", "specifications", "images", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "dragActive", "setDragActive", "newTag", "setNewTag", "toUpperCase", "replace", "substring", "Date", "now", "toString", "slice", "prev", "_objectSpread", "steps", "id", "validateStep", "step", "newErrors", "trim", "length", "isNaN", "parseFloat", "parseInt", "Object", "keys", "handleNext", "Math", "min", "handlePrev", "max", "handleInputChange", "field", "value", "handleImageUpload", "files", "newImages", "Array", "from", "map", "file", "random", "url", "URL", "createObjectURL", "size", "handleDrag", "e", "preventDefault", "stopPropagation", "handleDrop", "dataTransfer", "removeImage", "imageId", "filter", "img", "moveImage", "fromIndex", "toIndex", "removed", "splice", "addTag", "includes", "removeTag", "tagToRemove", "tag", "handleSubmit", "error", "console", "selectedCate<PERSON><PERSON>", "find", "cat", "renderStepContent", "_selectedCategory$sub", "_categories$find", "className", "children", "onChange", "target", "concat", "placeholder", "max<PERSON><PERSON><PERSON>", "rows", "disabled", "subcategories", "sub", "l", "checked", "toFixed", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "htmlFor", "multiple", "accept", "image", "index", "src", "alt", "onClick", "onKeyPress", "key", "c", "div", "initial", "opacity", "animate", "exit", "scale"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AddProductModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  XMarkIcon,\n  PhotoIcon,\n  PlusIcon,\n  TrashIcon,\n  ArrowUpTrayIcon\n} from '@heroicons/react/24/outline';\nimport { categories } from '../data/products';\n\nconst AddProductModal = ({ isOpen, onClose, onSubmit }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    shortDescription: '',\n    price: '',\n    discountPrice: '',\n    currency: 'USD',\n    category: '',\n    subcategory: '',\n    type: 'physical',\n    stockCount: '',\n    sku: '',\n    tags: [],\n    keywords: '',\n    isActive: true,\n    isFeatured: false,\n    specifications: {},\n    images: []\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [dragActive, setDragActive] = useState(false);\n  const [newTag, setNewTag] = useState('');\n\n  // Auto-generate SKU when product name changes\n  useEffect(() => {\n    if (formData.name && !formData.sku) {\n      const sku = formData.name\n        .toUpperCase()\n        .replace(/[^A-Z0-9]/g, '')\n        .substring(0, 8) + '-' + Date.now().toString().slice(-4);\n      setFormData(prev => ({ ...prev, sku }));\n    }\n  }, [formData.name]);\n\n  const steps = [\n    { id: 1, name: 'Basic Info', description: 'Product name, description, and category' },\n    { id: 2, name: 'Pricing', description: 'Price, discounts, and currency' },\n    { id: 3, name: 'Images', description: 'Product photos and media' },\n    { id: 4, name: 'Details', description: 'Stock, SKU, and specifications' },\n    { id: 5, name: 'Settings', description: 'Tags, keywords, and publication' }\n  ];\n\n  const validateStep = (step) => {\n    const newErrors = {};\n\n    switch (step) {\n      case 1:\n        if (!formData.name.trim()) newErrors.name = 'Product name is required';\n        if (formData.name.length > 100) newErrors.name = 'Product name must be less than 100 characters';\n        if (!formData.description.trim()) newErrors.description = 'Description is required';\n        if (formData.description.length > 2000) newErrors.description = 'Description must be less than 2000 characters';\n        if (!formData.category) newErrors.category = 'Category is required';\n        break;\n      case 2:\n        if (!formData.price) newErrors.price = 'Price is required';\n        if (isNaN(formData.price) || parseFloat(formData.price) <= 0) newErrors.price = 'Price must be a positive number';\n        if (formData.discountPrice && (isNaN(formData.discountPrice) || parseFloat(formData.discountPrice) <= 0)) {\n          newErrors.discountPrice = 'Discount price must be a positive number';\n        }\n        if (formData.discountPrice && parseFloat(formData.discountPrice) >= parseFloat(formData.price)) {\n          newErrors.discountPrice = 'Discount price must be less than regular price';\n        }\n        break;\n      case 3:\n        if (formData.images.length === 0) newErrors.images = 'At least one product image is required';\n        break;\n      case 4:\n        if (formData.type === 'physical' && (!formData.stockCount || isNaN(formData.stockCount) || parseInt(formData.stockCount) < 0)) {\n          newErrors.stockCount = 'Stock count must be a non-negative number for physical products';\n        }\n        if (!formData.sku.trim()) newErrors.sku = 'SKU is required';\n        break;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNext = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length));\n    }\n  };\n\n  const handlePrev = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const handleImageUpload = (files) => {\n    const newImages = Array.from(files).map(file => ({\n      id: Date.now() + Math.random(),\n      file,\n      url: URL.createObjectURL(file),\n      name: file.name,\n      size: file.size\n    }));\n\n    setFormData(prev => ({\n      ...prev,\n      images: [...prev.images, ...newImages]\n    }));\n  };\n\n  const handleDrag = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleImageUpload(e.dataTransfer.files);\n    }\n  };\n\n  const removeImage = (imageId) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter(img => img.id !== imageId)\n    }));\n  };\n\n  const moveImage = (fromIndex, toIndex) => {\n    const newImages = [...formData.images];\n    const [removed] = newImages.splice(fromIndex, 1);\n    newImages.splice(toIndex, 0, removed);\n    setFormData(prev => ({ ...prev, images: newImages }));\n  };\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }));\n      setNewTag('');\n    }\n  };\n\n  const removeTag = (tagToRemove) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleSubmit = async () => {\n    if (!validateStep(currentStep)) return;\n\n    setIsSubmitting(true);\n    try {\n      await onSubmit(formData);\n      onClose();\n      // Reset form\n      setFormData({\n        name: '',\n        description: '',\n        shortDescription: '',\n        price: '',\n        discountPrice: '',\n        currency: 'USD',\n        category: '',\n        subcategory: '',\n        type: 'physical',\n        stockCount: '',\n        sku: '',\n        tags: [],\n        keywords: '',\n        isActive: true,\n        isFeatured: false,\n        specifications: {},\n        images: []\n      });\n      setCurrentStep(1);\n      setErrors({});\n    } catch (error) {\n      console.error('Error creating product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const selectedCategory = categories.find(cat => cat.id === formData.category);\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.name ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter product name\"\n              />\n              {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Short Description\n              </label>\n              <input\n                type=\"text\"\n                value={formData.shortDescription}\n                onChange={(e) => handleInputChange('shortDescription', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                placeholder=\"Brief product description\"\n                maxLength={150}\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">{formData.shortDescription.length}/150 characters</p>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.description ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Detailed product description\"\n                maxLength={2000}\n              />\n              {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\n              <p className=\"mt-1 text-xs text-gray-500\">{formData.description.length}/2000 characters</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category *\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => handleInputChange('category', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.category ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">Select a category</option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.category && <p className=\"mt-1 text-sm text-red-600\">{errors.category}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Subcategory\n                </label>\n                <select\n                  value={formData.subcategory}\n                  onChange={(e) => handleInputChange('subcategory', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  disabled={!selectedCategory?.subcategories}\n                >\n                  <option value=\"\">Select a subcategory</option>\n                  {selectedCategory?.subcategories?.map(sub => (\n                    <option key={sub} value={sub}>\n                      {sub.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Type\n              </label>\n              <div className=\"flex space-x-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"physical\"\n                    checked={formData.type === 'physical'}\n                    onChange={(e) => handleInputChange('type', e.target.value)}\n                    className=\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                  />\n                  Physical Product\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    value=\"digital\"\n                    checked={formData.type === 'digital'}\n                    onChange={(e) => handleInputChange('type', e.target.value)}\n                    className=\"mr-2 text-light-orange-600 focus:ring-light-orange-500\"\n                  />\n                  Digital Product\n                </label>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Price * ({formData.currency})\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  value={formData.price}\n                  onChange={(e) => handleInputChange('price', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.price ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"0.00\"\n                />\n                {errors.price && <p className=\"mt-1 text-sm text-red-600\">{errors.price}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Currency\n                </label>\n                <select\n                  value={formData.currency}\n                  onChange={(e) => handleInputChange('currency', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                >\n                  <option value=\"USD\">USD ($)</option>\n                  <option value=\"EUR\">EUR (€)</option>\n                  <option value=\"GBP\">GBP (£)</option>\n                  <option value=\"CAD\">CAD (C$)</option>\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Discount Price ({formData.currency})\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                value={formData.discountPrice}\n                onChange={(e) => handleInputChange('discountPrice', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                  errors.discountPrice ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00 (optional)\"\n              />\n              {errors.discountPrice && <p className=\"mt-1 text-sm text-red-600\">{errors.discountPrice}</p>}\n              <p className=\"mt-1 text-xs text-gray-500\">Leave empty if no discount</p>\n            </div>\n\n            {formData.price && formData.discountPrice && (\n              <div className=\"p-4 bg-green-50 rounded-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-green-800\">Discount Amount:</span>\n                  <span className=\"text-sm font-bold text-green-800\">\n                    {formData.currency} {(parseFloat(formData.price) - parseFloat(formData.discountPrice)).toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between mt-1\">\n                  <span className=\"text-sm font-medium text-green-800\">Discount Percentage:</span>\n                  <span className=\"text-sm font-bold text-green-800\">\n                    {(((parseFloat(formData.price) - parseFloat(formData.discountPrice)) / parseFloat(formData.price)) * 100).toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Images *\n              </label>\n              <div\n                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n                  dragActive\n                    ? 'border-light-orange-500 bg-light-orange-50'\n                    : errors.images\n                      ? 'border-red-500 bg-red-50'\n                      : 'border-gray-300 hover:border-light-orange-400'\n                }`}\n                onDragEnter={handleDrag}\n                onDragLeave={handleDrag}\n                onDragOver={handleDrag}\n                onDrop={handleDrop}\n              >\n                <ArrowUpTrayIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <div className=\"mt-4\">\n                  <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n                    <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                      Drop images here or click to upload\n                    </span>\n                    <span className=\"mt-1 block text-xs text-gray-500\">\n                      PNG, JPG, GIF up to 10MB each\n                    </span>\n                  </label>\n                  <input\n                    id=\"file-upload\"\n                    name=\"file-upload\"\n                    type=\"file\"\n                    className=\"sr-only\"\n                    multiple\n                    accept=\"image/*\"\n                    onChange={(e) => handleImageUpload(e.target.files)}\n                  />\n                </div>\n              </div>\n              {errors.images && <p className=\"mt-1 text-sm text-red-600\">{errors.images}</p>}\n            </div>\n\n            {formData.images.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-700 mb-3\">\n                  Uploaded Images ({formData.images.length})\n                </h4>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                  {formData.images.map((image, index) => (\n                    <div key={image.id} className=\"relative group\">\n                      <img\n                        src={image.url}\n                        alt={image.name}\n                        className=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\n                      />\n                      <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\">\n                        <button\n                          onClick={() => removeImage(image.id)}\n                          className=\"p-2 bg-red-500 text-white rounded-full hover:bg-red-600\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                      {index === 0 && (\n                        <div className=\"absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded\">\n                          Main\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n                <p className=\"mt-2 text-xs text-gray-500\">\n                  The first image will be used as the main product image. Drag to reorder.\n                </p>\n              </div>\n            )}\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  SKU (Stock Keeping Unit) *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.sku}\n                  onChange={(e) => handleInputChange('sku', e.target.value.toUpperCase())}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                    errors.sku ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"AUTO-GENERATED\"\n                />\n                {errors.sku && <p className=\"mt-1 text-sm text-red-600\">{errors.sku}</p>}\n                <p className=\"mt-1 text-xs text-gray-500\">Unique identifier for this product</p>\n              </div>\n\n              {formData.type === 'physical' && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Stock Quantity *\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    value={formData.stockCount}\n                    onChange={(e) => handleInputChange('stockCount', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 ${\n                      errors.stockCount ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder=\"0\"\n                  />\n                  {errors.stockCount && <p className=\"mt-1 text-sm text-red-600\">{errors.stockCount}</p>}\n                </div>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Specifications\n              </label>\n              <div className=\"space-y-3\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Specification name (e.g., Weight)\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Value (e.g., 1.5 kg)\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  />\n                </div>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center space-x-2 text-sm text-light-orange-600 hover:text-light-orange-700\"\n                >\n                  <PlusIcon className=\"w-4 h-4\" />\n                  <span>Add Specification</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"space-y-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Tags\n              </label>\n              <div className=\"flex flex-wrap gap-2 mb-3\">\n                {formData.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-light-orange-100 text-light-orange-800\"\n                  >\n                    {tag}\n                    <button\n                      onClick={() => removeTag(tag)}\n                      className=\"ml-2 text-light-orange-600 hover:text-light-orange-800\"\n                    >\n                      <XMarkIcon className=\"w-3 h-3\" />\n                    </button>\n                  </span>\n                ))}\n              </div>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                  placeholder=\"Add a tag\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={addTag}\n                  className=\"px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600\"\n                >\n                  Add\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Keywords (for search)\n              </label>\n              <textarea\n                value={formData.keywords}\n                onChange={(e) => handleInputChange('keywords', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500\"\n                placeholder=\"Enter keywords separated by commas\"\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">Help customers find this product</p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isActive\"\n                  checked={formData.isActive}\n                  onChange={(e) => handleInputChange('isActive', e.target.checked)}\n                  className=\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n                />\n                <label htmlFor=\"isActive\" className=\"text-sm font-medium text-gray-700\">\n                  Publish product (make it visible to customers)\n                </label>\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"isFeatured\"\n                  checked={formData.isFeatured}\n                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}\n                  className=\"mr-3 text-light-orange-600 focus:ring-light-orange-500\"\n                />\n                <label htmlFor=\"isFeatured\" className=\"text-sm font-medium text-gray-700\">\n                  Feature this product (show in featured sections)\n                </label>\n              </div>\n            </div>\n\n            <div className=\"p-4 bg-blue-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-800 mb-2\">Product Summary</h4>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p><strong>Name:</strong> {formData.name || 'Not set'}</p>\n                <p><strong>Price:</strong> {formData.currency} {formData.price || '0.00'}</p>\n                <p><strong>Category:</strong> {categories.find(c => c.id === formData.category)?.name || 'Not set'}</p>\n                <p><strong>Type:</strong> {formData.type}</p>\n                <p><strong>Images:</strong> {formData.images.length} uploaded</p>\n                <p><strong>Status:</strong> {formData.isActive ? 'Active' : 'Draft'}</p>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Step content for step {currentStep}</div>;\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          onClick={(e) => e.stopPropagation()}\n          className=\"w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-xl overflow-hidden\"\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">Add New Product</h2>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}\n              </p>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Progress Steps */}\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                    currentStep > step.id\n                      ? 'bg-green-500 text-white'\n                      : currentStep === step.id\n                        ? 'bg-light-orange-500 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                  }`}>\n                    {currentStep > step.id ? '✓' : step.id}\n                  </div>\n                  <span className={`ml-2 text-sm font-medium ${\n                    currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'\n                  }`}>\n                    {step.name}\n                  </span>\n                  {index < steps.length - 1 && (\n                    <div className={`w-12 h-0.5 mx-4 ${\n                      currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 max-h-96 overflow-y-auto\">\n            {renderStepContent()}\n          </div>\n\n          {/* Footer */}\n          <div className=\"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50\">\n            <button\n              onClick={handlePrev}\n              disabled={currentStep === 1}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              Previous\n            </button>\n\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              \n              {currentStep < steps.length ? (\n                <button\n                  onClick={handleNext}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-light-orange-500 rounded-lg hover:bg-light-orange-600\"\n                >\n                  Next\n                </button>\n              ) : (\n                <button\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSubmitting ? 'Creating...' : 'Create Product'}\n                </button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default AddProductModal;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,SAAS,CACTC,SAAS,CACTC,QAAQ,CACRC,SAAS,CACTC,eAAe,KACV,6BAA6B,CACpC,OAASC,UAAU,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAmC,KAAAC,MAAA,IAAlC,CAAEC,MAAM,CAAEC,OAAO,CAAEC,QAAS,CAAC,CAAAJ,IAAA,CACpD,KAAM,CAACK,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACsB,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAC,CACvCwB,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,gBAAgB,CAAE,EAAE,CACpBC,KAAK,CAAE,EAAE,CACTC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,UAAU,CAChBC,UAAU,CAAE,EAAE,CACdC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBC,cAAc,CAAE,CAAC,CAAC,CAClBC,MAAM,CAAE,EACV,CAAC,CAAC,CACF,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxC,KAAM,CAAC2C,YAAY,CAAEC,eAAe,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC6C,UAAU,CAAEC,aAAa,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+C,MAAM,CAAEC,SAAS,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAExC;AACAC,SAAS,CAAC,IAAM,CACd,GAAIqB,QAAQ,CAACE,IAAI,EAAI,CAACF,QAAQ,CAACY,GAAG,CAAE,CAClC,KAAM,CAAAA,GAAG,CAAGZ,QAAQ,CAACE,IAAI,CACtByB,WAAW,CAAC,CAAC,CACbC,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CACzBC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAG,GAAG,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC1DhC,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEtB,GAAG,EAAG,CAAC,CACzC,CACF,CAAC,CAAE,CAACZ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAEnB,KAAM,CAAAkC,KAAK,CAAG,CACZ,CAAEC,EAAE,CAAE,CAAC,CAAEnC,IAAI,CAAE,YAAY,CAAEC,WAAW,CAAE,yCAA0C,CAAC,CACrF,CAAEkC,EAAE,CAAE,CAAC,CAAEnC,IAAI,CAAE,SAAS,CAAEC,WAAW,CAAE,gCAAiC,CAAC,CACzE,CAAEkC,EAAE,CAAE,CAAC,CAAEnC,IAAI,CAAE,QAAQ,CAAEC,WAAW,CAAE,0BAA2B,CAAC,CAClE,CAAEkC,EAAE,CAAE,CAAC,CAAEnC,IAAI,CAAE,SAAS,CAAEC,WAAW,CAAE,gCAAiC,CAAC,CACzE,CAAEkC,EAAE,CAAE,CAAC,CAAEnC,IAAI,CAAE,UAAU,CAAEC,WAAW,CAAE,iCAAkC,CAAC,CAC5E,CAED,KAAM,CAAAmC,YAAY,CAAIC,IAAI,EAAK,CAC7B,KAAM,CAAAC,SAAS,CAAG,CAAC,CAAC,CAEpB,OAAQD,IAAI,EACV,IAAK,EAAC,CACJ,GAAI,CAACvC,QAAQ,CAACE,IAAI,CAACuC,IAAI,CAAC,CAAC,CAAED,SAAS,CAACtC,IAAI,CAAG,0BAA0B,CACtE,GAAIF,QAAQ,CAACE,IAAI,CAACwC,MAAM,CAAG,GAAG,CAAEF,SAAS,CAACtC,IAAI,CAAG,+CAA+C,CAChG,GAAI,CAACF,QAAQ,CAACG,WAAW,CAACsC,IAAI,CAAC,CAAC,CAAED,SAAS,CAACrC,WAAW,CAAG,yBAAyB,CACnF,GAAIH,QAAQ,CAACG,WAAW,CAACuC,MAAM,CAAG,IAAI,CAAEF,SAAS,CAACrC,WAAW,CAAG,+CAA+C,CAC/G,GAAI,CAACH,QAAQ,CAACQ,QAAQ,CAAEgC,SAAS,CAAChC,QAAQ,CAAG,sBAAsB,CACnE,MACF,IAAK,EAAC,CACJ,GAAI,CAACR,QAAQ,CAACK,KAAK,CAAEmC,SAAS,CAACnC,KAAK,CAAG,mBAAmB,CAC1D,GAAIsC,KAAK,CAAC3C,QAAQ,CAACK,KAAK,CAAC,EAAIuC,UAAU,CAAC5C,QAAQ,CAACK,KAAK,CAAC,EAAI,CAAC,CAAEmC,SAAS,CAACnC,KAAK,CAAG,iCAAiC,CACjH,GAAIL,QAAQ,CAACM,aAAa,GAAKqC,KAAK,CAAC3C,QAAQ,CAACM,aAAa,CAAC,EAAIsC,UAAU,CAAC5C,QAAQ,CAACM,aAAa,CAAC,EAAI,CAAC,CAAC,CAAE,CACxGkC,SAAS,CAAClC,aAAa,CAAG,0CAA0C,CACtE,CACA,GAAIN,QAAQ,CAACM,aAAa,EAAIsC,UAAU,CAAC5C,QAAQ,CAACM,aAAa,CAAC,EAAIsC,UAAU,CAAC5C,QAAQ,CAACK,KAAK,CAAC,CAAE,CAC9FmC,SAAS,CAAClC,aAAa,CAAG,gDAAgD,CAC5E,CACA,MACF,IAAK,EAAC,CACJ,GAAIN,QAAQ,CAACkB,MAAM,CAACwB,MAAM,GAAK,CAAC,CAAEF,SAAS,CAACtB,MAAM,CAAG,wCAAwC,CAC7F,MACF,IAAK,EAAC,CACJ,GAAIlB,QAAQ,CAACU,IAAI,GAAK,UAAU,GAAK,CAACV,QAAQ,CAACW,UAAU,EAAIgC,KAAK,CAAC3C,QAAQ,CAACW,UAAU,CAAC,EAAIkC,QAAQ,CAAC7C,QAAQ,CAACW,UAAU,CAAC,CAAG,CAAC,CAAC,CAAE,CAC7H6B,SAAS,CAAC7B,UAAU,CAAG,iEAAiE,CAC1F,CACA,GAAI,CAACX,QAAQ,CAACY,GAAG,CAAC6B,IAAI,CAAC,CAAC,CAAED,SAAS,CAAC5B,GAAG,CAAG,iBAAiB,CAC3D,MACJ,CAEAQ,SAAS,CAACoB,SAAS,CAAC,CACpB,MAAO,CAAAM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACE,MAAM,GAAK,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAM,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAIV,YAAY,CAACxC,WAAW,CAAC,CAAE,CAC7BC,cAAc,CAACmC,IAAI,EAAIe,IAAI,CAACC,GAAG,CAAChB,IAAI,CAAG,CAAC,CAAEE,KAAK,CAACM,MAAM,CAAC,CAAC,CAC1D,CACF,CAAC,CAED,KAAM,CAAAS,UAAU,CAAGA,CAAA,GAAM,CACvBpD,cAAc,CAACmC,IAAI,EAAIe,IAAI,CAACG,GAAG,CAAClB,IAAI,CAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAmB,iBAAiB,CAAGA,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC1CtD,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACoB,KAAK,EAAGC,KAAK,EAAG,CAAC,CAClD,GAAIpC,MAAM,CAACmC,KAAK,CAAC,CAAE,CACjBlC,SAAS,CAACc,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACoB,KAAK,EAAG,EAAE,EAAG,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,KAAK,EAAK,CACnC,KAAM,CAAAC,SAAS,CAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAACC,IAAI,GAAK,CAC/CzB,EAAE,CAAEP,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGkB,IAAI,CAACc,MAAM,CAAC,CAAC,CAC9BD,IAAI,CACJE,GAAG,CAAEC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CAC9B5D,IAAI,CAAE4D,IAAI,CAAC5D,IAAI,CACfiE,IAAI,CAAEL,IAAI,CAACK,IACb,CAAC,CAAC,CAAC,CAEHlE,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACPhB,MAAM,CAAE,CAAC,GAAGgB,IAAI,CAAChB,MAAM,CAAE,GAAGwC,SAAS,CAAC,EACtC,CAAC,CACL,CAAC,CAED,KAAM,CAAAU,UAAU,CAAIC,CAAC,EAAK,CACxBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACnB,GAAIF,CAAC,CAAC3D,IAAI,GAAK,WAAW,EAAI2D,CAAC,CAAC3D,IAAI,GAAK,UAAU,CAAE,CACnDc,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI6C,CAAC,CAAC3D,IAAI,GAAK,WAAW,CAAE,CACjCc,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAgD,UAAU,CAAIH,CAAC,EAAK,CACxBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACnB/C,aAAa,CAAC,KAAK,CAAC,CAEpB,GAAI6C,CAAC,CAACI,YAAY,CAAChB,KAAK,EAAIY,CAAC,CAACI,YAAY,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAE,CACnDD,iBAAiB,CAACa,CAAC,CAACI,YAAY,CAAChB,KAAK,CAAC,CACzC,CACF,CAAC,CAED,KAAM,CAAAiB,WAAW,CAAIC,OAAO,EAAK,CAC/B1E,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACPhB,MAAM,CAAEgB,IAAI,CAAChB,MAAM,CAAC0D,MAAM,CAACC,GAAG,EAAIA,GAAG,CAACxC,EAAE,GAAKsC,OAAO,CAAC,EACrD,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,SAAS,CAAGA,CAACC,SAAS,CAAEC,OAAO,GAAK,CACxC,KAAM,CAAAtB,SAAS,CAAG,CAAC,GAAG1D,QAAQ,CAACkB,MAAM,CAAC,CACtC,KAAM,CAAC+D,OAAO,CAAC,CAAGvB,SAAS,CAACwB,MAAM,CAACH,SAAS,CAAE,CAAC,CAAC,CAChDrB,SAAS,CAACwB,MAAM,CAACF,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAC,CACrChF,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEhB,MAAM,CAAEwC,SAAS,EAAG,CAAC,CACvD,CAAC,CAED,KAAM,CAAAyB,MAAM,CAAGA,CAAA,GAAM,CACnB,GAAI1D,MAAM,CAACgB,IAAI,CAAC,CAAC,EAAI,CAACzC,QAAQ,CAACa,IAAI,CAACuE,QAAQ,CAAC3D,MAAM,CAACgB,IAAI,CAAC,CAAC,CAAC,CAAE,CAC3DxC,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACPrB,IAAI,CAAE,CAAC,GAAGqB,IAAI,CAACrB,IAAI,CAAEY,MAAM,CAACgB,IAAI,CAAC,CAAC,CAAC,EACnC,CAAC,CACHf,SAAS,CAAC,EAAE,CAAC,CACf,CACF,CAAC,CAED,KAAM,CAAA2D,SAAS,CAAIC,WAAW,EAAK,CACjCrF,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACPrB,IAAI,CAAEqB,IAAI,CAACrB,IAAI,CAAC+D,MAAM,CAACW,GAAG,EAAIA,GAAG,GAAKD,WAAW,CAAC,EAClD,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAClD,YAAY,CAACxC,WAAW,CAAC,CAAE,OAEhCwB,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAAzB,QAAQ,CAACG,QAAQ,CAAC,CACxBJ,OAAO,CAAC,CAAC,CACT;AACAK,WAAW,CAAC,CACVC,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,gBAAgB,CAAE,EAAE,CACpBC,KAAK,CAAE,EAAE,CACTC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,IAAI,CAAE,UAAU,CAChBC,UAAU,CAAE,EAAE,CACdC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KAAK,CACjBC,cAAc,CAAE,CAAC,CAAC,CAClBC,MAAM,CAAE,EACV,CAAC,CAAC,CACFnB,cAAc,CAAC,CAAC,CAAC,CACjBqB,SAAS,CAAC,CAAC,CAAC,CAAC,CACf,CAAE,MAAOqE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CAAC,OAAS,CACRnE,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAqE,gBAAgB,CAAGxG,UAAU,CAACyG,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACxD,EAAE,GAAKrC,QAAQ,CAACQ,QAAQ,CAAC,CAE7E,KAAM,CAAAsF,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,gBAAA,CAC9B,OAAQlG,WAAW,EACjB,IAAK,EAAC,CACJ,mBACEP,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gBAEhE,CAAO,CAAC,cACR7G,IAAA,UACEqB,IAAI,CAAC,MAAM,CACX6C,KAAK,CAAEvD,QAAQ,CAACE,IAAK,CACrBiG,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,MAAM,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC3D0C,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAACjB,IAAI,CAAG,gBAAgB,CAAG,iBAAiB,CACjD,CACHoG,WAAW,CAAC,oBAAoB,CACjC,CAAC,CACDnF,MAAM,CAACjB,IAAI,eAAIb,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACjB,IAAI,CAAI,CAAC,EACvE,CAAC,cAENX,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,mBAEhE,CAAO,CAAC,cACR7G,IAAA,UACEqB,IAAI,CAAC,MAAM,CACX6C,KAAK,CAAEvD,QAAQ,CAACI,gBAAiB,CACjC+F,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,kBAAkB,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CACvE0C,SAAS,CAAC,2HAA2H,CACrIK,WAAW,CAAC,2BAA2B,CACvCC,SAAS,CAAE,GAAI,CAChB,CAAC,cACFhH,KAAA,MAAG0G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAElG,QAAQ,CAACI,gBAAgB,CAACsC,MAAM,CAAC,iBAAe,EAAG,CAAC,EAC5F,CAAC,cAENnD,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAEhE,CAAO,CAAC,cACR7G,IAAA,aACEkE,KAAK,CAAEvD,QAAQ,CAACG,WAAY,CAC5BgG,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,aAAa,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAClEiD,IAAI,CAAE,CAAE,CACRP,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAAChB,WAAW,CAAG,gBAAgB,CAAG,iBAAiB,CACxD,CACHmG,WAAW,CAAC,8BAA8B,CAC1CC,SAAS,CAAE,IAAK,CACjB,CAAC,CACDpF,MAAM,CAAChB,WAAW,eAAId,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAAChB,WAAW,CAAI,CAAC,cACxFZ,KAAA,MAAG0G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAElG,QAAQ,CAACG,WAAW,CAACuC,MAAM,CAAC,kBAAgB,EAAG,CAAC,EACxF,CAAC,cAENnD,KAAA,QAAK0G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,YAEhE,CAAO,CAAC,cACR3G,KAAA,WACEgE,KAAK,CAAEvD,QAAQ,CAACQ,QAAS,CACzB2F,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,UAAU,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC/D0C,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAACX,QAAQ,CAAG,gBAAgB,CAAG,iBAAiB,CACrD,CAAA0F,QAAA,eAEH7G,IAAA,WAAQkE,KAAK,CAAC,EAAE,CAAA2C,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CAC1C/G,UAAU,CAAC0E,GAAG,CAACrD,QAAQ,eACtBnB,IAAA,WAA0BkE,KAAK,CAAE/C,QAAQ,CAAC6B,EAAG,CAAA6D,QAAA,CAC1C1F,QAAQ,CAACN,IAAI,EADHM,QAAQ,CAAC6B,EAEd,CACT,CAAC,EACI,CAAC,CACRlB,MAAM,CAACX,QAAQ,eAAInB,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACX,QAAQ,CAAI,CAAC,EAC/E,CAAC,cAENjB,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,aAEhE,CAAO,CAAC,cACR3G,KAAA,WACEgE,KAAK,CAAEvD,QAAQ,CAACS,WAAY,CAC5B0F,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,aAAa,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAClE0C,SAAS,CAAC,2HAA2H,CACrIQ,QAAQ,CAAE,EAACd,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEe,aAAa,CAAC,CAAAR,QAAA,eAE3C7G,IAAA,WAAQkE,KAAK,CAAC,EAAE,CAAA2C,QAAA,CAAC,sBAAoB,CAAQ,CAAC,CAC7CP,gBAAgB,SAAhBA,gBAAgB,kBAAAI,qBAAA,CAAhBJ,gBAAgB,CAAEe,aAAa,UAAAX,qBAAA,iBAA/BA,qBAAA,CAAiClC,GAAG,CAAC8C,GAAG,eACvCtH,IAAA,WAAkBkE,KAAK,CAAEoD,GAAI,CAAAT,QAAA,CAC1BS,GAAG,CAAC/E,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,CAAEgF,CAAC,EAAIA,CAAC,CAACjF,WAAW,CAAC,CAAC,CAAC,EADlDgF,GAEL,CACT,CAAC,EACI,CAAC,EACN,CAAC,EACH,CAAC,cAENpH,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,cAEhE,CAAO,CAAC,cACR3G,KAAA,QAAK0G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3G,KAAA,UAAO0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClC7G,IAAA,UACEqB,IAAI,CAAC,OAAO,CACZ6C,KAAK,CAAC,UAAU,CAChBsD,OAAO,CAAE7G,QAAQ,CAACU,IAAI,GAAK,UAAW,CACtCyF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,MAAM,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC3D0C,SAAS,CAAC,wDAAwD,CACnE,CAAC,mBAEJ,EAAO,CAAC,cACR1G,KAAA,UAAO0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClC7G,IAAA,UACEqB,IAAI,CAAC,OAAO,CACZ6C,KAAK,CAAC,SAAS,CACfsD,OAAO,CAAE7G,QAAQ,CAACU,IAAI,GAAK,SAAU,CACrCyF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,MAAM,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC3D0C,SAAS,CAAC,wDAAwD,CACnE,CAAC,kBAEJ,EAAO,CAAC,EACL,CAAC,EACH,CAAC,EACH,CAAC,CAGV,IAAK,EAAC,CACJ,mBACE1G,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAK0G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD3G,KAAA,QAAK0G,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3G,KAAA,UAAO0G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,EAAC,WACrD,CAAClG,QAAQ,CAACO,QAAQ,CAAC,GAC9B,EAAO,CAAC,cACRlB,IAAA,UACEqB,IAAI,CAAC,QAAQ,CACb6B,IAAI,CAAC,MAAM,CACXW,GAAG,CAAC,GAAG,CACPK,KAAK,CAAEvD,QAAQ,CAACK,KAAM,CACtB8F,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,OAAO,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC5D0C,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAACd,KAAK,CAAG,gBAAgB,CAAG,iBAAiB,CAClD,CACHiG,WAAW,CAAC,MAAM,CACnB,CAAC,CACDnF,MAAM,CAACd,KAAK,eAAIhB,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACd,KAAK,CAAI,CAAC,EACzE,CAAC,cAENd,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,UAEhE,CAAO,CAAC,cACR3G,KAAA,WACEgE,KAAK,CAAEvD,QAAQ,CAACO,QAAS,CACzB4F,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,UAAU,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC/D0C,SAAS,CAAC,2HAA2H,CAAAC,QAAA,eAErI7G,IAAA,WAAQkE,KAAK,CAAC,KAAK,CAAA2C,QAAA,CAAC,SAAO,CAAQ,CAAC,cACpC7G,IAAA,WAAQkE,KAAK,CAAC,KAAK,CAAA2C,QAAA,CAAC,cAAO,CAAQ,CAAC,cACpC7G,IAAA,WAAQkE,KAAK,CAAC,KAAK,CAAA2C,QAAA,CAAC,YAAO,CAAQ,CAAC,cACpC7G,IAAA,WAAQkE,KAAK,CAAC,KAAK,CAAA2C,QAAA,CAAC,UAAQ,CAAQ,CAAC,EAC/B,CAAC,EACN,CAAC,EACH,CAAC,cAEN3G,KAAA,QAAA2G,QAAA,eACE3G,KAAA,UAAO0G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,EAAC,kBAC9C,CAAClG,QAAQ,CAACO,QAAQ,CAAC,GACrC,EAAO,CAAC,cACRlB,IAAA,UACEqB,IAAI,CAAC,QAAQ,CACb6B,IAAI,CAAC,MAAM,CACXW,GAAG,CAAC,GAAG,CACPK,KAAK,CAAEvD,QAAQ,CAACM,aAAc,CAC9B6F,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,eAAe,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CACpE0C,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAACb,aAAa,CAAG,gBAAgB,CAAG,iBAAiB,CAC1D,CACHgG,WAAW,CAAC,iBAAiB,CAC9B,CAAC,CACDnF,MAAM,CAACb,aAAa,eAAIjB,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACb,aAAa,CAAI,CAAC,cAC5FjB,IAAA,MAAG4G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,4BAA0B,CAAG,CAAC,EACrE,CAAC,CAELlG,QAAQ,CAACK,KAAK,EAAIL,QAAQ,CAACM,aAAa,eACvCf,KAAA,QAAK0G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC3G,KAAA,QAAK0G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7G,IAAA,SAAM4G,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cAC5E3G,KAAA,SAAM0G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAC/ClG,QAAQ,CAACO,QAAQ,CAAC,GAAC,CAAC,CAACqC,UAAU,CAAC5C,QAAQ,CAACK,KAAK,CAAC,CAAGuC,UAAU,CAAC5C,QAAQ,CAACM,aAAa,CAAC,EAAEwG,OAAO,CAAC,CAAC,CAAC,EAC7F,CAAC,EACJ,CAAC,cACNvH,KAAA,QAAK0G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD7G,IAAA,SAAM4G,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,cAChF3G,KAAA,SAAM0G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAC/C,CAAE,CAACtD,UAAU,CAAC5C,QAAQ,CAACK,KAAK,CAAC,CAAGuC,UAAU,CAAC5C,QAAQ,CAACM,aAAa,CAAC,EAAIsC,UAAU,CAAC5C,QAAQ,CAACK,KAAK,CAAC,CAAI,GAAG,EAAEyG,OAAO,CAAC,CAAC,CAAC,CAAC,GACvH,EAAM,CAAC,EACJ,CAAC,EACH,CACN,EACE,CAAC,CAGV,IAAK,EAAC,CACJ,mBACEvH,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kBAEhE,CAAO,CAAC,cACR3G,KAAA,QACE0G,SAAS,wEAAAI,MAAA,CACP9E,UAAU,CACN,4CAA4C,CAC5CJ,MAAM,CAACD,MAAM,CACX,0BAA0B,CAC1B,+CAA+C,CACpD,CACH6F,WAAW,CAAE3C,UAAW,CACxB4C,WAAW,CAAE5C,UAAW,CACxB6C,UAAU,CAAE7C,UAAW,CACvB8C,MAAM,CAAE1C,UAAW,CAAA0B,QAAA,eAEnB7G,IAAA,CAACH,eAAe,EAAC+G,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC/D1G,KAAA,QAAK0G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3G,KAAA,UAAO4H,OAAO,CAAC,aAAa,CAAClB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eACrD7G,IAAA,SAAM4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,qCAE/D,CAAM,CAAC,cACP7G,IAAA,SAAM4G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,+BAEnD,CAAM,CAAC,EACF,CAAC,cACR7G,IAAA,UACEgD,EAAE,CAAC,aAAa,CAChBnC,IAAI,CAAC,aAAa,CAClBQ,IAAI,CAAC,MAAM,CACXuF,SAAS,CAAC,SAAS,CACnBmB,QAAQ,MACRC,MAAM,CAAC,SAAS,CAChBlB,QAAQ,CAAG9B,CAAC,EAAKb,iBAAiB,CAACa,CAAC,CAAC+B,MAAM,CAAC3C,KAAK,CAAE,CACpD,CAAC,EACC,CAAC,EACH,CAAC,CACLtC,MAAM,CAACD,MAAM,eAAI7B,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACD,MAAM,CAAI,CAAC,EAC3E,CAAC,CAELlB,QAAQ,CAACkB,MAAM,CAACwB,MAAM,CAAG,CAAC,eACzBnD,KAAA,QAAA2G,QAAA,eACE3G,KAAA,OAAI0G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,mBACpC,CAAClG,QAAQ,CAACkB,MAAM,CAACwB,MAAM,CAAC,GAC3C,EAAI,CAAC,cACLrD,IAAA,QAAK4G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDlG,QAAQ,CAACkB,MAAM,CAAC2C,GAAG,CAAC,CAACyD,KAAK,CAAEC,KAAK,gBAChChI,KAAA,QAAoB0G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC5C7G,IAAA,QACEmI,GAAG,CAAEF,KAAK,CAACtD,GAAI,CACfyD,GAAG,CAAEH,KAAK,CAACpH,IAAK,CAChB+F,SAAS,CAAC,4DAA4D,CACvE,CAAC,cACF5G,IAAA,QAAK4G,SAAS,CAAC,0IAA0I,CAAAC,QAAA,cACvJ7G,IAAA,WACEqI,OAAO,CAAEA,CAAA,GAAMhD,WAAW,CAAC4C,KAAK,CAACjF,EAAE,CAAE,CACrC4D,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cAEnE7G,IAAA,CAACJ,SAAS,EAACgH,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,CACN,CAAC,CACLsB,KAAK,GAAK,CAAC,eACVlI,IAAA,QAAK4G,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CAAC,MAEzF,CAAK,CACN,GAlBOoB,KAAK,CAACjF,EAmBX,CACN,CAAC,CACC,CAAC,cACNhD,IAAA,MAAG4G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,0EAE1C,CAAG,CAAC,EACD,CACN,EACE,CAAC,CAGV,IAAK,EAAC,CACJ,mBACE3G,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAK0G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,4BAEhE,CAAO,CAAC,cACR7G,IAAA,UACEqB,IAAI,CAAC,MAAM,CACX6C,KAAK,CAAEvD,QAAQ,CAACY,GAAI,CACpBuF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,KAAK,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAC5B,WAAW,CAAC,CAAC,CAAE,CACxEsE,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAACP,GAAG,CAAG,gBAAgB,CAAG,iBAAiB,CAChD,CACH0F,WAAW,CAAC,gBAAgB,CAC7B,CAAC,CACDnF,MAAM,CAACP,GAAG,eAAIvB,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACP,GAAG,CAAI,CAAC,cACxEvB,IAAA,MAAG4G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,oCAAkC,CAAG,CAAC,EAC7E,CAAC,CAELlG,QAAQ,CAACU,IAAI,GAAK,UAAU,eAC3BnB,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kBAEhE,CAAO,CAAC,cACR7G,IAAA,UACEqB,IAAI,CAAC,QAAQ,CACbwC,GAAG,CAAC,GAAG,CACPK,KAAK,CAAEvD,QAAQ,CAACW,UAAW,CAC3BwF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,YAAY,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CACjE0C,SAAS,8GAAAI,MAAA,CACPlF,MAAM,CAACR,UAAU,CAAG,gBAAgB,CAAG,iBAAiB,CACvD,CACH2F,WAAW,CAAC,GAAG,CAChB,CAAC,CACDnF,MAAM,CAACR,UAAU,eAAItB,IAAA,MAAG4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE/E,MAAM,CAACR,UAAU,CAAI,CAAC,EACnF,CACN,EACE,CAAC,cAENpB,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,wBAEhE,CAAO,CAAC,cACR3G,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAK0G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD7G,IAAA,UACEqB,IAAI,CAAC,MAAM,CACX4F,WAAW,CAAC,mCAAmC,CAC/CL,SAAS,CAAC,oHAAoH,CAC/H,CAAC,cACF5G,IAAA,UACEqB,IAAI,CAAC,MAAM,CACX4F,WAAW,CAAC,sBAAsB,CAClCL,SAAS,CAAC,oHAAoH,CAC/H,CAAC,EACC,CAAC,cACN1G,KAAA,WACEmB,IAAI,CAAC,QAAQ,CACbuF,SAAS,CAAC,uFAAuF,CAAAC,QAAA,eAEjG7G,IAAA,CAACL,QAAQ,EAACiH,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC5G,IAAA,SAAA6G,QAAA,CAAM,mBAAiB,CAAM,CAAC,EACxB,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAGV,IAAK,EAAC,CACJ,mBACE3G,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,cAEhE,CAAO,CAAC,cACR7G,IAAA,QAAK4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACvClG,QAAQ,CAACa,IAAI,CAACgD,GAAG,CAAC,CAAC0B,GAAG,CAAEgC,KAAK,gBAC5BhI,KAAA,SAEE0G,SAAS,CAAC,mGAAmG,CAAAC,QAAA,EAE5GX,GAAG,cACJlG,IAAA,WACEqI,OAAO,CAAEA,CAAA,GAAMrC,SAAS,CAACE,GAAG,CAAE,CAC9BU,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE7G,IAAA,CAACP,SAAS,EAACmH,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,GATJsB,KAUD,CACP,CAAC,CACC,CAAC,cACNhI,KAAA,QAAK0G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7G,IAAA,UACEqB,IAAI,CAAC,MAAM,CACX6C,KAAK,CAAE9B,MAAO,CACd0E,QAAQ,CAAG9B,CAAC,EAAK3C,SAAS,CAAC2C,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC3CoE,UAAU,CAAGtD,CAAC,EAAKA,CAAC,CAACuD,GAAG,GAAK,OAAO,GAAKvD,CAAC,CAACC,cAAc,CAAC,CAAC,CAAEa,MAAM,CAAC,CAAC,CAAE,CACvEc,SAAS,CAAC,2HAA2H,CACrIK,WAAW,CAAC,WAAW,CACxB,CAAC,cACFjH,IAAA,WACEqB,IAAI,CAAC,QAAQ,CACbgH,OAAO,CAAEvC,MAAO,CAChBc,SAAS,CAAC,+EAA+E,CAAAC,QAAA,CAC1F,KAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,UAAO4G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,uBAEhE,CAAO,CAAC,cACR7G,IAAA,aACEkE,KAAK,CAAEvD,QAAQ,CAACc,QAAS,CACzBqF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,UAAU,CAAEgB,CAAC,CAAC+B,MAAM,CAAC7C,KAAK,CAAE,CAC/DiD,IAAI,CAAE,CAAE,CACRP,SAAS,CAAC,2HAA2H,CACrIK,WAAW,CAAC,oCAAoC,CACjD,CAAC,cACFjH,IAAA,MAAG4G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kCAAgC,CAAG,CAAC,EAC3E,CAAC,cAEN3G,KAAA,QAAK0G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3G,KAAA,QAAK0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7G,IAAA,UACEqB,IAAI,CAAC,UAAU,CACf2B,EAAE,CAAC,UAAU,CACbwE,OAAO,CAAE7G,QAAQ,CAACe,QAAS,CAC3BoF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,UAAU,CAAEgB,CAAC,CAAC+B,MAAM,CAACS,OAAO,CAAE,CACjEZ,SAAS,CAAC,wDAAwD,CACnE,CAAC,cACF5G,IAAA,UAAO8H,OAAO,CAAC,UAAU,CAAClB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,gDAExE,CAAO,CAAC,EACL,CAAC,cAEN3G,KAAA,QAAK0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7G,IAAA,UACEqB,IAAI,CAAC,UAAU,CACf2B,EAAE,CAAC,YAAY,CACfwE,OAAO,CAAE7G,QAAQ,CAACgB,UAAW,CAC7BmF,QAAQ,CAAG9B,CAAC,EAAKhB,iBAAiB,CAAC,YAAY,CAAEgB,CAAC,CAAC+B,MAAM,CAACS,OAAO,CAAE,CACnEZ,SAAS,CAAC,wDAAwD,CACnE,CAAC,cACF5G,IAAA,UAAO8H,OAAO,CAAC,YAAY,CAAClB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,kDAE1E,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAEN3G,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC7G,IAAA,OAAI4G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC3E3G,KAAA,QAAK0G,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C3G,KAAA,MAAA2G,QAAA,eAAG7G,IAAA,WAAA6G,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAAClG,QAAQ,CAACE,IAAI,EAAI,SAAS,EAAI,CAAC,cAC1DX,KAAA,MAAA2G,QAAA,eAAG7G,IAAA,WAAA6G,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAAClG,QAAQ,CAACO,QAAQ,CAAC,GAAC,CAACP,QAAQ,CAACK,KAAK,EAAI,MAAM,EAAI,CAAC,cAC7Ed,KAAA,MAAA2G,QAAA,eAAG7G,IAAA,WAAA6G,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAC,EAAAF,gBAAA,CAAA7G,UAAU,CAACyG,IAAI,CAACiC,CAAC,EAAIA,CAAC,CAACxF,EAAE,GAAKrC,QAAQ,CAACQ,QAAQ,CAAC,UAAAwF,gBAAA,iBAAhDA,gBAAA,CAAkD9F,IAAI,GAAI,SAAS,EAAI,CAAC,cACvGX,KAAA,MAAA2G,QAAA,eAAG7G,IAAA,WAAA6G,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAAClG,QAAQ,CAACU,IAAI,EAAI,CAAC,cAC7CnB,KAAA,MAAA2G,QAAA,eAAG7G,IAAA,WAAA6G,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAClG,QAAQ,CAACkB,MAAM,CAACwB,MAAM,CAAC,WAAS,EAAG,CAAC,cACjEnD,KAAA,MAAA2G,QAAA,eAAG7G,IAAA,WAAA6G,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAClG,QAAQ,CAACe,QAAQ,CAAG,QAAQ,CAAG,OAAO,EAAI,CAAC,EACrE,CAAC,EACH,CAAC,EACH,CAAC,CAGV,QACE,mBAAOxB,KAAA,QAAA2G,QAAA,EAAK,wBAAsB,CAACpG,WAAW,EAAM,CAAC,CACzD,CACF,CAAC,CAED,GAAI,CAACH,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEN,IAAA,CAACR,eAAe,EAAAqH,QAAA,cACd7G,IAAA,CAACT,MAAM,CAACkJ,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACrB/B,SAAS,CAAC,gFAAgF,CAC1FyB,OAAO,CAAE9H,OAAQ,CAAAsG,QAAA,cAEjB3G,KAAA,CAACX,MAAM,CAACkJ,GAAG,EACTC,OAAO,CAAE,CAAEI,KAAK,CAAE,GAAG,CAAEH,OAAO,CAAE,CAAE,CAAE,CACpCC,OAAO,CAAE,CAAEE,KAAK,CAAE,CAAC,CAAEH,OAAO,CAAE,CAAE,CAAE,CAClCE,IAAI,CAAE,CAAEC,KAAK,CAAE,GAAG,CAAEH,OAAO,CAAE,CAAE,CAAE,CACjCN,OAAO,CAAGrD,CAAC,EAAKA,CAAC,CAACE,eAAe,CAAC,CAAE,CACpC0B,SAAS,CAAC,6EAA6E,CAAAC,QAAA,eAGvF3G,KAAA,QAAK0G,SAAS,CAAC,gEAAgE,CAAAC,QAAA,eAC7E3G,KAAA,QAAA2G,QAAA,eACE7G,IAAA,OAAI4G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrE3G,KAAA,MAAG0G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,OACnC,CAACpG,WAAW,CAAC,MAAI,CAACsC,KAAK,CAACM,MAAM,CAAC,IAAE,EAAAhD,MAAA,CAAC0C,KAAK,CAACtC,WAAW,CAAG,CAAC,CAAC,UAAAJ,MAAA,iBAAtBA,MAAA,CAAwBS,WAAW,EACzE,CAAC,EACD,CAAC,cACNd,IAAA,WACEqI,OAAO,CAAE9H,OAAQ,CACjBqG,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cAE9E7G,IAAA,CAACP,SAAS,EAACmH,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,cAGN5G,IAAA,QAAK4G,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD7G,IAAA,QAAK4G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C9D,KAAK,CAACyB,GAAG,CAAC,CAACtB,IAAI,CAAEgF,KAAK,gBACrBhI,KAAA,QAAmB0G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9C7G,IAAA,QAAK4G,SAAS,8EAAAI,MAAA,CACZvG,WAAW,CAAGyC,IAAI,CAACF,EAAE,CACjB,yBAAyB,CACzBvC,WAAW,GAAKyC,IAAI,CAACF,EAAE,CACrB,gCAAgC,CAChC,2BAA2B,CAChC,CAAA6D,QAAA,CACApG,WAAW,CAAGyC,IAAI,CAACF,EAAE,CAAG,GAAG,CAAGE,IAAI,CAACF,EAAE,CACnC,CAAC,cACNhD,IAAA,SAAM4G,SAAS,6BAAAI,MAAA,CACbvG,WAAW,EAAIyC,IAAI,CAACF,EAAE,CAAG,eAAe,CAAG,eAAe,CACzD,CAAA6D,QAAA,CACA3D,IAAI,CAACrC,IAAI,CACN,CAAC,CACNqH,KAAK,CAAGnF,KAAK,CAACM,MAAM,CAAG,CAAC,eACvBrD,IAAA,QAAK4G,SAAS,oBAAAI,MAAA,CACZvG,WAAW,CAAGyC,IAAI,CAACF,EAAE,CAAG,cAAc,CAAG,aAAa,CACrD,CAAE,CACN,GAnBOE,IAAI,CAACF,EAoBV,CACN,CAAC,CACC,CAAC,CACH,CAAC,cAGNhD,IAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAC1CJ,iBAAiB,CAAC,CAAC,CACjB,CAAC,cAGNvG,KAAA,QAAK0G,SAAS,CAAC,2EAA2E,CAAAC,QAAA,eACxF7G,IAAA,WACEqI,OAAO,CAAEvE,UAAW,CACpBsD,QAAQ,CAAE3G,WAAW,GAAK,CAAE,CAC5BmG,SAAS,CAAC,yJAAyJ,CAAAC,QAAA,CACpK,UAED,CAAQ,CAAC,cAET3G,KAAA,QAAK0G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7G,IAAA,WACEqI,OAAO,CAAE9H,OAAQ,CACjBqG,SAAS,CAAC,yGAAyG,CAAAC,QAAA,CACpH,QAED,CAAQ,CAAC,CAERpG,WAAW,CAAGsC,KAAK,CAACM,MAAM,cACzBrD,IAAA,WACEqI,OAAO,CAAE1E,UAAW,CACpBiD,SAAS,CAAC,mGAAmG,CAAAC,QAAA,CAC9G,MAED,CAAQ,CAAC,cAET7G,IAAA,WACEqI,OAAO,CAAElC,YAAa,CACtBiB,QAAQ,CAAEpF,YAAa,CACvB4E,SAAS,CAAC,qIAAqI,CAAAC,QAAA,CAE9I7E,YAAY,CAAG,aAAa,CAAG,gBAAgB,CAC1C,CACT,EACE,CAAC,EACH,CAAC,EACI,CAAC,CACH,CAAC,CACE,CAAC,CAEtB,CAAC,CAED,cAAe,CAAA7B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}