{"ast": null, "code": "import { getScrollParent as $62d8ded9296f3872$export$cfa2225e87938781 } from \"./getScrollParent.mjs\";\nimport { useEffectEvent as $8ae05eaa5c114e9c$export$7f54fc3180508a52 } from \"./useEffectEvent.mjs\";\nimport { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useRef as $7FoZl$useRef } from \"react\";\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $a5fa973c1850dd36$export$90a12e6abf95cbe0(props, ref) {\n  let {\n    collection: collection,\n    onLoadMore: onLoadMore,\n    scrollOffset = 1\n  } = props;\n  let sentinelObserver = (0, $7FoZl$useRef)(null);\n  let triggerLoadMore = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(entries => {\n    // Use \"isIntersecting\" over an equality check of 0 since it seems like there is cases where\n    // a intersection ratio of 0 can be reported when isIntersecting is actually true\n    for (let entry of entries)\n    // Note that this will be called if the collection changes, even if onLoadMore was already called and is being processed.\n    // Up to user discretion as to how to handle these multiple onLoadMore calls\n    if (entry.isIntersecting && onLoadMore) onLoadMore();\n  });\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    if (ref.current) {\n      // Tear down and set up a new IntersectionObserver when the collection changes so that we can properly trigger additional loadMores if there is room for more items\n      // Need to do this tear down and set up since using a large rootMargin will mean the observer's callback isn't called even when scrolling the item into view beause its visibility hasn't actually changed\n      // https://codesandbox.io/p/sandbox/magical-swanson-dhgp89?file=%2Fsrc%2FApp.js%3A21%2C21\n      sentinelObserver.current = new IntersectionObserver(triggerLoadMore, {\n        root: (0, $62d8ded9296f3872$export$cfa2225e87938781)(ref === null || ref === void 0 ? void 0 : ref.current),\n        rootMargin: `0px ${100 * scrollOffset}% ${100 * scrollOffset}% ${100 * scrollOffset}%`\n      });\n      sentinelObserver.current.observe(ref.current);\n    }\n    return () => {\n      if (sentinelObserver.current) sentinelObserver.current.disconnect();\n    };\n  }, [collection, triggerLoadMore, ref, scrollOffset]);\n}\nexport { $a5fa973c1850dd36$export$90a12e6abf95cbe0 as UNSTABLE_useLoadMoreSentinel };", "map": {"version": 3, "names": ["$a5fa973c1850dd36$export$90a12e6abf95cbe0", "props", "ref", "collection", "onLoadMore", "scrollOffset", "sentinelObserver", "$7FoZl$useRef", "triggerLoadMore", "$8ae05eaa5c114e9c$export$7f54fc3180508a52", "entries", "entry", "isIntersecting", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "current", "IntersectionObserver", "root", "$62d8ded9296f3872$export$cfa2225e87938781", "rootMargin", "observe", "disconnect"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useLoadMoreSentinel.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {AsyncLoadable, Collection} from '@react-types/shared';\nimport {getScrollParent} from './getScrollParent';\nimport {RefObject, useRef} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport interface LoadMoreSentinelProps extends Omit<AsyncLoadable, 'isLoading'> {\n  collection: Collection<any>,\n  /**\n   * The amount of offset from the bottom of your scrollable region that should trigger load more.\n   * Uses a percentage value relative to the scroll body's client height. Load more is then triggered\n   * when your current scroll position's distance from the bottom of the currently loaded list of items is less than\n   * or equal to the provided value. (e.g. 1 = 100% of the scroll region's height).\n   * @default 1\n   */\n  scrollOffset?: number\n}\n\nexport function UNSTABLE_useLoadMoreSentinel(props: LoadMoreSentinelProps, ref: RefObject<HTMLElement | null>): void {\n  let {collection, onLoadMore, scrollOffset = 1} = props;\n\n  let sentinelObserver = useRef<IntersectionObserver>(null);\n\n  let triggerLoadMore = useEffectEvent((entries: IntersectionObserverEntry[]) => {\n    // Use \"isIntersecting\" over an equality check of 0 since it seems like there is cases where\n    // a intersection ratio of 0 can be reported when isIntersecting is actually true\n    for (let entry of entries) {\n      // Note that this will be called if the collection changes, even if onLoadMore was already called and is being processed.\n      // Up to user discretion as to how to handle these multiple onLoadMore calls\n      if (entry.isIntersecting && onLoadMore) {\n        onLoadMore();\n      }\n    }\n  });\n\n  useLayoutEffect(() => {\n    if (ref.current) {\n      // Tear down and set up a new IntersectionObserver when the collection changes so that we can properly trigger additional loadMores if there is room for more items\n      // Need to do this tear down and set up since using a large rootMargin will mean the observer's callback isn't called even when scrolling the item into view beause its visibility hasn't actually changed\n      // https://codesandbox.io/p/sandbox/magical-swanson-dhgp89?file=%2Fsrc%2FApp.js%3A21%2C21\n      sentinelObserver.current = new IntersectionObserver(triggerLoadMore, {root: getScrollParent(ref?.current) as HTMLElement, rootMargin: `0px ${100 * scrollOffset}% ${100 * scrollOffset}% ${100 * scrollOffset}%`});\n      sentinelObserver.current.observe(ref.current);\n    }\n\n    return () => {\n      if (sentinelObserver.current) {\n        sentinelObserver.current.disconnect();\n      }\n    };\n  }, [collection, triggerLoadMore, ref, scrollOffset]);\n}\n"], "mappings": ";;;;;AAAA;;;;;;;;;;;;AA8BO,SAASA,0CAA6BC,KAA4B,EAAEC,GAAkC;EAC3G,IAAI;IAAAC,UAAA,EAACA,UAAU;IAAAC,UAAA,EAAEA,UAAU;IAAEC,YAAA,GAAe;EAAA,CAAE,GAAGJ,KAAA;EAEjD,IAAIK,gBAAA,GAAmB,IAAAC,aAAK,EAAwB;EAEpD,IAAIC,eAAA,GAAkB,IAAAC,yCAAa,EAAGC,OAAA;IACpC;IACA;IACA,KAAK,IAAIC,KAAA,IAASD,OAAA;IAChB;IACA;IACA,IAAIC,KAAA,CAAMC,cAAc,IAAIR,UAAA,EAC1BA,UAAA;EAGN;EAEA,IAAAS,yCAAc,EAAE;IACd,IAAIX,GAAA,CAAIY,OAAO,EAAE;MACf;MACA;MACA;MACAR,gBAAA,CAAiBQ,OAAO,GAAG,IAAIC,oBAAA,CAAqBP,eAAA,EAAiB;QAACQ,IAAA,EAAM,IAAAC,yCAAc,EAAEf,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAKY,OAAO;QAAkBI,UAAA,EAAY,OAAO,MAAMb,YAAA,KAAiB,MAAMA,YAAA,KAAiB,MAAMA,YAAA;MAAe;MAChNC,gBAAA,CAAiBQ,OAAO,CAACK,OAAO,CAACjB,GAAA,CAAIY,OAAO;IAC9C;IAEA,OAAO;MACL,IAAIR,gBAAA,CAAiBQ,OAAO,EAC1BR,gBAAA,CAAiBQ,OAAO,CAACM,UAAU;IAEvC;EACF,GAAG,CAACjB,UAAA,EAAYK,eAAA,EAAiBN,GAAA,EAAKG,YAAA,CAAa;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}