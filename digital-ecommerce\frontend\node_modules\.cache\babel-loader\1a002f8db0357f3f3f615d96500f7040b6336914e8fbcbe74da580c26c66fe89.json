{"ast": null, "code": "function memo(getDeps, fn, opts) {\n  var _opts$initialDeps;\n  let deps = (_opts$initialDeps = opts.initialDeps) !== null && _opts$initialDeps !== void 0 ? _opts$initialDeps : [];\n  let result;\n  function memoizedFunction() {\n    var _a, _b, _c, _d;\n    let depTime;\n    if (opts.key && ((_a = opts.debug) == null ? void 0 : _a.call(opts))) depTime = Date.now();\n    const newDeps = getDeps();\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && ((_b = opts.debug) == null ? void 0 : _b.call(opts))) resultTime = Date.now();\n    result = fn(...newDeps);\n    if (opts.key && ((_c = opts.debug) == null ? void 0 : _c.call(opts))) {\n      const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n      const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n      const resultFpsPercentage = resultEndTime / 16;\n      const pad = (str, num) => {\n        str = String(str);\n        while (str.length < num) {\n          str = \" \" + str;\n        }\n        return str;\n      };\n      console.info(\"%c\\u23F1 \".concat(pad(resultEndTime, 5), \" /\").concat(pad(depEndTime, 5), \" ms\"), \"\\n            font-size: .6rem;\\n            font-weight: bold;\\n            color: hsl(\".concat(Math.max(0, Math.min(120 - 120 * resultFpsPercentage, 120)), \"deg 100% 31%);\"), opts == null ? void 0 : opts.key);\n    }\n    (_d = opts == null ? void 0 : opts.onChange) == null ? void 0 : _d.call(opts, result);\n    return result;\n  }\n  memoizedFunction.updateDeps = newDeps => {\n    deps = newDeps;\n  };\n  return memoizedFunction;\n}\nfunction notUndefined(value, msg) {\n  if (value === void 0) {\n    throw new Error(\"Unexpected undefined\".concat(msg ? \": \".concat(msg) : \"\"));\n  } else {\n    return value;\n  }\n}\nconst approxEqual = (a, b) => Math.abs(a - b) <= 1;\nconst debounce = (targetWindow, fn, ms) => {\n  let timeoutId;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    targetWindow.clearTimeout(timeoutId);\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms);\n  };\n};\nexport { approxEqual, debounce, memo, notUndefined };", "map": {"version": 3, "names": ["memo", "getDeps", "fn", "opts", "_opts$initialDeps", "deps", "initialDeps", "result", "memoizedFunction", "_a", "_b", "_c", "_d", "depTime", "key", "debug", "call", "Date", "now", "newDeps", "depsChanged", "length", "some", "dep", "index", "resultTime", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "concat", "max", "min", "onChange", "updateDeps", "notUndefined", "value", "msg", "Error", "approxEqual", "a", "b", "abs", "debounce", "targetWindow", "ms", "timeoutId", "_len", "arguments", "args", "Array", "_key", "clearTimeout", "setTimeout", "apply"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@tanstack\\virtual-core\\src\\utils.ts"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) <= 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n"], "mappings": "AAIgB,SAAAA,KACdC,OAAA,EACAC,EAAA,EACAC,IAAA,EAMA;EAAA,IAAAC,iBAAA;EACI,IAAAC,IAAA,IAAAD,iBAAA,GAAOD,IAAA,CAAKG,WAAA,cAAAF,iBAAA,cAAAA,iBAAA,GAAe,EAAC;EAC5B,IAAAG,MAAA;EAEJ,SAASC,iBAAA,EAA4B;IAbvB,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;IAcR,IAAAC,OAAA;IACJ,IAAIV,IAAA,CAAKW,GAAA,MAAOL,EAAA,GAAAN,IAAA,CAAKY,KAAA,KAAL,gBAAAN,EAAA,CAAAO,IAAA,CAAAb,IAAA,IAAgBU,OAAA,GAAUI,IAAA,CAAKC,GAAA,CAAI;IAEnD,MAAMC,OAAA,GAAUlB,OAAA,CAAQ;IAExB,MAAMmB,WAAA,GACJD,OAAA,CAAQE,MAAA,KAAWhB,IAAA,CAAKgB,MAAA,IACxBF,OAAA,CAAQG,IAAA,CAAK,CAACC,GAAA,EAAUC,KAAA,KAAkBnB,IAAA,CAAKmB,KAAK,MAAMD,GAAG;IAE/D,IAAI,CAACH,WAAA,EAAa;MACT,OAAAb,MAAA;IAAA;IAGFF,IAAA,GAAAc,OAAA;IAEH,IAAAM,UAAA;IACJ,IAAItB,IAAA,CAAKW,GAAA,MAAOJ,EAAA,GAAAP,IAAA,CAAKY,KAAA,KAAL,gBAAAL,EAAA,CAAAM,IAAA,CAAAb,IAAA,IAAgBsB,UAAA,GAAaR,IAAA,CAAKC,GAAA,CAAI;IAE7CX,MAAA,GAAAL,EAAA,CAAG,GAAGiB,OAAO;IAEtB,IAAIhB,IAAA,CAAKW,GAAA,MAAOH,EAAA,GAAAR,IAAA,CAAKY,KAAA,KAAL,gBAAAJ,EAAA,CAAAK,IAAA,CAAAb,IAAA,IAAgB;MACxB,MAAAuB,UAAA,GAAaC,IAAA,CAAKC,KAAA,EAAOX,IAAA,CAAKC,GAAA,KAAQL,OAAA,IAAY,GAAG,IAAI;MACzD,MAAAgB,aAAA,GAAgBF,IAAA,CAAKC,KAAA,EAAOX,IAAA,CAAKC,GAAA,KAAQO,UAAA,IAAe,GAAG,IAAI;MACrE,MAAMK,mBAAA,GAAsBD,aAAA,GAAgB;MAEtC,MAAAE,GAAA,GAAMA,CAACC,GAAA,EAAsBC,GAAA,KAAgB;QACjDD,GAAA,GAAME,MAAA,CAAOF,GAAG;QACT,OAAAA,GAAA,CAAIX,MAAA,GAASY,GAAA,EAAK;UACvBD,GAAA,GAAM,MAAMA,GAAA;QAAA;QAEP,OAAAA,GAAA;MACT;MAEQG,OAAA,CAAAC,IAAA,aAAAC,MAAA,CACCN,GAAA,CAAIF,aAAA,EAAe,CAAC,CAAC,QAAAQ,MAAA,CAAKN,GAAA,CAAIL,UAAA,EAAY,CAAC,CAAC,qGAAAW,MAAA,CAIlCV,IAAA,CAAKW,GAAA,CAChB,GACAX,IAAA,CAAKY,GAAA,CAAI,MAAM,MAAMT,mBAAA,EAAqB,GAAG,CAC9C,sBACL3B,IAAA,oBAAAA,IAAA,CAAMW,GACR;IAAA;IAGF,CAAAF,EAAA,GAAAT,IAAA,oBAAAA,IAAA,CAAMqC,QAAA,KAAN,gBAAA5B,EAAA,CAAAI,IAAA,CAAAb,IAAA,EAAiBI,MAAA;IAEV,OAAAA,MAAA;EAAA;EAIQC,gBAAA,CAAAiC,UAAA,GAActB,OAAA,IAAwB;IAC9Cd,IAAA,GAAAc,OAAA;EACT;EAEO,OAAAX,gBAAA;AACT;AAEgB,SAAAkC,aAAgBC,KAAA,EAAsBC,GAAA,EAAiB;EACrE,IAAID,KAAA,KAAU,QAAW;IACjB,UAAIE,KAAA,wBAAAR,MAAA,CAA6BO,GAAA,QAAAP,MAAA,CAAWO,GAAG,IAAK,EAAE,CAAE;EAAA,OACzD;IACE,OAAAD,KAAA;EAAA;AAEX;AAEa,MAAAG,WAAA,GAAcA,CAACC,CAAA,EAAWC,CAAA,KAAcrB,IAAA,CAAKsB,GAAA,CAAIF,CAAA,GAAIC,CAAC,KAAK;AAEjE,MAAME,QAAA,GAAWA,CACtBC,YAAA,EACAjD,EAAA,EACAkD,EAAA,KACG;EACC,IAAAC,SAAA;EACJ,OAAO,YAA0C;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAlC,MAAA,EAAlBmC,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;IAAA;IAC7BP,YAAA,CAAaQ,YAAA,CAAaN,SAAS;IACvBA,SAAA,GAAAF,YAAA,CAAaS,UAAA,CAAW,MAAM1D,EAAA,CAAG2D,KAAA,CAAM,MAAML,IAAI,GAAGJ,EAAE;EACpE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}