{"ast": null, "code": "import { createContext as n, useContext as r, useMemo as i } from \"react\";\nimport { useOnUnmount as s } from '../../hooks/use-on-unmount.js';\nimport { ListboxMachine as a } from './listbox-machine.js';\nconst c = n(null);\nfunction p(o) {\n  let e = r(c);\n  if (e === null) {\n    let t = new Error(\"<\".concat(o, \" /> is missing a parent <Listbox /> component.\"));\n    throw Error.captureStackTrace && Error.captureStackTrace(t, u), t;\n  }\n  return e;\n}\nfunction u(_ref) {\n  let {\n    id: o,\n    __demoMode: e = !1\n  } = _ref;\n  let t = i(() => a.new({\n    id: o,\n    __demoMode: e\n  }), []);\n  return s(() => t.dispose()), t;\n}\nexport { c as ListboxContext, u as useListboxMachine, p as useListboxMachineContext };", "map": {"version": 3, "names": ["createContext", "n", "useContext", "r", "useMemo", "i", "useOnUnmount", "s", "ListboxMachine", "a", "c", "p", "o", "e", "t", "Error", "concat", "captureStackTrace", "u", "_ref", "id", "__demoMode", "new", "dispose", "ListboxContext", "useListboxMachine", "useListboxMachineContext"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/listbox/listbox-machine-glue.js"], "sourcesContent": ["import{createContext as n,useContext as r,useMemo as i}from\"react\";import{useOnUnmount as s}from'../../hooks/use-on-unmount.js';import{ListboxMachine as a}from'./listbox-machine.js';const c=n(null);function p(o){let e=r(c);if(e===null){let t=new Error(`<${o} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}return e}function u({id:o,__demoMode:e=!1}){let t=i(()=>a.new({id:o,__demoMode:e}),[]);return s(()=>t.dispose()),t}export{c as ListboxContext,u as useListboxMachine,p as useListboxMachineContext};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAACT,CAAC,CAAC,IAAI,CAAC;AAAC,SAASU,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAACO,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,KAAAC,MAAA,CAAKJ,CAAC,mDAAgD,CAAC;IAAC,MAAMG,KAAK,CAACE,iBAAiB,IAAEF,KAAK,CAACE,iBAAiB,CAACH,CAAC,EAACI,CAAC,CAAC,EAACJ,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASK,CAACA,CAAAC,IAAA,EAAwB;EAAA,IAAvB;IAACC,EAAE,EAACR,CAAC;IAACS,UAAU,EAACR,CAAC,GAAC,CAAC;EAAC,CAAC,GAAAM,IAAA;EAAE,IAAIL,CAAC,GAACT,CAAC,CAAC,MAAII,CAAC,CAACa,GAAG,CAAC;IAACF,EAAE,EAACR,CAAC;IAACS,UAAU,EAACR;EAAC,CAAC,CAAC,EAAC,EAAE,CAAC;EAAC,OAAON,CAAC,CAAC,MAAIO,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,EAACT,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIc,cAAc,EAACN,CAAC,IAAIO,iBAAiB,EAACd,CAAC,IAAIe,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}