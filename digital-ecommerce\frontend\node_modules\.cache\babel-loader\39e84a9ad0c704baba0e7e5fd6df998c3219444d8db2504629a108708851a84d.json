{"ast": null, "code": "import { getOwnerWindow as $djhjW$getOwnerWindow } from \"@react-aria/utils\";\n\n/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $645f2e67b85a24c9$var$isStyleVisible(element) {\n  const windowObject = (0, $djhjW$getOwnerWindow)(element);\n  if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) return false;\n  let {\n    display: display,\n    visibility: visibility\n  } = element.style;\n  let isVisible = display !== 'none' && visibility !== 'hidden' && visibility !== 'collapse';\n  if (isVisible) {\n    const {\n      getComputedStyle: getComputedStyle\n    } = element.ownerDocument.defaultView;\n    let {\n      display: computedDisplay,\n      visibility: computedVisibility\n    } = getComputedStyle(element);\n    isVisible = computedDisplay !== 'none' && computedVisibility !== 'hidden' && computedVisibility !== 'collapse';\n  }\n  return isVisible;\n}\nfunction $645f2e67b85a24c9$var$isAttributeVisible(element, childElement) {\n  return !element.hasAttribute('hidden') &&\n  // Ignore HiddenSelect when tree walking.\n  !element.hasAttribute('data-react-aria-prevent-focus') && (element.nodeName === 'DETAILS' && childElement && childElement.nodeName !== 'SUMMARY' ? element.hasAttribute('open') : true);\n}\nfunction $645f2e67b85a24c9$export$e989c0fffaa6b27a(element, childElement) {\n  return element.nodeName !== '#comment' && $645f2e67b85a24c9$var$isStyleVisible(element) && $645f2e67b85a24c9$var$isAttributeVisible(element, childElement) && (!element.parentElement || $645f2e67b85a24c9$export$e989c0fffaa6b27a(element.parentElement, element));\n}\nexport { $645f2e67b85a24c9$export$e989c0fffaa6b27a as isElementVisible };", "map": {"version": 3, "names": ["$645f2e67b85a24c9$var$isStyleVisible", "element", "windowObject", "$djhjW$getOwnerWindow", "HTMLElement", "SVGElement", "display", "visibility", "style", "isVisible", "getComputedStyle", "ownerDocument", "defaultView", "computedDisplay", "computedVisibility", "$645f2e67b85a24c9$var$isAttributeVisible", "childElement", "hasAttribute", "nodeName", "$645f2e67b85a24c9$export$e989c0fffaa6b27a", "parentElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\focus\\dist\\packages\\@react-aria\\focus\\src\\isElementVisible.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerWindow} from '@react-aria/utils';\n\nfunction isStyleVisible(element: Element) {\n  const windowObject = getOwnerWindow(element);\n  if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) {\n    return false;\n  }\n\n  let {display, visibility} = element.style;\n\n  let isVisible = (\n    display !== 'none' &&\n    visibility !== 'hidden' &&\n    visibility !== 'collapse'\n  );\n\n  if (isVisible) {\n    const {getComputedStyle} = element.ownerDocument.defaultView as unknown as Window;\n    let {display: computedDisplay, visibility: computedVisibility} = getComputedStyle(element);\n\n    isVisible = (\n      computedDisplay !== 'none' &&\n      computedVisibility !== 'hidden' &&\n      computedVisibility !== 'collapse'\n    );\n  }\n\n  return isVisible;\n}\n\nfunction isAttributeVisible(element: Element, childElement?: Element) {\n  return (\n    !element.hasAttribute('hidden') &&\n    // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') &&\n    (element.nodeName === 'DETAILS' &&\n      childElement &&\n      childElement.nodeName !== 'SUMMARY'\n      ? element.hasAttribute('open')\n      : true)\n  );\n}\n\n/**\n * Adapted from https://github.com/testing-library/jest-dom and\n * https://github.com/vuejs/vue-test-utils-next/.\n * Licensed under the MIT License.\n * @param element - Element to evaluate for display or visibility.\n */\nexport function isElementVisible(element: Element, childElement?: Element): boolean {\n  return (\n    element.nodeName !== '#comment' &&\n    isStyleVisible(element) &&\n    isAttributeVisible(element, childElement) &&\n    (!element.parentElement || isElementVisible(element.parentElement, element))\n  );\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAcA,SAASA,qCAAeC,OAAgB;EACtC,MAAMC,YAAA,GAAe,IAAAC,qBAAa,EAAEF,OAAA;EACpC,IAAI,EAAEA,OAAA,YAAmBC,YAAA,CAAaE,WAAW,CAAD,IAAM,EAAEH,OAAA,YAAmBC,YAAA,CAAaG,UAAU,CAAD,EAC/F,OAAO;EAGT,IAAI;IAAAC,OAAA,EAACA,OAAO;IAAAC,UAAA,EAAEA;EAAU,CAAC,GAAGN,OAAA,CAAQO,KAAK;EAEzC,IAAIC,SAAA,GACFH,OAAA,KAAY,UACZC,UAAA,KAAe,YACfA,UAAA,KAAe;EAGjB,IAAIE,SAAA,EAAW;IACb,MAAM;MAAAC,gBAAA,EAACA;IAAgB,CAAC,GAAGT,OAAA,CAAQU,aAAa,CAACC,WAAW;IAC5D,IAAI;MAACN,OAAA,EAASO,eAAe;MAAEN,UAAA,EAAYO;IAAkB,CAAC,GAAGJ,gBAAA,CAAiBT,OAAA;IAElFQ,SAAA,GACEI,eAAA,KAAoB,UACpBC,kBAAA,KAAuB,YACvBA,kBAAA,KAAuB;EAE3B;EAEA,OAAOL,SAAA;AACT;AAEA,SAASM,yCAAmBd,OAAgB,EAAEe,YAAsB;EAClE,OACE,CAACf,OAAA,CAAQgB,YAAY,CAAC;EACtB;EACA,CAAChB,OAAA,CAAQgB,YAAY,CAAC,qCACrBhB,OAAA,CAAQiB,QAAQ,KAAK,aACpBF,YAAA,IACAA,YAAA,CAAaE,QAAQ,KAAK,YACxBjB,OAAA,CAAQgB,YAAY,CAAC,UACrB,IAAG;AAEX;AAQO,SAASE,0CAAiBlB,OAAgB,EAAEe,YAAsB;EACvE,OACEf,OAAA,CAAQiB,QAAQ,KAAK,cACrBlB,oCAAA,CAAeC,OAAA,KACfc,wCAAA,CAAmBd,OAAA,EAASe,YAAA,MAC3B,CAACf,OAAA,CAAQmB,aAAa,IAAID,yCAAA,CAAiBlB,OAAA,CAAQmB,aAAa,EAAEnB,OAAA,CAAO;AAE9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}