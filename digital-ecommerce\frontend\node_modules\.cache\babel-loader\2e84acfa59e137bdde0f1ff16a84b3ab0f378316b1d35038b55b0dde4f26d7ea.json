{"ast": null, "code": "import { isScrollable as $cc38e7bd3fc7b213$export$2bb74740c4e19def } from \"./isScrollable.mjs\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $62d8ded9296f3872$export$cfa2225e87938781(node, checkForOverflow) {\n  let scrollableNode = node;\n  if ((0, $cc38e7bd3fc7b213$export$2bb74740c4e19def)(scrollableNode, checkForOverflow)) scrollableNode = scrollableNode.parentElement;\n  while (scrollableNode && !(0, $cc38e7bd3fc7b213$export$2bb74740c4e19def)(scrollableNode, checkForOverflow)) scrollableNode = scrollableNode.parentElement;\n  return scrollableNode || document.scrollingElement || document.documentElement;\n}\nexport { $62d8ded9296f3872$export$cfa2225e87938781 as getScrollParent };", "map": {"version": 3, "names": ["$62d8ded9296f3872$export$cfa2225e87938781", "node", "checkForOverflow", "scrollableNode", "$cc38e7bd3fc7b213$export$2bb74740c4e19def", "parentElement", "document", "scrollingElement", "documentElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\getScrollParent.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParent(node: Element, checkForOverflow?: boolean): Element {\n  let scrollableNode: Element | null = node;\n  if (isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  while (scrollableNode && !isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  return scrollableNode || document.scrollingElement || document.documentElement;\n}\n\n"], "mappings": ";;AAAA;;;;;;;;;;;AAcO,SAASA,0CAAgBC,IAAa,EAAEC,gBAA0B;EACvE,IAAIC,cAAA,GAAiCF,IAAA;EACrC,IAAI,IAAAG,yCAAW,EAAED,cAAA,EAAgBD,gBAAA,GAC/BC,cAAA,GAAiBA,cAAA,CAAeE,aAAa;EAG/C,OAAOF,cAAA,IAAkB,CAAC,IAAAC,yCAAW,EAAED,cAAA,EAAgBD,gBAAA,GACrDC,cAAA,GAAiBA,cAAA,CAAeE,aAAa;EAG/C,OAAOF,cAAA,IAAkBG,QAAA,CAASC,gBAAgB,IAAID,QAAA,CAASE,eAAe;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}