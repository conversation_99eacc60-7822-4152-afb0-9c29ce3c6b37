{"ast": null, "code": "import { ignoreFocusEvent as $8a9cb279dc87e130$export$fda7da73ab5d4c48 } from \"./utils.mjs\";\nimport { isMac as $28AnR$isMac, isVirtualClick as $28AnR$isVirtualClick, getOwnerWindow as $28AnR$getOwnerWindow, getOwnerDocument as $28AnR$getOwnerDocument } from \"@react-aria/utils\";\nimport { useState as $28AnR$useState, useEffect as $28AnR$useEffect } from \"react\";\nimport { useIsSSR as $28AnR$useIsSSR } from \"@react-aria/ssr\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nlet $507fabe10e71c6fb$var$currentModality = null;\nlet $507fabe10e71c6fb$var$changeHandlers = new Set();\nlet $507fabe10e71c6fb$export$d90243b58daecda7 = new Map(); // We use a map here to support setting event listeners across multiple document objects.\nlet $507fabe10e71c6fb$var$hasEventBeforeFocus = false;\nlet $507fabe10e71c6fb$var$hasBlurredWindowRecently = false;\n// Only Tab or Esc keys will make focus visible on text input elements\nconst $507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\nfunction $507fabe10e71c6fb$var$triggerChangeHandlers(modality, e) {\n  for (let handler of $507fabe10e71c6fb$var$changeHandlers) handler(modality, e);\n}\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction $507fabe10e71c6fb$var$isValidKey(e) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || !(0, $28AnR$isMac)() && e.altKey || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\nfunction $507fabe10e71c6fb$var$handleKeyboardEvent(e) {\n  $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n  if ($507fabe10e71c6fb$var$isValidKey(e)) {\n    $507fabe10e71c6fb$var$currentModality = 'keyboard';\n    $507fabe10e71c6fb$var$triggerChangeHandlers('keyboard', e);\n  }\n}\nfunction $507fabe10e71c6fb$var$handlePointerEvent(e) {\n  $507fabe10e71c6fb$var$currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n    $507fabe10e71c6fb$var$triggerChangeHandlers('pointer', e);\n  }\n}\nfunction $507fabe10e71c6fb$var$handleClickEvent(e) {\n  if ((0, $28AnR$isVirtualClick)(e)) {\n    $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n    $507fabe10e71c6fb$var$currentModality = 'virtual';\n  }\n}\nfunction $507fabe10e71c6fb$var$handleFocusEvent(e) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || (0, $8a9cb279dc87e130$export$fda7da73ab5d4c48) || !e.isTrusted) return;\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!$507fabe10e71c6fb$var$hasEventBeforeFocus && !$507fabe10e71c6fb$var$hasBlurredWindowRecently) {\n    $507fabe10e71c6fb$var$currentModality = 'virtual';\n    $507fabe10e71c6fb$var$triggerChangeHandlers('virtual', e);\n  }\n  $507fabe10e71c6fb$var$hasEventBeforeFocus = false;\n  $507fabe10e71c6fb$var$hasBlurredWindowRecently = false;\n}\nfunction $507fabe10e71c6fb$var$handleWindowBlur() {\n  if (0, $8a9cb279dc87e130$export$fda7da73ab5d4c48) return;\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  $507fabe10e71c6fb$var$hasEventBeforeFocus = false;\n  $507fabe10e71c6fb$var$hasBlurredWindowRecently = true;\n}\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction $507fabe10e71c6fb$var$setupGlobalFocusEvents(element) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || $507fabe10e71c6fb$export$d90243b58daecda7.get((0, $28AnR$getOwnerWindow)(element))) return;\n  const windowObject = (0, $28AnR$getOwnerWindow)(element);\n  const documentObject = (0, $28AnR$getOwnerDocument)(element);\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n    focus.apply(this, arguments);\n  };\n  documentObject.addEventListener('keydown', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n  documentObject.addEventListener('click', $507fabe10e71c6fb$var$handleClickEvent, true);\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', $507fabe10e71c6fb$var$handleFocusEvent, true);\n  windowObject.addEventListener('blur', $507fabe10e71c6fb$var$handleWindowBlur, false);\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n  }\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    $507fabe10e71c6fb$var$tearDownWindowFocusTracking(element);\n  }, {\n    once: true\n  });\n  $507fabe10e71c6fb$export$d90243b58daecda7.set(windowObject, {\n    focus: focus\n  });\n}\nconst $507fabe10e71c6fb$var$tearDownWindowFocusTracking = (element, loadListener) => {\n  const windowObject = (0, $28AnR$getOwnerWindow)(element);\n  const documentObject = (0, $28AnR$getOwnerDocument)(element);\n  if (loadListener) documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  if (!$507fabe10e71c6fb$export$d90243b58daecda7.has(windowObject)) return;\n  windowObject.HTMLElement.prototype.focus = $507fabe10e71c6fb$export$d90243b58daecda7.get(windowObject).focus;\n  documentObject.removeEventListener('keydown', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', $507fabe10e71c6fb$var$handleClickEvent, true);\n  windowObject.removeEventListener('focus', $507fabe10e71c6fb$var$handleFocusEvent, true);\n  windowObject.removeEventListener('blur', $507fabe10e71c6fb$var$handleWindowBlur, false);\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n  }\n  $507fabe10e71c6fb$export$d90243b58daecda7.delete(windowObject);\n};\nfunction $507fabe10e71c6fb$export$2f1888112f558a7d(element) {\n  const documentObject = (0, $28AnR$getOwnerDocument)(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') $507fabe10e71c6fb$var$setupGlobalFocusEvents(element);else {\n    loadListener = () => {\n      $507fabe10e71c6fb$var$setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n  return () => $507fabe10e71c6fb$var$tearDownWindowFocusTracking(element, loadListener);\n}\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') $507fabe10e71c6fb$export$2f1888112f558a7d();\nfunction $507fabe10e71c6fb$export$b9b3dfddab17db27() {\n  return $507fabe10e71c6fb$var$currentModality !== 'pointer';\n}\nfunction $507fabe10e71c6fb$export$630ff653c5ada6a9() {\n  return $507fabe10e71c6fb$var$currentModality;\n}\nfunction $507fabe10e71c6fb$export$8397ddfc504fdb9a(modality) {\n  $507fabe10e71c6fb$var$currentModality = modality;\n  $507fabe10e71c6fb$var$triggerChangeHandlers(modality, null);\n}\nfunction $507fabe10e71c6fb$export$98e20ec92f614cfe() {\n  $507fabe10e71c6fb$var$setupGlobalFocusEvents();\n  let [modality, setModality] = (0, $28AnR$useState)($507fabe10e71c6fb$var$currentModality);\n  (0, $28AnR$useEffect)(() => {\n    let handler = () => {\n      setModality($507fabe10e71c6fb$var$currentModality);\n    };\n    $507fabe10e71c6fb$var$changeHandlers.add(handler);\n    return () => {\n      $507fabe10e71c6fb$var$changeHandlers.delete(handler);\n    };\n  }, []);\n  return (0, $28AnR$useIsSSR)() ? null : modality;\n}\nconst $507fabe10e71c6fb$var$nonTextInputTypes = new Set(['checkbox', 'radio', 'range', 'color', 'file', 'image', 'button', 'submit', 'reset']);\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction $507fabe10e71c6fb$var$isKeyboardFocusEvent(isTextInput, modality, e) {\n  let document1 = (0, $28AnR$getOwnerDocument)(e === null || e === void 0 ? void 0 : e.target);\n  const IHTMLInputElement = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).KeyboardEvent : KeyboardEvent;\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput || document1.activeElement instanceof IHTMLInputElement && !$507fabe10e71c6fb$var$nonTextInputTypes.has(document1.activeElement.type) || document1.activeElement instanceof IHTMLTextAreaElement || document1.activeElement instanceof IHTMLElement && document1.activeElement.isContentEditable;\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !$507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\nfunction $507fabe10e71c6fb$export$ffd9e5021c1fb2d6() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let {\n    isTextInput: isTextInput,\n    autoFocus: autoFocus\n  } = props;\n  let [isFocusVisibleState, setFocusVisible] = (0, $28AnR$useState)(autoFocus || $507fabe10e71c6fb$export$b9b3dfddab17db27());\n  $507fabe10e71c6fb$export$ec71b4b83ac08ec3(isFocusVisible => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {\n    isTextInput: isTextInput\n  });\n  return {\n    isFocusVisible: isFocusVisibleState\n  };\n}\nfunction $507fabe10e71c6fb$export$ec71b4b83ac08ec3(fn, deps, opts) {\n  $507fabe10e71c6fb$var$setupGlobalFocusEvents();\n  (0, $28AnR$useEffect)(() => {\n    let handler = (modality, e) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!$507fabe10e71c6fb$var$isKeyboardFocusEvent(!!(opts === null || opts === void 0 ? void 0 : opts.isTextInput), modality, e)) return;\n      fn($507fabe10e71c6fb$export$b9b3dfddab17db27());\n    };\n    $507fabe10e71c6fb$var$changeHandlers.add(handler);\n    return () => {\n      $507fabe10e71c6fb$var$changeHandlers.delete(handler);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\nexport { $507fabe10e71c6fb$export$d90243b58daecda7 as hasSetupGlobalListeners, $507fabe10e71c6fb$export$2f1888112f558a7d as addWindowFocusTracking, $507fabe10e71c6fb$export$b9b3dfddab17db27 as isFocusVisible, $507fabe10e71c6fb$export$630ff653c5ada6a9 as getInteractionModality, $507fabe10e71c6fb$export$8397ddfc504fdb9a as setInteractionModality, $507fabe10e71c6fb$export$98e20ec92f614cfe as useInteractionModality, $507fabe10e71c6fb$export$ffd9e5021c1fb2d6 as useFocusVisible, $507fabe10e71c6fb$export$ec71b4b83ac08ec3 as useFocusVisibleListener };", "map": {"version": 3, "names": ["$507fabe10e71c6fb$var$currentModality", "$507fabe10e71c6fb$var$changeHandlers", "Set", "$507fabe10e71c6fb$export$d90243b58daecda7", "Map", "$507fabe10e71c6fb$var$hasEventBeforeFocus", "$507fabe10e71c6fb$var$hasBlurredWindowRecently", "$507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS", "Tab", "Escape", "$507fabe10e71c6fb$var$triggerChangeHandlers", "modality", "e", "handler", "$507fabe10e71c6fb$var$isValidKey", "metaKey", "$28AnR$isMac", "altKey", "ctrl<PERSON>ey", "key", "$507fabe10e71c6fb$var$handleKeyboardEvent", "$507fabe10e71c6fb$var$handlePointerEvent", "type", "$507fabe10e71c6fb$var$handleClickEvent", "$28AnR$isVirtualClick", "$507fabe10e71c6fb$var$handleFocusEvent", "target", "window", "document", "$8a9cb279dc87e130$export$fda7da73ab5d4c48", "isTrusted", "$507fabe10e71c6fb$var$handleWindowBlur", "$507fabe10e71c6fb$var$setupGlobalFocusEvents", "element", "get", "$28AnR$getOwnerWindow", "windowObject", "documentObject", "$28AnR$getOwnerDocument", "focus", "HTMLElement", "prototype", "apply", "arguments", "addEventListener", "PointerEvent", "process", "env", "NODE_ENV", "$507fabe10e71c6fb$var$tearDownWindowFocusTracking", "once", "set", "loadListener", "removeEventListener", "has", "delete", "$507fabe10e71c6fb$export$2f1888112f558a7d", "readyState", "$507fabe10e71c6fb$export$b9b3dfddab17db27", "$507fabe10e71c6fb$export$630ff653c5ada6a9", "$507fabe10e71c6fb$export$8397ddfc504fdb9a", "$507fabe10e71c6fb$export$98e20ec92f614cfe", "setModality", "$28AnR$useState", "$28AnR$useEffect", "add", "$28AnR$useIsSSR", "$507fabe10e71c6fb$var$nonTextInputTypes", "$507fabe10e71c6fb$var$isKeyboardFocusEvent", "isTextInput", "document1", "IHTMLInputElement", "HTMLInputElement", "IHTMLTextAreaElement", "HTMLTextAreaElement", "IHTMLElement", "IKeyboardEvent", "KeyboardEvent", "activeElement", "isContentEditable", "$507fabe10e71c6fb$export$ffd9e5021c1fb2d6", "props", "length", "undefined", "autoFocus", "isFocusVisibleState", "setFocusVisible", "$507fabe10e71c6fb$export$ec71b4b83ac08ec3", "isFocusVisible", "fn", "deps", "opts"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useFocusVisible.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n"], "mappings": ";;;;;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;;AAuBA,IAAIA,qCAAA,GAAmC;AACvC,IAAIC,oCAAA,GAAiB,IAAIC,GAAA;AAIlB,IAAIC,yCAAA,GAA0B,IAAIC,GAAA,IAAmC;AAC5E,IAAIC,yCAAA,GAAsB;AAC1B,IAAIC,8CAAA,GAA2B;AAE/B;AACA,MAAMC,8CAAA,GAA2B;EAC/BC,GAAA,EAAK;EACLC,MAAA,EAAQ;AACV;AAEA,SAASC,4CAAsBC,QAAkB,EAAEC,CAAe;EAChE,KAAK,IAAIC,OAAA,IAAWZ,oCAAA,EAClBY,OAAA,CAAQF,QAAA,EAAUC,CAAA;AAEtB;AAEA;;;AAGA,SAASE,iCAAWF,CAAgB;EAClC;EACA,OAAO,EAAEA,CAAA,CAAEG,OAAO,IAAK,CAAC,IAAAC,YAAI,OAAOJ,CAAA,CAAEK,MAAM,IAAKL,CAAA,CAAEM,OAAO,IAAIN,CAAA,CAAEO,GAAG,KAAK,aAAaP,CAAA,CAAEO,GAAG,KAAK,WAAWP,CAAA,CAAEO,GAAG,KAAK,MAAK;AAC1H;AAGA,SAASC,0CAAoBR,CAAgB;EAC3CP,yCAAA,GAAsB;EACtB,IAAIS,gCAAA,CAAWF,CAAA,GAAI;IACjBZ,qCAAA,GAAkB;IAClBU,2CAAA,CAAsB,YAAYE,CAAA;EACpC;AACF;AAEA,SAASS,yCAAmBT,CAA4B;EACtDZ,qCAAA,GAAkB;EAClB,IAAIY,CAAA,CAAEU,IAAI,KAAK,eAAeV,CAAA,CAAEU,IAAI,KAAK,eAAe;IACtDjB,yCAAA,GAAsB;IACtBK,2CAAA,CAAsB,WAAWE,CAAA;EACnC;AACF;AAEA,SAASW,uCAAiBX,CAAa;EACrC,IAAI,IAAAY,qBAAa,EAAEZ,CAAA,GAAI;IACrBP,yCAAA,GAAsB;IACtBL,qCAAA,GAAkB;EACpB;AACF;AAEA,SAASyB,uCAAiBb,CAAa;EACrC;EACA;EACA;EACA,IAAIA,CAAA,CAAEc,MAAM,KAAKC,MAAA,IAAUf,CAAA,CAAEc,MAAM,KAAKE,QAAA,KAAY,GAAAC,yCAAe,KAAK,CAACjB,CAAA,CAAEkB,SAAS,EAClF;EAGF;EACA;EACA,IAAI,CAACzB,yCAAA,IAAuB,CAACC,8CAAA,EAA0B;IACrDN,qCAAA,GAAkB;IAClBU,2CAAA,CAAsB,WAAWE,CAAA;EACnC;EAEAP,yCAAA,GAAsB;EACtBC,8CAAA,GAA2B;AAC7B;AAEA,SAASyB,uCAAA;EACP,IAAI,GAAAF,yCAAA,EACF;EAGF;EACA;EACAxB,yCAAA,GAAsB;EACtBC,8CAAA,GAA2B;AAC7B;AAEA;;;AAGA,SAAS0B,6CAAuBC,OAA4B;EAC1D,IAAI,OAAON,MAAA,KAAW,eAAe,OAAOC,QAAA,KAAa,eAAezB,yCAAA,CAAwB+B,GAAG,CAAC,IAAAC,qBAAa,EAAEF,OAAA,IACjH;EAGF,MAAMG,YAAA,GAAe,IAAAD,qBAAa,EAAEF,OAAA;EACpC,MAAMI,cAAA,GAAiB,IAAAC,uBAAe,EAAEL,OAAA;EAExC;EACA;EACA;EACA;EACA,IAAIM,KAAA,GAAQH,YAAA,CAAaI,WAAW,CAACC,SAAS,CAACF,KAAK;EACpDH,YAAA,CAAaI,WAAW,CAACC,SAAS,CAACF,KAAK,GAAG;IACzClC,yCAAA,GAAsB;IACtBkC,KAAA,CAAMG,KAAK,CAAC,IAAI,EAAEC,SAAA;EACpB;EAEAN,cAAA,CAAeO,gBAAgB,CAAC,WAAWxB,yCAAA,EAAqB;EAChEiB,cAAA,CAAeO,gBAAgB,CAAC,SAASxB,yCAAA,EAAqB;EAC9DiB,cAAA,CAAeO,gBAAgB,CAAC,SAASrB,sCAAA,EAAkB;EAE3D;EACA;EACAa,YAAA,CAAaQ,gBAAgB,CAAC,SAASnB,sCAAA,EAAkB;EACzDW,YAAA,CAAaQ,gBAAgB,CAAC,QAAQb,sCAAA,EAAkB;EAExD,IAAI,OAAOc,YAAA,KAAiB,aAAa;IACvCR,cAAA,CAAeO,gBAAgB,CAAC,eAAevB,wCAAA,EAAoB;IACnEgB,cAAA,CAAeO,gBAAgB,CAAC,eAAevB,wCAAA,EAAoB;IACnEgB,cAAA,CAAeO,gBAAgB,CAAC,aAAavB,wCAAA,EAAoB;EACnE,OAAO,IAAIyB,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;IAC1CX,cAAA,CAAeO,gBAAgB,CAAC,aAAavB,wCAAA,EAAoB;IACjEgB,cAAA,CAAeO,gBAAgB,CAAC,aAAavB,wCAAA,EAAoB;IACjEgB,cAAA,CAAeO,gBAAgB,CAAC,WAAWvB,wCAAA,EAAoB;EACjE;EAEA;EACAe,YAAA,CAAaQ,gBAAgB,CAAC,gBAAgB;IAC5CK,iDAAA,CAA4BhB,OAAA;EAC9B,GAAG;IAACiB,IAAA,EAAM;EAAI;EAEd/C,yCAAA,CAAwBgD,GAAG,CAACf,YAAA,EAAc;WAACG;EAAK;AAClD;AAEA,MAAMU,iDAAA,GAA8BA,CAAChB,OAAA,EAASmB,YAAA;EAC5C,MAAMhB,YAAA,GAAe,IAAAD,qBAAa,EAAEF,OAAA;EACpC,MAAMI,cAAA,GAAiB,IAAAC,uBAAe,EAAEL,OAAA;EACxC,IAAImB,YAAA,EACFf,cAAA,CAAegB,mBAAmB,CAAC,oBAAoBD,YAAA;EAEzD,IAAI,CAACjD,yCAAA,CAAwBmD,GAAG,CAAClB,YAAA,GAC/B;EAEFA,YAAA,CAAaI,WAAW,CAACC,SAAS,CAACF,KAAK,GAAGpC,yCAAA,CAAwB+B,GAAG,CAACE,YAAA,EAAeG,KAAK;EAE3FF,cAAA,CAAegB,mBAAmB,CAAC,WAAWjC,yCAAA,EAAqB;EACnEiB,cAAA,CAAegB,mBAAmB,CAAC,SAASjC,yCAAA,EAAqB;EACjEiB,cAAA,CAAegB,mBAAmB,CAAC,SAAS9B,sCAAA,EAAkB;EAE9Da,YAAA,CAAaiB,mBAAmB,CAAC,SAAS5B,sCAAA,EAAkB;EAC5DW,YAAA,CAAaiB,mBAAmB,CAAC,QAAQtB,sCAAA,EAAkB;EAE3D,IAAI,OAAOc,YAAA,KAAiB,aAAa;IACvCR,cAAA,CAAegB,mBAAmB,CAAC,eAAehC,wCAAA,EAAoB;IACtEgB,cAAA,CAAegB,mBAAmB,CAAC,eAAehC,wCAAA,EAAoB;IACtEgB,cAAA,CAAegB,mBAAmB,CAAC,aAAahC,wCAAA,EAAoB;EACtE,OAAO,IAAIyB,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;IAC1CX,cAAA,CAAegB,mBAAmB,CAAC,aAAahC,wCAAA,EAAoB;IACpEgB,cAAA,CAAegB,mBAAmB,CAAC,aAAahC,wCAAA,EAAoB;IACpEgB,cAAA,CAAegB,mBAAmB,CAAC,WAAWhC,wCAAA,EAAoB;EACpE;EAEAlB,yCAAA,CAAwBoD,MAAM,CAACnB,YAAA;AACjC;AAmBO,SAASoB,0CAAuBvB,OAA4B;EACjE,MAAMI,cAAA,GAAiB,IAAAC,uBAAe,EAAEL,OAAA;EACxC,IAAImB,YAAA;EACJ,IAAIf,cAAA,CAAeoB,UAAU,KAAK,WAChCzB,4CAAA,CAAuBC,OAAA,OAClB;IACLmB,YAAA,GAAeA,CAAA;MACbpB,4CAAA,CAAuBC,OAAA;IACzB;IACAI,cAAA,CAAeO,gBAAgB,CAAC,oBAAoBQ,YAAA;EACtD;EAEA,OAAO,MAAMH,iDAAA,CAA4BhB,OAAA,EAASmB,YAAA;AACpD;AAEA;AACA;AACA,IAAI,OAAOxB,QAAA,KAAa,aACtB4B,yCAAA;AAMK,SAASE,0CAAA;EACd,OAAO1D,qCAAA,KAAoB;AAC7B;AAEO,SAAS2D,0CAAA;EACd,OAAO3D,qCAAA;AACT;AAEO,SAAS4D,0CAAuBjD,QAAkB;EACvDX,qCAAA,GAAkBW,QAAA;EAClBD,2CAAA,CAAsBC,QAAA,EAAU;AAClC;AAKO,SAASkD,0CAAA;EACd7B,4CAAA;EAEA,IAAI,CAACrB,QAAA,EAAUmD,WAAA,CAAY,GAAG,IAAAC,eAAO,EAAE/D,qCAAA;EACvC,IAAAgE,gBAAQ,EAAE;IACR,IAAInD,OAAA,GAAUA,CAAA;MACZiD,WAAA,CAAY9D,qCAAA;IACd;IAEAC,oCAAA,CAAegE,GAAG,CAACpD,OAAA;IACnB,OAAO;MACLZ,oCAAA,CAAesD,MAAM,CAAC1C,OAAA;IACxB;EACF,GAAG,EAAE;EAEL,OAAO,IAAAqD,eAAO,MAAM,OAAOvD,QAAA;AAC7B;AAEA,MAAMwD,uCAAA,GAAoB,IAAIjE,GAAA,CAAI,CAChC,YACA,SACA,SACA,SACA,QACA,SACA,UACA,UACA,QACD;AAED;;;;AAIA,SAASkE,2CAAqBC,WAAoB,EAAE1D,QAAkB,EAAEC,CAAe;EACrF,IAAI0D,SAAA,GAAW,IAAAhC,uBAAe,EAAE1B,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAGc,MAAM;EACzC,MAAM6C,iBAAA,GAAoB,OAAO5C,MAAA,KAAW,cAAc,IAAAQ,qBAAa,EAAEvB,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAGc,MAAM,EAAa8C,gBAAgB,GAAGA,gBAAA;EAClH,MAAMC,oBAAA,GAAuB,OAAO9C,MAAA,KAAW,cAAc,IAAAQ,qBAAa,EAAEvB,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAGc,MAAM,EAAagD,mBAAmB,GAAGA,mBAAA;EACxH,MAAMC,YAAA,GAAe,OAAOhD,MAAA,KAAW,cAAc,IAAAQ,qBAAa,EAAEvB,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAGc,MAAM,EAAac,WAAW,GAAGA,WAAA;EACxG,MAAMoC,cAAA,GAAiB,OAAOjD,MAAA,KAAW,cAAc,IAAAQ,qBAAa,EAAEvB,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAGc,MAAM,EAAamD,aAAa,GAAGA,aAAA;EAE5G;EACA;EACAR,WAAA,GAAcA,WAAA,IACXC,SAAA,CAASQ,aAAa,YAAYP,iBAAA,IAAqB,CAACJ,uCAAA,CAAkBb,GAAG,CAACgB,SAAA,CAASQ,aAAa,CAACxD,IAAI,KAC1GgD,SAAA,CAASQ,aAAa,YAAYL,oBAAA,IACjCH,SAAA,CAASQ,aAAa,YAAYH,YAAA,IAAgBL,SAAA,CAASQ,aAAa,CAACC,iBAAiB;EAC7F,OAAO,EAAEV,WAAA,IAAe1D,QAAA,KAAa,cAAcC,CAAA,YAAagE,cAAA,IAAkB,CAACrE,8CAAwB,CAACK,CAAA,CAAEO,GAAG,CAAC,CAAD;AACnH;AAKO,SAAS6D,0CAAA,EAA6C;EAAA,IAA7BC,KAAA,GAAAtC,SAAA,CAAAuC,MAAA,QAAAvC,SAAA,QAAAwC,SAAA,GAAAxC,SAAA,MAA2B,CAAC,CAAC;EAC3D,IAAI;IAAA0B,WAAA,EAACA,WAAW;IAAAe,SAAA,EAAEA;EAAS,CAAC,GAAGH,KAAA;EAC/B,IAAI,CAACI,mBAAA,EAAqBC,eAAA,CAAgB,GAAG,IAAAvB,eAAO,EAAEqB,SAAA,IAAa1B,yCAAA;EACnE6B,yCAAA,CAAyBC,cAAA;IACvBF,eAAA,CAAgBE,cAAA;EAClB,GAAG,CAACnB,WAAA,CAAY,EAAE;iBAACA;EAAW;EAE9B,OAAO;IAACmB,cAAA,EAAgBH;EAAmB;AAC7C;AAKO,SAASE,0CAAwBE,EAAuB,EAAEC,IAAwB,EAAEC,IAA8B;EACvH3D,4CAAA;EAEA,IAAAgC,gBAAQ,EAAE;IACR,IAAInD,OAAA,GAAUA,CAACF,QAAA,EAAoBC,CAAA;MACjC;MACA,IAAI,CAACwD,0CAAA,CAAqB,CAAC,EAAEuB,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAMtB,WAAW,GAAG1D,QAAA,EAAUC,CAAA,GACzD;MAEF6E,EAAA,CAAG/B,yCAAA;IACL;IACAzD,oCAAA,CAAegE,GAAG,CAACpD,OAAA;IACnB,OAAO;MACLZ,oCAAA,CAAesD,MAAM,CAAC1C,OAAA;IACxB;IACF;EACA,GAAG6E,IAAA;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}