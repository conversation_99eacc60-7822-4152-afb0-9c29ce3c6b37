{"ast": null, "code": "import { disableTextSelection as $14c0b72509d70225$export$16a4697467175487, restoreTextSelection as $14c0b72509d70225$export$b0d6fa1ab32e3295 } from \"./textSelection.mjs\";\nimport { useRef as $5GN7j$useRef, useMemo as $5GN7j$useMemo } from \"react\";\nimport { useGlobalListeners as $5GN7j$useGlobalListeners, useEffectEvent as $5GN7j$useEffectEvent } from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $e8a7022cf87cba2a$export$36da96379f79f245(props) {\n  let {\n    onMoveStart: onMoveStart,\n    onMove: onMove,\n    onMoveEnd: onMoveEnd\n  } = props;\n  let state = (0, $5GN7j$useRef)({\n    didMove: false,\n    lastPosition: null,\n    id: null\n  });\n  let {\n    addGlobalListener: addGlobalListener,\n    removeGlobalListener: removeGlobalListener\n  } = (0, $5GN7j$useGlobalListeners)();\n  let move = (0, $5GN7j$useEffectEvent)((originalEvent, pointerType, deltaX, deltaY) => {\n    if (deltaX === 0 && deltaY === 0) return;\n    if (!state.current.didMove) {\n      state.current.didMove = true;\n      onMoveStart === null || onMoveStart === void 0 ? void 0 : onMoveStart({\n        type: 'movestart',\n        pointerType: pointerType,\n        shiftKey: originalEvent.shiftKey,\n        metaKey: originalEvent.metaKey,\n        ctrlKey: originalEvent.ctrlKey,\n        altKey: originalEvent.altKey\n      });\n    }\n    onMove === null || onMove === void 0 ? void 0 : onMove({\n      type: 'move',\n      pointerType: pointerType,\n      deltaX: deltaX,\n      deltaY: deltaY,\n      shiftKey: originalEvent.shiftKey,\n      metaKey: originalEvent.metaKey,\n      ctrlKey: originalEvent.ctrlKey,\n      altKey: originalEvent.altKey\n    });\n  });\n  let end = (0, $5GN7j$useEffectEvent)((originalEvent, pointerType) => {\n    (0, $14c0b72509d70225$export$b0d6fa1ab32e3295)();\n    if (state.current.didMove) onMoveEnd === null || onMoveEnd === void 0 ? void 0 : onMoveEnd({\n      type: 'moveend',\n      pointerType: pointerType,\n      shiftKey: originalEvent.shiftKey,\n      metaKey: originalEvent.metaKey,\n      ctrlKey: originalEvent.ctrlKey,\n      altKey: originalEvent.altKey\n    });\n  });\n  let moveProps = (0, $5GN7j$useMemo)(() => {\n    let moveProps = {};\n    let start = () => {\n      (0, $14c0b72509d70225$export$16a4697467175487)();\n      state.current.didMove = false;\n    };\n    if (typeof PointerEvent === 'undefined' && process.env.NODE_ENV === 'test') {\n      let onMouseMove = e => {\n        if (e.button === 0) {\n          var _state_current_lastPosition, _state_current_lastPosition1;\n          var _state_current_lastPosition_pageX, _state_current_lastPosition_pageY;\n          move(e, 'mouse', e.pageX - ((_state_current_lastPosition_pageX = (_state_current_lastPosition = state.current.lastPosition) === null || _state_current_lastPosition === void 0 ? void 0 : _state_current_lastPosition.pageX) !== null && _state_current_lastPosition_pageX !== void 0 ? _state_current_lastPosition_pageX : 0), e.pageY - ((_state_current_lastPosition_pageY = (_state_current_lastPosition1 = state.current.lastPosition) === null || _state_current_lastPosition1 === void 0 ? void 0 : _state_current_lastPosition1.pageY) !== null && _state_current_lastPosition_pageY !== void 0 ? _state_current_lastPosition_pageY : 0));\n          state.current.lastPosition = {\n            pageX: e.pageX,\n            pageY: e.pageY\n          };\n        }\n      };\n      let onMouseUp = e => {\n        if (e.button === 0) {\n          end(e, 'mouse');\n          removeGlobalListener(window, 'mousemove', onMouseMove, false);\n          removeGlobalListener(window, 'mouseup', onMouseUp, false);\n        }\n      };\n      moveProps.onMouseDown = e => {\n        if (e.button === 0) {\n          start();\n          e.stopPropagation();\n          e.preventDefault();\n          state.current.lastPosition = {\n            pageX: e.pageX,\n            pageY: e.pageY\n          };\n          addGlobalListener(window, 'mousemove', onMouseMove, false);\n          addGlobalListener(window, 'mouseup', onMouseUp, false);\n        }\n      };\n      let onTouchMove = e => {\n        let touch = [...e.changedTouches].findIndex(_ref => {\n          let {\n            identifier: identifier\n          } = _ref;\n          return identifier === state.current.id;\n        });\n        if (touch >= 0) {\n          var _state_current_lastPosition, _state_current_lastPosition1;\n          let {\n            pageX: pageX,\n            pageY: pageY\n          } = e.changedTouches[touch];\n          var _state_current_lastPosition_pageX, _state_current_lastPosition_pageY;\n          move(e, 'touch', pageX - ((_state_current_lastPosition_pageX = (_state_current_lastPosition = state.current.lastPosition) === null || _state_current_lastPosition === void 0 ? void 0 : _state_current_lastPosition.pageX) !== null && _state_current_lastPosition_pageX !== void 0 ? _state_current_lastPosition_pageX : 0), pageY - ((_state_current_lastPosition_pageY = (_state_current_lastPosition1 = state.current.lastPosition) === null || _state_current_lastPosition1 === void 0 ? void 0 : _state_current_lastPosition1.pageY) !== null && _state_current_lastPosition_pageY !== void 0 ? _state_current_lastPosition_pageY : 0));\n          state.current.lastPosition = {\n            pageX: pageX,\n            pageY: pageY\n          };\n        }\n      };\n      let onTouchEnd = e => {\n        let touch = [...e.changedTouches].findIndex(_ref2 => {\n          let {\n            identifier: identifier\n          } = _ref2;\n          return identifier === state.current.id;\n        });\n        if (touch >= 0) {\n          end(e, 'touch');\n          state.current.id = null;\n          removeGlobalListener(window, 'touchmove', onTouchMove);\n          removeGlobalListener(window, 'touchend', onTouchEnd);\n          removeGlobalListener(window, 'touchcancel', onTouchEnd);\n        }\n      };\n      moveProps.onTouchStart = e => {\n        if (e.changedTouches.length === 0 || state.current.id != null) return;\n        let {\n          pageX: pageX,\n          pageY: pageY,\n          identifier: identifier\n        } = e.changedTouches[0];\n        start();\n        e.stopPropagation();\n        e.preventDefault();\n        state.current.lastPosition = {\n          pageX: pageX,\n          pageY: pageY\n        };\n        state.current.id = identifier;\n        addGlobalListener(window, 'touchmove', onTouchMove, false);\n        addGlobalListener(window, 'touchend', onTouchEnd, false);\n        addGlobalListener(window, 'touchcancel', onTouchEnd, false);\n      };\n    } else {\n      let onPointerMove = e => {\n        if (e.pointerId === state.current.id) {\n          var _state_current_lastPosition, _state_current_lastPosition1;\n          let pointerType = e.pointerType || 'mouse';\n          var _state_current_lastPosition_pageX, _state_current_lastPosition_pageY;\n          // Problems with PointerEvent#movementX/movementY:\n          // 1. it is always 0 on macOS Safari.\n          // 2. On Chrome Android, it's scaled by devicePixelRatio, but not on Chrome macOS\n          move(e, pointerType, e.pageX - ((_state_current_lastPosition_pageX = (_state_current_lastPosition = state.current.lastPosition) === null || _state_current_lastPosition === void 0 ? void 0 : _state_current_lastPosition.pageX) !== null && _state_current_lastPosition_pageX !== void 0 ? _state_current_lastPosition_pageX : 0), e.pageY - ((_state_current_lastPosition_pageY = (_state_current_lastPosition1 = state.current.lastPosition) === null || _state_current_lastPosition1 === void 0 ? void 0 : _state_current_lastPosition1.pageY) !== null && _state_current_lastPosition_pageY !== void 0 ? _state_current_lastPosition_pageY : 0));\n          state.current.lastPosition = {\n            pageX: e.pageX,\n            pageY: e.pageY\n          };\n        }\n      };\n      let onPointerUp = e => {\n        if (e.pointerId === state.current.id) {\n          let pointerType = e.pointerType || 'mouse';\n          end(e, pointerType);\n          state.current.id = null;\n          removeGlobalListener(window, 'pointermove', onPointerMove, false);\n          removeGlobalListener(window, 'pointerup', onPointerUp, false);\n          removeGlobalListener(window, 'pointercancel', onPointerUp, false);\n        }\n      };\n      moveProps.onPointerDown = e => {\n        if (e.button === 0 && state.current.id == null) {\n          start();\n          e.stopPropagation();\n          e.preventDefault();\n          state.current.lastPosition = {\n            pageX: e.pageX,\n            pageY: e.pageY\n          };\n          state.current.id = e.pointerId;\n          addGlobalListener(window, 'pointermove', onPointerMove, false);\n          addGlobalListener(window, 'pointerup', onPointerUp, false);\n          addGlobalListener(window, 'pointercancel', onPointerUp, false);\n        }\n      };\n    }\n    let triggerKeyboardMove = (e, deltaX, deltaY) => {\n      start();\n      move(e, 'keyboard', deltaX, deltaY);\n      end(e, 'keyboard');\n    };\n    moveProps.onKeyDown = e => {\n      switch (e.key) {\n        case 'Left':\n        case 'ArrowLeft':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, -1, 0);\n          break;\n        case 'Right':\n        case 'ArrowRight':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 1, 0);\n          break;\n        case 'Up':\n        case 'ArrowUp':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 0, -1);\n          break;\n        case 'Down':\n        case 'ArrowDown':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 0, 1);\n          break;\n      }\n    };\n    return moveProps;\n  }, [state, addGlobalListener, removeGlobalListener, move, end]);\n  return {\n    moveProps: moveProps\n  };\n}\nexport { $e8a7022cf87cba2a$export$36da96379f79f245 as useMove };", "map": {"version": 3, "names": ["$e8a7022cf87cba2a$export$36da96379f79f245", "props", "onMoveStart", "onMove", "onMoveEnd", "state", "$5GN7j$useRef", "did<PERSON>ove", "lastPosition", "id", "addGlobalListener", "removeGlobalListener", "$5GN7j$useGlobalListeners", "move", "$5GN7j$useEffectEvent", "originalEvent", "pointerType", "deltaX", "deltaY", "current", "type", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "altKey", "end", "$14c0b72509d70225$export$b0d6fa1ab32e3295", "moveProps", "$5GN7j$useMemo", "start", "$14c0b72509d70225$export$16a4697467175487", "PointerEvent", "process", "env", "NODE_ENV", "onMouseMove", "e", "button", "_state_current_lastPosition", "_state_current_lastPosition1", "_state_current_lastPosition_pageX", "_state_current_lastPosition_pageY", "pageX", "pageY", "onMouseUp", "window", "onMouseDown", "stopPropagation", "preventDefault", "onTouchMove", "touch", "changedTouches", "findIndex", "_ref", "identifier", "onTouchEnd", "_ref2", "onTouchStart", "length", "onPointerMove", "pointerId", "onPointerUp", "onPointerDown", "triggerKeyboardMove", "onKeyDown", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useMove.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {disableTextSelection, restoreTextSelection}  from './textSelection';\nimport {DOMAttributes, MoveEvents, PointerType} from '@react-types/shared';\nimport React, {useMemo, useRef} from 'react';\nimport {useEffectEvent, useGlobalListeners} from '@react-aria/utils';\n\nexport interface MoveResult {\n  /** Props to spread on the target element. */\n  moveProps: DOMAttributes\n}\n\ninterface EventBase {\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean\n}\n\n/**\n * <PERSON>les move interactions across mouse, touch, and keyboard, including dragging with\n * the mouse or touch, and using the arrow keys. Normalizes behavior across browsers and\n * platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useMove(props: MoveEvents): MoveResult {\n  let {onMoveStart, onMove, onMoveEnd} = props;\n\n  let state = useRef<{\n    didMove: boolean,\n    lastPosition: {pageX: number, pageY: number} | null,\n    id: number | null\n  }>({didMove: false, lastPosition: null, id: null});\n\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let move = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, deltaX: number, deltaY: number) => {\n    if (deltaX === 0 && deltaY === 0) {\n      return;\n    }\n\n    if (!state.current.didMove) {\n      state.current.didMove = true;\n      onMoveStart?.({\n        type: 'movestart',\n        pointerType,\n        shiftKey: originalEvent.shiftKey,\n        metaKey: originalEvent.metaKey,\n        ctrlKey: originalEvent.ctrlKey,\n        altKey: originalEvent.altKey\n      });\n    }\n    onMove?.({\n      type: 'move',\n      pointerType,\n      deltaX: deltaX,\n      deltaY: deltaY,\n      shiftKey: originalEvent.shiftKey,\n      metaKey: originalEvent.metaKey,\n      ctrlKey: originalEvent.ctrlKey,\n      altKey: originalEvent.altKey\n    });\n  });\n\n  let end = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    restoreTextSelection();\n    if (state.current.didMove) {\n      onMoveEnd?.({\n        type: 'moveend',\n        pointerType,\n        shiftKey: originalEvent.shiftKey,\n        metaKey: originalEvent.metaKey,\n        ctrlKey: originalEvent.ctrlKey,\n        altKey: originalEvent.altKey\n      });\n    }\n  });\n\n  let moveProps = useMemo(() => {\n    let moveProps: DOMAttributes = {};\n\n    let start = () => {\n      disableTextSelection();\n      state.current.didMove = false;\n    };\n\n    if (typeof PointerEvent === 'undefined' && process.env.NODE_ENV === 'test') {\n      let onMouseMove = (e: MouseEvent) => {\n        if (e.button === 0) {\n          move(e, 'mouse', e.pageX - (state.current.lastPosition?.pageX ?? 0), e.pageY - (state.current.lastPosition?.pageY ?? 0));\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n        }\n      };\n      let onMouseUp = (e: MouseEvent) => {\n        if (e.button === 0) {\n          end(e, 'mouse');\n          removeGlobalListener(window, 'mousemove', onMouseMove, false);\n          removeGlobalListener(window, 'mouseup', onMouseUp, false);\n        }\n      };\n      moveProps.onMouseDown = (e: React.MouseEvent) => {\n        if (e.button === 0) {\n          start();\n          e.stopPropagation();\n          e.preventDefault();\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n          addGlobalListener(window, 'mousemove', onMouseMove, false);\n          addGlobalListener(window, 'mouseup', onMouseUp, false);\n        }\n      };\n\n      let onTouchMove = (e: TouchEvent) => {\n        let touch = [...e.changedTouches].findIndex(({identifier}) => identifier === state.current.id);\n        if (touch >= 0) {\n          let {pageX, pageY} = e.changedTouches[touch];\n          move(e, 'touch', pageX - (state.current.lastPosition?.pageX ?? 0), pageY - (state.current.lastPosition?.pageY ?? 0));\n          state.current.lastPosition = {pageX, pageY};\n        }\n      };\n      let onTouchEnd = (e: TouchEvent) => {\n        let touch = [...e.changedTouches].findIndex(({identifier}) => identifier === state.current.id);\n        if (touch >= 0) {\n          end(e, 'touch');\n          state.current.id = null;\n          removeGlobalListener(window, 'touchmove', onTouchMove);\n          removeGlobalListener(window, 'touchend', onTouchEnd);\n          removeGlobalListener(window, 'touchcancel', onTouchEnd);\n        }\n      };\n      moveProps.onTouchStart = (e: React.TouchEvent) => {\n        if (e.changedTouches.length === 0 || state.current.id != null) {\n          return;\n        }\n\n        let {pageX, pageY, identifier} = e.changedTouches[0];\n        start();\n        e.stopPropagation();\n        e.preventDefault();\n        state.current.lastPosition = {pageX, pageY};\n        state.current.id = identifier;\n        addGlobalListener(window, 'touchmove', onTouchMove, false);\n        addGlobalListener(window, 'touchend', onTouchEnd, false);\n        addGlobalListener(window, 'touchcancel', onTouchEnd, false);\n      };\n    } else {\n      let onPointerMove = (e: PointerEvent) => {\n        if (e.pointerId === state.current.id) {\n          let pointerType = (e.pointerType || 'mouse') as PointerType;\n\n          // Problems with PointerEvent#movementX/movementY:\n          // 1. it is always 0 on macOS Safari.\n          // 2. On Chrome Android, it's scaled by devicePixelRatio, but not on Chrome macOS\n          move(e, pointerType, e.pageX - (state.current.lastPosition?.pageX ?? 0), e.pageY - (state.current.lastPosition?.pageY ?? 0));\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.current.id) {\n          let pointerType = (e.pointerType || 'mouse') as PointerType;\n          end(e, pointerType);\n          state.current.id = null;\n          removeGlobalListener(window, 'pointermove', onPointerMove, false);\n          removeGlobalListener(window, 'pointerup', onPointerUp, false);\n          removeGlobalListener(window, 'pointercancel', onPointerUp, false);\n        }\n      };\n\n      moveProps.onPointerDown = (e: React.PointerEvent) => {\n        if (e.button === 0 && state.current.id == null) {\n          start();\n          e.stopPropagation();\n          e.preventDefault();\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n          state.current.id = e.pointerId;\n          addGlobalListener(window, 'pointermove', onPointerMove, false);\n          addGlobalListener(window, 'pointerup', onPointerUp, false);\n          addGlobalListener(window, 'pointercancel', onPointerUp, false);\n        }\n      };\n    }\n\n    let triggerKeyboardMove = (e: EventBase, deltaX: number, deltaY: number) => {\n      start();\n      move(e, 'keyboard', deltaX, deltaY);\n      end(e, 'keyboard');\n    };\n\n    moveProps.onKeyDown = (e) => {\n      switch (e.key) {\n        case 'Left':\n        case 'ArrowLeft':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, -1, 0);\n          break;\n        case 'Right':\n        case 'ArrowRight':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 1, 0);\n          break;\n        case 'Up':\n        case 'ArrowUp':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 0, -1);\n          break;\n        case 'Down':\n        case 'ArrowDown':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 0, 1);\n          break;\n      }\n    };\n\n    return moveProps;\n  }, [state, addGlobalListener, removeGlobalListener, move, end]);\n\n  return {moveProps};\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAkCO,SAASA,0CAAQC,KAAiB;EACvC,IAAI;IAAAC,WAAA,EAACA,WAAW;IAAAC,MAAA,EAAEA,MAAM;IAAAC,SAAA,EAAEA;EAAS,CAAC,GAAGH,KAAA;EAEvC,IAAII,KAAA,GAAQ,IAAAC,aAAK,EAId;IAACC,OAAA,EAAS;IAAOC,YAAA,EAAc;IAAMC,EAAA,EAAI;EAAI;EAEhD,IAAI;IAAAC,iBAAA,EAACA,iBAAiB;IAAAC,oBAAA,EAAEA;EAAoB,CAAC,GAAG,IAAAC,yBAAiB;EAEjE,IAAIC,IAAA,GAAO,IAAAC,qBAAa,EAAE,CAACC,aAAA,EAA0BC,WAAA,EAA0BC,MAAA,EAAgBC,MAAA;IAC7F,IAAID,MAAA,KAAW,KAAKC,MAAA,KAAW,GAC7B;IAGF,IAAI,CAACb,KAAA,CAAMc,OAAO,CAACZ,OAAO,EAAE;MAC1BF,KAAA,CAAMc,OAAO,CAACZ,OAAO,GAAG;MACxBL,WAAA,aAAAA,WAAA,uBAAAA,WAAA,CAAc;QACZkB,IAAA,EAAM;qBACNJ,WAAA;QACAK,QAAA,EAAUN,aAAA,CAAcM,QAAQ;QAChCC,OAAA,EAASP,aAAA,CAAcO,OAAO;QAC9BC,OAAA,EAASR,aAAA,CAAcQ,OAAO;QAC9BC,MAAA,EAAQT,aAAA,CAAcS;MACxB;IACF;IACArB,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAS;MACPiB,IAAA,EAAM;mBACNJ,WAAA;MACAC,MAAA,EAAQA,MAAA;MACRC,MAAA,EAAQA,MAAA;MACRG,QAAA,EAAUN,aAAA,CAAcM,QAAQ;MAChCC,OAAA,EAASP,aAAA,CAAcO,OAAO;MAC9BC,OAAA,EAASR,aAAA,CAAcQ,OAAO;MAC9BC,MAAA,EAAQT,aAAA,CAAcS;IACxB;EACF;EAEA,IAAIC,GAAA,GAAM,IAAAX,qBAAa,EAAE,CAACC,aAAA,EAA0BC,WAAA;IAClD,IAAAU,yCAAmB;IACnB,IAAIrB,KAAA,CAAMc,OAAO,CAACZ,OAAO,EACvBH,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAY;MACVgB,IAAA,EAAM;mBACNJ,WAAA;MACAK,QAAA,EAAUN,aAAA,CAAcM,QAAQ;MAChCC,OAAA,EAASP,aAAA,CAAcO,OAAO;MAC9BC,OAAA,EAASR,aAAA,CAAcQ,OAAO;MAC9BC,MAAA,EAAQT,aAAA,CAAcS;IACxB;EAEJ;EAEA,IAAIG,SAAA,GAAY,IAAAC,cAAM,EAAE;IACtB,IAAID,SAAA,GAA2B,CAAC;IAEhC,IAAIE,KAAA,GAAQA,CAAA;MACV,IAAAC,yCAAmB;MACnBzB,KAAA,CAAMc,OAAO,CAACZ,OAAO,GAAG;IAC1B;IAEA,IAAI,OAAOwB,YAAA,KAAiB,eAAeC,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;MAC1E,IAAIC,WAAA,GAAeC,CAAA;QACjB,IAAIA,CAAA,CAAEC,MAAM,KAAK,GAAG;cACUC,2BAAA,EAAoDC,4BAAA;cAApDC,iCAAA,EAAoDC,iCAAA;UAAhF5B,IAAA,CAAKuB,CAAA,EAAG,SAASA,CAAA,CAAEM,KAAK,IAAI,CAAAF,iCAAA,IAAAF,2BAAA,GAAAjC,KAAA,CAAMc,OAAO,CAACX,YAAY,cAA1B8B,2BAAA,uBAAAA,2BAAA,CAA4BI,KAAK,cAAjCF,iCAAA,cAAAA,iCAAA,GAAqC,IAAIJ,CAAA,CAAEO,KAAK,IAAI,CAAAF,iCAAA,IAAAF,4BAAA,GAAAlC,KAAA,CAAMc,OAAO,CAACX,YAAY,cAA1B+B,4BAAA,uBAAAA,4BAAA,CAA4BI,KAAK,cAAjCF,iCAAA,cAAAA,iCAAA,GAAqC;UACrHpC,KAAA,CAAMc,OAAO,CAACX,YAAY,GAAG;YAACkC,KAAA,EAAON,CAAA,CAAEM,KAAK;YAAEC,KAAA,EAAOP,CAAA,CAAEO;UAAK;QAC9D;MACF;MACA,IAAIC,SAAA,GAAaR,CAAA;QACf,IAAIA,CAAA,CAAEC,MAAM,KAAK,GAAG;UAClBZ,GAAA,CAAIW,CAAA,EAAG;UACPzB,oBAAA,CAAqBkC,MAAA,EAAQ,aAAaV,WAAA,EAAa;UACvDxB,oBAAA,CAAqBkC,MAAA,EAAQ,WAAWD,SAAA,EAAW;QACrD;MACF;MACAjB,SAAA,CAAUmB,WAAW,GAAIV,CAAA;QACvB,IAAIA,CAAA,CAAEC,MAAM,KAAK,GAAG;UAClBR,KAAA;UACAO,CAAA,CAAEW,eAAe;UACjBX,CAAA,CAAEY,cAAc;UAChB3C,KAAA,CAAMc,OAAO,CAACX,YAAY,GAAG;YAACkC,KAAA,EAAON,CAAA,CAAEM,KAAK;YAAEC,KAAA,EAAOP,CAAA,CAAEO;UAAK;UAC5DjC,iBAAA,CAAkBmC,MAAA,EAAQ,aAAaV,WAAA,EAAa;UACpDzB,iBAAA,CAAkBmC,MAAA,EAAQ,WAAWD,SAAA,EAAW;QAClD;MACF;MAEA,IAAIK,WAAA,GAAeb,CAAA;QACjB,IAAIc,KAAA,GAAQ,C,GAAId,CAAA,CAAEe,cAAc,CAAC,CAACC,SAAS,CAACC,IAAA;UAAA,IAAC;YAAAC,UAAA,EAACA;UAAU,CAAC,GAAAD,IAAA;UAAA,OAAKC,UAAA,KAAejD,KAAA,CAAMc,OAAO,CAACV,EAAE;QAAA;QAC7F,IAAIyC,KAAA,IAAS,GAAG;cAEYZ,2BAAA,EAAkDC,4BAAA;UAD5E,IAAI;YAAAG,KAAA,EAACA,KAAK;YAAAC,KAAA,EAAEA;UAAK,CAAC,GAAGP,CAAA,CAAEe,cAAc,CAACD,KAAA,CAAM;cAClBV,iCAAA,EAAkDC,iCAAA;UAA5E5B,IAAA,CAAKuB,CAAA,EAAG,SAASM,KAAA,IAAS,CAAAF,iCAAA,IAAAF,2BAAA,GAAAjC,KAAA,CAAMc,OAAO,CAACX,YAAY,cAA1B8B,2BAAA,uBAAAA,2BAAA,CAA4BI,KAAK,cAAjCF,iCAAA,cAAAA,iCAAA,GAAqC,IAAIG,KAAA,IAAS,CAAAF,iCAAA,IAAAF,4BAAA,GAAAlC,KAAA,CAAMc,OAAO,CAACX,YAAY,cAA1B+B,4BAAA,uBAAAA,4BAAA,CAA4BI,KAAK,cAAjCF,iCAAA,cAAAA,iCAAA,GAAqC;UACjHpC,KAAA,CAAMc,OAAO,CAACX,YAAY,GAAG;mBAACkC,KAAA;mBAAOC;UAAK;QAC5C;MACF;MACA,IAAIY,UAAA,GAAcnB,CAAA;QAChB,IAAIc,KAAA,GAAQ,C,GAAId,CAAA,CAAEe,cAAc,CAAC,CAACC,SAAS,CAACI,KAAA;UAAA,IAAC;YAAAF,UAAA,EAACA;UAAU,CAAC,GAAAE,KAAA;UAAA,OAAKF,UAAA,KAAejD,KAAA,CAAMc,OAAO,CAACV,EAAE;QAAA;QAC7F,IAAIyC,KAAA,IAAS,GAAG;UACdzB,GAAA,CAAIW,CAAA,EAAG;UACP/B,KAAA,CAAMc,OAAO,CAACV,EAAE,GAAG;UACnBE,oBAAA,CAAqBkC,MAAA,EAAQ,aAAaI,WAAA;UAC1CtC,oBAAA,CAAqBkC,MAAA,EAAQ,YAAYU,UAAA;UACzC5C,oBAAA,CAAqBkC,MAAA,EAAQ,eAAeU,UAAA;QAC9C;MACF;MACA5B,SAAA,CAAU8B,YAAY,GAAIrB,CAAA;QACxB,IAAIA,CAAA,CAAEe,cAAc,CAACO,MAAM,KAAK,KAAKrD,KAAA,CAAMc,OAAO,CAACV,EAAE,IAAI,MACvD;QAGF,IAAI;UAAAiC,KAAA,EAACA,KAAK;UAAAC,KAAA,EAAEA,KAAK;UAAAW,UAAA,EAAEA;QAAU,CAAC,GAAGlB,CAAA,CAAEe,cAAc,CAAC,EAAE;QACpDtB,KAAA;QACAO,CAAA,CAAEW,eAAe;QACjBX,CAAA,CAAEY,cAAc;QAChB3C,KAAA,CAAMc,OAAO,CAACX,YAAY,GAAG;iBAACkC,KAAA;iBAAOC;QAAK;QAC1CtC,KAAA,CAAMc,OAAO,CAACV,EAAE,GAAG6C,UAAA;QACnB5C,iBAAA,CAAkBmC,MAAA,EAAQ,aAAaI,WAAA,EAAa;QACpDvC,iBAAA,CAAkBmC,MAAA,EAAQ,YAAYU,UAAA,EAAY;QAClD7C,iBAAA,CAAkBmC,MAAA,EAAQ,eAAeU,UAAA,EAAY;MACvD;IACF,OAAO;MACL,IAAII,aAAA,GAAiBvB,CAAA;QACnB,IAAIA,CAAA,CAAEwB,SAAS,KAAKvD,KAAA,CAAMc,OAAO,CAACV,EAAE,EAAE;cAMJ6B,2BAAA,EAAoDC,4BAAA;UALpF,IAAIvB,WAAA,GAAeoB,CAAA,CAAEpB,WAAW,IAAI;cAKJwB,iCAAA,EAAoDC,iCAAA;UAHpF;UACA;UACA;UACA5B,IAAA,CAAKuB,CAAA,EAAGpB,WAAA,EAAaoB,CAAA,CAAEM,KAAK,IAAI,CAAAF,iCAAA,IAAAF,2BAAA,GAAAjC,KAAA,CAAMc,OAAO,CAACX,YAAY,cAA1B8B,2BAAA,uBAAAA,2BAAA,CAA4BI,KAAK,cAAjCF,iCAAA,cAAAA,iCAAA,GAAqC,IAAIJ,CAAA,CAAEO,KAAK,IAAI,CAAAF,iCAAA,IAAAF,4BAAA,GAAAlC,KAAA,CAAMc,OAAO,CAACX,YAAY,cAA1B+B,4BAAA,uBAAAA,4BAAA,CAA4BI,KAAK,cAAjCF,iCAAA,cAAAA,iCAAA,GAAqC;UACzHpC,KAAA,CAAMc,OAAO,CAACX,YAAY,GAAG;YAACkC,KAAA,EAAON,CAAA,CAAEM,KAAK;YAAEC,KAAA,EAAOP,CAAA,CAAEO;UAAK;QAC9D;MACF;MAEA,IAAIkB,WAAA,GAAezB,CAAA;QACjB,IAAIA,CAAA,CAAEwB,SAAS,KAAKvD,KAAA,CAAMc,OAAO,CAACV,EAAE,EAAE;UACpC,IAAIO,WAAA,GAAeoB,CAAA,CAAEpB,WAAW,IAAI;UACpCS,GAAA,CAAIW,CAAA,EAAGpB,WAAA;UACPX,KAAA,CAAMc,OAAO,CAACV,EAAE,GAAG;UACnBE,oBAAA,CAAqBkC,MAAA,EAAQ,eAAec,aAAA,EAAe;UAC3DhD,oBAAA,CAAqBkC,MAAA,EAAQ,aAAagB,WAAA,EAAa;UACvDlD,oBAAA,CAAqBkC,MAAA,EAAQ,iBAAiBgB,WAAA,EAAa;QAC7D;MACF;MAEAlC,SAAA,CAAUmC,aAAa,GAAI1B,CAAA;QACzB,IAAIA,CAAA,CAAEC,MAAM,KAAK,KAAKhC,KAAA,CAAMc,OAAO,CAACV,EAAE,IAAI,MAAM;UAC9CoB,KAAA;UACAO,CAAA,CAAEW,eAAe;UACjBX,CAAA,CAAEY,cAAc;UAChB3C,KAAA,CAAMc,OAAO,CAACX,YAAY,GAAG;YAACkC,KAAA,EAAON,CAAA,CAAEM,KAAK;YAAEC,KAAA,EAAOP,CAAA,CAAEO;UAAK;UAC5DtC,KAAA,CAAMc,OAAO,CAACV,EAAE,GAAG2B,CAAA,CAAEwB,SAAS;UAC9BlD,iBAAA,CAAkBmC,MAAA,EAAQ,eAAec,aAAA,EAAe;UACxDjD,iBAAA,CAAkBmC,MAAA,EAAQ,aAAagB,WAAA,EAAa;UACpDnD,iBAAA,CAAkBmC,MAAA,EAAQ,iBAAiBgB,WAAA,EAAa;QAC1D;MACF;IACF;IAEA,IAAIE,mBAAA,GAAsBA,CAAC3B,CAAA,EAAcnB,MAAA,EAAgBC,MAAA;MACvDW,KAAA;MACAhB,IAAA,CAAKuB,CAAA,EAAG,YAAYnB,MAAA,EAAQC,MAAA;MAC5BO,GAAA,CAAIW,CAAA,EAAG;IACT;IAEAT,SAAA,CAAUqC,SAAS,GAAI5B,CAAA;MACrB,QAAQA,CAAA,CAAE6B,GAAG;QACX,KAAK;QACL,KAAK;UACH7B,CAAA,CAAEY,cAAc;UAChBZ,CAAA,CAAEW,eAAe;UACjBgB,mBAAA,CAAoB3B,CAAA,EAAG,IAAI;UAC3B;QACF,KAAK;QACL,KAAK;UACHA,CAAA,CAAEY,cAAc;UAChBZ,CAAA,CAAEW,eAAe;UACjBgB,mBAAA,CAAoB3B,CAAA,EAAG,GAAG;UAC1B;QACF,KAAK;QACL,KAAK;UACHA,CAAA,CAAEY,cAAc;UAChBZ,CAAA,CAAEW,eAAe;UACjBgB,mBAAA,CAAoB3B,CAAA,EAAG,GAAG;UAC1B;QACF,KAAK;QACL,KAAK;UACHA,CAAA,CAAEY,cAAc;UAChBZ,CAAA,CAAEW,eAAe;UACjBgB,mBAAA,CAAoB3B,CAAA,EAAG,GAAG;UAC1B;MACJ;IACF;IAEA,OAAOT,SAAA;EACT,GAAG,CAACtB,KAAA,EAAOK,iBAAA,EAAmBC,oBAAA,EAAsBE,IAAA,EAAMY,GAAA,CAAI;EAE9D,OAAO;eAACE;EAAS;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}