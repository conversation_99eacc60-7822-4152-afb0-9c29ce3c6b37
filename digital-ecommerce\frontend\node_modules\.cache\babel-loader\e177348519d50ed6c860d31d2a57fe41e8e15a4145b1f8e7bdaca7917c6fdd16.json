{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"__demoMode\"],\n  _excluded2 = [\"id\", \"disabled\", \"autoFocus\"],\n  _excluded3 = [\"id\", \"anchor\", \"portal\", \"modal\", \"transition\"],\n  _excluded4 = [\"id\", \"disabled\"],\n  _excluded5 = [\"id\"];\nimport { useFocusRing as Te } from \"@react-aria/focus\";\nimport { useHover as fe } from \"@react-aria/interactions\";\nimport x, { Fragment as ee, useCallback as k, useEffect as ye, useMemo as K, useRef as Q, useState as Pe } from \"react\";\nimport { flushSync as $ } from \"react-dom\";\nimport { useActivePress as Ee } from '../../hooks/use-active-press.js';\nimport { useDidElementMove as ge } from '../../hooks/use-did-element-move.js';\nimport { useDisposables as Me } from '../../hooks/use-disposables.js';\nimport { useElementSize as be } from '../../hooks/use-element-size.js';\nimport { useEvent as P } from '../../hooks/use-event.js';\nimport { useId as B } from '../../hooks/use-id.js';\nimport { useInertOthers as Ae } from '../../hooks/use-inert-others.js';\nimport { useIsoMorphicEffect as W } from '../../hooks/use-iso-morphic-effect.js';\nimport { useOnDisappear as _e } from '../../hooks/use-on-disappear.js';\nimport { useOutsideClick as Ie } from '../../hooks/use-outside-click.js';\nimport { useOwnerDocument as te } from '../../hooks/use-owner.js';\nimport { Action as J, useQuickRelease as Se } from '../../hooks/use-quick-release.js';\nimport { useResolveButtonType as Re } from '../../hooks/use-resolve-button-type.js';\nimport { useScrollLock as De } from '../../hooks/use-scroll-lock.js';\nimport { useSyncRefs as V } from '../../hooks/use-sync-refs.js';\nimport { useTextValue as Fe } from '../../hooks/use-text-value.js';\nimport { useTrackedPointer as he } from '../../hooks/use-tracked-pointer.js';\nimport { transitionDataAttributes as xe, useTransition as Ce } from '../../hooks/use-transition.js';\nimport { useTreeWalker as Le } from '../../hooks/use-tree-walker.js';\nimport { FloatingProvider as ve, useFloatingPanel as Oe, useFloatingPanelProps as He, useFloatingReference as Ue, useFloatingReferenceProps as Ge, useResolvedAnchor as Ne } from '../../internal/floating.js';\nimport { OpenClosedProvider as ke, State as X, useOpenClosed as Be } from '../../internal/open-closed.js';\nimport { stackMachines as we } from '../../machines/stack-machine.js';\nimport { useSlice as D } from '../../react-glue.js';\nimport { isDisabledReactIssue7711 as Ke } from '../../utils/bugs.js';\nimport { Focus as A } from '../../utils/calculate-active-index.js';\nimport { disposables as We } from '../../utils/disposables.js';\nimport * as Je from '../../utils/dom.js';\nimport { Focus as oe, FocusableMode as Ve, focusFrom as Xe, isFocusableElement as Qe, restoreFocusIfNecessary as ne } from '../../utils/focus-management.js';\nimport { match as $e } from '../../utils/match.js';\nimport { RenderFeatures as re, forwardRefWithAs as C, mergeProps as ae, useRender as L } from '../../utils/render.js';\nimport { useDescriptions as je } from '../description/description.js';\nimport { Keys as d } from '../keyboard.js';\nimport { useLabelContext as qe, useLabels as se } from '../label/label.js';\nimport { Portal as ze } from '../portal/portal.js';\nimport { ActionTypes as r, ActivationTrigger as j, MenuState as m } from './menu-machine.js';\nimport { MenuContext as Ye, useMenuMachine as Ze, useMenuMachineContext as q } from './menu-machine-glue.js';\nlet et = ee;\nfunction tt(c, E) {\n  let p = B(),\n    {\n      __demoMode: a = !1\n    } = c,\n    s = _objectWithoutProperties(c, _excluded),\n    l = Ze({\n      id: p,\n      __demoMode: a\n    }),\n    [n, g, y] = D(l, T => [T.menuState, T.itemsElement, T.buttonElement]),\n    I = V(E),\n    o = we.get(null),\n    h = D(o, k(T => o.selectors.isTop(T, p), [o, p]));\n  Ie(h, [y, g], (T, u) => {\n    var f;\n    l.send({\n      type: r.CloseMenu\n    }), Qe(u, Ve.Loose) || (T.preventDefault(), (f = l.state.buttonElement) == null || f.focus());\n  });\n  let _ = P(() => {\n      l.send({\n        type: r.CloseMenu\n      });\n    }),\n    M = K(() => ({\n      open: n === m.Open,\n      close: _\n    }), [n, _]),\n    i = {\n      ref: I\n    },\n    b = L();\n  return x.createElement(ve, null, x.createElement(Ye.Provider, {\n    value: l\n  }, x.createElement(ke, {\n    value: $e(n, {\n      [m.Open]: X.Open,\n      [m.Closed]: X.Closed\n    })\n  }, b({\n    ourProps: i,\n    theirProps: s,\n    slot: M,\n    defaultTag: et,\n    name: \"Menu\"\n  }))));\n}\nlet ot = \"button\";\nfunction nt(c, E) {\n  let p = q(\"Menu.Button\"),\n    a = B(),\n    {\n      id: s = \"headlessui-menu-button-\".concat(a),\n      disabled: l = !1,\n      autoFocus: n = !1\n    } = c,\n    g = _objectWithoutProperties(c, _excluded2),\n    y = Q(null),\n    I = Ge(),\n    o = V(E, y, Ue(), P(e => p.send({\n      type: r.SetButtonElement,\n      element: e\n    }))),\n    h = P(e => {\n      switch (e.key) {\n        case d.Space:\n        case d.Enter:\n        case d.ArrowDown:\n          e.preventDefault(), e.stopPropagation(), p.send({\n            type: r.OpenMenu,\n            focus: {\n              focus: A.First\n            }\n          });\n          break;\n        case d.ArrowUp:\n          e.preventDefault(), e.stopPropagation(), p.send({\n            type: r.OpenMenu,\n            focus: {\n              focus: A.Last\n            }\n          });\n          break;\n      }\n    }),\n    _ = P(e => {\n      switch (e.key) {\n        case d.Space:\n          e.preventDefault();\n          break;\n      }\n    }),\n    [M, i, b] = D(p, e => [e.menuState, e.buttonElement, e.itemsElement]),\n    T = M === m.Open;\n  Se(T, {\n    trigger: i,\n    action: k(e => {\n      if (i != null && i.contains(e.target)) return J.Ignore;\n      let R = e.target.closest('[role=\"menuitem\"]:not([data-disabled])');\n      return Je.isHTMLElement(R) ? J.Select(R) : b != null && b.contains(e.target) ? J.Ignore : J.Close;\n    }, [i, b]),\n    close: k(() => p.send({\n      type: r.CloseMenu\n    }), []),\n    select: k(e => e.click(), [])\n  });\n  let u = P(e => {\n      var R;\n      if (e.button === 0) {\n        if (Ke(e.currentTarget)) return e.preventDefault();\n        l || (M === m.Open ? ($(() => p.send({\n          type: r.CloseMenu\n        })), (R = y.current) == null || R.focus({\n          preventScroll: !0\n        })) : (e.preventDefault(), p.send({\n          type: r.OpenMenu,\n          focus: {\n            focus: A.Nothing\n          },\n          trigger: j.Pointer\n        })));\n      }\n    }),\n    {\n      isFocusVisible: f,\n      focusProps: v\n    } = Te({\n      autoFocus: n\n    }),\n    {\n      isHovered: S,\n      hoverProps: O\n    } = fe({\n      isDisabled: l\n    }),\n    {\n      pressed: F,\n      pressProps: U\n    } = Ee({\n      disabled: l\n    }),\n    H = K(() => ({\n      open: M === m.Open,\n      active: F || M === m.Open,\n      disabled: l,\n      hover: S,\n      focus: f,\n      autofocus: n\n    }), [M, S, f, F, l, n]),\n    G = ae(I(), {\n      ref: o,\n      id: s,\n      type: Re(c, y.current),\n      \"aria-haspopup\": \"menu\",\n      \"aria-controls\": b == null ? void 0 : b.id,\n      \"aria-expanded\": M === m.Open,\n      disabled: l || void 0,\n      autoFocus: n,\n      onKeyDown: h,\n      onKeyUp: _,\n      onPointerDown: u\n    }, v, O, U);\n  return L()({\n    ourProps: G,\n    theirProps: g,\n    slot: H,\n    defaultTag: ot,\n    name: \"Menu.Button\"\n  });\n}\nlet rt = \"div\",\n  at = re.RenderStrategy | re.Static;\nfunction st(c, E) {\n  let p = B(),\n    {\n      id: a = \"headlessui-menu-items-\".concat(p),\n      anchor: s,\n      portal: l = !1,\n      modal: n = !0,\n      transition: g = !1\n    } = c,\n    y = _objectWithoutProperties(c, _excluded3),\n    I = Ne(s),\n    o = q(\"Menu.Items\"),\n    [h, _] = Oe(I),\n    M = He(),\n    [i, b] = Pe(null),\n    T = V(E, I ? h : null, P(t => o.send({\n      type: r.SetItemsElement,\n      element: t\n    })), b),\n    [u, f] = D(o, t => [t.menuState, t.buttonElement]),\n    v = te(f),\n    S = te(i);\n  I && (l = !0);\n  let O = Be(),\n    [F, U] = Ce(g, i, O !== null ? (O & X.Open) === X.Open : u === m.Open);\n  _e(F, f, () => {\n    o.send({\n      type: r.CloseMenu\n    });\n  });\n  let H = D(o, t => t.__demoMode),\n    G = H ? !1 : n && u === m.Open;\n  De(G, S);\n  let w = H ? !1 : n && u === m.Open;\n  Ae(w, {\n    allowed: k(() => [f, i], [f, i])\n  });\n  let e = u !== m.Open,\n    le = ge(e, f) ? !1 : F;\n  ye(() => {\n    let t = i;\n    t && u === m.Open && t !== (S == null ? void 0 : S.activeElement) && t.focus({\n      preventScroll: !0\n    });\n  }, [u, i, S]), Le(u === m.Open, {\n    container: i,\n    accept(t) {\n      return t.getAttribute(\"role\") === \"menuitem\" ? NodeFilter.FILTER_REJECT : t.hasAttribute(\"role\") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;\n    },\n    walk(t) {\n      t.setAttribute(\"role\", \"none\");\n    }\n  });\n  let z = Me(),\n    pe = P(t => {\n      var N, Y, Z;\n      switch (z.dispose(), t.key) {\n        case d.Space:\n          if (o.state.searchQuery !== \"\") return t.preventDefault(), t.stopPropagation(), o.send({\n            type: r.Search,\n            value: t.key\n          });\n        case d.Enter:\n          if (t.preventDefault(), t.stopPropagation(), o.state.activeItemIndex !== null) {\n            let {\n              dataRef: ce\n            } = o.state.items[o.state.activeItemIndex];\n            (Y = (N = ce.current) == null ? void 0 : N.domRef.current) == null || Y.click();\n          }\n          o.send({\n            type: r.CloseMenu\n          }), ne(o.state.buttonElement);\n          break;\n        case d.ArrowDown:\n          return t.preventDefault(), t.stopPropagation(), o.send({\n            type: r.GoToItem,\n            focus: A.Next\n          });\n        case d.ArrowUp:\n          return t.preventDefault(), t.stopPropagation(), o.send({\n            type: r.GoToItem,\n            focus: A.Previous\n          });\n        case d.Home:\n        case d.PageUp:\n          return t.preventDefault(), t.stopPropagation(), o.send({\n            type: r.GoToItem,\n            focus: A.First\n          });\n        case d.End:\n        case d.PageDown:\n          return t.preventDefault(), t.stopPropagation(), o.send({\n            type: r.GoToItem,\n            focus: A.Last\n          });\n        case d.Escape:\n          t.preventDefault(), t.stopPropagation(), $(() => o.send({\n            type: r.CloseMenu\n          })), (Z = o.state.buttonElement) == null || Z.focus({\n            preventScroll: !0\n          });\n          break;\n        case d.Tab:\n          t.preventDefault(), t.stopPropagation(), $(() => o.send({\n            type: r.CloseMenu\n          })), Xe(o.state.buttonElement, t.shiftKey ? oe.Previous : oe.Next);\n          break;\n        default:\n          t.key.length === 1 && (o.send({\n            type: r.Search,\n            value: t.key\n          }), z.setTimeout(() => o.send({\n            type: r.ClearSearch\n          }), 350));\n          break;\n      }\n    }),\n    ie = P(t => {\n      switch (t.key) {\n        case d.Space:\n          t.preventDefault();\n          break;\n      }\n    }),\n    ue = K(() => ({\n      open: u === m.Open\n    }), [u]),\n    de = ae(I ? M() : {}, _objectSpread({\n      \"aria-activedescendant\": D(o, o.selectors.activeDescendantId),\n      \"aria-labelledby\": D(o, t => {\n        var N;\n        return (N = t.buttonElement) == null ? void 0 : N.id;\n      }),\n      id: a,\n      onKeyDown: pe,\n      onKeyUp: ie,\n      role: \"menu\",\n      tabIndex: u === m.Open ? 0 : void 0,\n      ref: T,\n      style: _objectSpread(_objectSpread(_objectSpread({}, y.style), _), {}, {\n        \"--button-width\": be(f, !0).width\n      })\n    }, xe(U))),\n    me = L();\n  return x.createElement(ze, {\n    enabled: l ? c.static || F : !1,\n    ownerDocument: v\n  }, me({\n    ourProps: de,\n    theirProps: y,\n    slot: ue,\n    defaultTag: rt,\n    features: at,\n    visible: le,\n    name: \"Menu.Items\"\n  }));\n}\nlet lt = ee;\nfunction pt(c, E) {\n  let p = B(),\n    {\n      id: a = \"headlessui-menu-item-\".concat(p),\n      disabled: s = !1\n    } = c,\n    l = _objectWithoutProperties(c, _excluded4),\n    n = q(\"Menu.Item\"),\n    g = D(n, e => n.selectors.isActive(e, a)),\n    y = Q(null),\n    I = V(E, y),\n    o = D(n, e => n.selectors.shouldScrollIntoView(e, a));\n  W(() => {\n    if (o) return We().requestAnimationFrame(() => {\n      var e, R;\n      (R = (e = y.current) == null ? void 0 : e.scrollIntoView) == null || R.call(e, {\n        block: \"nearest\"\n      });\n    });\n  }, [o, y]);\n  let h = Fe(y),\n    _ = Q({\n      disabled: s,\n      domRef: y,\n      get textValue() {\n        return h();\n      }\n    });\n  W(() => {\n    _.current.disabled = s;\n  }, [_, s]), W(() => (n.actions.registerItem(a, _), () => n.actions.unregisterItem(a)), [_, a]);\n  let M = P(() => {\n      n.send({\n        type: r.CloseMenu\n      });\n    }),\n    i = P(e => {\n      if (s) return e.preventDefault();\n      n.send({\n        type: r.CloseMenu\n      }), ne(n.state.buttonElement);\n    }),\n    b = P(() => {\n      if (s) return n.send({\n        type: r.GoToItem,\n        focus: A.Nothing\n      });\n      n.send({\n        type: r.GoToItem,\n        focus: A.Specific,\n        id: a\n      });\n    }),\n    T = he(),\n    u = P(e => {\n      T.update(e), !s && (g || n.send({\n        type: r.GoToItem,\n        focus: A.Specific,\n        id: a,\n        trigger: j.Pointer\n      }));\n    }),\n    f = P(e => {\n      T.wasMoved(e) && (s || g || n.send({\n        type: r.GoToItem,\n        focus: A.Specific,\n        id: a,\n        trigger: j.Pointer\n      }));\n    }),\n    v = P(e => {\n      T.wasMoved(e) && (s || g && n.send({\n        type: r.GoToItem,\n        focus: A.Nothing\n      }));\n    }),\n    [S, O] = se(),\n    [F, U] = je(),\n    H = K(() => ({\n      active: g,\n      focus: g,\n      disabled: s,\n      close: M\n    }), [g, s, M]),\n    G = {\n      id: a,\n      ref: I,\n      role: \"menuitem\",\n      tabIndex: s === !0 ? void 0 : -1,\n      \"aria-disabled\": s === !0 ? !0 : void 0,\n      \"aria-labelledby\": S,\n      \"aria-describedby\": F,\n      disabled: void 0,\n      onClick: i,\n      onFocus: b,\n      onPointerEnter: u,\n      onMouseEnter: u,\n      onPointerMove: f,\n      onMouseMove: f,\n      onPointerLeave: v,\n      onMouseLeave: v\n    },\n    w = L();\n  return x.createElement(O, null, x.createElement(U, null, w({\n    ourProps: G,\n    theirProps: l,\n    slot: H,\n    defaultTag: lt,\n    name: \"Menu.Item\"\n  })));\n}\nlet it = \"div\";\nfunction ut(c, E) {\n  let [p, a] = se(),\n    s = c,\n    l = {\n      ref: E,\n      \"aria-labelledby\": p,\n      role: \"group\"\n    },\n    n = L();\n  return x.createElement(a, null, n({\n    ourProps: l,\n    theirProps: s,\n    slot: {},\n    defaultTag: it,\n    name: \"Menu.Section\"\n  }));\n}\nlet dt = \"header\";\nfunction mt(c, E) {\n  let p = B(),\n    {\n      id: a = \"headlessui-menu-heading-\".concat(p)\n    } = c,\n    s = _objectWithoutProperties(c, _excluded5),\n    l = qe();\n  W(() => l.register(a), [a, l.register]);\n  let n = _objectSpread({\n    id: a,\n    ref: E,\n    role: \"presentation\"\n  }, l.props);\n  return L()({\n    ourProps: n,\n    theirProps: s,\n    slot: {},\n    defaultTag: dt,\n    name: \"Menu.Heading\"\n  });\n}\nlet ct = \"div\";\nfunction Tt(c, E) {\n  let p = c,\n    a = {\n      ref: E,\n      role: \"separator\"\n    };\n  return L()({\n    ourProps: a,\n    theirProps: p,\n    slot: {},\n    defaultTag: ct,\n    name: \"Menu.Separator\"\n  });\n}\nlet ft = C(tt),\n  yt = C(nt),\n  Pt = C(st),\n  Et = C(pt),\n  gt = C(ut),\n  Mt = C(mt),\n  bt = C(Tt),\n  lo = Object.assign(ft, {\n    Button: yt,\n    Items: Pt,\n    Item: Et,\n    Section: gt,\n    Heading: Mt,\n    Separator: bt\n  });\nexport { lo as Menu, yt as MenuButton, Mt as MenuHeading, Et as MenuItem, Pt as MenuItems, gt as MenuSection, bt as MenuSeparator };", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "_excluded3", "_excluded4", "_excluded5", "useFocusRing", "Te", "useHover", "fe", "x", "Fragment", "ee", "useCallback", "k", "useEffect", "ye", "useMemo", "K", "useRef", "Q", "useState", "Pe", "flushSync", "$", "useActivePress", "Ee", "useDidElementMove", "ge", "useDisposables", "Me", "useElementSize", "be", "useEvent", "P", "useId", "B", "useInertOthers", "Ae", "useIsoMorphicEffect", "W", "useOnDisappear", "_e", "useOutsideClick", "Ie", "useOwnerDocument", "te", "Action", "J", "useQuickRelease", "Se", "useResolveButtonType", "Re", "useScrollLock", "De", "useSyncRefs", "V", "useTextValue", "Fe", "useTrackedPointer", "he", "transitionDataAttributes", "xe", "useTransition", "Ce", "useTreeWalker", "Le", "FloatingProvider", "ve", "useFloatingPanel", "Oe", "useFloatingPanelProps", "He", "useFloatingReference", "Ue", "useFloatingReferenceProps", "Ge", "useResolvedAnchor", "Ne", "OpenClosedProvider", "ke", "State", "X", "useOpenClosed", "Be", "stackMachines", "we", "useSlice", "D", "isDisabledReactIssue7711", "<PERSON>", "Focus", "A", "disposables", "We", "Je", "oe", "FocusableMode", "Ve", "focusFrom", "Xe", "isFocusableElement", "Qe", "restoreFocusIfNecessary", "ne", "match", "$e", "RenderFeatures", "re", "forwardRefWithAs", "C", "mergeProps", "ae", "useRender", "L", "useDescriptions", "je", "Keys", "d", "useLabelContext", "qe", "useLabels", "se", "Portal", "ze", "ActionTypes", "r", "ActivationTrigger", "j", "MenuState", "m", "MenuContext", "Ye", "useMenuMachine", "Ze", "useMenuMachineContext", "q", "et", "tt", "c", "E", "p", "__demoMode", "a", "s", "l", "id", "n", "g", "y", "T", "menuState", "itemsElement", "buttonElement", "I", "o", "get", "h", "selectors", "isTop", "u", "f", "send", "type", "CloseMenu", "Loose", "preventDefault", "state", "focus", "_", "M", "open", "Open", "close", "i", "ref", "b", "createElement", "Provider", "value", "Closed", "ourProps", "theirProps", "slot", "defaultTag", "name", "ot", "nt", "concat", "disabled", "autoFocus", "e", "SetButtonElement", "element", "key", "Space", "Enter", "ArrowDown", "stopPropagation", "OpenMenu", "First", "ArrowUp", "Last", "trigger", "action", "contains", "target", "Ignore", "R", "closest", "isHTMLElement", "Select", "Close", "select", "click", "button", "currentTarget", "current", "preventScroll", "Nothing", "Pointer", "isFocusVisible", "focusProps", "v", "isHovered", "S", "hoverProps", "O", "isDisabled", "pressed", "F", "pressProps", "U", "H", "active", "hover", "autofocus", "G", "onKeyDown", "onKeyUp", "onPointerDown", "rt", "at", "RenderStrategy", "Static", "st", "anchor", "portal", "modal", "transition", "t", "SetItemsElement", "w", "allowed", "le", "activeElement", "container", "accept", "getAttribute", "Node<PERSON><PERSON><PERSON>", "FILTER_REJECT", "hasAttribute", "FILTER_SKIP", "FILTER_ACCEPT", "walk", "setAttribute", "z", "pe", "N", "Y", "Z", "dispose", "searchQuery", "Search", "activeItemIndex", "dataRef", "ce", "items", "domRef", "GoToItem", "Next", "Previous", "Home", "PageUp", "End", "PageDown", "Escape", "Tab", "shift<PERSON>ey", "length", "setTimeout", "ClearSearch", "ie", "ue", "de", "activeDescendantId", "role", "tabIndex", "style", "width", "me", "enabled", "static", "ownerDocument", "features", "visible", "lt", "pt", "isActive", "shouldScrollIntoView", "requestAnimationFrame", "scrollIntoView", "call", "block", "textValue", "actions", "registerItem", "unregisterItem", "Specific", "update", "wasMoved", "onClick", "onFocus", "onPointerEnter", "onMouseEnter", "onPointerMove", "onMouseMove", "onPointerLeave", "onMouseLeave", "it", "ut", "dt", "mt", "register", "props", "ct", "Tt", "ft", "yt", "Pt", "Et", "gt", "Mt", "bt", "lo", "Object", "assign", "<PERSON><PERSON>", "Items", "<PERSON><PERSON>", "Section", "Heading", "Separator", "<PERSON><PERSON>", "MenuButton", "MenuHeading", "MenuItem", "MenuItems", "MenuSection", "MenuSeparator"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/menu/menu.js"], "sourcesContent": ["\"use client\";import{useFocusRing as Te}from\"@react-aria/focus\";import{useHover as fe}from\"@react-aria/interactions\";import x,{Fragment as ee,useCallback as k,useEffect as ye,useMemo as K,useRef as Q,useState as Pe}from\"react\";import{flushSync as $}from\"react-dom\";import{useActivePress as Ee}from'../../hooks/use-active-press.js';import{useDidElementMove as ge}from'../../hooks/use-did-element-move.js';import{useDisposables as Me}from'../../hooks/use-disposables.js';import{useElementSize as be}from'../../hooks/use-element-size.js';import{useEvent as P}from'../../hooks/use-event.js';import{useId as B}from'../../hooks/use-id.js';import{useInertOthers as Ae}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as W}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as _e}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ie}from'../../hooks/use-outside-click.js';import{useOwnerDocument as te}from'../../hooks/use-owner.js';import{Action as J,useQuickRelease as Se}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Re}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as De}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as V}from'../../hooks/use-sync-refs.js';import{useTextValue as Fe}from'../../hooks/use-text-value.js';import{useTrackedPointer as he}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as xe,useTransition as Ce}from'../../hooks/use-transition.js';import{useTreeWalker as Le}from'../../hooks/use-tree-walker.js';import{FloatingProvider as ve,useFloatingPanel as Oe,useFloatingPanelProps as He,useFloatingReference as Ue,useFloatingReferenceProps as Ge,useResolvedAnchor as Ne}from'../../internal/floating.js';import{OpenClosedProvider as ke,State as X,useOpenClosed as Be}from'../../internal/open-closed.js';import{stackMachines as we}from'../../machines/stack-machine.js';import{useSlice as D}from'../../react-glue.js';import{isDisabledReactIssue7711 as Ke}from'../../utils/bugs.js';import{Focus as A}from'../../utils/calculate-active-index.js';import{disposables as We}from'../../utils/disposables.js';import*as Je from'../../utils/dom.js';import{Focus as oe,FocusableMode as Ve,focusFrom as Xe,isFocusableElement as Qe,restoreFocusIfNecessary as ne}from'../../utils/focus-management.js';import{match as $e}from'../../utils/match.js';import{RenderFeatures as re,forwardRefWithAs as C,mergeProps as ae,useRender as L}from'../../utils/render.js';import{useDescriptions as je}from'../description/description.js';import{Keys as d}from'../keyboard.js';import{useLabelContext as qe,useLabels as se}from'../label/label.js';import{Portal as ze}from'../portal/portal.js';import{ActionTypes as r,ActivationTrigger as j,MenuState as m}from'./menu-machine.js';import{MenuContext as Ye,useMenuMachine as Ze,useMenuMachineContext as q}from'./menu-machine-glue.js';let et=ee;function tt(c,E){let p=B(),{__demoMode:a=!1,...s}=c,l=Ze({id:p,__demoMode:a}),[n,g,y]=D(l,T=>[T.menuState,T.itemsElement,T.buttonElement]),I=V(E),o=we.get(null),h=D(o,k(T=>o.selectors.isTop(T,p),[o,p]));Ie(h,[y,g],(T,u)=>{var f;l.send({type:r.CloseMenu}),Qe(u,Ve.Loose)||(T.preventDefault(),(f=l.state.buttonElement)==null||f.focus())});let _=P(()=>{l.send({type:r.CloseMenu})}),M=K(()=>({open:n===m.Open,close:_}),[n,_]),i={ref:I},b=L();return x.createElement(ve,null,x.createElement(Ye.Provider,{value:l},x.createElement(ke,{value:$e(n,{[m.Open]:X.Open,[m.Closed]:X.Closed})},b({ourProps:i,theirProps:s,slot:M,defaultTag:et,name:\"Menu\"}))))}let ot=\"button\";function nt(c,E){let p=q(\"Menu.Button\"),a=B(),{id:s=`headlessui-menu-button-${a}`,disabled:l=!1,autoFocus:n=!1,...g}=c,y=Q(null),I=Ge(),o=V(E,y,Ue(),P(e=>p.send({type:r.SetButtonElement,element:e}))),h=P(e=>{switch(e.key){case d.Space:case d.Enter:case d.ArrowDown:e.preventDefault(),e.stopPropagation(),p.send({type:r.OpenMenu,focus:{focus:A.First}});break;case d.ArrowUp:e.preventDefault(),e.stopPropagation(),p.send({type:r.OpenMenu,focus:{focus:A.Last}});break}}),_=P(e=>{switch(e.key){case d.Space:e.preventDefault();break}}),[M,i,b]=D(p,e=>[e.menuState,e.buttonElement,e.itemsElement]),T=M===m.Open;Se(T,{trigger:i,action:k(e=>{if(i!=null&&i.contains(e.target))return J.Ignore;let R=e.target.closest('[role=\"menuitem\"]:not([data-disabled])');return Je.isHTMLElement(R)?J.Select(R):b!=null&&b.contains(e.target)?J.Ignore:J.Close},[i,b]),close:k(()=>p.send({type:r.CloseMenu}),[]),select:k(e=>e.click(),[])});let u=P(e=>{var R;if(e.button===0){if(Ke(e.currentTarget))return e.preventDefault();l||(M===m.Open?($(()=>p.send({type:r.CloseMenu})),(R=y.current)==null||R.focus({preventScroll:!0})):(e.preventDefault(),p.send({type:r.OpenMenu,focus:{focus:A.Nothing},trigger:j.Pointer})))}}),{isFocusVisible:f,focusProps:v}=Te({autoFocus:n}),{isHovered:S,hoverProps:O}=fe({isDisabled:l}),{pressed:F,pressProps:U}=Ee({disabled:l}),H=K(()=>({open:M===m.Open,active:F||M===m.Open,disabled:l,hover:S,focus:f,autofocus:n}),[M,S,f,F,l,n]),G=ae(I(),{ref:o,id:s,type:Re(c,y.current),\"aria-haspopup\":\"menu\",\"aria-controls\":b==null?void 0:b.id,\"aria-expanded\":M===m.Open,disabled:l||void 0,autoFocus:n,onKeyDown:h,onKeyUp:_,onPointerDown:u},v,O,U);return L()({ourProps:G,theirProps:g,slot:H,defaultTag:ot,name:\"Menu.Button\"})}let rt=\"div\",at=re.RenderStrategy|re.Static;function st(c,E){let p=B(),{id:a=`headlessui-menu-items-${p}`,anchor:s,portal:l=!1,modal:n=!0,transition:g=!1,...y}=c,I=Ne(s),o=q(\"Menu.Items\"),[h,_]=Oe(I),M=He(),[i,b]=Pe(null),T=V(E,I?h:null,P(t=>o.send({type:r.SetItemsElement,element:t})),b),[u,f]=D(o,t=>[t.menuState,t.buttonElement]),v=te(f),S=te(i);I&&(l=!0);let O=Be(),[F,U]=Ce(g,i,O!==null?(O&X.Open)===X.Open:u===m.Open);_e(F,f,()=>{o.send({type:r.CloseMenu})});let H=D(o,t=>t.__demoMode),G=H?!1:n&&u===m.Open;De(G,S);let w=H?!1:n&&u===m.Open;Ae(w,{allowed:k(()=>[f,i],[f,i])});let e=u!==m.Open,le=ge(e,f)?!1:F;ye(()=>{let t=i;t&&u===m.Open&&t!==(S==null?void 0:S.activeElement)&&t.focus({preventScroll:!0})},[u,i,S]),Le(u===m.Open,{container:i,accept(t){return t.getAttribute(\"role\")===\"menuitem\"?NodeFilter.FILTER_REJECT:t.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(t){t.setAttribute(\"role\",\"none\")}});let z=Me(),pe=P(t=>{var N,Y,Z;switch(z.dispose(),t.key){case d.Space:if(o.state.searchQuery!==\"\")return t.preventDefault(),t.stopPropagation(),o.send({type:r.Search,value:t.key});case d.Enter:if(t.preventDefault(),t.stopPropagation(),o.state.activeItemIndex!==null){let{dataRef:ce}=o.state.items[o.state.activeItemIndex];(Y=(N=ce.current)==null?void 0:N.domRef.current)==null||Y.click()}o.send({type:r.CloseMenu}),ne(o.state.buttonElement);break;case d.ArrowDown:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.Next});case d.ArrowUp:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.Previous});case d.Home:case d.PageUp:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.First});case d.End:case d.PageDown:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.Last});case d.Escape:t.preventDefault(),t.stopPropagation(),$(()=>o.send({type:r.CloseMenu})),(Z=o.state.buttonElement)==null||Z.focus({preventScroll:!0});break;case d.Tab:t.preventDefault(),t.stopPropagation(),$(()=>o.send({type:r.CloseMenu})),Xe(o.state.buttonElement,t.shiftKey?oe.Previous:oe.Next);break;default:t.key.length===1&&(o.send({type:r.Search,value:t.key}),z.setTimeout(()=>o.send({type:r.ClearSearch}),350));break}}),ie=P(t=>{switch(t.key){case d.Space:t.preventDefault();break}}),ue=K(()=>({open:u===m.Open}),[u]),de=ae(I?M():{},{\"aria-activedescendant\":D(o,o.selectors.activeDescendantId),\"aria-labelledby\":D(o,t=>{var N;return(N=t.buttonElement)==null?void 0:N.id}),id:a,onKeyDown:pe,onKeyUp:ie,role:\"menu\",tabIndex:u===m.Open?0:void 0,ref:T,style:{...y.style,..._,\"--button-width\":be(f,!0).width},...xe(U)}),me=L();return x.createElement(ze,{enabled:l?c.static||F:!1,ownerDocument:v},me({ourProps:de,theirProps:y,slot:ue,defaultTag:rt,features:at,visible:le,name:\"Menu.Items\"}))}let lt=ee;function pt(c,E){let p=B(),{id:a=`headlessui-menu-item-${p}`,disabled:s=!1,...l}=c,n=q(\"Menu.Item\"),g=D(n,e=>n.selectors.isActive(e,a)),y=Q(null),I=V(E,y),o=D(n,e=>n.selectors.shouldScrollIntoView(e,a));W(()=>{if(o)return We().requestAnimationFrame(()=>{var e,R;(R=(e=y.current)==null?void 0:e.scrollIntoView)==null||R.call(e,{block:\"nearest\"})})},[o,y]);let h=Fe(y),_=Q({disabled:s,domRef:y,get textValue(){return h()}});W(()=>{_.current.disabled=s},[_,s]),W(()=>(n.actions.registerItem(a,_),()=>n.actions.unregisterItem(a)),[_,a]);let M=P(()=>{n.send({type:r.CloseMenu})}),i=P(e=>{if(s)return e.preventDefault();n.send({type:r.CloseMenu}),ne(n.state.buttonElement)}),b=P(()=>{if(s)return n.send({type:r.GoToItem,focus:A.Nothing});n.send({type:r.GoToItem,focus:A.Specific,id:a})}),T=he(),u=P(e=>{T.update(e),!s&&(g||n.send({type:r.GoToItem,focus:A.Specific,id:a,trigger:j.Pointer}))}),f=P(e=>{T.wasMoved(e)&&(s||g||n.send({type:r.GoToItem,focus:A.Specific,id:a,trigger:j.Pointer}))}),v=P(e=>{T.wasMoved(e)&&(s||g&&n.send({type:r.GoToItem,focus:A.Nothing}))}),[S,O]=se(),[F,U]=je(),H=K(()=>({active:g,focus:g,disabled:s,close:M}),[g,s,M]),G={id:a,ref:I,role:\"menuitem\",tabIndex:s===!0?void 0:-1,\"aria-disabled\":s===!0?!0:void 0,\"aria-labelledby\":S,\"aria-describedby\":F,disabled:void 0,onClick:i,onFocus:b,onPointerEnter:u,onMouseEnter:u,onPointerMove:f,onMouseMove:f,onPointerLeave:v,onMouseLeave:v},w=L();return x.createElement(O,null,x.createElement(U,null,w({ourProps:G,theirProps:l,slot:H,defaultTag:lt,name:\"Menu.Item\"})))}let it=\"div\";function ut(c,E){let[p,a]=se(),s=c,l={ref:E,\"aria-labelledby\":p,role:\"group\"},n=L();return x.createElement(a,null,n({ourProps:l,theirProps:s,slot:{},defaultTag:it,name:\"Menu.Section\"}))}let dt=\"header\";function mt(c,E){let p=B(),{id:a=`headlessui-menu-heading-${p}`,...s}=c,l=qe();W(()=>l.register(a),[a,l.register]);let n={id:a,ref:E,role:\"presentation\",...l.props};return L()({ourProps:n,theirProps:s,slot:{},defaultTag:dt,name:\"Menu.Heading\"})}let ct=\"div\";function Tt(c,E){let p=c,a={ref:E,role:\"separator\"};return L()({ourProps:a,theirProps:p,slot:{},defaultTag:ct,name:\"Menu.Separator\"})}let ft=C(tt),yt=C(nt),Pt=C(st),Et=C(pt),gt=C(ut),Mt=C(mt),bt=C(Tt),lo=Object.assign(ft,{Button:yt,Items:Pt,Item:Et,Section:gt,Heading:Mt,Separator:bt});export{lo as Menu,yt as MenuButton,Mt as MenuHeading,Et as MenuItem,Pt as MenuItems,gt as MenuSection,bt as MenuSeparator};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;EAAAC,UAAA;EAAAC,UAAA;EAAAC,UAAA;EAAAC,UAAA;AAAA,SAAOC,YAAY,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,0BAA0B;AAAC,OAAOC,CAAC,IAAEC,QAAQ,IAAIC,EAAE,EAACC,WAAW,IAAIC,CAAC,EAACC,SAAS,IAAIC,EAAE,EAACC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,EAAE,QAAK,OAAO;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,WAAW;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,iBAAiB,IAAIC,EAAE,QAAK,qCAAqC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,oBAAoB,IAAIC,EAAE,QAAK,wCAAwC;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,iBAAiB,IAAIC,EAAE,QAAK,oCAAoC;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,EAACC,gBAAgB,IAAIC,EAAE,EAACC,qBAAqB,IAAIC,EAAE,EAACC,oBAAoB,IAAIC,EAAE,EAACC,yBAAyB,IAAIC,EAAE,EAACC,iBAAiB,IAAIC,EAAE,QAAK,4BAA4B;AAAC,SAAOC,kBAAkB,IAAIC,EAAE,EAACC,KAAK,IAAIC,CAAC,EAACC,aAAa,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,4BAA4B;AAAC,OAAM,KAAIC,EAAE,MAAK,oBAAoB;AAAC,SAAOJ,KAAK,IAAIK,EAAE,EAACC,aAAa,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,EAACC,kBAAkB,IAAIC,EAAE,EAACC,uBAAuB,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,cAAc,IAAIC,EAAE,EAACC,gBAAgB,IAAIC,CAAC,EAACC,UAAU,IAAIC,EAAE,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,eAAe,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,MAAM,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,iBAAiB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,EAAE,EAACC,cAAc,IAAIC,EAAE,EAACC,qBAAqB,IAAIC,CAAC,QAAK,wBAAwB;AAAC,IAAIC,EAAE,GAAC7H,EAAE;AAAC,SAAS8H,EAAEA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACzG,CAAC,CAAC,CAAC;IAAC;MAAC0G,UAAU,EAACC,CAAC,GAAC,CAAC;IAAM,CAAC,GAACJ,CAAC;IAAJK,CAAC,GAAAhJ,wBAAA,CAAE2I,CAAC,EAAA1I,SAAA;IAACgJ,CAAC,GAACX,EAAE,CAAC;MAACY,EAAE,EAACL,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,CAAC;IAAC,CAACI,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,GAAC7D,CAAC,CAACyD,CAAC,EAACK,CAAC,IAAE,CAACA,CAAC,CAACC,SAAS,EAACD,CAAC,CAACE,YAAY,EAACF,CAAC,CAACG,aAAa,CAAC,CAAC;IAACC,CAAC,GAAClG,CAAC,CAACoF,CAAC,CAAC;IAACe,CAAC,GAACrE,EAAE,CAACsE,GAAG,CAAC,IAAI,CAAC;IAACC,CAAC,GAACrE,CAAC,CAACmE,CAAC,EAAC7I,CAAC,CAACwI,CAAC,IAAEK,CAAC,CAACG,SAAS,CAACC,KAAK,CAACT,CAAC,EAACT,CAAC,CAAC,EAAC,CAACc,CAAC,EAACd,CAAC,CAAC,CAAC,CAAC;EAACjG,EAAE,CAACiH,CAAC,EAAC,CAACR,CAAC,EAACD,CAAC,CAAC,EAAC,CAACE,CAAC,EAACU,CAAC,KAAG;IAAC,IAAIC,CAAC;IAAChB,CAAC,CAACiB,IAAI,CAAC;MAACC,IAAI,EAACrC,CAAC,CAACsC;IAAS,CAAC,CAAC,EAAC9D,EAAE,CAAC0D,CAAC,EAAC9D,EAAE,CAACmE,KAAK,CAAC,KAAGf,CAAC,CAACgB,cAAc,CAAC,CAAC,EAAC,CAACL,CAAC,GAAChB,CAAC,CAACsB,KAAK,CAACd,aAAa,KAAG,IAAI,IAAEQ,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC;EAAC,IAAIC,CAAC,GAACvI,CAAC,CAAC,MAAI;MAAC+G,CAAC,CAACiB,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACsC;MAAS,CAAC,CAAC;IAAA,CAAC,CAAC;IAACM,CAAC,GAACxJ,CAAC,CAAC,OAAK;MAACyJ,IAAI,EAACxB,CAAC,KAAGjB,CAAC,CAAC0C,IAAI;MAACC,KAAK,EAACJ;IAAC,CAAC,CAAC,EAAC,CAACtB,CAAC,EAACsB,CAAC,CAAC,CAAC;IAACK,CAAC,GAAC;MAACC,GAAG,EAACrB;IAAC,CAAC;IAACsB,CAAC,GAAC9D,CAAC,CAAC,CAAC;EAAC,OAAOxG,CAAC,CAACuK,aAAa,CAAC7G,EAAE,EAAC,IAAI,EAAC1D,CAAC,CAACuK,aAAa,CAAC7C,EAAE,CAAC8C,QAAQ,EAAC;IAACC,KAAK,EAAClC;EAAC,CAAC,EAACvI,CAAC,CAACuK,aAAa,CAACjG,EAAE,EAAC;IAACmG,KAAK,EAACzE,EAAE,CAACyC,CAAC,EAAC;MAAC,CAACjB,CAAC,CAAC0C,IAAI,GAAE1F,CAAC,CAAC0F,IAAI;MAAC,CAAC1C,CAAC,CAACkD,MAAM,GAAElG,CAAC,CAACkG;IAAM,CAAC;EAAC,CAAC,EAACJ,CAAC,CAAC;IAACK,QAAQ,EAACP,CAAC;IAACQ,UAAU,EAACtC,CAAC;IAACuC,IAAI,EAACb,CAAC;IAACc,UAAU,EAAC/C,EAAE;IAACgD,IAAI,EAAC;EAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIC,EAAE,GAAC,QAAQ;AAAC,SAASC,EAAEA,CAAChD,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,CAAC,CAAC,aAAa,CAAC;IAACO,CAAC,GAAC3G,CAAC,CAAC,CAAC;IAAC;MAAC8G,EAAE,EAACF,CAAC,6BAAA4C,MAAA,CAA2B7C,CAAC,CAAE;MAAC8C,QAAQ,EAAC5C,CAAC,GAAC,CAAC,CAAC;MAAC6C,SAAS,EAAC3C,CAAC,GAAC,CAAC;IAAM,CAAC,GAACR,CAAC;IAAJS,CAAC,GAAApJ,wBAAA,CAAE2I,CAAC,EAAAzI,UAAA;IAACmJ,CAAC,GAACjI,CAAC,CAAC,IAAI,CAAC;IAACsI,CAAC,GAAC9E,EAAE,CAAC,CAAC;IAAC+E,CAAC,GAACnG,CAAC,CAACoF,CAAC,EAACS,CAAC,EAAC3E,EAAE,CAAC,CAAC,EAACxC,CAAC,CAAC6J,CAAC,IAAElD,CAAC,CAACqB,IAAI,CAAC;MAACC,IAAI,EAACrC,CAAC,CAACkE,gBAAgB;MAACC,OAAO,EAACF;IAAC,CAAC,CAAC,CAAC,CAAC;IAAClC,CAAC,GAAC3H,CAAC,CAAC6J,CAAC,IAAE;MAAC,QAAOA,CAAC,CAACG,GAAG;QAAE,KAAK5E,CAAC,CAAC6E,KAAK;QAAC,KAAK7E,CAAC,CAAC8E,KAAK;QAAC,KAAK9E,CAAC,CAAC+E,SAAS;UAACN,CAAC,CAACzB,cAAc,CAAC,CAAC,EAACyB,CAAC,CAACO,eAAe,CAAC,CAAC,EAACzD,CAAC,CAACqB,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACyE,QAAQ;YAAC/B,KAAK,EAAC;cAACA,KAAK,EAAC5E,CAAC,CAAC4G;YAAK;UAAC,CAAC,CAAC;UAAC;QAAM,KAAKlF,CAAC,CAACmF,OAAO;UAACV,CAAC,CAACzB,cAAc,CAAC,CAAC,EAACyB,CAAC,CAACO,eAAe,CAAC,CAAC,EAACzD,CAAC,CAACqB,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACyE,QAAQ;YAAC/B,KAAK,EAAC;cAACA,KAAK,EAAC5E,CAAC,CAAC8G;YAAI;UAAC,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAACjC,CAAC,GAACvI,CAAC,CAAC6J,CAAC,IAAE;MAAC,QAAOA,CAAC,CAACG,GAAG;QAAE,KAAK5E,CAAC,CAAC6E,KAAK;UAACJ,CAAC,CAACzB,cAAc,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAAC,CAACI,CAAC,EAACI,CAAC,EAACE,CAAC,CAAC,GAACxF,CAAC,CAACqD,CAAC,EAACkD,CAAC,IAAE,CAACA,CAAC,CAACxC,SAAS,EAACwC,CAAC,CAACtC,aAAa,EAACsC,CAAC,CAACvC,YAAY,CAAC,CAAC;IAACF,CAAC,GAACoB,CAAC,KAAGxC,CAAC,CAAC0C,IAAI;EAAC1H,EAAE,CAACoG,CAAC,EAAC;IAACqD,OAAO,EAAC7B,CAAC;IAAC8B,MAAM,EAAC9L,CAAC,CAACiL,CAAC,IAAE;MAAC,IAAGjB,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC+B,QAAQ,CAACd,CAAC,CAACe,MAAM,CAAC,EAAC,OAAO9J,CAAC,CAAC+J,MAAM;MAAC,IAAIC,CAAC,GAACjB,CAAC,CAACe,MAAM,CAACG,OAAO,CAAC,wCAAwC,CAAC;MAAC,OAAOlH,EAAE,CAACmH,aAAa,CAACF,CAAC,CAAC,GAAChK,CAAC,CAACmK,MAAM,CAACH,CAAC,CAAC,GAAChC,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC6B,QAAQ,CAACd,CAAC,CAACe,MAAM,CAAC,GAAC9J,CAAC,CAAC+J,MAAM,GAAC/J,CAAC,CAACoK,KAAK;IAAA,CAAC,EAAC,CAACtC,CAAC,EAACE,CAAC,CAAC,CAAC;IAACH,KAAK,EAAC/J,CAAC,CAAC,MAAI+H,CAAC,CAACqB,IAAI,CAAC;MAACC,IAAI,EAACrC,CAAC,CAACsC;IAAS,CAAC,CAAC,EAAC,EAAE,CAAC;IAACiD,MAAM,EAACvM,CAAC,CAACiL,CAAC,IAAEA,CAAC,CAACuB,KAAK,CAAC,CAAC,EAAC,EAAE;EAAC,CAAC,CAAC;EAAC,IAAItD,CAAC,GAAC9H,CAAC,CAAC6J,CAAC,IAAE;MAAC,IAAIiB,CAAC;MAAC,IAAGjB,CAAC,CAACwB,MAAM,KAAG,CAAC,EAAC;QAAC,IAAG7H,EAAE,CAACqG,CAAC,CAACyB,aAAa,CAAC,EAAC,OAAOzB,CAAC,CAACzB,cAAc,CAAC,CAAC;QAACrB,CAAC,KAAGyB,CAAC,KAAGxC,CAAC,CAAC0C,IAAI,IAAEpJ,CAAC,CAAC,MAAIqH,CAAC,CAACqB,IAAI,CAAC;UAACC,IAAI,EAACrC,CAAC,CAACsC;QAAS,CAAC,CAAC,CAAC,EAAC,CAAC4C,CAAC,GAAC3D,CAAC,CAACoE,OAAO,KAAG,IAAI,IAAET,CAAC,CAACxC,KAAK,CAAC;UAACkD,aAAa,EAAC,CAAC;QAAC,CAAC,CAAC,KAAG3B,CAAC,CAACzB,cAAc,CAAC,CAAC,EAACzB,CAAC,CAACqB,IAAI,CAAC;UAACC,IAAI,EAACrC,CAAC,CAACyE,QAAQ;UAAC/B,KAAK,EAAC;YAACA,KAAK,EAAC5E,CAAC,CAAC+H;UAAO,CAAC;UAAChB,OAAO,EAAC3E,CAAC,CAAC4F;QAAO,CAAC,CAAC,CAAC,CAAC;MAAA;IAAC,CAAC,CAAC;IAAC;MAACC,cAAc,EAAC5D,CAAC;MAAC6D,UAAU,EAACC;IAAC,CAAC,GAACxN,EAAE,CAAC;MAACuL,SAAS,EAAC3C;IAAC,CAAC,CAAC;IAAC;MAAC6E,SAAS,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC1N,EAAE,CAAC;MAAC2N,UAAU,EAACnF;IAAC,CAAC,CAAC;IAAC;MAACoF,OAAO,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC9M,EAAE,CAAC;MAACmK,QAAQ,EAAC5C;IAAC,CAAC,CAAC;IAACwF,CAAC,GAACvN,CAAC,CAAC,OAAK;MAACyJ,IAAI,EAACD,CAAC,KAAGxC,CAAC,CAAC0C,IAAI;MAAC8D,MAAM,EAACJ,CAAC,IAAE5D,CAAC,KAAGxC,CAAC,CAAC0C,IAAI;MAACiB,QAAQ,EAAC5C,CAAC;MAAC0F,KAAK,EAACV,CAAC;MAACzD,KAAK,EAACP,CAAC;MAAC2E,SAAS,EAACzF;IAAC,CAAC,CAAC,EAAC,CAACuB,CAAC,EAACuD,CAAC,EAAChE,CAAC,EAACqE,CAAC,EAACrF,CAAC,EAACE,CAAC,CAAC,CAAC;IAAC0F,CAAC,GAAC7H,EAAE,CAAC0C,CAAC,CAAC,CAAC,EAAC;MAACqB,GAAG,EAACpB,CAAC;MAACT,EAAE,EAACF,CAAC;MAACmB,IAAI,EAAC/G,EAAE,CAACuF,CAAC,EAACU,CAAC,CAACoE,OAAO,CAAC;MAAC,eAAe,EAAC,MAAM;MAAC,eAAe,EAACzC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC9B,EAAE;MAAC,eAAe,EAACwB,CAAC,KAAGxC,CAAC,CAAC0C,IAAI;MAACiB,QAAQ,EAAC5C,CAAC,IAAE,KAAK,CAAC;MAAC6C,SAAS,EAAC3C,CAAC;MAAC2F,SAAS,EAACjF,CAAC;MAACkF,OAAO,EAACtE,CAAC;MAACuE,aAAa,EAAChF;IAAC,CAAC,EAAC+D,CAAC,EAACI,CAAC,EAACK,CAAC,CAAC;EAAC,OAAOtH,CAAC,CAAC,CAAC,CAAC;IAACmE,QAAQ,EAACwD,CAAC;IAACvD,UAAU,EAAClC,CAAC;IAACmC,IAAI,EAACkD,CAAC;IAACjD,UAAU,EAACE,EAAE;IAACD,IAAI,EAAC;EAAa,CAAC,CAAC;AAAA;AAAC,IAAIwD,EAAE,GAAC,KAAK;EAACC,EAAE,GAACtI,EAAE,CAACuI,cAAc,GAACvI,EAAE,CAACwI,MAAM;AAAC,SAASC,EAAEA,CAAC1G,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACzG,CAAC,CAAC,CAAC;IAAC;MAAC8G,EAAE,EAACH,CAAC,4BAAA6C,MAAA,CAA0B/C,CAAC,CAAE;MAACyG,MAAM,EAACtG,CAAC;MAACuG,MAAM,EAACtG,CAAC,GAAC,CAAC,CAAC;MAACuG,KAAK,EAACrG,CAAC,GAAC,CAAC,CAAC;MAACsG,UAAU,EAACrG,CAAC,GAAC,CAAC;IAAM,CAAC,GAACT,CAAC;IAAJU,CAAC,GAAArJ,wBAAA,CAAE2I,CAAC,EAAAxI,UAAA;IAACuJ,CAAC,GAAC5E,EAAE,CAACkE,CAAC,CAAC;IAACW,CAAC,GAACnB,CAAC,CAAC,YAAY,CAAC;IAAC,CAACqB,CAAC,EAACY,CAAC,CAAC,GAACnG,EAAE,CAACoF,CAAC,CAAC;IAACgB,CAAC,GAAClG,EAAE,CAAC,CAAC;IAAC,CAACsG,CAAC,EAACE,CAAC,CAAC,GAAC1J,EAAE,CAAC,IAAI,CAAC;IAACgI,CAAC,GAAC9F,CAAC,CAACoF,CAAC,EAACc,CAAC,GAACG,CAAC,GAAC,IAAI,EAAC3H,CAAC,CAACwN,CAAC,IAAE/F,CAAC,CAACO,IAAI,CAAC;MAACC,IAAI,EAACrC,CAAC,CAAC6H,eAAe;MAAC1D,OAAO,EAACyD;IAAC,CAAC,CAAC,CAAC,EAAC1E,CAAC,CAAC;IAAC,CAAChB,CAAC,EAACC,CAAC,CAAC,GAACzE,CAAC,CAACmE,CAAC,EAAC+F,CAAC,IAAE,CAACA,CAAC,CAACnG,SAAS,EAACmG,CAAC,CAACjG,aAAa,CAAC,CAAC;IAACsE,CAAC,GAACjL,EAAE,CAACmH,CAAC,CAAC;IAACgE,CAAC,GAACnL,EAAE,CAACgI,CAAC,CAAC;EAACpB,CAAC,KAAGT,CAAC,GAAC,CAAC,CAAC,CAAC;EAAC,IAAIkF,CAAC,GAAC/I,EAAE,CAAC,CAAC;IAAC,CAACkJ,CAAC,EAACE,CAAC,CAAC,GAACxK,EAAE,CAACoF,CAAC,EAAC0B,CAAC,EAACqD,CAAC,KAAG,IAAI,GAAC,CAACA,CAAC,GAACjJ,CAAC,CAAC0F,IAAI,MAAI1F,CAAC,CAAC0F,IAAI,GAACZ,CAAC,KAAG9B,CAAC,CAAC0C,IAAI,CAAC;EAAClI,EAAE,CAAC4L,CAAC,EAACrE,CAAC,EAAC,MAAI;IAACN,CAAC,CAACO,IAAI,CAAC;MAACC,IAAI,EAACrC,CAAC,CAACsC;IAAS,CAAC,CAAC;EAAA,CAAC,CAAC;EAAC,IAAIqE,CAAC,GAACjJ,CAAC,CAACmE,CAAC,EAAC+F,CAAC,IAAEA,CAAC,CAAC5G,UAAU,CAAC;IAAC+F,CAAC,GAACJ,CAAC,GAAC,CAAC,CAAC,GAACtF,CAAC,IAAEa,CAAC,KAAG9B,CAAC,CAAC0C,IAAI;EAACtH,EAAE,CAACuL,CAAC,EAACZ,CAAC,CAAC;EAAC,IAAI2B,CAAC,GAACnB,CAAC,GAAC,CAAC,CAAC,GAACtF,CAAC,IAAEa,CAAC,KAAG9B,CAAC,CAAC0C,IAAI;EAACtI,EAAE,CAACsN,CAAC,EAAC;IAACC,OAAO,EAAC/O,CAAC,CAAC,MAAI,CAACmJ,CAAC,EAACa,CAAC,CAAC,EAAC,CAACb,CAAC,EAACa,CAAC,CAAC;EAAC,CAAC,CAAC;EAAC,IAAIiB,CAAC,GAAC/B,CAAC,KAAG9B,CAAC,CAAC0C,IAAI;IAACkF,EAAE,GAAClO,EAAE,CAACmK,CAAC,EAAC9B,CAAC,CAAC,GAAC,CAAC,CAAC,GAACqE,CAAC;EAACtN,EAAE,CAAC,MAAI;IAAC,IAAI0O,CAAC,GAAC5E,CAAC;IAAC4E,CAAC,IAAE1F,CAAC,KAAG9B,CAAC,CAAC0C,IAAI,IAAE8E,CAAC,MAAIzB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC8B,aAAa,CAAC,IAAEL,CAAC,CAAClF,KAAK,CAAC;MAACkD,aAAa,EAAC,CAAC;IAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC1D,CAAC,EAACc,CAAC,EAACmD,CAAC,CAAC,CAAC,EAAC/J,EAAE,CAAC8F,CAAC,KAAG9B,CAAC,CAAC0C,IAAI,EAAC;IAACoF,SAAS,EAAClF,CAAC;IAACmF,MAAMA,CAACP,CAAC,EAAC;MAAC,OAAOA,CAAC,CAACQ,YAAY,CAAC,MAAM,CAAC,KAAG,UAAU,GAACC,UAAU,CAACC,aAAa,GAACV,CAAC,CAACW,YAAY,CAAC,MAAM,CAAC,GAACF,UAAU,CAACG,WAAW,GAACH,UAAU,CAACI,aAAa;IAAA,CAAC;IAACC,IAAIA,CAACd,CAAC,EAAC;MAACA,CAAC,CAACe,YAAY,CAAC,MAAM,EAAC,MAAM,CAAC;IAAA;EAAC,CAAC,CAAC;EAAC,IAAIC,CAAC,GAAC5O,EAAE,CAAC,CAAC;IAAC6O,EAAE,GAACzO,CAAC,CAACwN,CAAC,IAAE;MAAC,IAAIkB,CAAC,EAACC,CAAC,EAACC,CAAC;MAAC,QAAOJ,CAAC,CAACK,OAAO,CAAC,CAAC,EAACrB,CAAC,CAACxD,GAAG;QAAE,KAAK5E,CAAC,CAAC6E,KAAK;UAAC,IAAGxC,CAAC,CAACY,KAAK,CAACyG,WAAW,KAAG,EAAE,EAAC,OAAOtB,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC3C,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACmJ,MAAM;YAAC9F,KAAK,EAACuE,CAAC,CAACxD;UAAG,CAAC,CAAC;QAAC,KAAK5E,CAAC,CAAC8E,KAAK;UAAC,IAAGsD,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC3C,CAAC,CAACY,KAAK,CAAC2G,eAAe,KAAG,IAAI,EAAC;YAAC,IAAG;cAACC,OAAO,EAACC;YAAE,CAAC,GAACzH,CAAC,CAACY,KAAK,CAAC8G,KAAK,CAAC1H,CAAC,CAACY,KAAK,CAAC2G,eAAe,CAAC;YAAC,CAACL,CAAC,GAAC,CAACD,CAAC,GAACQ,EAAE,CAAC3D,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACmD,CAAC,CAACU,MAAM,CAAC7D,OAAO,KAAG,IAAI,IAAEoD,CAAC,CAACvD,KAAK,CAAC,CAAC;UAAA;UAAC3D,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACsC;UAAS,CAAC,CAAC,EAAC5D,EAAE,CAACmD,CAAC,CAACY,KAAK,CAACd,aAAa,CAAC;UAAC;QAAM,KAAKnC,CAAC,CAAC+E,SAAS;UAAC,OAAOqD,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC3C,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;YAAC/G,KAAK,EAAC5E,CAAC,CAAC4L;UAAI,CAAC,CAAC;QAAC,KAAKlK,CAAC,CAACmF,OAAO;UAAC,OAAOiD,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC3C,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;YAAC/G,KAAK,EAAC5E,CAAC,CAAC6L;UAAQ,CAAC,CAAC;QAAC,KAAKnK,CAAC,CAACoK,IAAI;QAAC,KAAKpK,CAAC,CAACqK,MAAM;UAAC,OAAOjC,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC3C,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;YAAC/G,KAAK,EAAC5E,CAAC,CAAC4G;UAAK,CAAC,CAAC;QAAC,KAAKlF,CAAC,CAACsK,GAAG;QAAC,KAAKtK,CAAC,CAACuK,QAAQ;UAAC,OAAOnC,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC3C,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;YAAC/G,KAAK,EAAC5E,CAAC,CAAC8G;UAAI,CAAC,CAAC;QAAC,KAAKpF,CAAC,CAACwK,MAAM;UAACpC,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC9K,CAAC,CAAC,MAAImI,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACsC;UAAS,CAAC,CAAC,CAAC,EAAC,CAAC0G,CAAC,GAACnH,CAAC,CAACY,KAAK,CAACd,aAAa,KAAG,IAAI,IAAEqH,CAAC,CAACtG,KAAK,CAAC;YAACkD,aAAa,EAAC,CAAC;UAAC,CAAC,CAAC;UAAC;QAAM,KAAKpG,CAAC,CAACyK,GAAG;UAACrC,CAAC,CAACpF,cAAc,CAAC,CAAC,EAACoF,CAAC,CAACpD,eAAe,CAAC,CAAC,EAAC9K,CAAC,CAAC,MAAImI,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACsC;UAAS,CAAC,CAAC,CAAC,EAAChE,EAAE,CAACuD,CAAC,CAACY,KAAK,CAACd,aAAa,EAACiG,CAAC,CAACsC,QAAQ,GAAChM,EAAE,CAACyL,QAAQ,GAACzL,EAAE,CAACwL,IAAI,CAAC;UAAC;QAAM;UAAQ9B,CAAC,CAACxD,GAAG,CAAC+F,MAAM,KAAG,CAAC,KAAGtI,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACmJ,MAAM;YAAC9F,KAAK,EAACuE,CAAC,CAACxD;UAAG,CAAC,CAAC,EAACwE,CAAC,CAACwB,UAAU,CAAC,MAAIvI,CAAC,CAACO,IAAI,CAAC;YAACC,IAAI,EAACrC,CAAC,CAACqK;UAAW,CAAC,CAAC,EAAC,GAAG,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAACC,EAAE,GAAClQ,CAAC,CAACwN,CAAC,IAAE;MAAC,QAAOA,CAAC,CAACxD,GAAG;QAAE,KAAK5E,CAAC,CAAC6E,KAAK;UAACuD,CAAC,CAACpF,cAAc,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAAC+H,EAAE,GAACnR,CAAC,CAAC,OAAK;MAACyJ,IAAI,EAACX,CAAC,KAAG9B,CAAC,CAAC0C;IAAI,CAAC,CAAC,EAAC,CAACZ,CAAC,CAAC,CAAC;IAACsI,EAAE,GAACtL,EAAE,CAAC0C,CAAC,GAACgB,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,EAAA3K,aAAA;MAAE,uBAAuB,EAACyF,CAAC,CAACmE,CAAC,EAACA,CAAC,CAACG,SAAS,CAACyI,kBAAkB,CAAC;MAAC,iBAAiB,EAAC/M,CAAC,CAACmE,CAAC,EAAC+F,CAAC,IAAE;QAAC,IAAIkB,CAAC;QAAC,OAAM,CAACA,CAAC,GAAClB,CAAC,CAACjG,aAAa,KAAG,IAAI,GAAC,KAAK,CAAC,GAACmH,CAAC,CAAC1H,EAAE;MAAA,CAAC,CAAC;MAACA,EAAE,EAACH,CAAC;MAAC+F,SAAS,EAAC6B,EAAE;MAAC5B,OAAO,EAACqD,EAAE;MAACI,IAAI,EAAC,MAAM;MAACC,QAAQ,EAACzI,CAAC,KAAG9B,CAAC,CAAC0C,IAAI,GAAC,CAAC,GAAC,KAAK,CAAC;MAACG,GAAG,EAACzB,CAAC;MAACoJ,KAAK,EAAA3S,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAKsJ,CAAC,CAACqJ,KAAK,GAAIjI,CAAC;QAAC,gBAAgB,EAACzI,EAAE,CAACiI,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC0I;MAAK;IAAC,GAAI7O,EAAE,CAAC0K,CAAC,CAAC,CAAC,CAAC;IAACoE,EAAE,GAAC1L,CAAC,CAAC,CAAC;EAAC,OAAOxG,CAAC,CAACuK,aAAa,CAACrD,EAAE,EAAC;IAACiL,OAAO,EAAC5J,CAAC,GAACN,CAAC,CAACmK,MAAM,IAAExE,CAAC,GAAC,CAAC,CAAC;IAACyE,aAAa,EAAChF;EAAC,CAAC,EAAC6E,EAAE,CAAC;IAACvH,QAAQ,EAACiH,EAAE;IAAChH,UAAU,EAACjC,CAAC;IAACkC,IAAI,EAAC8G,EAAE;IAAC7G,UAAU,EAACyD,EAAE;IAAC+D,QAAQ,EAAC9D,EAAE;IAAC+D,OAAO,EAACnD,EAAE;IAACrE,IAAI,EAAC;EAAY,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIyH,EAAE,GAACtS,EAAE;AAAC,SAASuS,EAAEA,CAACxK,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACzG,CAAC,CAAC,CAAC;IAAC;MAAC8G,EAAE,EAACH,CAAC,2BAAA6C,MAAA,CAAyB/C,CAAC,CAAE;MAACgD,QAAQ,EAAC7C,CAAC,GAAC,CAAC;IAAM,CAAC,GAACL,CAAC;IAAJM,CAAC,GAAAjJ,wBAAA,CAAE2I,CAAC,EAAAvI,UAAA;IAAC+I,CAAC,GAACX,CAAC,CAAC,WAAW,CAAC;IAACY,CAAC,GAAC5D,CAAC,CAAC2D,CAAC,EAAC4C,CAAC,IAAE5C,CAAC,CAACW,SAAS,CAACsJ,QAAQ,CAACrH,CAAC,EAAChD,CAAC,CAAC,CAAC;IAACM,CAAC,GAACjI,CAAC,CAAC,IAAI,CAAC;IAACsI,CAAC,GAAClG,CAAC,CAACoF,CAAC,EAACS,CAAC,CAAC;IAACM,CAAC,GAACnE,CAAC,CAAC2D,CAAC,EAAC4C,CAAC,IAAE5C,CAAC,CAACW,SAAS,CAACuJ,oBAAoB,CAACtH,CAAC,EAAChD,CAAC,CAAC,CAAC;EAACvG,CAAC,CAAC,MAAI;IAAC,IAAGmH,CAAC,EAAC,OAAO7D,EAAE,CAAC,CAAC,CAACwN,qBAAqB,CAAC,MAAI;MAAC,IAAIvH,CAAC,EAACiB,CAAC;MAAC,CAACA,CAAC,GAAC,CAACjB,CAAC,GAAC1C,CAAC,CAACoE,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC1B,CAAC,CAACwH,cAAc,KAAG,IAAI,IAAEvG,CAAC,CAACwG,IAAI,CAACzH,CAAC,EAAC;QAAC0H,KAAK,EAAC;MAAS,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC9J,CAAC,EAACN,CAAC,CAAC,CAAC;EAAC,IAAIQ,CAAC,GAACnG,EAAE,CAAC2F,CAAC,CAAC;IAACoB,CAAC,GAACrJ,CAAC,CAAC;MAACyK,QAAQ,EAAC7C,CAAC;MAACsI,MAAM,EAACjI,CAAC;MAAC,IAAIqK,SAASA,CAAA,EAAE;QAAC,OAAO7J,CAAC,CAAC,CAAC;MAAA;IAAC,CAAC,CAAC;EAACrH,CAAC,CAAC,MAAI;IAACiI,CAAC,CAACgD,OAAO,CAAC5B,QAAQ,GAAC7C,CAAC;EAAA,CAAC,EAAC,CAACyB,CAAC,EAACzB,CAAC,CAAC,CAAC,EAACxG,CAAC,CAAC,OAAK2G,CAAC,CAACwK,OAAO,CAACC,YAAY,CAAC7K,CAAC,EAAC0B,CAAC,CAAC,EAAC,MAAItB,CAAC,CAACwK,OAAO,CAACE,cAAc,CAAC9K,CAAC,CAAC,CAAC,EAAC,CAAC0B,CAAC,EAAC1B,CAAC,CAAC,CAAC;EAAC,IAAI2B,CAAC,GAACxI,CAAC,CAAC,MAAI;MAACiH,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACsC;MAAS,CAAC,CAAC;IAAA,CAAC,CAAC;IAACU,CAAC,GAAC5I,CAAC,CAAC6J,CAAC,IAAE;MAAC,IAAG/C,CAAC,EAAC,OAAO+C,CAAC,CAACzB,cAAc,CAAC,CAAC;MAACnB,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACsC;MAAS,CAAC,CAAC,EAAC5D,EAAE,CAAC2C,CAAC,CAACoB,KAAK,CAACd,aAAa,CAAC;IAAA,CAAC,CAAC;IAACuB,CAAC,GAAC9I,CAAC,CAAC,MAAI;MAAC,IAAG8G,CAAC,EAAC,OAAOG,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;QAAC/G,KAAK,EAAC5E,CAAC,CAAC+H;MAAO,CAAC,CAAC;MAACxE,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;QAAC/G,KAAK,EAAC5E,CAAC,CAACkO,QAAQ;QAAC5K,EAAE,EAACH;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACO,CAAC,GAAC1F,EAAE,CAAC,CAAC;IAACoG,CAAC,GAAC9H,CAAC,CAAC6J,CAAC,IAAE;MAACzC,CAAC,CAACyK,MAAM,CAAChI,CAAC,CAAC,EAAC,CAAC/C,CAAC,KAAGI,CAAC,IAAED,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;QAAC/G,KAAK,EAAC5E,CAAC,CAACkO,QAAQ;QAAC5K,EAAE,EAACH,CAAC;QAAC4D,OAAO,EAAC3E,CAAC,CAAC4F;MAAO,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC3D,CAAC,GAAC/H,CAAC,CAAC6J,CAAC,IAAE;MAACzC,CAAC,CAAC0K,QAAQ,CAACjI,CAAC,CAAC,KAAG/C,CAAC,IAAEI,CAAC,IAAED,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;QAAC/G,KAAK,EAAC5E,CAAC,CAACkO,QAAQ;QAAC5K,EAAE,EAACH,CAAC;QAAC4D,OAAO,EAAC3E,CAAC,CAAC4F;MAAO,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACG,CAAC,GAAC7L,CAAC,CAAC6J,CAAC,IAAE;MAACzC,CAAC,CAAC0K,QAAQ,CAACjI,CAAC,CAAC,KAAG/C,CAAC,IAAEI,CAAC,IAAED,CAAC,CAACe,IAAI,CAAC;QAACC,IAAI,EAACrC,CAAC,CAACyJ,QAAQ;QAAC/G,KAAK,EAAC5E,CAAC,CAAC+H;MAAO,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC,CAACM,CAAC,EAACE,CAAC,CAAC,GAACzG,EAAE,CAAC,CAAC;IAAC,CAAC4G,CAAC,EAACE,CAAC,CAAC,GAACpH,EAAE,CAAC,CAAC;IAACqH,CAAC,GAACvN,CAAC,CAAC,OAAK;MAACwN,MAAM,EAACtF,CAAC;MAACoB,KAAK,EAACpB,CAAC;MAACyC,QAAQ,EAAC7C,CAAC;MAAC6B,KAAK,EAACH;IAAC,CAAC,CAAC,EAAC,CAACtB,CAAC,EAACJ,CAAC,EAAC0B,CAAC,CAAC,CAAC;IAACmE,CAAC,GAAC;MAAC3F,EAAE,EAACH,CAAC;MAACgC,GAAG,EAACrB,CAAC;MAAC8I,IAAI,EAAC,UAAU;MAACC,QAAQ,EAACzJ,CAAC,KAAG,CAAC,CAAC,GAAC,KAAK,CAAC,GAAC,CAAC,CAAC;MAAC,eAAe,EAACA,CAAC,KAAG,CAAC,CAAC,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAAC,iBAAiB,EAACiF,CAAC;MAAC,kBAAkB,EAACK,CAAC;MAACzC,QAAQ,EAAC,KAAK,CAAC;MAACoI,OAAO,EAACnJ,CAAC;MAACoJ,OAAO,EAAClJ,CAAC;MAACmJ,cAAc,EAACnK,CAAC;MAACoK,YAAY,EAACpK,CAAC;MAACqK,aAAa,EAACpK,CAAC;MAACqK,WAAW,EAACrK,CAAC;MAACsK,cAAc,EAACxG,CAAC;MAACyG,YAAY,EAACzG;IAAC,CAAC;IAAC6B,CAAC,GAAC1I,CAAC,CAAC,CAAC;EAAC,OAAOxG,CAAC,CAACuK,aAAa,CAACkD,CAAC,EAAC,IAAI,EAACzN,CAAC,CAACuK,aAAa,CAACuD,CAAC,EAAC,IAAI,EAACoB,CAAC,CAAC;IAACvE,QAAQ,EAACwD,CAAC;IAACvD,UAAU,EAACrC,CAAC;IAACsC,IAAI,EAACkD,CAAC;IAACjD,UAAU,EAAC0H,EAAE;IAACzH,IAAI,EAAC;EAAW,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIgJ,EAAE,GAAC,KAAK;AAAC,SAASC,EAAEA,CAAC/L,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACE,CAAC,CAAC,GAACrB,EAAE,CAAC,CAAC;IAACsB,CAAC,GAACL,CAAC;IAACM,CAAC,GAAC;MAAC8B,GAAG,EAACnC,CAAC;MAAC,iBAAiB,EAACC,CAAC;MAAC2J,IAAI,EAAC;IAAO,CAAC;IAACrJ,CAAC,GAACjC,CAAC,CAAC,CAAC;EAAC,OAAOxG,CAAC,CAACuK,aAAa,CAAClC,CAAC,EAAC,IAAI,EAACI,CAAC,CAAC;IAACkC,QAAQ,EAACpC,CAAC;IAACqC,UAAU,EAACtC,CAAC;IAACuC,IAAI,EAAC,CAAC,CAAC;IAACC,UAAU,EAACiJ,EAAE;IAAChJ,IAAI,EAAC;EAAc,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIkJ,EAAE,GAAC,QAAQ;AAAC,SAASC,EAAEA,CAACjM,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACzG,CAAC,CAAC,CAAC;IAAC;MAAC8G,EAAE,EAACH,CAAC,8BAAA6C,MAAA,CAA4B/C,CAAC;IAAO,CAAC,GAACF,CAAC;IAAJK,CAAC,GAAAhJ,wBAAA,CAAE2I,CAAC,EAAAtI,UAAA;IAAC4I,CAAC,GAACzB,EAAE,CAAC,CAAC;EAAChF,CAAC,CAAC,MAAIyG,CAAC,CAAC4L,QAAQ,CAAC9L,CAAC,CAAC,EAAC,CAACA,CAAC,EAACE,CAAC,CAAC4L,QAAQ,CAAC,CAAC;EAAC,IAAI1L,CAAC,GAAApJ,aAAA;IAAEmJ,EAAE,EAACH,CAAC;IAACgC,GAAG,EAACnC,CAAC;IAAC4J,IAAI,EAAC;EAAc,GAAIvJ,CAAC,CAAC6L,KAAK,CAAC;EAAC,OAAO5N,CAAC,CAAC,CAAC,CAAC;IAACmE,QAAQ,EAAClC,CAAC;IAACmC,UAAU,EAACtC,CAAC;IAACuC,IAAI,EAAC,CAAC,CAAC;IAACC,UAAU,EAACmJ,EAAE;IAAClJ,IAAI,EAAC;EAAc,CAAC,CAAC;AAAA;AAAC,IAAIsJ,EAAE,GAAC,KAAK;AAAC,SAASC,EAAEA,CAACrM,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACF,CAAC;IAACI,CAAC,GAAC;MAACgC,GAAG,EAACnC,CAAC;MAAC4J,IAAI,EAAC;IAAW,CAAC;EAAC,OAAOtL,CAAC,CAAC,CAAC,CAAC;IAACmE,QAAQ,EAACtC,CAAC;IAACuC,UAAU,EAACzC,CAAC;IAAC0C,IAAI,EAAC,CAAC,CAAC;IAACC,UAAU,EAACuJ,EAAE;IAACtJ,IAAI,EAAC;EAAgB,CAAC,CAAC;AAAA;AAAC,IAAIwJ,EAAE,GAACnO,CAAC,CAAC4B,EAAE,CAAC;EAACwM,EAAE,GAACpO,CAAC,CAAC6E,EAAE,CAAC;EAACwJ,EAAE,GAACrO,CAAC,CAACuI,EAAE,CAAC;EAAC+F,EAAE,GAACtO,CAAC,CAACqM,EAAE,CAAC;EAACkC,EAAE,GAACvO,CAAC,CAAC4N,EAAE,CAAC;EAACY,EAAE,GAACxO,CAAC,CAAC8N,EAAE,CAAC;EAACW,EAAE,GAACzO,CAAC,CAACkO,EAAE,CAAC;EAACQ,EAAE,GAACC,MAAM,CAACC,MAAM,CAACT,EAAE,EAAC;IAACU,MAAM,EAACT,EAAE;IAACU,KAAK,EAACT,EAAE;IAACU,IAAI,EAACT,EAAE;IAACU,OAAO,EAACT,EAAE;IAACU,OAAO,EAACT,EAAE;IAACU,SAAS,EAACT;EAAE,CAAC,CAAC;AAAC,SAAOC,EAAE,IAAIS,IAAI,EAACf,EAAE,IAAIgB,UAAU,EAACZ,EAAE,IAAIa,WAAW,EAACf,EAAE,IAAIgB,QAAQ,EAACjB,EAAE,IAAIkB,SAAS,EAAChB,EAAE,IAAIiB,WAAW,EAACf,EAAE,IAAIgB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}