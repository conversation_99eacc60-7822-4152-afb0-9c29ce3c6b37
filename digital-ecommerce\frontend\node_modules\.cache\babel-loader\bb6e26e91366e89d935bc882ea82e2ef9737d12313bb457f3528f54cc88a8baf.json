{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      console.log('Searching for:', searchQuery);\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    const handleClickOutside = event => {\n      if (!event.target.closest('.user-dropdown')) {\n        setShowUserDropdown(false);\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n  const navigationItems = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100' : 'bg-white/10 backdrop-blur-sm'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-18 lg:h-22\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3 group\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                rotate: 360,\n                scale: 1.1\n              },\n              transition: {\n                duration: 0.6,\n                type: \"spring\",\n                stiffness: 200\n              },\n              className: \"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-2xl font-bold transition-all duration-300 ${isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'}`,\n                children: \"ShopHub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs font-medium transition-all duration-300 ${isScrolled ? 'text-light-orange-600' : 'text-white/80'}`,\n                children: \"Premium Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-2\",\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                y: -2\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${isActive(item.href) ? isScrolled ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25' : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm' : isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this), isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                  layoutId: \"activeNavBg\",\n                  className: \"absolute inset-0 rounded-xl\",\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 23\n                }, this), !isActive(item.href) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)\n            }, item.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center flex-1 max-w-lg mx-8\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative w-full group\",\n              whileHover: {\n                scale: 1.02\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 transition-colors duration-300 ${isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search for products, brands, and more...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${isScrolled ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100' : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'} focus:outline-none shadow-lg hover:shadow-xl`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${isScrolled ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100' : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.1\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n              children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/account\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: `relative p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.profilePicture,\n                  alt: \"Profile\",\n                  className: \"w-8 h-8 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: user === null || user === void 0 ? void 0 : user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/account\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"My Account\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/orders\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Order History\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/wishlist\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Wishlist\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: logout,\n                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(!isOpen),\n              className: `lg:hidden p-2 rounded-md transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600' : 'text-white hover:text-yellow-300'}`,\n              children: isOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto'\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: \"lg:hidden backdrop-blur-md border-t bg-white/95 border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-6 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full pl-10 pr-4 py-3 rounded-lg bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                onClick: () => setIsOpen(false),\n                className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${isActive(item.href) ? 'bg-light-orange-100 text-light-orange-700' : 'text-gray-700 hover:bg-gray-100'}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 23\n                }, this)]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-around pt-4 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-600\",\n                  children: \"Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 lg:h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navigation, \"EHd0Z8auy7azxY1ShWbP2rKcWRk=\", false, function () {\n  return [useLocation, useUser];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "ShoppingCart", "useUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Navigation", "_s", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "showUserDropdown", "setShowUserDropdown", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "console", "log", "handleScroll", "window", "scrollY", "handleClickOutside", "event", "target", "closest", "addEventListener", "document", "removeEventListener", "navigationItems", "name", "href", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "layoutId", "damping", "placeholder", "value", "onChange", "button", "whileTap", "profilePicture", "src", "alt", "firstName", "lastName", "email", "onClick", "opacity", "height", "exit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      console.log('Searching for:', searchQuery);\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    const handleClickOutside = (event) => {\n      if (!event.target.closest('.user-dropdown')) {\n        setShowUserDropdown(false);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('click', handleClickOutside);\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n          isScrolled\n            ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100'\n            : 'bg-white/10 backdrop-blur-sm'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-18 lg:h-22\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n              <motion.div\n                whileHover={{ rotate: 360, scale: 1.1 }}\n                transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n              >\n                <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n              </motion.div>\n              <div className=\"flex flex-col\">\n                <span className={`text-2xl font-bold transition-all duration-300 ${\n                  isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'\n                }`}>\n                  ShopHub\n                </span>\n                <span className={`text-xs font-medium transition-all duration-300 ${\n                  isScrolled ? 'text-light-orange-600' : 'text-white/80'\n                }`}>\n                  Premium Store\n                </span>\n              </div>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-2\">\n              {navigationItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ y: -2 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <Link\n                    to={item.href}\n                    className={`relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${\n                      isActive(item.href)\n                        ? isScrolled\n                          ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'\n                          : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm'\n                        : isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'\n                    }`}\n                  >\n                    <span className=\"relative z-10\">{item.name}</span>\n                    {isActive(item.href) && (\n                      <motion.div\n                        layoutId=\"activeNavBg\"\n                        className=\"absolute inset-0 rounded-xl\"\n                        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                      />\n                    )}\n                    {!isActive(item.href) && (\n                      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"></div>\n                    )}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-lg mx-8\">\n              <motion.div\n                className=\"relative w-full group\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.2 }}\n              >\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${\n                    isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for products, brands, and more...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${\n                    isScrolled\n                      ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'\n                      : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'\n                  } focus:outline-none shadow-lg hover:shadow-xl`}\n                />\n                <div className={`absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${\n                  isScrolled\n                    ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100'\n                    : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'\n                }`}></div>\n              </motion.div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                className={`p-2 rounded-full transition-colors duration-300 ${\n                  isScrolled\n                    ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                    : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                }`}\n              >\n                <HeartIcon className=\"w-6 h-6\" />\n              </motion.button>\n\n              <Link to=\"/account\">\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className={`p-2 rounded-full transition-colors duration-300 ${\n                    isScrolled\n                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                      : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                  }`}\n                >\n                  <UserIcon className=\"w-6 h-6\" />\n                </motion.button>\n              </Link>\n\n              <ShoppingCart />\n\n              {/* User Account */}\n              {isAuthenticated ? (\n                <div className=\"relative group\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className={`relative p-2 rounded-full transition-colors duration-300 ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                        : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                    }`}\n                  >\n                    {user?.profilePicture ? (\n                      <img\n                        src={user.profilePicture}\n                        alt=\"Profile\"\n                        className=\"w-8 h-8 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <UserIcon className=\"w-6 h-6\" />\n                    )}\n                  </motion.button>\n\n                  {/* User Dropdown */}\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                    <div className=\"p-3 border-b border-gray-100\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {user?.firstName} {user?.lastName}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                    <div className=\"py-1\">\n                      <Link\n                        to=\"/account\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        My Account\n                      </Link>\n                      <Link\n                        to=\"/orders\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Order History\n                      </Link>\n                      <Link\n                        to=\"/wishlist\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Wishlist\n                      </Link>\n                      <button\n                        onClick={logout}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Sign Out\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <Link to=\"/login\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                      }`}\n                    >\n                      Sign In\n                    </motion.button>\n                  </Link>\n                  <Link to=\"/register\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\"\n                    >\n                      Sign Up\n                    </motion.button>\n                  </Link>\n                </div>\n              )}\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600' \n                    : 'text-white hover:text-yellow-300'\n                }`}\n              >\n                {isOpen ? (\n                  <XMarkIcon className=\"w-6 h-6\" />\n                ) : (\n                  <Bars3Icon className=\"w-6 h-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden backdrop-blur-md border-t bg-white/95 border-gray-200\"\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {/* Mobile Search */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n                  />\n                </div>\n\n                {/* Mobile Navigation Links */}\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setIsOpen(false)}\n                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-light-orange-100 text-light-orange-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      }`}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ))}\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"flex items-center justify-around pt-4 border-t border-gray-200\">\n                  <button className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <HeartIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Wishlist</span>\n                  </button>\n                  <Link to=\"/account\" className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <UserIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Account</span>\n                  </Link>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ShoppingCart />\n                    <span className=\"text-xs text-gray-600\">Cart</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16 lg:h-20\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,EACrBC,eAAe,QACV,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMiC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+B,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIV,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEb,WAAW,CAAC;IAC5C;EACF,CAAC;EAED5B,SAAS,CAAC,MAAM;IACd,MAAM0C,YAAY,GAAGA,CAAA,KAAM;MACzBf,aAAa,CAACgB,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAED,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3CjB,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAEDY,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAC/CQ,QAAQ,CAACD,gBAAgB,CAAC,OAAO,EAAEJ,kBAAkB,CAAC;IAEtD,OAAO,MAAM;MACXF,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAET,YAAY,CAAC;MAClDQ,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEN,kBAAkB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE5C;EAAS,CAAC,EAC3C;IAAE0C,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE3C;EAAQ,CAAC,EACtD;IAAEyC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAE3C;EAAQ,CAAC,EAC7D;IAAEyC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAEzC;EAAsB,CAAC,EAC9D;IAAEuC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE1C;EAAU,CAAC,CACvD;EAED,MAAM2C,QAAQ,GAAIC,IAAI,IAAKzB,QAAQ,CAAC0B,QAAQ,KAAKD,IAAI;EAErD,oBACEtC,OAAA,CAAAE,SAAA;IAAAsC,QAAA,gBACExC,OAAA,CAAChB,MAAM,CAACyD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,SAAS,EAAE,+DACTtC,UAAU,GACN,iEAAiE,GACjE,8BAA8B,EACjC;MAAAiC,QAAA,gBAEHxC,OAAA;QAAK6C,SAAS,EAAC,wCAAwC;QAAAL,QAAA,eACrDxC,OAAA;UAAK6C,SAAS,EAAC,gDAAgD;UAAAL,QAAA,gBAE7DxC,OAAA,CAAClB,IAAI;YAACgE,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,mCAAmC;YAAAL,QAAA,gBACxDxC,OAAA,CAAChB,MAAM,CAAC+D,GAAG;cACTC,UAAU,EAAE;gBAAEC,MAAM,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cACxCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE;cAAI,CAAE;cAC9DT,SAAS,EAAC,2MAA2M;cAAAL,QAAA,gBAErNxC,OAAA,CAACZ,eAAe;gBAACyD,SAAS,EAAC;cAAoB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClD1D,OAAA;gBAAK6C,SAAS,EAAC;cAA6E;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACb1D,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAL,QAAA,gBAC5BxC,OAAA;gBAAM6C,SAAS,EAAE,kDACftC,UAAU,GAAG,eAAe,GAAG,2BAA2B,EACzD;gBAAAiC,QAAA,EAAC;cAEJ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP1D,OAAA;gBAAM6C,SAAS,EAAE,mDACftC,UAAU,GAAG,uBAAuB,GAAG,eAAe,EACrD;gBAAAiC,QAAA,EAAC;cAEJ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGP1D,OAAA;YAAK6C,SAAS,EAAC,uCAAuC;YAAAL,QAAA,EACnDP,eAAe,CAAC0B,GAAG,CAAEC,IAAI,iBACxB5D,OAAA,CAAChB,MAAM,CAAC+D,GAAG;cAETC,UAAU,EAAE;gBAAEL,CAAC,EAAE,CAAC;cAAE,CAAE;cACtBQ,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,eAE9BxC,OAAA,CAAClB,IAAI;gBACHgE,EAAE,EAAEc,IAAI,CAACzB,IAAK;gBACdU,SAAS,EAAE,2FACTR,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,GACf5B,UAAU,GACR,qEAAqE,GACrE,sDAAsD,GACxDA,UAAU,GACR,oEAAoE,GACpE,mEAAmE,EACxE;gBAAAiC,QAAA,gBAEHxC,OAAA;kBAAM6C,SAAS,EAAC,eAAe;kBAAAL,QAAA,EAAEoB,IAAI,CAAC1B;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACjDrB,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,iBAClBnC,OAAA,CAAChB,MAAM,CAAC+D,GAAG;kBACTc,QAAQ,EAAC,aAAa;kBACtBhB,SAAS,EAAC,6BAA6B;kBACvCM,UAAU,EAAE;oBAAEE,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE,GAAG;oBAAEQ,OAAO,EAAE;kBAAG;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CACF,EACA,CAACrB,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,iBACnBnC,OAAA;kBAAK6C,SAAS,EAAC;gBAAyJ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/K;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA3BFE,IAAI,CAAC1B,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BJ,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1D,OAAA;YAAK6C,SAAS,EAAC,kDAAkD;YAAAL,QAAA,eAC/DxC,OAAA,CAAChB,MAAM,CAAC+D,GAAG;cACTF,SAAS,EAAC,uBAAuB;cACjCG,UAAU,EAAE;gBAAEE,KAAK,EAAE;cAAK,CAAE;cAC5BC,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,gBAE9BxC,OAAA;gBAAK6C,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFxC,OAAA,CAACX,mBAAmB;kBAACwD,SAAS,EAAE,0CAC9BtC,UAAU,GAAG,iDAAiD,GAAG,sCAAsC;gBACtG;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1D,OAAA;gBACEqD,IAAI,EAAC,MAAM;gBACXU,WAAW,EAAC,0CAA0C;gBACtDC,KAAK,EAAEvD,WAAY;gBACnBwD,QAAQ,EAAG/C,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACU,MAAM,CAACoC,KAAK,CAAE;gBAChDnB,SAAS,EAAE,2EACTtC,UAAU,GACN,qJAAqJ,GACrJ,uJAAuJ;cAC7G;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACF1D,OAAA;gBAAK6C,SAAS,EAAE,oFACdtC,UAAU,GACN,4FAA4F,GAC5F,6EAA6E;cAChF;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGN1D,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAC1CxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;cACZlB,UAAU,EAAE;gBAAEE,KAAK,EAAE;cAAI,CAAE;cAC3BiB,QAAQ,EAAE;gBAAEjB,KAAK,EAAE;cAAI,CAAE;cACzBL,SAAS,EAAE,mDACTtC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;cAAAiC,QAAA,eAEHxC,OAAA,CAACT,SAAS;gBAACsD,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAEhB1D,OAAA,CAAClB,IAAI;cAACgE,EAAE,EAAC,UAAU;cAAAN,QAAA,eACjBxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;gBACZlB,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAI,CAAE;gBAC3BiB,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAI,CAAE;gBACzBL,SAAS,EAAE,mDACTtC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;gBAAAiC,QAAA,eAEHxC,OAAA,CAACV,QAAQ;kBAACuD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEP1D,OAAA,CAACH,YAAY;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAGf3C,eAAe,gBACdf,OAAA;cAAK6C,SAAS,EAAC,gBAAgB;cAAAL,QAAA,gBAC7BxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;gBACZlB,UAAU,EAAE;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAC5BiB,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAK,CAAE;gBAC1BL,SAAS,EAAE,4DACTtC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;gBAAAiC,QAAA,EAEF1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsD,cAAc,gBACnBpE,OAAA;kBACEqE,GAAG,EAAEvD,IAAI,CAACsD,cAAe;kBACzBE,GAAG,EAAC,SAAS;kBACbzB,SAAS,EAAC;gBAAmC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEF1D,OAAA,CAACV,QAAQ;kBAACuD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAChC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC,eAGhB1D,OAAA;gBAAK6C,SAAS,EAAC,kLAAkL;gBAAAL,QAAA,gBAC/LxC,OAAA;kBAAK6C,SAAS,EAAC,8BAA8B;kBAAAL,QAAA,gBAC3CxC,OAAA;oBAAG6C,SAAS,EAAC,mCAAmC;oBAAAL,QAAA,GAC7C1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,SAAS,EAAC,GAAC,EAACzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,QAAQ;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACJ1D,OAAA;oBAAG6C,SAAS,EAAC,uBAAuB;oBAAAL,QAAA,EAAE1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D;kBAAK;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACN1D,OAAA;kBAAK6C,SAAS,EAAC,MAAM;kBAAAL,QAAA,gBACnBxC,OAAA,CAAClB,IAAI;oBACHgE,EAAE,EAAC,UAAU;oBACbD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1D,OAAA,CAAClB,IAAI;oBACHgE,EAAE,EAAC,SAAS;oBACZD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1D,OAAA,CAAClB,IAAI;oBACHgE,EAAE,EAAC,WAAW;oBACdD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1D,OAAA;oBACE0E,OAAO,EAAE1D,MAAO;oBAChB6B,SAAS,EAAC,6GAA6G;oBAAAL,QAAA,EACxH;kBAED;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN1D,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAL,QAAA,gBAC1CxC,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACfxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;kBACZlB,UAAU,EAAE;oBAAEE,KAAK,EAAE;kBAAK,CAAE;kBAC5BiB,QAAQ,EAAE;oBAAEjB,KAAK,EAAE;kBAAK,CAAE;kBAC1BL,SAAS,EAAE,6EACTtC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;kBAAAiC,QAAA,EACJ;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACP1D,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClBxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;kBACZlB,UAAU,EAAE;oBAAEE,KAAK,EAAE;kBAAK,CAAE;kBAC5BiB,QAAQ,EAAE;oBAAEjB,KAAK,EAAE;kBAAK,CAAE;kBAC1BL,SAAS,EAAC,oIAAoI;kBAAAL,QAAA,EAC/I;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGD1D,OAAA;cACE0E,OAAO,EAAEA,CAAA,KAAMpE,SAAS,CAAC,CAACD,MAAM,CAAE;cAClCwC,SAAS,EAAE,2DACTtC,UAAU,GACN,2CAA2C,GAC3C,kCAAkC,EACrC;cAAAiC,QAAA,EAEFnC,MAAM,gBACLL,OAAA,CAACb,SAAS;gBAAC0D,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjC1D,OAAA,CAACd,SAAS;gBAAC2D,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA,CAACf,eAAe;QAAAuD,QAAA,EACbnC,MAAM,iBACLL,OAAA,CAAChB,MAAM,CAAC+D,GAAG;UACTL,OAAO,EAAE;YAAEiC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACnChC,OAAO,EAAE;YAAE+B,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAE;UACxCC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAChC/B,SAAS,EAAC,iEAAiE;UAAAL,QAAA,eAE3ExC,OAAA;YAAK6C,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAElCxC,OAAA;cAAK6C,SAAS,EAAC,UAAU;cAAAL,QAAA,gBACvBxC,OAAA;gBAAK6C,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFxC,OAAA,CAACX,mBAAmB;kBAACwD,SAAS,EAAC;gBAAuB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACN1D,OAAA;gBACEqD,IAAI,EAAC,MAAM;gBACXU,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAEvD,WAAY;gBACnBwD,QAAQ,EAAG/C,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACU,MAAM,CAACoC,KAAK,CAAE;gBAChDnB,SAAS,EAAC;cAA0I;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1D,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAL,QAAA,EACvBP,eAAe,CAAC0B,GAAG,CAAEC,IAAI,iBACxB5D,OAAA,CAAClB,IAAI;gBAEHgE,EAAE,EAAEc,IAAI,CAACzB,IAAK;gBACduC,OAAO,EAAEA,CAAA,KAAMpE,SAAS,CAAC,KAAK,CAAE;gBAChCuC,SAAS,EAAE,mFACTR,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,GACf,2CAA2C,GAC3C,iCAAiC,EACpC;gBAAAK,QAAA,gBAEHxC,OAAA,CAAC4D,IAAI,CAACxB,IAAI;kBAACS,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC1D,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAL,QAAA,EAAEoB,IAAI,CAAC1B;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAV3CE,IAAI,CAAC1B,IAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1D,OAAA;cAAK6C,SAAS,EAAC,gEAAgE;cAAAL,QAAA,gBAC7ExC,OAAA;gBAAQ6C,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBAChGxC,OAAA,CAACT,SAAS;kBAACsD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC1D,OAAA;kBAAM6C,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACT1D,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,UAAU;gBAACD,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBAC5GxC,OAAA,CAACV,QAAQ;kBAACuD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC1D,OAAA;kBAAM6C,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACP1D,OAAA;gBAAK6C,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnDxC,OAAA,CAACH,YAAY;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChB1D,OAAA;kBAAM6C,SAAS,EAAC,uBAAuB;kBAAAL,QAAA,EAAC;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGb1D,OAAA;MAAK6C,SAAS,EAAC;IAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAACtD,EAAA,CAhWID,UAAU;EAAA,QAKGpB,WAAW,EACce,OAAO;AAAA;AAAAgF,EAAA,GAN7C3E,UAAU;AAkWhB,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}