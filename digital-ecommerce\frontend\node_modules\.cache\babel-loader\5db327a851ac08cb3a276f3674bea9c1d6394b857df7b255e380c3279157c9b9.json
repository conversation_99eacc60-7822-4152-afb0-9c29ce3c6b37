{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{ShoppingBagIcon,TagIcon,UserGroupIcon,CurrencyDollarIcon,ArrowTrendingUpIcon,ArrowTrendingDownIcon,EyeIcon,PlusIcon}from'@heroicons/react/24/outline';import{useAdmin}from'../contexts/AdminContext';import{useProducts}from'../contexts/ProductContext';import AdminLayout from'../components/AdminLayout';import AddProductModal from'../components/AddProductModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminDashboardPage=()=>{const{admin}=useAdmin();const{products,categories,addProduct}=useProducts();const[showAddProductModal,setShowAddProductModal]=useState(false);const[stats,setStats]=useState({totalProducts:0,totalCategories:0,totalRevenue:0,totalOrders:0,recentProducts:[],lowStockProducts:[]});useEffect(()=>{// Calculate dashboard statistics\nconst totalProducts=products.length;const totalCategories=categories.length;const totalRevenue=products.reduce((sum,product)=>sum+product.price*(product.sold||0),0);const totalOrders=products.reduce((sum,product)=>sum+(product.sold||0),0);// Get recent products (last 5)\nconst recentProducts=products.slice(-5).reverse();// Get low stock products\nconst lowStockProducts=products.filter(product=>product.stockCount&&product.stockCount<10).slice(0,5);setStats({totalProducts,totalCategories,totalRevenue,totalOrders,recentProducts,lowStockProducts});},[]);const StatCard=_ref=>{let{title,value,icon:Icon,trend,trendValue,color='blue'}=_ref;return/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"p-6 rounded-xl shadow-lg transition-all duration-300 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-3xl font-bold text-gray-900\",children:value}),trend&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mt-2\",children:[trend==='up'?/*#__PURE__*/_jsx(ArrowTrendingUpIcon,{className:\"w-4 h-4 text-green-500 mr-1\"}):/*#__PURE__*/_jsx(ArrowTrendingDownIcon,{className:\"w-4 h-4 text-red-500 mr-1\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm \".concat(trend==='up'?'text-green-600':'text-red-600'),children:trendValue})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 rounded-full \".concat(color==='blue'?'bg-blue-100 dark:bg-blue-900/20':color==='green'?'bg-green-100 dark:bg-green-900/20':color==='yellow'?'bg-yellow-100 dark:bg-yellow-900/20':'bg-purple-100 dark:bg-purple-900/20'),children:/*#__PURE__*/_jsx(Icon,{className:\"w-6 h-6 \".concat(color==='blue'?'text-blue-600':color==='green'?'text-green-600':color==='yellow'?'text-yellow-600':'text-purple-600')})})]})});};return/*#__PURE__*/_jsxs(AdminLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:[\"Welcome back, \",admin===null||admin===void 0?void 0:admin.firstName,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Here's what's happening with your store today.\"})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>setShowAddProductModal(true),className:\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Add Product\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(StatCard,{title:\"Total Products\",value:stats.totalProducts,icon:ShoppingBagIcon,trend:\"up\",trendValue:\"+12%\",color:\"blue\"}),/*#__PURE__*/_jsx(StatCard,{title:\"Categories\",value:stats.totalCategories,icon:TagIcon,color:\"green\"}),/*#__PURE__*/_jsx(StatCard,{title:\"Total Revenue\",value:\"$\".concat(stats.totalRevenue.toLocaleString()),icon:CurrencyDollarIcon,trend:\"up\",trendValue:\"+8.2%\",color:\"yellow\"}),/*#__PURE__*/_jsx(StatCard,{title:\"Total Orders\",value:stats.totalOrders,icon:UserGroupIcon,trend:\"up\",trendValue:\"+23%\",color:\"purple\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:\"p-6 rounded-xl shadow-lg bg-white\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Recent Products\"}),/*#__PURE__*/_jsx(\"button\",{className:\"text-sm text-light-orange-600 hover:text-light-orange-700\",children:\"View All\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:stats.recentProducts.map((product,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.images?product.images[0]:'/placeholder-image.jpg',alt:product.name,className:\"w-12 h-12 rounded-lg object-cover\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium truncate text-gray-900\",children:product.name}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"$\",product.price]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"p-2 rounded-lg hover:bg-gray-100\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4 text-gray-400\"})})]},product.id))})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:\"p-6 rounded-xl shadow-lg bg-white\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Low Stock Alert\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full\",children:[stats.lowStockProducts.length,\" items\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:stats.lowStockProducts.length>0?stats.lowStockProducts.map(product=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.images?product.images[0]:'/placeholder-image.jpg',alt:product.name,className:\"w-10 h-10 rounded-lg object-cover\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900\",children:product.name}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-red-600\",children:[product.stockCount,\" left\"]})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors\",children:\"Restock\"})]},product.id)):/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"All products are well stocked!\"})})]})]})]}),/*#__PURE__*/_jsx(AddProductModal,{isOpen:showAddProductModal,onClose:()=>setShowAddProductModal(false),onSubmit:async productData=>{const result=await addProduct(productData);if(result.success){setShowAddProductModal(false);// Show success notification (you can add a toast notification here)\nconsole.log('Product added successfully:',result.product);}else{// Show error notification\nconsole.error('Failed to add product:',result.error);}}})]});};export default AdminDashboardPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "ShoppingBagIcon", "TagIcon", "UserGroupIcon", "CurrencyDollarIcon", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "EyeIcon", "PlusIcon", "useAdmin", "useProducts", "AdminLayout", "AddProductModal", "jsx", "_jsx", "jsxs", "_jsxs", "AdminDashboardPage", "admin", "products", "categories", "addProduct", "showAddProductModal", "setShowAddProductModal", "stats", "setStats", "totalProducts", "totalCategories", "totalRevenue", "totalOrders", "recentProducts", "lowStockProducts", "length", "reduce", "sum", "product", "price", "sold", "slice", "reverse", "filter", "stockCount", "StatCard", "_ref", "title", "value", "icon", "Icon", "trend", "trendValue", "color", "div", "initial", "opacity", "y", "animate", "className", "children", "concat", "firstName", "button", "whileHover", "scale", "whileTap", "onClick", "toLocaleString", "x", "map", "index", "src", "images", "alt", "name", "id", "isOpen", "onClose", "onSubmit", "productData", "result", "success", "console", "log", "error"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminDashboardPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ShoppingBagIcon,\n  TagIcon,\n  UserGroupIcon,\n  CurrencyDollarIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n  EyeIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\n\nconst AdminDashboardPage = () => {\n  const { admin } = useAdmin();\n  const { products, categories, addProduct } = useProducts();\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalCategories: 0,\n    totalRevenue: 0,\n    totalOrders: 0,\n    recentProducts: [],\n    lowStockProducts: []\n  });\n\n  useEffect(() => {\n    // Calculate dashboard statistics\n    const totalProducts = products.length;\n    const totalCategories = categories.length;\n    const totalRevenue = products.reduce((sum, product) => sum + (product.price * (product.sold || 0)), 0);\n    const totalOrders = products.reduce((sum, product) => sum + (product.sold || 0), 0);\n    \n    // Get recent products (last 5)\n    const recentProducts = products.slice(-5).reverse();\n    \n    // Get low stock products\n    const lowStockProducts = products.filter(product => \n      product.stockCount && product.stockCount < 10\n    ).slice(0, 5);\n\n    setStats({\n      totalProducts,\n      totalCategories,\n      totalRevenue,\n      totalOrders,\n      recentProducts,\n      lowStockProducts\n    });\n  }, []);\n\n  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue' }) => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"p-6 rounded-xl shadow-lg transition-all duration-300 bg-white\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600\">\n            {title}\n          </p>\n          <p className=\"text-3xl font-bold text-gray-900\">\n            {value}\n          </p>\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              {trend === 'up' ? (\n                <ArrowTrendingUpIcon className=\"w-4 h-4 text-green-500 mr-1\" />\n              ) : (\n                <ArrowTrendingDownIcon className=\"w-4 h-4 text-red-500 mr-1\" />\n              )}\n              <span className={`text-sm ${\n                trend === 'up' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trendValue}\n              </span>\n            </div>\n          )}\n        </div>\n        <div className={`p-3 rounded-full ${\n          color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' :\n          color === 'green' ? 'bg-green-100 dark:bg-green-900/20' :\n          color === 'yellow' ? 'bg-yellow-100 dark:bg-yellow-900/20' :\n          'bg-purple-100 dark:bg-purple-900/20'\n        }`}>\n          <Icon className={`w-6 h-6 ${\n            color === 'blue' ? 'text-blue-600' :\n            color === 'green' ? 'text-green-600' :\n            color === 'yellow' ? 'text-yellow-600' :\n            'text-purple-600'\n          }`} />\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-8\">\n        {/* Welcome Section */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Welcome back, {admin?.firstName}!\n            </h1>\n            <p className=\"mt-2 text-gray-600\">\n              Here's what's happening with your store today.\n            </p>\n          </div>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setShowAddProductModal(true)}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n          >\n            <PlusIcon className=\"w-5 h-5\" />\n            <span>Add Product</span>\n          </motion.button>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <StatCard\n            title=\"Total Products\"\n            value={stats.totalProducts}\n            icon={ShoppingBagIcon}\n            trend=\"up\"\n            trendValue=\"+12%\"\n            color=\"blue\"\n          />\n          <StatCard\n            title=\"Categories\"\n            value={stats.totalCategories}\n            icon={TagIcon}\n            color=\"green\"\n          />\n          <StatCard\n            title=\"Total Revenue\"\n            value={`$${stats.totalRevenue.toLocaleString()}`}\n            icon={CurrencyDollarIcon}\n            trend=\"up\"\n            trendValue=\"+8.2%\"\n            color=\"yellow\"\n          />\n          <StatCard\n            title=\"Total Orders\"\n            value={stats.totalOrders}\n            icon={UserGroupIcon}\n            trend=\"up\"\n            trendValue=\"+23%\"\n            color=\"purple\"\n          />\n        </div>\n\n        {/* Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Products */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className=\"p-6 rounded-xl shadow-lg bg-white\"\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                Recent Products\n              </h3>\n              <button className=\"text-sm text-light-orange-600 hover:text-light-orange-700\">\n                View All\n              </button>\n            </div>\n            <div className=\"space-y-4\">\n              {stats.recentProducts.map((product, index) => (\n                <div key={product.id} className=\"flex items-center space-x-4\">\n                  <img\n                    src={product.images ? product.images[0] : '/placeholder-image.jpg'}\n                    alt={product.name}\n                    className=\"w-12 h-12 rounded-lg object-cover\"\n                  />\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium truncate text-gray-900\">\n                      {product.name}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      ${product.price}\n                    </p>\n                  </div>\n                  <button className=\"p-2 rounded-lg hover:bg-gray-100\">\n                    <EyeIcon className=\"w-4 h-4 text-gray-400\" />\n                  </button>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Low Stock Alert */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className=\"p-6 rounded-xl shadow-lg bg-white\"\n          >\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                Low Stock Alert\n              </h3>\n              <span className=\"px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-600 text-xs font-medium rounded-full\">\n                {stats.lowStockProducts.length} items\n              </span>\n            </div>\n            <div className=\"space-y-4\">\n              {stats.lowStockProducts.length > 0 ? (\n                stats.lowStockProducts.map((product) => (\n                  <div key={product.id} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <img\n                        src={product.images ? product.images[0] : '/placeholder-image.jpg'}\n                        alt={product.name}\n                        className=\"w-10 h-10 rounded-lg object-cover\"\n                      />\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {product.name}\n                        </p>\n                        <p className=\"text-xs text-red-600\">\n                          {product.stockCount} left\n                        </p>\n                      </div>\n                    </div>\n                    <button className=\"px-3 py-1 bg-light-orange-500 text-white text-xs rounded-lg hover:bg-light-orange-600 transition-colors\">\n                      Restock\n                    </button>\n                  </div>\n                ))\n              ) : (\n                <p className=\"text-sm text-gray-500\">\n                  All products are well stocked!\n                </p>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Add Product Modal */}\n      <AddProductModal\n        isOpen={showAddProductModal}\n        onClose={() => setShowAddProductModal(false)}\n        onSubmit={async (productData) => {\n          const result = await addProduct(productData);\n          if (result.success) {\n            setShowAddProductModal(false);\n            // Show success notification (you can add a toast notification here)\n            console.log('Product added successfully:', result.product);\n          } else {\n            // Show error notification\n            console.error('Failed to add product:', result.error);\n          }\n        }}\n      />\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboardPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,eAAe,CACfC,OAAO,CACPC,aAAa,CACbC,kBAAkB,CAClBC,mBAAmB,CACnBC,qBAAqB,CACrBC,OAAO,CACPC,QAAQ,KACH,6BAA6B,CACpC,OAASC,QAAQ,KAAQ,0BAA0B,CACnD,OAASC,WAAW,KAAQ,4BAA4B,CACxD,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAEC,KAAM,CAAC,CAAGT,QAAQ,CAAC,CAAC,CAC5B,KAAM,CAAEU,QAAQ,CAAEC,UAAU,CAAEC,UAAW,CAAC,CAAGX,WAAW,CAAC,CAAC,CAC1D,KAAM,CAACY,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC0B,KAAK,CAAEC,QAAQ,CAAC,CAAG3B,QAAQ,CAAC,CACjC4B,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,YAAY,CAAE,CAAC,CACfC,WAAW,CAAE,CAAC,CACdC,cAAc,CAAE,EAAE,CAClBC,gBAAgB,CAAE,EACpB,CAAC,CAAC,CAEFhC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2B,aAAa,CAAGP,QAAQ,CAACa,MAAM,CACrC,KAAM,CAAAL,eAAe,CAAGP,UAAU,CAACY,MAAM,CACzC,KAAM,CAAAJ,YAAY,CAAGT,QAAQ,CAACc,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAKD,GAAG,CAAIC,OAAO,CAACC,KAAK,EAAID,OAAO,CAACE,IAAI,EAAI,CAAC,CAAE,CAAE,CAAC,CAAC,CACtG,KAAM,CAAAR,WAAW,CAAGV,QAAQ,CAACc,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAKD,GAAG,EAAIC,OAAO,CAACE,IAAI,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAEnF;AACA,KAAM,CAAAP,cAAc,CAAGX,QAAQ,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAR,gBAAgB,CAAGZ,QAAQ,CAACqB,MAAM,CAACL,OAAO,EAC9CA,OAAO,CAACM,UAAU,EAAIN,OAAO,CAACM,UAAU,CAAG,EAC7C,CAAC,CAACH,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEbb,QAAQ,CAAC,CACPC,aAAa,CACbC,eAAe,CACfC,YAAY,CACZC,WAAW,CACXC,cAAc,CACdC,gBACF,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,IAAI,CAAEC,KAAK,CAAEC,UAAU,CAAEC,KAAK,CAAG,MAAO,CAAC,CAAAP,IAAA,oBAC/E7B,IAAA,CAACd,MAAM,CAACmD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAEzEzC,KAAA,QAAKwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,MAAG0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC7Cb,KAAK,CACL,CAAC,cACJ9B,IAAA,MAAG0C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5CZ,KAAK,CACL,CAAC,CACHG,KAAK,eACJhC,KAAA,QAAKwC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EACpCT,KAAK,GAAK,IAAI,cACblC,IAAA,CAACT,mBAAmB,EAACmD,SAAS,CAAC,6BAA6B,CAAE,CAAC,cAE/D1C,IAAA,CAACR,qBAAqB,EAACkD,SAAS,CAAC,2BAA2B,CAAE,CAC/D,cACD1C,IAAA,SAAM0C,SAAS,YAAAE,MAAA,CACbV,KAAK,GAAK,IAAI,CAAG,gBAAgB,CAAG,cAAc,CACjD,CAAAS,QAAA,CACAR,UAAU,CACP,CAAC,EACJ,CACN,EACE,CAAC,cACNnC,IAAA,QAAK0C,SAAS,qBAAAE,MAAA,CACZR,KAAK,GAAK,MAAM,CAAG,iCAAiC,CACpDA,KAAK,GAAK,OAAO,CAAG,mCAAmC,CACvDA,KAAK,GAAK,QAAQ,CAAG,qCAAqC,CAC1D,qCAAqC,CACpC,CAAAO,QAAA,cACD3C,IAAA,CAACiC,IAAI,EAACS,SAAS,YAAAE,MAAA,CACbR,KAAK,GAAK,MAAM,CAAG,eAAe,CAClCA,KAAK,GAAK,OAAO,CAAG,gBAAgB,CACpCA,KAAK,GAAK,QAAQ,CAAG,iBAAiB,CACtC,iBAAiB,CAChB,CAAE,CAAC,CACH,CAAC,EACH,CAAC,CACI,CAAC,EACd,CAED,mBACElC,KAAA,CAACL,WAAW,EAAA8C,QAAA,eACVzC,KAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBzC,KAAA,QAAKwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzC,KAAA,QAAAyC,QAAA,eACEzC,KAAA,OAAIwC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAC,gBACjC,CAACvC,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyC,SAAS,CAAC,GAClC,EAAI,CAAC,cACL7C,IAAA,MAAG0C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gDAElC,CAAG,CAAC,EACD,CAAC,cACNzC,KAAA,CAAChB,MAAM,CAAC4D,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAMzC,sBAAsB,CAAC,IAAI,CAAE,CAC5CiC,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAEvI3C,IAAA,CAACN,QAAQ,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC1C,IAAA,SAAA2C,QAAA,CAAM,aAAW,CAAM,CAAC,EACX,CAAC,EACb,CAAC,cAGNzC,KAAA,QAAKwC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE3C,IAAA,CAAC4B,QAAQ,EACPE,KAAK,CAAC,gBAAgB,CACtBC,KAAK,CAAErB,KAAK,CAACE,aAAc,CAC3BoB,IAAI,CAAE7C,eAAgB,CACtB+C,KAAK,CAAC,IAAI,CACVC,UAAU,CAAC,MAAM,CACjBC,KAAK,CAAC,MAAM,CACb,CAAC,cACFpC,IAAA,CAAC4B,QAAQ,EACPE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAErB,KAAK,CAACG,eAAgB,CAC7BmB,IAAI,CAAE5C,OAAQ,CACdgD,KAAK,CAAC,OAAO,CACd,CAAC,cACFpC,IAAA,CAAC4B,QAAQ,EACPE,KAAK,CAAC,eAAe,CACrBC,KAAK,KAAAa,MAAA,CAAMlC,KAAK,CAACI,YAAY,CAACqC,cAAc,CAAC,CAAC,CAAG,CACjDnB,IAAI,CAAE1C,kBAAmB,CACzB4C,KAAK,CAAC,IAAI,CACVC,UAAU,CAAC,OAAO,CAClBC,KAAK,CAAC,QAAQ,CACf,CAAC,cACFpC,IAAA,CAAC4B,QAAQ,EACPE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAErB,KAAK,CAACK,WAAY,CACzBiB,IAAI,CAAE3C,aAAc,CACpB6C,KAAK,CAAC,IAAI,CACVC,UAAU,CAAC,MAAM,CACjBC,KAAK,CAAC,QAAQ,CACf,CAAC,EACC,CAAC,cAGNlC,KAAA,QAAKwC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDzC,KAAA,CAAChB,MAAM,CAACmD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCX,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAE,CAC9BV,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAE7CzC,KAAA,QAAKwC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD3C,IAAA,OAAI0C,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAEpD,CAAI,CAAC,cACL3C,IAAA,WAAQ0C,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,UAE9E,CAAQ,CAAC,EACN,CAAC,cACN3C,IAAA,QAAK0C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBjC,KAAK,CAACM,cAAc,CAACqC,GAAG,CAAC,CAAChC,OAAO,CAAEiC,KAAK,gBACvCpD,KAAA,QAAsBwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC3D3C,IAAA,QACEuD,GAAG,CAAElC,OAAO,CAACmC,MAAM,CAAGnC,OAAO,CAACmC,MAAM,CAAC,CAAC,CAAC,CAAG,wBAAyB,CACnEC,GAAG,CAAEpC,OAAO,CAACqC,IAAK,CAClBhB,SAAS,CAAC,mCAAmC,CAC9C,CAAC,cACFxC,KAAA,QAAKwC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3C,IAAA,MAAG0C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACtDtB,OAAO,CAACqC,IAAI,CACZ,CAAC,cACJxD,KAAA,MAAGwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,GAClC,CAACtB,OAAO,CAACC,KAAK,EACd,CAAC,EACD,CAAC,cACNtB,IAAA,WAAQ0C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAClD3C,IAAA,CAACP,OAAO,EAACiD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvC,CAAC,GAhBDrB,OAAO,CAACsC,EAiBb,CACN,CAAC,CACC,CAAC,EACI,CAAC,cAGbzD,KAAA,CAAChB,MAAM,CAACmD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,EAAG,CAAE,CAC/BX,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAE,CAC9BV,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAE7CzC,KAAA,QAAKwC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD3C,IAAA,OAAI0C,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAEpD,CAAI,CAAC,cACLzC,KAAA,SAAMwC,SAAS,CAAC,uFAAuF,CAAAC,QAAA,EACpGjC,KAAK,CAACO,gBAAgB,CAACC,MAAM,CAAC,QACjC,EAAM,CAAC,EACJ,CAAC,cACNlB,IAAA,QAAK0C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBjC,KAAK,CAACO,gBAAgB,CAACC,MAAM,CAAG,CAAC,CAChCR,KAAK,CAACO,gBAAgB,CAACoC,GAAG,CAAEhC,OAAO,eACjCnB,KAAA,QAAsBwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eACjEzC,KAAA,QAAKwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3C,IAAA,QACEuD,GAAG,CAAElC,OAAO,CAACmC,MAAM,CAAGnC,OAAO,CAACmC,MAAM,CAAC,CAAC,CAAC,CAAG,wBAAyB,CACnEC,GAAG,CAAEpC,OAAO,CAACqC,IAAK,CAClBhB,SAAS,CAAC,mCAAmC,CAC9C,CAAC,cACFxC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,MAAG0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC7CtB,OAAO,CAACqC,IAAI,CACZ,CAAC,cACJxD,KAAA,MAAGwC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAChCtB,OAAO,CAACM,UAAU,CAAC,OACtB,EAAG,CAAC,EACD,CAAC,EACH,CAAC,cACN3B,IAAA,WAAQ0C,SAAS,CAAC,yGAAyG,CAAAC,QAAA,CAAC,SAE5H,CAAQ,CAAC,GAlBDtB,OAAO,CAACsC,EAmBb,CACN,CAAC,cAEF3D,IAAA,MAAG0C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAErC,CAAG,CACJ,CACE,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,cAGN3C,IAAA,CAACF,eAAe,EACd8D,MAAM,CAAEpD,mBAAoB,CAC5BqD,OAAO,CAAEA,CAAA,GAAMpD,sBAAsB,CAAC,KAAK,CAAE,CAC7CqD,QAAQ,CAAE,KAAO,CAAAC,WAAW,EAAK,CAC/B,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAzD,UAAU,CAACwD,WAAW,CAAC,CAC5C,GAAIC,MAAM,CAACC,OAAO,CAAE,CAClBxD,sBAAsB,CAAC,KAAK,CAAC,CAC7B;AACAyD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEH,MAAM,CAAC3C,OAAO,CAAC,CAC5D,CAAC,IAAM,CACL;AACA6C,OAAO,CAACE,KAAK,CAAC,wBAAwB,CAAEJ,MAAM,CAACI,KAAK,CAAC,CACvD,CACF,CAAE,CACH,CAAC,EACS,CAAC,CAElB,CAAC,CAED,cAAe,CAAAjE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}