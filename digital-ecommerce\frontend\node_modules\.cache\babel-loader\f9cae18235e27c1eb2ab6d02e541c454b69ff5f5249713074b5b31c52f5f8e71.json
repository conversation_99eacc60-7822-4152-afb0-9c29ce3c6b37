{"ast": null, "code": "import React,{useState}from'react';import{Link,useLocation,useNavigate}from'react-router-dom';import{motion,AnimatePresence}from'framer-motion';import{Bars3Icon,XMarkIcon,HomeIcon,ShoppingBagIcon,TagIcon,PhotoIcon,ChartBarIcon,CogIcon,ArrowRightOnRectangleIcon,UserIcon,ClipboardDocumentListIcon}from'@heroicons/react/24/outline';import{useAdmin}from'../contexts/AdminContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AdminLayout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);const{admin,adminLogout,hasPermission}=useAdmin();const location=useLocation();const navigate=useNavigate();const handleLogout=()=>{adminLogout();navigate('/admin/login');};const navigationItems=[{name:'Dashboard',href:'/admin/dashboard',icon:HomeIcon,permission:null},{name:'Products',href:'/admin/products',icon:ShoppingBagIcon,permission:'products'},{name:'Categories',href:'/admin/categories',icon:TagIcon,permission:'categories'},{name:'Inventory',href:'/admin/inventory',icon:ClipboardDocumentListIcon,permission:'inventory'},{name:'Media',href:'/admin/media',icon:PhotoIcon,permission:'media'},{name:'Analytics',href:'/admin/analytics',icon:ChartBarIcon,permission:'analytics'},{name:'Settings',href:'/admin/settings',icon:CogIcon,permission:'settings'}];const filteredNavItems=navigationItems.filter(item=>!item.permission||hasPermission(item.permission));const isActive=path=>location.pathname===path;const Sidebar=_ref2=>{var _admin$role;let{mobile=false}=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col h-full bg-white border-r border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16 px-6 border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/admin/dashboard\",className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold text-gray-900\",children:\"Admin\"})]}),mobile&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSidebarOpen(false),className:\"p-2 rounded-md text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-6 h-6\"})})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"flex-1 px-4 py-6 space-y-2\",children:filteredNavItems.map(item=>/*#__PURE__*/_jsxs(Link,{to:item.href,onClick:mobile?()=>setSidebarOpen(false):undefined,className:\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 \".concat(isActive(item.href)?'bg-light-orange-100 text-light-orange-700':'text-gray-700 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(item.icon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:item.name})]},item.name))}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-t border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 p-3 rounded-lg bg-gray-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-light-orange-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium text-gray-900 truncate\",children:[admin===null||admin===void 0?void 0:admin.firstName,\" \",admin===null||admin===void 0?void 0:admin.lastName]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 truncate\",children:admin===null||admin===void 0?void 0:(_admin$role=admin.role)===null||_admin$role===void 0?void 0:_admin$role.replace('_',' ')})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleLogout,className:\"w-full mt-3 flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-red-700 hover:bg-red-50 transition-colors duration-200\",children:[/*#__PURE__*/_jsx(ArrowRightOnRectangleIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Sign Out\"})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",children:/*#__PURE__*/_jsx(Sidebar,{})}),/*#__PURE__*/_jsx(AnimatePresence,{children:sidebarOpen&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"fixed inset-0 z-40 lg:hidden\",onClick:()=>setSidebarOpen(false),children:/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black opacity-50\"})}),/*#__PURE__*/_jsx(motion.div,{initial:{x:-256},animate:{x:0},exit:{x:-256},transition:{type:\"spring\",damping:30,stiffness:300},className:\"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\",children:/*#__PURE__*/_jsx(Sidebar,{mobile:true})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:pl-64\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 bg-white border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSidebarOpen(true),className:\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\",children:/*#__PURE__*/_jsx(Bars3Icon,{className:\"w-6 h-6\"})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"Admin Dashboard\"})]})}),/*#__PURE__*/_jsx(\"main\",{className:\"p-4 sm:p-6 lg:p-8\",children:children})]})]});};export default AdminLayout;", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "HomeIcon", "ShoppingBagIcon", "TagIcon", "PhotoIcon", "ChartBarIcon", "CogIcon", "ArrowRightOnRectangleIcon", "UserIcon", "ClipboardDocumentListIcon", "useAdmin", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AdminLayout", "_ref", "children", "sidebarOpen", "setSidebarOpen", "admin", "adminLogout", "hasPermission", "location", "navigate", "handleLogout", "navigationItems", "name", "href", "icon", "permission", "filteredNavItems", "filter", "item", "isActive", "path", "pathname", "Sidebar", "_ref2", "_admin$role", "mobile", "className", "to", "onClick", "map", "undefined", "concat", "firstName", "lastName", "role", "replace", "div", "initial", "opacity", "animate", "exit", "x", "transition", "type", "damping", "stiffness"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AdminLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  ShoppingBagIcon,\n  TagIcon,\n  PhotoIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  UserIcon,\n  ClipboardDocumentListIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\n\nconst AdminLayout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { admin, adminLogout, hasPermission } = useAdmin();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    adminLogout();\n    navigate('/admin/login');\n  };\n\n  const navigationItems = [\n    {\n      name: 'Dashboard',\n      href: '/admin/dashboard',\n      icon: HomeIcon,\n      permission: null\n    },\n    {\n      name: 'Products',\n      href: '/admin/products',\n      icon: ShoppingBagIcon,\n      permission: 'products'\n    },\n    {\n      name: 'Categories',\n      href: '/admin/categories',\n      icon: TagIcon,\n      permission: 'categories'\n    },\n    {\n      name: 'Inventory',\n      href: '/admin/inventory',\n      icon: ClipboardDocumentListIcon,\n      permission: 'inventory'\n    },\n    {\n      name: 'Media',\n      href: '/admin/media',\n      icon: PhotoIcon,\n      permission: 'media'\n    },\n    {\n      name: 'Analytics',\n      href: '/admin/analytics',\n      icon: ChartBarIcon,\n      permission: 'analytics'\n    },\n    {\n      name: 'Settings',\n      href: '/admin/settings',\n      icon: CogIcon,\n      permission: 'settings'\n    }\n  ];\n\n  const filteredNavItems = navigationItems.filter(item => \n    !item.permission || hasPermission(item.permission)\n  );\n\n  const isActive = (path) => location.pathname === path;\n\n  const Sidebar = ({ mobile = false }) => (\n    <div className=\"flex flex-col h-full bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200\">\n        <Link to=\"/admin/dashboard\" className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-lg flex items-center justify-center\">\n            <ShoppingBagIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <span className=\"text-xl font-bold text-gray-900\">\n            Admin\n          </span>\n        </Link>\n        {mobile && (\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-600\"\n          >\n            <XMarkIcon className=\"w-6 h-6\" />\n          </button>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {filteredNavItems.map((item) => (\n          <Link\n            key={item.name}\n            to={item.href}\n            onClick={mobile ? () => setSidebarOpen(false) : undefined}\n            className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${\n              isActive(item.href)\n                ? 'bg-light-orange-100 text-light-orange-700'\n                : 'text-gray-700 hover:bg-gray-100'\n            }`}\n          >\n            <item.icon className=\"w-5 h-5\" />\n            <span>{item.name}</span>\n          </Link>\n        ))}\n      </nav>\n\n      {/* User Info & Logout */}\n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-gray-50\">\n          <div className=\"w-8 h-8 bg-light-orange-500 rounded-full flex items-center justify-center\">\n            <UserIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-medium text-gray-900 truncate\">\n              {admin?.firstName} {admin?.lastName}\n            </p>\n            <p className=\"text-xs text-gray-500 truncate\">\n              {admin?.role?.replace('_', ' ')}\n            </p>\n          </div>\n        </div>\n        <button\n          onClick={handleLogout}\n          className=\"w-full mt-3 flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-red-700 hover:bg-red-50 transition-colors duration-200\"\n        >\n          <ArrowRightOnRectangleIcon className=\"w-5 h-5\" />\n          <span>Sign Out</span>\n        </button>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile Sidebar */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-40 lg:hidden\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <div className=\"absolute inset-0 bg-black opacity-50\" />\n            </motion.div>\n            <motion.div\n              initial={{ x: -256 }}\n              animate={{ x: 0 }}\n              exit={{ x: -256 }}\n              transition={{ type: \"spring\", damping: 30, stiffness: 300 }}\n              className=\"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\"\n            >\n              <Sidebar mobile />\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Main Content */}\n      <div className=\"lg:pl-64\">\n        {/* Top Header */}\n        <header className=\"sticky top-0 z-30 flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 bg-white border-b border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\"\n            >\n              <Bars3Icon className=\"w-6 h-6\" />\n            </button>\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              Admin Dashboard\n            </h1>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"p-4 sm:p-6 lg:p-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,SAAS,CACTC,SAAS,CACTC,QAAQ,CACRC,eAAe,CACfC,OAAO,CACPC,SAAS,CACTC,YAAY,CACZC,OAAO,CACPC,yBAAyB,CACzBC,QAAQ,CACRC,yBAAyB,KACpB,6BAA6B,CACpC,OAASC,QAAQ,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpD,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAE6B,KAAK,CAAEC,WAAW,CAAEC,aAAc,CAAC,CAAGd,QAAQ,CAAC,CAAC,CACxD,KAAM,CAAAe,QAAQ,CAAG9B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+B,QAAQ,CAAG9B,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAA+B,YAAY,CAAGA,CAAA,GAAM,CACzBJ,WAAW,CAAC,CAAC,CACbG,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CACEC,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAE9B,QAAQ,CACd+B,UAAU,CAAE,IACd,CAAC,CACD,CACEH,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,iBAAiB,CACvBC,IAAI,CAAE7B,eAAe,CACrB8B,UAAU,CAAE,UACd,CAAC,CACD,CACEH,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE5B,OAAO,CACb6B,UAAU,CAAE,YACd,CAAC,CACD,CACEH,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAEtB,yBAAyB,CAC/BuB,UAAU,CAAE,WACd,CAAC,CACD,CACEH,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,cAAc,CACpBC,IAAI,CAAE3B,SAAS,CACf4B,UAAU,CAAE,OACd,CAAC,CACD,CACEH,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,kBAAkB,CACxBC,IAAI,CAAE1B,YAAY,CAClB2B,UAAU,CAAE,WACd,CAAC,CACD,CACEH,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,iBAAiB,CACvBC,IAAI,CAAEzB,OAAO,CACb0B,UAAU,CAAE,UACd,CAAC,CACF,CAED,KAAM,CAAAC,gBAAgB,CAAGL,eAAe,CAACM,MAAM,CAACC,IAAI,EAClD,CAACA,IAAI,CAACH,UAAU,EAAIR,aAAa,CAACW,IAAI,CAACH,UAAU,CACnD,CAAC,CAED,KAAM,CAAAI,QAAQ,CAAIC,IAAI,EAAKZ,QAAQ,CAACa,QAAQ,GAAKD,IAAI,CAErD,KAAM,CAAAE,OAAO,CAAGC,KAAA,OAAAC,WAAA,IAAC,CAAEC,MAAM,CAAG,KAAM,CAAC,CAAAF,KAAA,oBACjC1B,KAAA,QAAK6B,SAAS,CAAC,wDAAwD,CAAAxB,QAAA,eAErEL,KAAA,QAAK6B,SAAS,CAAC,sEAAsE,CAAAxB,QAAA,eACnFL,KAAA,CAACpB,IAAI,EAACkD,EAAE,CAAC,kBAAkB,CAACD,SAAS,CAAC,6BAA6B,CAAAxB,QAAA,eACjEP,IAAA,QAAK+B,SAAS,CAAC,gHAAgH,CAAAxB,QAAA,cAC7HP,IAAA,CAACV,eAAe,EAACyC,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC/C,CAAC,cACN/B,IAAA,SAAM+B,SAAS,CAAC,iCAAiC,CAAAxB,QAAA,CAAC,OAElD,CAAM,CAAC,EACH,CAAC,CACNuB,MAAM,eACL9B,IAAA,WACEiC,OAAO,CAAEA,CAAA,GAAMxB,cAAc,CAAC,KAAK,CAAE,CACrCsB,SAAS,CAAC,kDAAkD,CAAAxB,QAAA,cAE5DP,IAAA,CAACZ,SAAS,EAAC2C,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CACT,EACE,CAAC,cAGN/B,IAAA,QAAK+B,SAAS,CAAC,4BAA4B,CAAAxB,QAAA,CACxCc,gBAAgB,CAACa,GAAG,CAAEX,IAAI,eACzBrB,KAAA,CAACpB,IAAI,EAEHkD,EAAE,CAAET,IAAI,CAACL,IAAK,CACde,OAAO,CAAEH,MAAM,CAAG,IAAMrB,cAAc,CAAC,KAAK,CAAC,CAAG0B,SAAU,CAC1DJ,SAAS,wGAAAK,MAAA,CACPZ,QAAQ,CAACD,IAAI,CAACL,IAAI,CAAC,CACf,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAX,QAAA,eAEHP,IAAA,CAACuB,IAAI,CAACJ,IAAI,EAACY,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC/B,IAAA,SAAAO,QAAA,CAAOgB,IAAI,CAACN,IAAI,CAAO,CAAC,GAVnBM,IAAI,CAACN,IAWN,CACP,CAAC,CACC,CAAC,cAGNf,KAAA,QAAK6B,SAAS,CAAC,8BAA8B,CAAAxB,QAAA,eAC3CL,KAAA,QAAK6B,SAAS,CAAC,uDAAuD,CAAAxB,QAAA,eACpEP,IAAA,QAAK+B,SAAS,CAAC,2EAA2E,CAAAxB,QAAA,cACxFP,IAAA,CAACJ,QAAQ,EAACmC,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CAAC,cACN7B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAxB,QAAA,eAC7BL,KAAA,MAAG6B,SAAS,CAAC,4CAA4C,CAAAxB,QAAA,EACtDG,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE2B,SAAS,CAAC,GAAC,CAAC3B,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE4B,QAAQ,EAClC,CAAC,cACJtC,IAAA,MAAG+B,SAAS,CAAC,gCAAgC,CAAAxB,QAAA,CAC1CG,KAAK,SAALA,KAAK,kBAAAmB,WAAA,CAALnB,KAAK,CAAE6B,IAAI,UAAAV,WAAA,iBAAXA,WAAA,CAAaW,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAC9B,CAAC,EACD,CAAC,EACH,CAAC,cACNtC,KAAA,WACE+B,OAAO,CAAElB,YAAa,CACtBgB,SAAS,CAAC,8IAA8I,CAAAxB,QAAA,eAExJP,IAAA,CAACL,yBAAyB,EAACoC,SAAS,CAAC,SAAS,CAAE,CAAC,cACjD/B,IAAA,SAAAO,QAAA,CAAM,UAAQ,CAAM,CAAC,EACf,CAAC,EACN,CAAC,EACH,CAAC,EACP,CAED,mBACEL,KAAA,QAAK6B,SAAS,CAAC,yBAAyB,CAAAxB,QAAA,eAEtCP,IAAA,QAAK+B,SAAS,CAAC,0DAA0D,CAAAxB,QAAA,cACvEP,IAAA,CAAC2B,OAAO,GAAE,CAAC,CACR,CAAC,cAGN3B,IAAA,CAACd,eAAe,EAAAqB,QAAA,CACbC,WAAW,eACVN,KAAA,CAAAE,SAAA,EAAAG,QAAA,eACEP,IAAA,CAACf,MAAM,CAACwD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACrBZ,SAAS,CAAC,8BAA8B,CACxCE,OAAO,CAAEA,CAAA,GAAMxB,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,cAErCP,IAAA,QAAK+B,SAAS,CAAC,sCAAsC,CAAE,CAAC,CAC9C,CAAC,cACb/B,IAAA,CAACf,MAAM,CAACwD,GAAG,EACTC,OAAO,CAAE,CAAEI,CAAC,CAAE,CAAC,GAAI,CAAE,CACrBF,OAAO,CAAE,CAAEE,CAAC,CAAE,CAAE,CAAE,CAClBD,IAAI,CAAE,CAAEC,CAAC,CAAE,CAAC,GAAI,CAAE,CAClBC,UAAU,CAAE,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAE,EAAE,CAAEC,SAAS,CAAE,GAAI,CAAE,CAC5DnB,SAAS,CAAC,4CAA4C,CAAAxB,QAAA,cAEtDP,IAAA,CAAC2B,OAAO,EAACG,MAAM,MAAE,CAAC,CACR,CAAC,EACb,CACH,CACc,CAAC,cAGlB5B,KAAA,QAAK6B,SAAS,CAAC,UAAU,CAAAxB,QAAA,eAEvBP,IAAA,WAAQ+B,SAAS,CAAC,iHAAiH,CAAAxB,QAAA,cACjIL,KAAA,QAAK6B,SAAS,CAAC,6BAA6B,CAAAxB,QAAA,eAC1CP,IAAA,WACEiC,OAAO,CAAEA,CAAA,GAAMxB,cAAc,CAAC,IAAI,CAAE,CACpCsB,SAAS,CAAC,4DAA4D,CAAAxB,QAAA,cAEtEP,IAAA,CAACb,SAAS,EAAC4C,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,cACT/B,IAAA,OAAI+B,SAAS,CAAC,kCAAkC,CAAAxB,QAAA,CAAC,iBAEjD,CAAI,CAAC,EACF,CAAC,CACA,CAAC,cAGTP,IAAA,SAAM+B,SAAS,CAAC,mBAAmB,CAAAxB,QAAA,CAChCA,QAAQ,CACL,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}