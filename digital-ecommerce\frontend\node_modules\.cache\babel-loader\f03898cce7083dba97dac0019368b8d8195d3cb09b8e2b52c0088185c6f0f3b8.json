{"ast": null, "code": "import { useRef as i } from \"react\";\nimport { useIsoMorphicEffect as u } from './use-iso-morphic-effect.js';\nfunction s(n, t) {\n  let e = i({\n    left: 0,\n    top: 0\n  });\n  if (u(() => {\n    if (!t) return;\n    let r = t.getBoundingClientRect();\n    r && (e.current = r);\n  }, [n, t]), t == null || !n || t === document.activeElement) return !1;\n  let o = t.getBoundingClientRect();\n  return o.top !== e.current.top || o.left !== e.current.left;\n}\nexport { s as useDidElementMove };", "map": {"version": 3, "names": ["useRef", "i", "useIsoMorphicEffect", "u", "s", "n", "t", "e", "left", "top", "r", "getBoundingClientRect", "current", "document", "activeElement", "o", "useDidElementMove"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-did-element-move.js"], "sourcesContent": ["import{useRef as i}from\"react\";import{useIsoMorphicEffect as u}from'./use-iso-morphic-effect.js';function s(n,t){let e=i({left:0,top:0});if(u(()=>{if(!t)return;let r=t.getBoundingClientRect();r&&(e.current=r)},[n,t]),t==null||!n||t===document.activeElement)return!1;let o=t.getBoundingClientRect();return o.top!==e.current.top||o.left!==e.current.left}export{s as useDidElementMove};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAAC;IAACO,IAAI,EAAC,CAAC;IAACC,GAAG,EAAC;EAAC,CAAC,CAAC;EAAC,IAAGN,CAAC,CAAC,MAAI;IAAC,IAAG,CAACG,CAAC,EAAC;IAAO,IAAII,CAAC,GAACJ,CAAC,CAACK,qBAAqB,CAAC,CAAC;IAACD,CAAC,KAAGH,CAAC,CAACK,OAAO,GAACF,CAAC,CAAC;EAAA,CAAC,EAAC,CAACL,CAAC,EAACC,CAAC,CAAC,CAAC,EAACA,CAAC,IAAE,IAAI,IAAE,CAACD,CAAC,IAAEC,CAAC,KAAGO,QAAQ,CAACC,aAAa,EAAC,OAAM,CAAC,CAAC;EAAC,IAAIC,CAAC,GAACT,CAAC,CAACK,qBAAqB,CAAC,CAAC;EAAC,OAAOI,CAAC,CAACN,GAAG,KAAGF,CAAC,CAACK,OAAO,CAACH,GAAG,IAAEM,CAAC,CAACP,IAAI,KAAGD,CAAC,CAACK,OAAO,CAACJ,IAAI;AAAA;AAAC,SAAOJ,CAAC,IAAIY,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}