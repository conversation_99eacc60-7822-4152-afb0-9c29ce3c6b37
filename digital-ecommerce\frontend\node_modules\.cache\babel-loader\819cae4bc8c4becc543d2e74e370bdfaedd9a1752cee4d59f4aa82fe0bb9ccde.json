{"ast": null, "code": "\"use client\";\n\nimport n from \"react\";\nimport { useClose as T } from '../../internal/close-provider.js';\nimport { forwardRefWithAs as p, mergeProps as r } from '../../utils/render.js';\nimport { Button as s } from '../button/button.js';\nlet i = \"button\";\nfunction l(t, e) {\n  let o = T();\n  return n.createElement(s, {\n    ref: e,\n    ...r({\n      onClick: o\n    }, t)\n  });\n}\nlet y = p(l);\nexport { y as CloseButton };", "map": {"version": 3, "names": ["n", "useClose", "T", "forwardRefWithAs", "p", "mergeProps", "r", "<PERSON><PERSON>", "s", "i", "l", "t", "e", "o", "createElement", "ref", "onClick", "y", "CloseButton"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/close-button/close-button.js"], "sourcesContent": ["\"use client\";import n from\"react\";import{useClose as T}from'../../internal/close-provider.js';import{forwardRefWithAs as p,mergeProps as r}from'../../utils/render.js';import{Button as s}from'../button/button.js';let i=\"button\";function l(t,e){let o=T();return n.createElement(s,{ref:e,...r({onClick:o},t)})}let y=p(l);export{y as CloseButton};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAOA,CAAC,MAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,kCAAkC;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,MAAM,IAAIC,CAAC,QAAK,qBAAqB;AAAC,IAAIC,CAAC,GAAC,QAAQ;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACX,CAAC,CAAC,CAAC;EAAC,OAAOF,CAAC,CAACc,aAAa,CAACN,CAAC,EAAC;IAACO,GAAG,EAACH,CAAC;IAAC,GAAGN,CAAC,CAAC;MAACU,OAAO,EAACH;IAAC,CAAC,EAACF,CAAC;EAAC,CAAC,CAAC;AAAA;AAAC,IAAIM,CAAC,GAACb,CAAC,CAACM,CAAC,CAAC;AAAC,SAAOO,CAAC,IAAIC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}