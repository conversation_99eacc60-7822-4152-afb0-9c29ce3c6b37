import React, { useState, useEffect } from 'react';
import { GiftIcon, FireIcon, ClockIcon, TagIcon } from '@heroicons/react/24/outline';

const mockPromotions = [
  {
    id: 1,
    type: 'flash_sale',
    title: 'Flash Sale!',
    description: 'Up to 50% off on selected items',
    discount: '50%',
    endTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    code: 'FLASH50',
    bgGradient: 'from-red-500 to-red-600',
    icon: FireIcon
  },
  {
    id: 2,
    type: 'seasonal',
    title: 'Winter Collection',
    description: 'Cozy up with 30% off winter essentials',
    discount: '30%',
    endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    code: 'WINTER30',
    bgGradient: 'from-blue-500 to-blue-600',
    icon: GiftIcon
  },
  {
    id: 3,
    type: 'new_user',
    title: 'Welcome Offer',
    description: 'First-time buyers get 20% off',
    discount: '20%',
    endTime: null,
    code: 'WELCOME20',
    bgGradient: 'from-light-orange-500 to-light-orange-600',
    icon: TagIcon
  }
];

const Promotions = () => {
  const [promotions, setPromotions] = useState(mockPromotions);
  const [timeLeft, setTimeLeft] = useState({});

  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = {};
      promotions.forEach(promo => {
        if (promo.endTime) {
          const now = new Date().getTime();
          const distance = promo.endTime.getTime() - now;

          if (distance > 0) {
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            newTimeLeft[promo.id] = { hours, minutes, seconds };
          } else {
            newTimeLeft[promo.id] = null;
          }
        }
      });
      setTimeLeft(newTimeLeft);
    }, 1000);

    return () => clearInterval(timer);
  }, [promotions]);

  const copyPromoCode = (code) => {
    navigator.clipboard.writeText(code);
    // You could show a toast notification here
  };

  return (
    <div className="space-y-6">
      {/* Main Promotion Banner */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold flex items-center">
              <GiftIcon className="w-7 h-7 mr-2" />
              Special Offers
            </h2>
            <div className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
              <span className="text-sm font-medium">Limited Time</span>
            </div>
          </div>
          <p className="text-light-orange-100 mb-4">
            Don't miss out on these amazing deals! Save big on your favorite products.
          </p>
          <button className="bg-white text-light-orange-600 px-6 py-2 rounded-lg font-semibold hover:bg-light-orange-50 transition-colors">
            Shop Now
          </button>
        </div>
      </div>

      {/* Individual Promotion Cards */}
      <div className="space-y-4">
        {promotions.map((promo) => {
          const IconComponent = promo.icon;
          const currentTimeLeft = timeLeft[promo.id];

          return (
            <div
              key={promo.id}
              className="bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden hover:shadow-xl transition-shadow"
            >
              <div className={`bg-gradient-to-r ${promo.bgGradient} p-4`}>
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center space-x-3">
                    <div className="bg-white bg-opacity-20 p-2 rounded-lg">
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">{promo.title}</h3>
                      <p className="text-sm opacity-90">{promo.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">{promo.discount}</div>
                    <div className="text-sm opacity-90">OFF</div>
                  </div>
                </div>
              </div>

              <div className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="bg-light-orange-100 px-3 py-1 rounded-lg">
                      <span className="text-light-orange-800 font-mono text-sm font-semibold">
                        {promo.code}
                      </span>
                    </div>
                    <button
                      onClick={() => copyPromoCode(promo.code)}
                      className="text-light-orange-600 hover:text-light-orange-700 text-sm font-medium transition-colors"
                    >
                      Copy Code
                    </button>
                  </div>

                  {currentTimeLeft && (
                    <div className="flex items-center space-x-2 text-sm text-light-orange-600">
                      <ClockIcon className="w-4 h-4" />
                      <span>
                        {currentTimeLeft.hours}h {currentTimeLeft.minutes}m {currentTimeLeft.seconds}s left
                      </span>
                    </div>
                  )}
                </div>

                {promo.endTime && currentTimeLeft && (
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-light-orange-600 mb-1">
                      <span>Time remaining</span>
                      <span>Hurry up!</span>
                    </div>
                    <div className="w-full bg-light-orange-100 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-light-orange-400 to-light-orange-500 h-2 rounded-full transition-all duration-1000"
                        style={{
                          width: `${Math.max(10, (currentTimeLeft.hours * 60 + currentTimeLeft.minutes) / 120 * 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Newsletter Signup Promotion */}
      <div className="bg-gradient-to-r from-light-orange-100 to-light-orange-200 rounded-xl p-6 border border-light-orange-300">
        <div className="text-center">
          <div className="bg-light-orange-500 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
            <GiftIcon className="w-6 h-6 text-white" />
          </div>
          <h3 className="text-xl font-bold text-light-orange-800 mb-2">
            Get Exclusive Offers
          </h3>
          <p className="text-light-orange-700 mb-4">
            Subscribe to our newsletter and get 15% off your next purchase plus early access to sales!
          </p>
          <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 border border-light-orange-300 rounded-lg focus:ring-2 focus:ring-light-orange-400 focus:border-light-orange-400 transition-colors"
            />
            <button className="bg-light-orange-500 text-white px-6 py-2 rounded-lg hover:bg-light-orange-600 transition-colors font-semibold">
              Subscribe
            </button>
          </div>
          <p className="text-xs text-light-orange-600 mt-2">
            * Offer valid for new subscribers only
          </p>
        </div>
      </div>
    </div>
  );
};

export default Promotions;
