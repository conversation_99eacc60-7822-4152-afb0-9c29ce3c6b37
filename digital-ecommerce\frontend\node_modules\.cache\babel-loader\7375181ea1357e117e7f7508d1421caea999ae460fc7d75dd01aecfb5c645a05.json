{"ast": null, "code": "import React from'react';import{createRoot}from'react-dom/client';import'./index.css';import'./App.css';import App from'./App';import*as serviceWorker from'./serviceWorker';import{jsx as _jsx}from\"react/jsx-runtime\";const container=document.getElementById('root');const root=createRoot(container);root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://bit.ly/CRA-PWA\nserviceWorker.unregister();", "map": {"version": 3, "names": ["React", "createRoot", "App", "serviceWorker", "jsx", "_jsx", "container", "document", "getElementById", "root", "render", "StrictMode", "children", "unregister"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './index.css';\nimport './App.css';\nimport App from './App';\nimport * as serviceWorker from './serviceWorker';\n\nconst container = document.getElementById('root');\nconst root = createRoot(container);\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://bit.ly/CRA-PWA\nserviceWorker.unregister();\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,UAAU,KAAQ,kBAAkB,CAC7C,MAAO,aAAa,CACpB,MAAO,WAAW,CAClB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,GAAK,CAAAC,aAAa,KAAM,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEjD,KAAM,CAAAC,SAAS,CAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CACjD,KAAM,CAAAC,IAAI,CAAGR,UAAU,CAACK,SAAS,CAAC,CAClCG,IAAI,CAACC,MAAM,cACTL,IAAA,CAACL,KAAK,CAACW,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACH,GAAG,GAAE,CAAC,CACS,CACpB,CAAC,CAED;AACA;AACA;AACAC,aAAa,CAACU,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}