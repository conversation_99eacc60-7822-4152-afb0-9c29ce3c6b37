{"ast": null, "code": "import { getOffset as $ab71dadb03a6fb2e$export$622cea445a1c5b7d } from \"./getOffset.mjs\";\nimport { useRef as $1rnCS$useRef } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /* eslint-disable rulesdir/pure-render */\n\n// Keep track of elements that we are currently handling dragging for via useDrag1D.\n// If there's an ancestor and a descendant both using useDrag1D(), and the user starts\n// dragging the descendant, we don't want useDrag1D events to fire for the ancestor.\nconst $9cc09df9fd7676be$var$draggingElements = [];\nfunction $9cc09df9fd7676be$export$7bbed75feba39706(props) {\n  console.warn('useDrag1D is deprecated, please use `useMove` instead https://react-spectrum.adobe.com/react-aria/useMove.html');\n  let {\n    containerRef: containerRef,\n    reverse: reverse,\n    orientation: orientation,\n    onHover: onHover,\n    onDrag: onDrag,\n    onPositionChange: onPositionChange,\n    onIncrement: onIncrement,\n    onDecrement: onDecrement,\n    onIncrementToMax: onIncrementToMax,\n    onDecrementToMin: onDecrementToMin,\n    onCollapseToggle: onCollapseToggle\n  } = props;\n  let getPosition = e => orientation === 'horizontal' ? e.clientX : e.clientY;\n  let getNextOffset = e => {\n    let containerOffset = (0, $ab71dadb03a6fb2e$export$622cea445a1c5b7d)(containerRef.current, reverse, orientation);\n    let mouseOffset = getPosition(e);\n    let nextOffset = reverse ? containerOffset - mouseOffset : mouseOffset - containerOffset;\n    return nextOffset;\n  };\n  let dragging = (0, $1rnCS$useRef)(false);\n  let prevPosition = (0, $1rnCS$useRef)(0);\n  // Keep track of the current handlers in a ref so that the events can access them.\n  let handlers = (0, $1rnCS$useRef)({\n    onPositionChange: onPositionChange,\n    onDrag: onDrag\n  });\n  handlers.current.onDrag = onDrag;\n  handlers.current.onPositionChange = onPositionChange;\n  let onMouseDragged = e => {\n    e.preventDefault();\n    let nextOffset = getNextOffset(e);\n    if (!dragging.current) {\n      dragging.current = true;\n      if (handlers.current.onDrag) handlers.current.onDrag(true);\n      if (handlers.current.onPositionChange) handlers.current.onPositionChange(nextOffset);\n    }\n    if (prevPosition.current === nextOffset) return;\n    prevPosition.current = nextOffset;\n    if (onPositionChange) onPositionChange(nextOffset);\n  };\n  let onMouseUp = e => {\n    const target = e.target;\n    dragging.current = false;\n    let nextOffset = getNextOffset(e);\n    if (handlers.current.onDrag) handlers.current.onDrag(false);\n    if (handlers.current.onPositionChange) handlers.current.onPositionChange(nextOffset);\n    $9cc09df9fd7676be$var$draggingElements.splice($9cc09df9fd7676be$var$draggingElements.indexOf(target), 1);\n    window.removeEventListener('mouseup', onMouseUp, false);\n    window.removeEventListener('mousemove', onMouseDragged, false);\n  };\n  let onMouseDown = e => {\n    const target = e.currentTarget;\n    // If we're already handling dragging on a descendant with useDrag1D, then\n    // we don't want to handle the drag motion on this target as well.\n    if ($9cc09df9fd7676be$var$draggingElements.some(elt => target.contains(elt))) return;\n    $9cc09df9fd7676be$var$draggingElements.push(target);\n    window.addEventListener('mousemove', onMouseDragged, false);\n    window.addEventListener('mouseup', onMouseUp, false);\n  };\n  let onMouseEnter = () => {\n    if (onHover) onHover(true);\n  };\n  let onMouseOut = () => {\n    if (onHover) onHover(false);\n  };\n  let onKeyDown = e => {\n    switch (e.key) {\n      case 'Left':\n      case 'ArrowLeft':\n        if (orientation === 'horizontal') {\n          e.preventDefault();\n          if (onDecrement && !reverse) onDecrement();else if (onIncrement && reverse) onIncrement();\n        }\n        break;\n      case 'Up':\n      case 'ArrowUp':\n        if (orientation === 'vertical') {\n          e.preventDefault();\n          if (onDecrement && !reverse) onDecrement();else if (onIncrement && reverse) onIncrement();\n        }\n        break;\n      case 'Right':\n      case 'ArrowRight':\n        if (orientation === 'horizontal') {\n          e.preventDefault();\n          if (onIncrement && !reverse) onIncrement();else if (onDecrement && reverse) onDecrement();\n        }\n        break;\n      case 'Down':\n      case 'ArrowDown':\n        if (orientation === 'vertical') {\n          e.preventDefault();\n          if (onIncrement && !reverse) onIncrement();else if (onDecrement && reverse) onDecrement();\n        }\n        break;\n      case 'Home':\n        e.preventDefault();\n        if (onDecrementToMin) onDecrementToMin();\n        break;\n      case 'End':\n        e.preventDefault();\n        if (onIncrementToMax) onIncrementToMax();\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (onCollapseToggle) onCollapseToggle();\n        break;\n    }\n  };\n  return {\n    onMouseDown: onMouseDown,\n    onMouseEnter: onMouseEnter,\n    onMouseOut: onMouseOut,\n    onKeyDown: onKeyDown\n  };\n}\nexport { $9cc09df9fd7676be$export$7bbed75feba39706 as useDrag1D };", "map": {"version": 3, "names": ["$9cc09df9fd7676be$var$draggingElements", "$9cc09df9fd7676be$export$7bbed75feba39706", "props", "console", "warn", "containerRef", "reverse", "orientation", "onHover", "onDrag", "onPositionChange", "onIncrement", "onDecrement", "onIncrementToMax", "onDecrementToMin", "onCollapseToggle", "getPosition", "e", "clientX", "clientY", "getNextOffset", "containerOffset", "$ab71dadb03a6fb2e$export$622cea445a1c5b7d", "current", "mouseOffset", "nextOffset", "dragging", "$1rnCS$useRef", "prevPosition", "handlers", "onMouseDragged", "preventDefault", "onMouseUp", "target", "splice", "indexOf", "window", "removeEventListener", "onMouseDown", "currentTarget", "some", "elt", "contains", "push", "addEventListener", "onMouseEnter", "onMouseOut", "onKeyDown", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useDrag1D.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n /* eslint-disable rulesdir/pure-render */\n\nimport {getOffset} from './getOffset';\nimport {Orientation} from '@react-types/shared';\nimport React, {HTMLAttributes, MutableRefObject, useRef} from 'react';\n\ninterface UseDrag1DProps {\n  containerRef: MutableRefObject<HTMLElement>,\n  reverse?: boolean,\n  orientation?: Orientation,\n  onHover?: (hovered: boolean) => void,\n  onDrag?: (dragging: boolean) => void,\n  onPositionChange?: (position: number) => void,\n  onIncrement?: () => void,\n  onDecrement?: () => void,\n  onIncrementToMax?: () => void,\n  onDecrementToMin?: () => void,\n  onCollapseToggle?: () => void\n}\n\n// Keep track of elements that we are currently handling dragging for via useDrag1D.\n// If there's an ancestor and a descendant both using useDrag1D(), and the user starts\n// dragging the descendant, we don't want useDrag1D events to fire for the ancestor.\nconst draggingElements: HTMLElement[] = [];\n\n// created for splitview, this should be reusable for things like sliders/dials\n// It also handles keyboard events on the target allowing for increment/decrement by a given stepsize as well as minifying/maximizing and toggling between minified and previous size\n// It can also take a 'reverse' param to say if we should measure from the right/bottom instead of the top/left\n// It can also handle either a vertical or horizontal movement, but not both at the same time\n\nexport function useDrag1D(props: UseDrag1DProps): HTMLAttributes<HTMLElement> {\n  console.warn('useDrag1D is deprecated, please use `useMove` instead https://react-spectrum.adobe.com/react-aria/useMove.html');\n  let {containerRef, reverse, orientation, onHover, onDrag, onPositionChange, onIncrement, onDecrement, onIncrementToMax, onDecrementToMin, onCollapseToggle} = props;\n  let getPosition = (e) => orientation === 'horizontal' ? e.clientX : e.clientY;\n  let getNextOffset = (e: MouseEvent) => {\n    let containerOffset = getOffset(containerRef.current, reverse, orientation);\n    let mouseOffset = getPosition(e);\n    let nextOffset = reverse ? containerOffset - mouseOffset : mouseOffset - containerOffset;\n    return nextOffset;\n  };\n  let dragging = useRef(false);\n  let prevPosition = useRef(0);\n\n  // Keep track of the current handlers in a ref so that the events can access them.\n  let handlers = useRef({onPositionChange, onDrag});\n  handlers.current.onDrag = onDrag;\n  handlers.current.onPositionChange = onPositionChange;\n\n  let onMouseDragged = (e: MouseEvent) => {\n    e.preventDefault();\n    let nextOffset = getNextOffset(e);\n    if (!dragging.current) {\n      dragging.current = true;\n      if (handlers.current.onDrag) {\n        handlers.current.onDrag(true);\n      }\n      if (handlers.current.onPositionChange) {\n        handlers.current.onPositionChange(nextOffset);\n      }\n    }\n    if (prevPosition.current === nextOffset) {\n      return;\n    }\n    prevPosition.current = nextOffset;\n    if (onPositionChange) {\n      onPositionChange(nextOffset);\n    }\n  };\n\n  let onMouseUp = (e: MouseEvent) => {\n    const target = e.target as HTMLElement;\n    dragging.current = false;\n    let nextOffset = getNextOffset(e);\n    if (handlers.current.onDrag) {\n      handlers.current.onDrag(false);\n    }\n    if (handlers.current.onPositionChange) {\n      handlers.current.onPositionChange(nextOffset);\n    }\n\n    draggingElements.splice(draggingElements.indexOf(target), 1);\n    window.removeEventListener('mouseup', onMouseUp, false);\n    window.removeEventListener('mousemove', onMouseDragged, false);\n  };\n\n  let onMouseDown = (e: React.MouseEvent<HTMLElement>) => {\n    const target = e.currentTarget;\n    // If we're already handling dragging on a descendant with useDrag1D, then\n    // we don't want to handle the drag motion on this target as well.\n    if (draggingElements.some(elt => target.contains(elt))) {\n      return;\n    }\n    draggingElements.push(target);\n    window.addEventListener('mousemove', onMouseDragged, false);\n    window.addEventListener('mouseup', onMouseUp, false);\n  };\n\n  let onMouseEnter = () => {\n    if (onHover) {\n      onHover(true);\n    }\n  };\n\n  let onMouseOut = () => {\n    if (onHover) {\n      onHover(false);\n    }\n  };\n\n  let onKeyDown = (e) => {\n    switch (e.key) {\n      case 'Left':\n      case 'ArrowLeft':\n        if (orientation === 'horizontal') {\n          e.preventDefault();\n          if (onDecrement && !reverse) {\n            onDecrement();\n          } else if (onIncrement && reverse) {\n            onIncrement();\n          }\n        }\n        break;\n      case 'Up':\n      case 'ArrowUp':\n        if (orientation === 'vertical') {\n          e.preventDefault();\n          if (onDecrement && !reverse) {\n            onDecrement();\n          } else if (onIncrement && reverse) {\n            onIncrement();\n          }\n        }\n        break;\n      case 'Right':\n      case 'ArrowRight':\n        if (orientation === 'horizontal') {\n          e.preventDefault();\n          if (onIncrement && !reverse) {\n            onIncrement();\n          } else if (onDecrement && reverse) {\n            onDecrement();\n          }\n        }\n        break;\n      case 'Down':\n      case 'ArrowDown':\n        if (orientation === 'vertical') {\n          e.preventDefault();\n          if (onIncrement && !reverse) {\n            onIncrement();\n          } else if (onDecrement && reverse) {\n            onDecrement();\n          }\n        }\n        break;\n      case 'Home':\n        e.preventDefault();\n        if (onDecrementToMin) {\n          onDecrementToMin();\n        }\n        break;\n      case 'End':\n        e.preventDefault();\n        if (onIncrementToMax) {\n          onIncrementToMax();\n        }\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (onCollapseToggle) {\n          onCollapseToggle();\n        }\n        break;\n    }\n  };\n\n  return {onMouseDown, onMouseEnter, onMouseOut, onKeyDown};\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;GAAA,CAYC;;AAoBD;AACA;AACA;AACA,MAAMA,sCAAA,GAAkC,EAAE;AAOnC,SAASC,0CAAUC,KAAqB;EAC7CC,OAAA,CAAQC,IAAI,CAAC;EACb,IAAI;IAAAC,YAAA,EAACA,YAAY;IAAAC,OAAA,EAAEA,OAAO;IAAAC,WAAA,EAAEA,WAAW;IAAAC,OAAA,EAAEA,OAAO;IAAAC,MAAA,EAAEA,MAAM;IAAAC,gBAAA,EAAEA,gBAAgB;IAAAC,WAAA,EAAEA,WAAW;IAAAC,WAAA,EAAEA,WAAW;IAAAC,gBAAA,EAAEA,gBAAgB;IAAAC,gBAAA,EAAEA,gBAAgB;IAAAC,gBAAA,EAAEA;EAAgB,CAAC,GAAGb,KAAA;EAC9J,IAAIc,WAAA,GAAeC,CAAA,IAAMV,WAAA,KAAgB,eAAeU,CAAA,CAAEC,OAAO,GAAGD,CAAA,CAAEE,OAAO;EAC7E,IAAIC,aAAA,GAAiBH,CAAA;IACnB,IAAII,eAAA,GAAkB,IAAAC,yCAAQ,EAAEjB,YAAA,CAAakB,OAAO,EAAEjB,OAAA,EAASC,WAAA;IAC/D,IAAIiB,WAAA,GAAcR,WAAA,CAAYC,CAAA;IAC9B,IAAIQ,UAAA,GAAanB,OAAA,GAAUe,eAAA,GAAkBG,WAAA,GAAcA,WAAA,GAAcH,eAAA;IACzE,OAAOI,UAAA;EACT;EACA,IAAIC,QAAA,GAAW,IAAAC,aAAK,EAAE;EACtB,IAAIC,YAAA,GAAe,IAAAD,aAAK,EAAE;EAE1B;EACA,IAAIE,QAAA,GAAW,IAAAF,aAAK,EAAE;sBAACjB,gBAAA;YAAkBD;EAAM;EAC/CoB,QAAA,CAASN,OAAO,CAACd,MAAM,GAAGA,MAAA;EAC1BoB,QAAA,CAASN,OAAO,CAACb,gBAAgB,GAAGA,gBAAA;EAEpC,IAAIoB,cAAA,GAAkBb,CAAA;IACpBA,CAAA,CAAEc,cAAc;IAChB,IAAIN,UAAA,GAAaL,aAAA,CAAcH,CAAA;IAC/B,IAAI,CAACS,QAAA,CAASH,OAAO,EAAE;MACrBG,QAAA,CAASH,OAAO,GAAG;MACnB,IAAIM,QAAA,CAASN,OAAO,CAACd,MAAM,EACzBoB,QAAA,CAASN,OAAO,CAACd,MAAM,CAAC;MAE1B,IAAIoB,QAAA,CAASN,OAAO,CAACb,gBAAgB,EACnCmB,QAAA,CAASN,OAAO,CAACb,gBAAgB,CAACe,UAAA;IAEtC;IACA,IAAIG,YAAA,CAAaL,OAAO,KAAKE,UAAA,EAC3B;IAEFG,YAAA,CAAaL,OAAO,GAAGE,UAAA;IACvB,IAAIf,gBAAA,EACFA,gBAAA,CAAiBe,UAAA;EAErB;EAEA,IAAIO,SAAA,GAAaf,CAAA;IACf,MAAMgB,MAAA,GAAShB,CAAA,CAAEgB,MAAM;IACvBP,QAAA,CAASH,OAAO,GAAG;IACnB,IAAIE,UAAA,GAAaL,aAAA,CAAcH,CAAA;IAC/B,IAAIY,QAAA,CAASN,OAAO,CAACd,MAAM,EACzBoB,QAAA,CAASN,OAAO,CAACd,MAAM,CAAC;IAE1B,IAAIoB,QAAA,CAASN,OAAO,CAACb,gBAAgB,EACnCmB,QAAA,CAASN,OAAO,CAACb,gBAAgB,CAACe,UAAA;IAGpCzB,sCAAA,CAAiBkC,MAAM,CAAClC,sCAAA,CAAiBmC,OAAO,CAACF,MAAA,GAAS;IAC1DG,MAAA,CAAOC,mBAAmB,CAAC,WAAWL,SAAA,EAAW;IACjDI,MAAA,CAAOC,mBAAmB,CAAC,aAAaP,cAAA,EAAgB;EAC1D;EAEA,IAAIQ,WAAA,GAAerB,CAAA;IACjB,MAAMgB,MAAA,GAAShB,CAAA,CAAEsB,aAAa;IAC9B;IACA;IACA,IAAIvC,sCAAA,CAAiBwC,IAAI,CAACC,GAAA,IAAOR,MAAA,CAAOS,QAAQ,CAACD,GAAA,IAC/C;IAEFzC,sCAAA,CAAiB2C,IAAI,CAACV,MAAA;IACtBG,MAAA,CAAOQ,gBAAgB,CAAC,aAAad,cAAA,EAAgB;IACrDM,MAAA,CAAOQ,gBAAgB,CAAC,WAAWZ,SAAA,EAAW;EAChD;EAEA,IAAIa,YAAA,GAAeA,CAAA;IACjB,IAAIrC,OAAA,EACFA,OAAA,CAAQ;EAEZ;EAEA,IAAIsC,UAAA,GAAaA,CAAA;IACf,IAAItC,OAAA,EACFA,OAAA,CAAQ;EAEZ;EAEA,IAAIuC,SAAA,GAAa9B,CAAA;IACf,QAAQA,CAAA,CAAE+B,GAAG;MACX,KAAK;MACL,KAAK;QACH,IAAIzC,WAAA,KAAgB,cAAc;UAChCU,CAAA,CAAEc,cAAc;UAChB,IAAInB,WAAA,IAAe,CAACN,OAAA,EAClBM,WAAA,QACK,IAAID,WAAA,IAAeL,OAAA,EACxBK,WAAA;QAEJ;QACA;MACF,KAAK;MACL,KAAK;QACH,IAAIJ,WAAA,KAAgB,YAAY;UAC9BU,CAAA,CAAEc,cAAc;UAChB,IAAInB,WAAA,IAAe,CAACN,OAAA,EAClBM,WAAA,QACK,IAAID,WAAA,IAAeL,OAAA,EACxBK,WAAA;QAEJ;QACA;MACF,KAAK;MACL,KAAK;QACH,IAAIJ,WAAA,KAAgB,cAAc;UAChCU,CAAA,CAAEc,cAAc;UAChB,IAAIpB,WAAA,IAAe,CAACL,OAAA,EAClBK,WAAA,QACK,IAAIC,WAAA,IAAeN,OAAA,EACxBM,WAAA;QAEJ;QACA;MACF,KAAK;MACL,KAAK;QACH,IAAIL,WAAA,KAAgB,YAAY;UAC9BU,CAAA,CAAEc,cAAc;UAChB,IAAIpB,WAAA,IAAe,CAACL,OAAA,EAClBK,WAAA,QACK,IAAIC,WAAA,IAAeN,OAAA,EACxBM,WAAA;QAEJ;QACA;MACF,KAAK;QACHK,CAAA,CAAEc,cAAc;QAChB,IAAIjB,gBAAA,EACFA,gBAAA;QAEF;MACF,KAAK;QACHG,CAAA,CAAEc,cAAc;QAChB,IAAIlB,gBAAA,EACFA,gBAAA;QAEF;MACF,KAAK;QACHI,CAAA,CAAEc,cAAc;QAChB,IAAIhB,gBAAA,EACFA,gBAAA;QAEF;IACJ;EACF;EAEA,OAAO;iBAACuB,WAAA;kBAAaO,YAAA;gBAAcC,UAAA;eAAYC;EAAS;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}