{"ast": null, "code": "import { PressResponderContext as $ae1eeba8b9eafd08$export$5165eccb35aaadb5 } from \"./context.mjs\";\nimport { useObjectRef as $87RPk$useObjectRef, mergeProps as $87RPk$mergeProps, useSyncRef as $87RPk$useSyncRef } from \"@react-aria/utils\";\nimport $87RPk$react, { useRef as $87RPk$useRef, useContext as $87RPk$useContext, useEffect as $87RPk$useEffect, useMemo as $87RPk$useMemo } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nconst $f1ab8c75478c6f73$export$3351871ee4b288b8 = /*#__PURE__*/(0, $87RPk$react).forwardRef(({\n  children: children,\n  ...props\n}, ref) => {\n  let isRegistered = (0, $87RPk$useRef)(false);\n  let prevContext = (0, $87RPk$useContext)((0, $ae1eeba8b9eafd08$export$5165eccb35aaadb5));\n  ref = (0, $87RPk$useObjectRef)(ref || (prevContext === null || prevContext === void 0 ? void 0 : prevContext.ref));\n  let context = (0, $87RPk$mergeProps)(prevContext || {}, {\n    ...props,\n    ref: ref,\n    register() {\n      isRegistered.current = true;\n      if (prevContext) prevContext.register();\n    }\n  });\n  (0, $87RPk$useSyncRef)(prevContext, ref);\n  (0, $87RPk$useEffect)(() => {\n    if (!isRegistered.current) {\n      if (process.env.NODE_ENV !== 'production') console.warn(\"A PressResponder was rendered without a pressable child. Either call the usePress hook, or wrap your DOM node with <Pressable> component.\");\n      isRegistered.current = true; // only warn once in strict mode.\n    }\n  }, []);\n  return /*#__PURE__*/(0, $87RPk$react).createElement((0, $ae1eeba8b9eafd08$export$5165eccb35aaadb5).Provider, {\n    value: context\n  }, children);\n});\nfunction $f1ab8c75478c6f73$export$cf75428e0b9ed1ea({\n  children: children\n}) {\n  let context = (0, $87RPk$useMemo)(() => ({\n    register: () => {}\n  }), []);\n  return /*#__PURE__*/(0, $87RPk$react).createElement((0, $ae1eeba8b9eafd08$export$5165eccb35aaadb5).Provider, {\n    value: context\n  }, children);\n}\nexport { $f1ab8c75478c6f73$export$3351871ee4b288b8 as PressResponder, $f1ab8c75478c6f73$export$cf75428e0b9ed1ea as ClearPressResponder };", "map": {"version": 3, "names": ["$f1ab8c75478c6f73$export$3351871ee4b288b8", "$87RPk$react", "forwardRef", "children", "props", "ref", "isRegistered", "$87RPk$useRef", "prevContext", "$87RPk$useContext", "$ae1eeba8b9eafd08$export$5165eccb35aaadb5", "$87RPk$useObjectRef", "context", "$87RPk$mergeProps", "register", "current", "$87RPk$useSyncRef", "$87RPk$useEffect", "process", "env", "NODE_ENV", "console", "warn", "createElement", "Provider", "value", "$f1ab8c75478c6f73$export$cf75428e0b9ed1ea", "$87RPk$useMemo"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\PressResponder.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {mergeProps, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport {PressProps} from './usePress';\nimport {PressResponderContext} from './context';\nimport React, {ForwardedRef, JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\ninterface PressResponderProps extends PressProps {\n  children: ReactNode\n}\n\nexport const PressResponder = React.forwardRef(({children, ...props}: PressResponderProps, ref: ForwardedRef<FocusableElement>) => {\n  let isRegistered = useRef(false);\n  let prevContext = useContext(PressResponderContext);\n  ref = useObjectRef(ref || prevContext?.ref);\n  let context = mergeProps(prevContext || {}, {\n    ...props,\n    ref,\n    register() {\n      isRegistered.current = true;\n      if (prevContext) {\n        prevContext.register();\n      }\n    }\n  });\n\n  useSyncRef(prevContext, ref);\n\n  useEffect(() => {\n    if (!isRegistered.current) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'A PressResponder was rendered without a pressable child. ' +\n          'Either call the usePress hook, or wrap your DOM node with <Pressable> component.'\n        );\n      }\n      isRegistered.current = true; // only warn once in strict mode.\n    }\n  }, []);\n\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n});\n\nexport function ClearPressResponder({children}: {children: ReactNode}): JSX.Element {\n  let context = useMemo(() => ({register: () => {}}), []);\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;AAsBO,MAAMA,yCAAA,gBAAiB,IAAAC,YAAI,EAAEC,UAAU,CAAC,CAAC;EAAAC,QAAA,EAACA,QAAQ;EAAE,GAAGC;AAAA,CAA2B,EAAEC,GAAA;EACzF,IAAIC,YAAA,GAAe,IAAAC,aAAK,EAAE;EAC1B,IAAIC,WAAA,GAAc,IAAAC,iBAAS,GAAE,GAAAC,yCAAoB;EACjDL,GAAA,GAAM,IAAAM,mBAAW,EAAEN,GAAA,KAAOG,WAAA,aAAAA,WAAA,uBAAAA,WAAA,CAAaH,GAAG;EAC1C,IAAIO,OAAA,GAAU,IAAAC,iBAAS,EAAEL,WAAA,IAAe,CAAC,GAAG;IAC1C,GAAGJ,KAAK;SACRC,GAAA;IACAS,SAAA;MACER,YAAA,CAAaS,OAAO,GAAG;MACvB,IAAIP,WAAA,EACFA,WAAA,CAAYM,QAAQ;IAExB;EACF;EAEA,IAAAE,iBAAS,EAAER,WAAA,EAAaH,GAAA;EAExB,IAAAY,gBAAQ,EAAE;IACR,IAAI,CAACX,YAAA,CAAaS,OAAO,EAAE;MACzB,IAAIG,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cAC3BC,OAAA,CAAQC,IAAI,CACV;MAIJhB,YAAA,CAAaS,OAAO,GAAG,MAAM;IAC/B;EACF,GAAG,EAAE;EAEL,oBACE,IAAAd,YAAA,EAAAsB,aAAA,CAAC,IAAAb,yCAAoB,EAAEc,QAAQ;IAACC,KAAA,EAAOb;KACpCT,QAAA;AAGP;AAEO,SAASuB,0CAAoB;EAAAvB,QAAA,EAACA;AAAQ,CAAwB;EACnE,IAAIS,OAAA,GAAU,IAAAe,cAAM,EAAE,OAAO;IAACb,QAAA,EAAUA,CAAA,MAAO;EAAC,IAAI,EAAE;EACtD,oBACE,IAAAb,YAAA,EAAAsB,aAAA,CAAC,IAAAb,yCAAoB,EAAEc,QAAQ;IAACC,KAAA,EAAOb;KACpCT,QAAA;AAGP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}