{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Navigate to products page with search query\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    const handleClickOutside = event => {\n      if (!event.target.closest('.user-dropdown')) {\n        setShowUserDropdown(false);\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n  const navigationItems = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100' : 'bg-white/10 backdrop-blur-sm'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-18 lg:h-22\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3 group\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                rotate: 360,\n                scale: 1.1\n              },\n              transition: {\n                duration: 0.6,\n                type: \"spring\",\n                stiffness: 200\n              },\n              className: \"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-2xl font-bold transition-all duration-300 ${isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'}`,\n                children: \"ShopHub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs font-medium transition-all duration-300 ${isScrolled ? 'text-light-orange-600' : 'text-white/80'}`,\n                children: \"Premium Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-2\",\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                y: -2\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${isActive(item.href) ? isScrolled ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25' : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm' : isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this), isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                  layoutId: \"activeNavBg\",\n                  className: \"absolute inset-0 rounded-xl\",\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 23\n                }, this), !isActive(item.href) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this)\n            }, item.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center flex-1 max-w-lg mx-8\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative w-full group\",\n              whileHover: {\n                scale: 1.02\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 transition-colors duration-300 ${isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search for products, brands, and more...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                className: `w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${isScrolled ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100' : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'} focus:outline-none shadow-lg hover:shadow-xl`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${isScrolled ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100' : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/wishlist\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: `relative p-3 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'}`,\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative user-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setShowUserDropdown(!showUserDropdown),\n                className: `relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'}`,\n                children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.profilePicture,\n                  alt: \"Profile\",\n                  className: \"w-8 h-8 rounded-full object-cover ring-2 ring-white/20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden md:block text-sm font-medium\",\n                  children: (user === null || user === void 0 ? void 0 : user.firstName) || 'Account'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                  className: `w-4 h-4 transition-transform duration-300 ${showUserDropdown ? 'rotate-180' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                children: showUserDropdown && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 10,\n                    scale: 0.95\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: 10,\n                    scale: 0.95\n                  },\n                  transition: {\n                    duration: 0.2\n                  },\n                  className: \"absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden z-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3\",\n                      children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.profilePicture,\n                        alt: \"Profile\",\n                        className: \"w-12 h-12 rounded-full object-cover ring-2 ring-white/30\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                          className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 242,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-semibold text-white\",\n                          children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 246,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-white/80\",\n                          children: user === null || user === void 0 ? void 0 : user.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 249,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"py-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/account\",\n                      onClick: () => setShowUserDropdown(false),\n                      className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"My Account\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/orders\",\n                      onClick: () => setShowUserDropdown(false),\n                      className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Order History\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/wishlist\",\n                      onClick: () => setShowUserDropdown(false),\n                      className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Wishlist\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border-t border-gray-100 mt-2 pt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          logout();\n                          setShowUserDropdown(false);\n                        },\n                        className: \"flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\",\n                        children: [/*#__PURE__*/_jsxDEV(XMarkIcon, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 286,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Sign Out\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 287,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 279,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    y: -2\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm border border-white/20 hover:border-white/40'}`,\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    y: -2\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.1\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              onClick: () => setIsOpen(!isOpen),\n              className: `lg:hidden p-3 rounded-xl transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'}`,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  rotate: isOpen ? 180 : 0\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: isOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto',\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            height: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.3,\n            ease: \"easeInOut\"\n          },\n          className: \"lg:hidden backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-8 space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search for products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                onKeyDown: e => e.key === 'Enter' && handleSearch(e),\n                className: \"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.2\n              },\n              className: \"space-y-3\",\n              children: navigationItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.1 * (index + 3)\n                },\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: item.href,\n                  onClick: () => setIsOpen(false),\n                  className: `flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group ${isActive(item.href) ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg' : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600'}`,\n                  children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                    className: `w-6 h-6 ${isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-semibold text-lg\",\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 25\n                  }, this), isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                    layoutId: \"activeMobileTab\",\n                    className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 23\n                }, this)\n              }, item.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.4\n              },\n              className: \"flex space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"flex-1\",\n                onClick: () => setIsOpen(false),\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\",\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"flex-1\",\n                onClick: () => setIsOpen(false),\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.5\n              },\n              className: \"flex items-center justify-around pt-6 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/wishlist\",\n                onClick: () => setIsOpen(false),\n                className: \"flex flex-col items-center space-y-2 text-gray-600 hover:text-light-orange-600 transition-colors duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 rounded-2xl bg-gray-100 hover:bg-light-orange-50 transition-colors duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium\",\n                  children: \"Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                onClick: () => setIsOpen(false),\n                className: \"flex flex-col items-center space-y-2 text-gray-600 hover:text-light-orange-600 transition-colors duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 rounded-2xl bg-gray-100 hover:bg-light-orange-50 transition-colors duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 rounded-2xl bg-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-18 lg:h-22\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navigation, \"EHd0Z8auy7azxY1ShWbP2rKcWRk=\", false, function () {\n  return [useLocation, useUser];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "ShoppingCart", "useUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Navigation", "_s", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "showUserDropdown", "setShowUserDropdown", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "window", "href", "encodeURIComponent", "handleScroll", "scrollY", "handleClickOutside", "event", "target", "closest", "addEventListener", "document", "removeEventListener", "navigationItems", "name", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "layoutId", "damping", "placeholder", "value", "onChange", "onKeyDown", "key", "button", "whileTap", "onClick", "profilePicture", "src", "alt", "firstName", "opacity", "exit", "lastName", "email", "height", "ease", "x", "delay", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Navigate to products page with search query\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    const handleClickOutside = (event) => {\n      if (!event.target.closest('.user-dropdown')) {\n        setShowUserDropdown(false);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('click', handleClickOutside);\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n          isScrolled\n            ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100'\n            : 'bg-white/10 backdrop-blur-sm'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-18 lg:h-22\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n              <motion.div\n                whileHover={{ rotate: 360, scale: 1.1 }}\n                transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n              >\n                <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n              </motion.div>\n              <div className=\"flex flex-col\">\n                <span className={`text-2xl font-bold transition-all duration-300 ${\n                  isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'\n                }`}>\n                  ShopHub\n                </span>\n                <span className={`text-xs font-medium transition-all duration-300 ${\n                  isScrolled ? 'text-light-orange-600' : 'text-white/80'\n                }`}>\n                  Premium Store\n                </span>\n              </div>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-2\">\n              {navigationItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ y: -2 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <Link\n                    to={item.href}\n                    className={`relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${\n                      isActive(item.href)\n                        ? isScrolled\n                          ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'\n                          : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm'\n                        : isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'\n                    }`}\n                  >\n                    <span className=\"relative z-10\">{item.name}</span>\n                    {isActive(item.href) && (\n                      <motion.div\n                        layoutId=\"activeNavBg\"\n                        className=\"absolute inset-0 rounded-xl\"\n                        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                      />\n                    )}\n                    {!isActive(item.href) && (\n                      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"></div>\n                    )}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-lg mx-8\">\n              <motion.div\n                className=\"relative w-full group\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.2 }}\n              >\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${\n                    isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for products, brands, and more...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                  className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${\n                    isScrolled\n                      ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'\n                      : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'\n                  } focus:outline-none shadow-lg hover:shadow-xl`}\n                />\n                <div className={`absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${\n                  isScrolled\n                    ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100'\n                    : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'\n                }`}></div>\n              </motion.div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-3\">\n              <Link to=\"/wishlist\">\n                <motion.button\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                  className={`relative p-3 rounded-xl transition-all duration-300 group ${\n                    isScrolled\n                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                      : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'\n                  }`}\n                >\n                  <HeartIcon className=\"w-6 h-6\" />\n                  <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                </motion.button>\n              </Link>\n\n              <div className=\"relative\">\n                <ShoppingCart />\n              </div>\n\n              {/* User Account */}\n              {isAuthenticated ? (\n                <div className=\"relative user-dropdown\">\n                  <motion.button\n                    whileHover={{ scale: 1.05, y: -2 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                        : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'\n                    }`}\n                  >\n                    {user?.profilePicture ? (\n                      <img\n                        src={user.profilePicture}\n                        alt=\"Profile\"\n                        className=\"w-8 h-8 rounded-full object-cover ring-2 ring-white/20\"\n                      />\n                    ) : (\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\">\n                        <UserIcon className=\"w-5 h-5 text-white\" />\n                      </div>\n                    )}\n                    <span className=\"hidden md:block text-sm font-medium\">\n                      {user?.firstName || 'Account'}\n                    </span>\n                    <ChevronDownIcon className={`w-4 h-4 transition-transform duration-300 ${showUserDropdown ? 'rotate-180' : ''}`} />\n                  </motion.button>\n\n                  {/* User Dropdown */}\n                  <AnimatePresence>\n                    {showUserDropdown && (\n                      <motion.div\n                        initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                        animate={{ opacity: 1, y: 0, scale: 1 }}\n                        exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden z-50\"\n                      >\n                        <div className=\"p-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\">\n                          <div className=\"flex items-center space-x-3\">\n                            {user?.profilePicture ? (\n                              <img\n                                src={user.profilePicture}\n                                alt=\"Profile\"\n                                className=\"w-12 h-12 rounded-full object-cover ring-2 ring-white/30\"\n                              />\n                            ) : (\n                              <div className=\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\">\n                                <UserIcon className=\"w-6 h-6 text-white\" />\n                              </div>\n                            )}\n                            <div>\n                              <p className=\"font-semibold text-white\">\n                                {user?.firstName} {user?.lastName}\n                              </p>\n                              <p className=\"text-sm text-white/80\">{user?.email}</p>\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"py-2\">\n                          <Link\n                            to=\"/account\"\n                            onClick={() => setShowUserDropdown(false)}\n                            className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\"\n                          >\n                            <UserIcon className=\"w-5 h-5\" />\n                            <span>My Account</span>\n                          </Link>\n                          <Link\n                            to=\"/orders\"\n                            onClick={() => setShowUserDropdown(false)}\n                            className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\"\n                          >\n                            <ShoppingBagIcon className=\"w-5 h-5\" />\n                            <span>Order History</span>\n                          </Link>\n                          <Link\n                            to=\"/wishlist\"\n                            onClick={() => setShowUserDropdown(false)}\n                            className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\"\n                          >\n                            <HeartIcon className=\"w-5 h-5\" />\n                            <span>Wishlist</span>\n                          </Link>\n                          <div className=\"border-t border-gray-100 mt-2 pt-2\">\n                            <button\n                              onClick={() => {\n                                logout();\n                                setShowUserDropdown(false);\n                              }}\n                              className=\"flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\"\n                            >\n                              <XMarkIcon className=\"w-5 h-5\" />\n                              <span>Sign Out</span>\n                            </button>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-3\">\n                  <Link to=\"/login\">\n                    <motion.button\n                      whileHover={{ scale: 1.05, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'\n                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm border border-white/20 hover:border-white/40'\n                      }`}\n                    >\n                      Sign In\n                    </motion.button>\n                  </Link>\n                  <Link to=\"/register\">\n                    <motion.button\n                      whileHover={{ scale: 1.05, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\n                    >\n                      Sign Up\n                    </motion.button>\n                  </Link>\n                </div>\n              )}\n\n              {/* Mobile Menu Button */}\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-3 rounded-xl transition-all duration-300 ${\n                  isScrolled\n                    ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                    : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'\n                }`}\n              >\n                <motion.div\n                  animate={{ rotate: isOpen ? 180 : 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  {isOpen ? (\n                    <XMarkIcon className=\"w-6 h-6\" />\n                  ) : (\n                    <Bars3Icon className=\"w-6 h-6\" />\n                  )}\n                </motion.div>\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0, y: -20 }}\n              animate={{ opacity: 1, height: 'auto', y: 0 }}\n              exit={{ opacity: 0, height: 0, y: -20 }}\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\n              className=\"lg:hidden backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\"\n            >\n              <div className=\"px-6 py-8 space-y-6\">\n                {/* Mobile Search */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.1 }}\n                  className=\"relative\"\n                >\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search for products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                    className=\"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                  />\n                </motion.div>\n\n                {/* Mobile Navigation Links */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.2 }}\n                  className=\"space-y-3\"\n                >\n                  {navigationItems.map((item, index) => (\n                    <motion.div\n                      key={item.name}\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: 0.1 * (index + 3) }}\n                    >\n                      <Link\n                        to={item.href}\n                        onClick={() => setIsOpen(false)}\n                        className={`flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group ${\n                          isActive(item.href)\n                            ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'\n                            : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600'\n                        }`}\n                      >\n                        <item.icon className={`w-6 h-6 ${isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500'}`} />\n                        <span className=\"font-semibold text-lg\">{item.name}</span>\n                        {isActive(item.href) && (\n                          <motion.div\n                            layoutId=\"activeMobileTab\"\n                            className=\"ml-auto w-2 h-2 bg-white rounded-full\"\n                          />\n                        )}\n                      </Link>\n                    </motion.div>\n                  ))}\n                </motion.div>\n\n                {/* Mobile Auth Buttons */}\n                {!isAuthenticated && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.4 }}\n                    className=\"flex space-x-4 pt-4\"\n                  >\n                    <Link to=\"/login\" className=\"flex-1\" onClick={() => setIsOpen(false)}>\n                      <button className=\"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\">\n                        Sign In\n                      </button>\n                    </Link>\n                    <Link to=\"/register\" className=\"flex-1\" onClick={() => setIsOpen(false)}>\n                      <button className=\"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\">\n                        Sign Up\n                      </button>\n                    </Link>\n                  </motion.div>\n                )}\n\n                {/* Mobile Action Buttons */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5 }}\n                  className=\"flex items-center justify-around pt-6 border-t border-gray-200\"\n                >\n                  <Link to=\"/wishlist\" onClick={() => setIsOpen(false)} className=\"flex flex-col items-center space-y-2 text-gray-600 hover:text-light-orange-600 transition-colors duration-300\">\n                    <div className=\"p-3 rounded-2xl bg-gray-100 hover:bg-light-orange-50 transition-colors duration-300\">\n                      <HeartIcon className=\"w-6 h-6\" />\n                    </div>\n                    <span className=\"text-sm font-medium\">Wishlist</span>\n                  </Link>\n                  <Link to=\"/account\" onClick={() => setIsOpen(false)} className=\"flex flex-col items-center space-y-2 text-gray-600 hover:text-light-orange-600 transition-colors duration-300\">\n                    <div className=\"p-3 rounded-2xl bg-gray-100 hover:bg-light-orange-50 transition-colors duration-300\">\n                      <UserIcon className=\"w-6 h-6\" />\n                    </div>\n                    <span className=\"text-sm font-medium\">Account</span>\n                  </Link>\n                  <div className=\"flex flex-col items-center space-y-2\">\n                    <div className=\"p-3 rounded-2xl bg-gray-100\">\n                      <ShoppingCart />\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-600\">Cart</span>\n                  </div>\n                </motion.div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-18 lg:h-22\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,EACrBC,eAAe,QACV,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMiC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+B,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIV,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACtB;MACAC,MAAM,CAACR,QAAQ,CAACS,IAAI,GAAG,oBAAoBC,kBAAkB,CAACd,WAAW,CAACW,IAAI,CAAC,CAAC,CAAC,EAAE;IACrF;EACF,CAAC;EAEDvC,SAAS,CAAC,MAAM;IACd,MAAM2C,YAAY,GAAGA,CAAA,KAAM;MACzBhB,aAAa,CAACa,MAAM,CAACI,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAED,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3CjB,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAEDS,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC/CO,QAAQ,CAACD,gBAAgB,CAAC,OAAO,EAAEJ,kBAAkB,CAAC;IAEtD,OAAO,MAAM;MACXL,MAAM,CAACW,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;MAClDO,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEN,kBAAkB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,MAAM;IAAEZ,IAAI,EAAE,GAAG;IAAEa,IAAI,EAAE3C;EAAS,CAAC,EAC3C;IAAE0C,IAAI,EAAE,UAAU;IAAEZ,IAAI,EAAE,WAAW;IAAEa,IAAI,EAAE1C;EAAQ,CAAC,EACtD;IAAEyC,IAAI,EAAE,SAAS;IAAEZ,IAAI,EAAE,mBAAmB;IAAEa,IAAI,EAAE1C;EAAQ,CAAC,EAC7D;IAAEyC,IAAI,EAAE,OAAO;IAAEZ,IAAI,EAAE,QAAQ;IAAEa,IAAI,EAAExC;EAAsB,CAAC,EAC9D;IAAEuC,IAAI,EAAE,SAAS;IAAEZ,IAAI,EAAE,UAAU;IAAEa,IAAI,EAAEzC;EAAU,CAAC,CACvD;EAED,MAAM0C,QAAQ,GAAIC,IAAI,IAAKxB,QAAQ,CAACyB,QAAQ,KAAKD,IAAI;EAErD,oBACErC,OAAA,CAAAE,SAAA;IAAAqC,QAAA,gBACEvC,OAAA,CAAChB,MAAM,CAACwD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,SAAS,EAAE,+DACTrC,UAAU,GACN,iEAAiE,GACjE,8BAA8B,EACjC;MAAAgC,QAAA,gBAEHvC,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAL,QAAA,eACrDvC,OAAA;UAAK4C,SAAS,EAAC,gDAAgD;UAAAL,QAAA,gBAE7DvC,OAAA,CAAClB,IAAI;YAAC+D,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,mCAAmC;YAAAL,QAAA,gBACxDvC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTC,UAAU,EAAE;gBAAEC,MAAM,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cACxCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE;cAAI,CAAE;cAC9DT,SAAS,EAAC,2MAA2M;cAAAL,QAAA,gBAErNvC,OAAA,CAACZ,eAAe;gBAACwD,SAAS,EAAC;cAAoB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDzD,OAAA;gBAAK4C,SAAS,EAAC;cAA6E;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACbzD,OAAA;cAAK4C,SAAS,EAAC,eAAe;cAAAL,QAAA,gBAC5BvC,OAAA;gBAAM4C,SAAS,EAAE,kDACfrC,UAAU,GAAG,eAAe,GAAG,2BAA2B,EACzD;gBAAAgC,QAAA,EAAC;cAEJ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPzD,OAAA;gBAAM4C,SAAS,EAAE,mDACfrC,UAAU,GAAG,uBAAuB,GAAG,eAAe,EACrD;gBAAAgC,QAAA,EAAC;cAEJ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPzD,OAAA;YAAK4C,SAAS,EAAC,uCAAuC;YAAAL,QAAA,EACnDN,eAAe,CAACyB,GAAG,CAAEC,IAAI,iBACxB3D,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cAETC,UAAU,EAAE;gBAAEL,CAAC,EAAE,CAAC;cAAE,CAAE;cACtBQ,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,eAE9BvC,OAAA,CAAClB,IAAI;gBACH+D,EAAE,EAAEc,IAAI,CAACrC,IAAK;gBACdsB,SAAS,EAAE,2FACTR,QAAQ,CAACuB,IAAI,CAACrC,IAAI,CAAC,GACff,UAAU,GACR,qEAAqE,GACrE,sDAAsD,GACxDA,UAAU,GACR,oEAAoE,GACpE,mEAAmE,EACxE;gBAAAgC,QAAA,gBAEHvC,OAAA;kBAAM4C,SAAS,EAAC,eAAe;kBAAAL,QAAA,EAAEoB,IAAI,CAACzB;gBAAI;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACjDrB,QAAQ,CAACuB,IAAI,CAACrC,IAAI,CAAC,iBAClBtB,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBACTc,QAAQ,EAAC,aAAa;kBACtBhB,SAAS,EAAC,6BAA6B;kBACvCM,UAAU,EAAE;oBAAEE,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE,GAAG;oBAAEQ,OAAO,EAAE;kBAAG;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CACF,EACA,CAACrB,QAAQ,CAACuB,IAAI,CAACrC,IAAI,CAAC,iBACnBtB,OAAA;kBAAK4C,SAAS,EAAC;gBAAyJ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/K;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA3BFE,IAAI,CAACzB,IAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BJ,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzD,OAAA;YAAK4C,SAAS,EAAC,kDAAkD;YAAAL,QAAA,eAC/DvC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTF,SAAS,EAAC,uBAAuB;cACjCG,UAAU,EAAE;gBAAEE,KAAK,EAAE;cAAK,CAAE;cAC5BC,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,gBAE9BvC,OAAA;gBAAK4C,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFvC,OAAA,CAACX,mBAAmB;kBAACuD,SAAS,EAAE,0CAC9BrC,UAAU,GAAG,iDAAiD,GAAG,sCAAsC;gBACtG;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBACEoD,IAAI,EAAC,MAAM;gBACXU,WAAW,EAAC,0CAA0C;gBACtDC,KAAK,EAAEtD,WAAY;gBACnBuD,QAAQ,EAAG9C,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACU,MAAM,CAACmC,KAAK,CAAE;gBAChDE,SAAS,EAAG/C,CAAC,IAAKA,CAAC,CAACgD,GAAG,KAAK,OAAO,IAAIjD,YAAY,CAACC,CAAC,CAAE;gBACvD0B,SAAS,EAAE,2EACTrC,UAAU,GACN,qJAAqJ,GACrJ,uJAAuJ;cAC7G;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACFzD,OAAA;gBAAK4C,SAAS,EAAE,oFACdrC,UAAU,GACN,4FAA4F,GAC5F,6EAA6E;cAChF;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNzD,OAAA;YAAK4C,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAC1CvC,OAAA,CAAClB,IAAI;cAAC+D,EAAE,EAAC,WAAW;cAAAN,QAAA,eAClBvC,OAAA,CAAChB,MAAM,CAACmF,MAAM;gBACZpB,UAAU,EAAE;kBAAEE,KAAK,EAAE,GAAG;kBAAEP,CAAC,EAAE,CAAC;gBAAE,CAAE;gBAClC0B,QAAQ,EAAE;kBAAEnB,KAAK,EAAE;gBAAK,CAAE;gBAC1BL,SAAS,EAAE,6DACTrC,UAAU,GACN,oFAAoF,GACpF,mFAAmF,EACtF;gBAAAgC,QAAA,gBAEHvC,OAAA,CAACT,SAAS;kBAACqD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCzD,OAAA;kBAAK4C,SAAS,EAAC;gBAA4H;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEPzD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAL,QAAA,eACvBvC,OAAA,CAACH,YAAY;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EAGL1C,eAAe,gBACdf,OAAA;cAAK4C,SAAS,EAAC,wBAAwB;cAAAL,QAAA,gBACrCvC,OAAA,CAAChB,MAAM,CAACmF,MAAM;gBACZpB,UAAU,EAAE;kBAAEE,KAAK,EAAE,IAAI;kBAAEP,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACnC0B,QAAQ,EAAE;kBAAEnB,KAAK,EAAE;gBAAK,CAAE;gBAC1BoB,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBACtDiC,SAAS,EAAE,+FACTrC,UAAU,GACN,oFAAoF,GACpF,mFAAmF,EACtF;gBAAAgC,QAAA,GAEFzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwD,cAAc,gBACnBtE,OAAA;kBACEuE,GAAG,EAAEzD,IAAI,CAACwD,cAAe;kBACzBE,GAAG,EAAC,SAAS;kBACb5B,SAAS,EAAC;gBAAwD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,gBAEFzD,OAAA;kBAAK4C,SAAS,EAAC,mHAAmH;kBAAAL,QAAA,eAChIvC,OAAA,CAACV,QAAQ;oBAACsD,SAAS,EAAC;kBAAoB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CACN,eACDzD,OAAA;kBAAM4C,SAAS,EAAC,qCAAqC;kBAAAL,QAAA,EAClD,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,SAAS,KAAI;gBAAS;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACPzD,OAAA,CAACJ,eAAe;kBAACgD,SAAS,EAAE,6CAA6CjC,gBAAgB,GAAG,YAAY,GAAG,EAAE;gBAAG;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CAAC,eAGhBzD,OAAA,CAACf,eAAe;gBAAAsD,QAAA,EACb5B,gBAAgB,iBACfX,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBACTL,OAAO,EAAE;oBAAEiC,OAAO,EAAE,CAAC;oBAAEhC,CAAC,EAAE,EAAE;oBAAEO,KAAK,EAAE;kBAAK,CAAE;kBAC5CN,OAAO,EAAE;oBAAE+B,OAAO,EAAE,CAAC;oBAAEhC,CAAC,EAAE,CAAC;oBAAEO,KAAK,EAAE;kBAAE,CAAE;kBACxC0B,IAAI,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEhC,CAAC,EAAE,EAAE;oBAAEO,KAAK,EAAE;kBAAK,CAAE;kBACzCC,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAC9BP,SAAS,EAAC,wGAAwG;kBAAAL,QAAA,gBAElHvC,OAAA;oBAAK4C,SAAS,EAAC,2EAA2E;oBAAAL,QAAA,eACxFvC,OAAA;sBAAK4C,SAAS,EAAC,6BAA6B;sBAAAL,QAAA,GACzCzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwD,cAAc,gBACnBtE,OAAA;wBACEuE,GAAG,EAAEzD,IAAI,CAACwD,cAAe;wBACzBE,GAAG,EAAC,SAAS;wBACb5B,SAAS,EAAC;sBAA0D;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC,gBAEFzD,OAAA;wBAAK4C,SAAS,EAAC,qEAAqE;wBAAAL,QAAA,eAClFvC,OAAA,CAACV,QAAQ;0BAACsD,SAAS,EAAC;wBAAoB;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACN,eACDzD,OAAA;wBAAAuC,QAAA,gBACEvC,OAAA;0BAAG4C,SAAS,EAAC,0BAA0B;0BAAAL,QAAA,GACpCzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,SAAS,EAAC,GAAC,EAAC3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,QAAQ;wBAAA;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACJzD,OAAA;0BAAG4C,SAAS,EAAC,uBAAuB;0BAAAL,QAAA,EAAEzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D;wBAAK;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA;oBAAK4C,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBACnBvC,OAAA,CAAClB,IAAI;sBACH+D,EAAE,EAAC,UAAU;sBACbwB,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAAC,KAAK,CAAE;sBAC1CgC,SAAS,EAAC,iJAAiJ;sBAAAL,QAAA,gBAE3JvC,OAAA,CAACV,QAAQ;wBAACsD,SAAS,EAAC;sBAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChCzD,OAAA;wBAAAuC,QAAA,EAAM;sBAAU;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACPzD,OAAA,CAAClB,IAAI;sBACH+D,EAAE,EAAC,SAAS;sBACZwB,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAAC,KAAK,CAAE;sBAC1CgC,SAAS,EAAC,iJAAiJ;sBAAAL,QAAA,gBAE3JvC,OAAA,CAACZ,eAAe;wBAACwD,SAAS,EAAC;sBAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvCzD,OAAA;wBAAAuC,QAAA,EAAM;sBAAa;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACPzD,OAAA,CAAClB,IAAI;sBACH+D,EAAE,EAAC,WAAW;sBACdwB,OAAO,EAAEA,CAAA,KAAMzD,mBAAmB,CAAC,KAAK,CAAE;sBAC1CgC,SAAS,EAAC,iJAAiJ;sBAAAL,QAAA,gBAE3JvC,OAAA,CAACT,SAAS;wBAACqD,SAAS,EAAC;sBAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjCzD,OAAA;wBAAAuC,QAAA,EAAM;sBAAQ;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACPzD,OAAA;sBAAK4C,SAAS,EAAC,oCAAoC;sBAAAL,QAAA,eACjDvC,OAAA;wBACEqE,OAAO,EAAEA,CAAA,KAAM;0BACbrD,MAAM,CAAC,CAAC;0BACRJ,mBAAmB,CAAC,KAAK,CAAC;wBAC5B,CAAE;wBACFgC,SAAS,EAAC,kHAAkH;wBAAAL,QAAA,gBAE5HvC,OAAA,CAACb,SAAS;0BAACyD,SAAS,EAAC;wBAAS;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACjCzD,OAAA;0BAAAuC,QAAA,EAAM;wBAAQ;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,gBAENzD,OAAA;cAAK4C,SAAS,EAAC,6BAA6B;cAAAL,QAAA,gBAC1CvC,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACfvC,OAAA,CAAChB,MAAM,CAACmF,MAAM;kBACZpB,UAAU,EAAE;oBAAEE,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnC0B,QAAQ,EAAE;oBAAEnB,KAAK,EAAE;kBAAK,CAAE;kBAC1BL,SAAS,EAAE,4EACTrC,UAAU,GACN,yHAAyH,GACzH,gHAAgH,EACnH;kBAAAgC,QAAA,EACJ;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACPzD,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClBvC,OAAA,CAAChB,MAAM,CAACmF,MAAM;kBACZpB,UAAU,EAAE;oBAAEE,KAAK,EAAE,IAAI;oBAAEP,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnC0B,QAAQ,EAAE;oBAAEnB,KAAK,EAAE;kBAAK,CAAE;kBAC1BL,SAAS,EAAC,gOAAgO;kBAAAL,QAAA,EAC3O;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGDzD,OAAA,CAAChB,MAAM,CAACmF,MAAM;cACZpB,UAAU,EAAE;gBAAEE,KAAK,EAAE;cAAI,CAAE;cAC3BmB,QAAQ,EAAE;gBAAEnB,KAAK,EAAE;cAAI,CAAE;cACzBoB,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,CAACD,MAAM,CAAE;cAClCuC,SAAS,EAAE,wDACTrC,UAAU,GACN,oEAAoE,GACpE,mEAAmE,EACtE;cAAAgC,QAAA,eAEHvC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTH,OAAO,EAAE;kBAAEK,MAAM,EAAE3C,MAAM,GAAG,GAAG,GAAG;gBAAE,CAAE;gBACtC6C,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAZ,QAAA,EAE7BlC,MAAM,gBACLL,OAAA,CAACb,SAAS;kBAACyD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEjCzD,OAAA,CAACd,SAAS;kBAAC0D,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACjC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA,CAACf,eAAe;QAAAsD,QAAA,EACblC,MAAM,iBACLL,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTL,OAAO,EAAE;YAAEiC,OAAO,EAAE,CAAC;YAAEI,MAAM,EAAE,CAAC;YAAEpC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC3CC,OAAO,EAAE;YAAE+B,OAAO,EAAE,CAAC;YAAEI,MAAM,EAAE,MAAM;YAAEpC,CAAC,EAAE;UAAE,CAAE;UAC9CiC,IAAI,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEI,MAAM,EAAE,CAAC;YAAEpC,CAAC,EAAE,CAAC;UAAG,CAAE;UACxCQ,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAE4B,IAAI,EAAE;UAAY,CAAE;UACjDnC,SAAS,EAAC,4EAA4E;UAAAL,QAAA,eAEtFvC,OAAA;YAAK4C,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAElCvC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTL,OAAO,EAAE;gBAAEiC,OAAO,EAAE,CAAC;gBAAEM,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCrC,OAAO,EAAE;gBAAE+B,OAAO,EAAE,CAAC;gBAAEM,CAAC,EAAE;cAAE,CAAE;cAC9B9B,UAAU,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAE;cAC3BrC,SAAS,EAAC,UAAU;cAAAL,QAAA,gBAEpBvC,OAAA;gBAAK4C,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFvC,OAAA,CAACX,mBAAmB;kBAACuD,SAAS,EAAC;gBAAuB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNzD,OAAA;gBACEoD,IAAI,EAAC,MAAM;gBACXU,WAAW,EAAC,wBAAwB;gBACpCC,KAAK,EAAEtD,WAAY;gBACnBuD,QAAQ,EAAG9C,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACU,MAAM,CAACmC,KAAK,CAAE;gBAChDE,SAAS,EAAG/C,CAAC,IAAKA,CAAC,CAACgD,GAAG,KAAK,OAAO,IAAIjD,YAAY,CAACC,CAAC,CAAE;gBACvD0B,SAAS,EAAC;cAAgP;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3P,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGbzD,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTL,OAAO,EAAE;gBAAEiC,OAAO,EAAE,CAAC;gBAAEhC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAE+B,OAAO,EAAE,CAAC;gBAAEhC,CAAC,EAAE;cAAE,CAAE;cAC9BQ,UAAU,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAE;cAC3BrC,SAAS,EAAC,WAAW;cAAAL,QAAA,EAEpBN,eAAe,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEuB,KAAK,kBAC/BlF,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETL,OAAO,EAAE;kBAAEiC,OAAO,EAAE,CAAC;kBAAEM,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCrC,OAAO,EAAE;kBAAE+B,OAAO,EAAE,CAAC;kBAAEM,CAAC,EAAE;gBAAE,CAAE;gBAC9B9B,UAAU,EAAE;kBAAE+B,KAAK,EAAE,GAAG,IAAIC,KAAK,GAAG,CAAC;gBAAE,CAAE;gBAAA3C,QAAA,eAEzCvC,OAAA,CAAClB,IAAI;kBACH+D,EAAE,EAAEc,IAAI,CAACrC,IAAK;kBACd+C,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,KAAK,CAAE;kBAChCsC,SAAS,EAAE,uFACTR,QAAQ,CAACuB,IAAI,CAACrC,IAAI,CAAC,GACf,iFAAiF,GACjF,oEAAoE,EACvE;kBAAAiB,QAAA,gBAEHvC,OAAA,CAAC2D,IAAI,CAACxB,IAAI;oBAACS,SAAS,EAAE,WAAWR,QAAQ,CAACuB,IAAI,CAACrC,IAAI,CAAC,GAAG,YAAY,GAAG,iDAAiD;kBAAG;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7HzD,OAAA;oBAAM4C,SAAS,EAAC,uBAAuB;oBAAAL,QAAA,EAAEoB,IAAI,CAACzB;kBAAI;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACzDrB,QAAQ,CAACuB,IAAI,CAACrC,IAAI,CAAC,iBAClBtB,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTc,QAAQ,EAAC,iBAAiB;oBAC1BhB,SAAS,EAAC;kBAAuC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC,GAtBFE,IAAI,CAACzB,IAAI;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBJ,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,EAGZ,CAAC1C,eAAe,iBACff,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTL,OAAO,EAAE;gBAAEiC,OAAO,EAAE,CAAC;gBAAEhC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAE+B,OAAO,EAAE,CAAC;gBAAEhC,CAAC,EAAE;cAAE,CAAE;cAC9BQ,UAAU,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAE;cAC3BrC,SAAS,EAAC,qBAAqB;cAAAL,QAAA,gBAE/BvC,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,QAAQ;gBAACD,SAAS,EAAC,QAAQ;gBAACyB,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,KAAK,CAAE;gBAAAiC,QAAA,eACnEvC,OAAA;kBAAQ4C,SAAS,EAAC,wJAAwJ;kBAAAL,QAAA,EAAC;gBAE3K;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPzD,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,WAAW;gBAACD,SAAS,EAAC,QAAQ;gBAACyB,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,KAAK,CAAE;gBAAAiC,QAAA,eACtEvC,OAAA;kBAAQ4C,SAAS,EAAC,8MAA8M;kBAAAL,QAAA,EAAC;gBAEjO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACb,eAGDzD,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTL,OAAO,EAAE;gBAAEiC,OAAO,EAAE,CAAC;gBAAEhC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAE+B,OAAO,EAAE,CAAC;gBAAEhC,CAAC,EAAE;cAAE,CAAE;cAC9BQ,UAAU,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAE;cAC3BrC,SAAS,EAAC,gEAAgE;cAAAL,QAAA,gBAE1EvC,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,WAAW;gBAACwB,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,KAAK,CAAE;gBAACsC,SAAS,EAAC,+GAA+G;gBAAAL,QAAA,gBAC7KvC,OAAA;kBAAK4C,SAAS,EAAC,qFAAqF;kBAAAL,QAAA,eAClGvC,OAAA,CAACT,SAAS;oBAACqD,SAAS,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNzD,OAAA;kBAAM4C,SAAS,EAAC,qBAAqB;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACPzD,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,UAAU;gBAACwB,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,KAAK,CAAE;gBAACsC,SAAS,EAAC,+GAA+G;gBAAAL,QAAA,gBAC5KvC,OAAA;kBAAK4C,SAAS,EAAC,qFAAqF;kBAAAL,QAAA,eAClGvC,OAAA,CAACV,QAAQ;oBAACsD,SAAS,EAAC;kBAAS;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACNzD,OAAA;kBAAM4C,SAAS,EAAC,qBAAqB;kBAAAL,QAAA,EAAC;gBAAO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACPzD,OAAA;gBAAK4C,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnDvC,OAAA;kBAAK4C,SAAS,EAAC,6BAA6B;kBAAAL,QAAA,eAC1CvC,OAAA,CAACH,YAAY;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACNzD,OAAA;kBAAM4C,SAAS,EAAC,mCAAmC;kBAAAL,QAAA,EAAC;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGbzD,OAAA;MAAK4C,SAAS,EAAC;IAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAACrD,EAAA,CApcID,UAAU;EAAA,QAKGpB,WAAW,EACce,OAAO;AAAA;AAAAqF,EAAA,GAN7ChF,UAAU;AAschB,eAAeA,UAAU;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}