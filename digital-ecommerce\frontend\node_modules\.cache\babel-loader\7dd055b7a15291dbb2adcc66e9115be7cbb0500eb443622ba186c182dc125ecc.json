{"ast": null, "code": "import { disposables as s } from '../../utils/disposables.js';\nimport { createStore as i } from '../../utils/store.js';\nimport { adjustScrollbarPadding as l } from './adjust-scrollbar-padding.js';\nimport { handleIOSLocking as d } from './handle-ios-locking.js';\nimport { preventScroll as p } from './prevent-scroll.js';\nfunction m(e) {\n  let n = {};\n  for (let t of e) Object.assign(n, t(n));\n  return n;\n}\nlet a = i(() => new Map(), {\n  PUSH(e, n) {\n    var o;\n    let t = (o = this.get(e)) != null ? o : {\n      doc: e,\n      count: 0,\n      d: s(),\n      meta: new Set()\n    };\n    return t.count++, t.meta.add(n), this.set(e, t), this;\n  },\n  POP(e, n) {\n    let t = this.get(e);\n    return t && (t.count--, t.meta.delete(n)), this;\n  },\n  SCROLL_PREVENT(_ref) {\n    let {\n      doc: e,\n      d: n,\n      meta: t\n    } = _ref;\n    let o = {\n        doc: e,\n        d: n,\n        meta: m(t)\n      },\n      c = [d(), l(), p()];\n    c.forEach(_ref2 => {\n      let {\n        before: r\n      } = _ref2;\n      return r == null ? void 0 : r(o);\n    }), c.forEach(_ref3 => {\n      let {\n        after: r\n      } = _ref3;\n      return r == null ? void 0 : r(o);\n    });\n  },\n  SCROLL_ALLOW(_ref4) {\n    let {\n      d: e\n    } = _ref4;\n    e.dispose();\n  },\n  TEARDOWN(_ref5) {\n    let {\n      doc: e\n    } = _ref5;\n    this.delete(e);\n  }\n});\na.subscribe(() => {\n  let e = a.getSnapshot(),\n    n = new Map();\n  for (let [t] of e) n.set(t, t.documentElement.style.overflow);\n  for (let t of e.values()) {\n    let o = n.get(t.doc) === \"hidden\",\n      c = t.count !== 0;\n    (c && !o || !c && o) && a.dispatch(t.count > 0 ? \"SCROLL_PREVENT\" : \"SCROLL_ALLOW\", t), t.count === 0 && a.dispatch(\"TEARDOWN\", t);\n  }\n});\nexport { a as overflows };", "map": {"version": 3, "names": ["disposables", "s", "createStore", "i", "adjustScrollbarPadding", "l", "handleIOSLocking", "d", "preventScroll", "p", "m", "e", "n", "t", "Object", "assign", "a", "Map", "PUSH", "o", "get", "doc", "count", "meta", "Set", "add", "set", "POP", "delete", "SCROLL_PREVENT", "_ref", "c", "for<PERSON>ach", "_ref2", "before", "r", "_ref3", "after", "SCROLL_ALLOW", "_ref4", "dispose", "TEARDOWN", "_ref5", "subscribe", "getSnapshot", "documentElement", "style", "overflow", "values", "dispatch", "overflows"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js"], "sourcesContent": ["import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,sBAAsB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC,CAAC,CAAC;EAAC,KAAI,IAAIC,CAAC,IAAIF,CAAC,EAACG,MAAM,CAACC,MAAM,CAACH,CAAC,EAACC,CAAC,CAACD,CAAC,CAAC,CAAC;EAAC,OAAOA,CAAC;AAAA;AAAC,IAAII,CAAC,GAACb,CAAC,CAAC,MAAI,IAAIc,GAAG,CAAD,CAAC,EAAC;EAACC,IAAIA,CAACP,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIO,CAAC;IAAC,IAAIN,CAAC,GAAC,CAACM,CAAC,GAAC,IAAI,CAACC,GAAG,CAACT,CAAC,CAAC,KAAG,IAAI,GAACQ,CAAC,GAAC;MAACE,GAAG,EAACV,CAAC;MAACW,KAAK,EAAC,CAAC;MAACf,CAAC,EAACN,CAAC,CAAC,CAAC;MAACsB,IAAI,EAAC,IAAIC,GAAG,CAAD;IAAC,CAAC;IAAC,OAAOX,CAAC,CAACS,KAAK,EAAE,EAACT,CAAC,CAACU,IAAI,CAACE,GAAG,CAACb,CAAC,CAAC,EAAC,IAAI,CAACc,GAAG,CAACf,CAAC,EAACE,CAAC,CAAC,EAAC,IAAI;EAAA,CAAC;EAACc,GAAGA,CAAChB,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI,CAACO,GAAG,CAACT,CAAC,CAAC;IAAC,OAAOE,CAAC,KAAGA,CAAC,CAACS,KAAK,EAAE,EAACT,CAAC,CAACU,IAAI,CAACK,MAAM,CAAChB,CAAC,CAAC,CAAC,EAAC,IAAI;EAAA,CAAC;EAACiB,cAAcA,CAAAC,IAAA,EAAoB;IAAA,IAAnB;MAACT,GAAG,EAACV,CAAC;MAACJ,CAAC,EAACK,CAAC;MAACW,IAAI,EAACV;IAAC,CAAC,GAAAiB,IAAA;IAAE,IAAIX,CAAC,GAAC;QAACE,GAAG,EAACV,CAAC;QAACJ,CAAC,EAACK,CAAC;QAACW,IAAI,EAACb,CAAC,CAACG,CAAC;MAAC,CAAC;MAACkB,CAAC,GAAC,CAACxB,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC,CAAC,EAACI,CAAC,CAAC,CAAC,CAAC;IAACsB,CAAC,CAACC,OAAO,CAACC,KAAA;MAAA,IAAC;QAACC,MAAM,EAACC;MAAC,CAAC,GAAAF,KAAA;MAAA,OAAGE,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAChB,CAAC,CAAC;IAAA,EAAC,EAACY,CAAC,CAACC,OAAO,CAACI,KAAA;MAAA,IAAC;QAACC,KAAK,EAACF;MAAC,CAAC,GAAAC,KAAA;MAAA,OAAGD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAChB,CAAC,CAAC;IAAA,EAAC;EAAA,CAAC;EAACmB,YAAYA,CAAAC,KAAA,EAAO;IAAA,IAAN;MAAChC,CAAC,EAACI;IAAC,CAAC,GAAA4B,KAAA;IAAE5B,CAAC,CAAC6B,OAAO,CAAC,CAAC;EAAA,CAAC;EAACC,QAAQA,CAAAC,KAAA,EAAS;IAAA,IAAR;MAACrB,GAAG,EAACV;IAAC,CAAC,GAAA+B,KAAA;IAAE,IAAI,CAACd,MAAM,CAACjB,CAAC,CAAC;EAAA;AAAC,CAAC,CAAC;AAACK,CAAC,CAAC2B,SAAS,CAAC,MAAI;EAAC,IAAIhC,CAAC,GAACK,CAAC,CAAC4B,WAAW,CAAC,CAAC;IAAChC,CAAC,GAAC,IAAIK,GAAG,CAAD,CAAC;EAAC,KAAI,IAAG,CAACJ,CAAC,CAAC,IAAGF,CAAC,EAACC,CAAC,CAACc,GAAG,CAACb,CAAC,EAACA,CAAC,CAACgC,eAAe,CAACC,KAAK,CAACC,QAAQ,CAAC;EAAC,KAAI,IAAIlC,CAAC,IAAIF,CAAC,CAACqC,MAAM,CAAC,CAAC,EAAC;IAAC,IAAI7B,CAAC,GAACP,CAAC,CAACQ,GAAG,CAACP,CAAC,CAACQ,GAAG,CAAC,KAAG,QAAQ;MAACU,CAAC,GAAClB,CAAC,CAACS,KAAK,KAAG,CAAC;IAAC,CAACS,CAAC,IAAE,CAACZ,CAAC,IAAE,CAACY,CAAC,IAAEZ,CAAC,KAAGH,CAAC,CAACiC,QAAQ,CAACpC,CAAC,CAACS,KAAK,GAAC,CAAC,GAAC,gBAAgB,GAAC,cAAc,EAACT,CAAC,CAAC,EAACA,CAAC,CAACS,KAAK,KAAG,CAAC,IAAEN,CAAC,CAACiC,QAAQ,CAAC,UAAU,EAACpC,CAAC,CAAC;EAAA;AAAC,CAAC,CAAC;AAAC,SAAOG,CAAC,IAAIkC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}