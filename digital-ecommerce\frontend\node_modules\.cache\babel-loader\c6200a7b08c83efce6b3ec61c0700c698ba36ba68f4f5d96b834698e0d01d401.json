{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      console.log('Searching for:', searchQuery);\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    const handleClickOutside = event => {\n      if (!event.target.closest('.user-dropdown')) {\n        setShowUserDropdown(false);\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n  const navigationItems = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100' : 'bg-white/10 backdrop-blur-sm'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-18 lg:h-22\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3 group\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                rotate: 360,\n                scale: 1.1\n              },\n              transition: {\n                duration: 0.6,\n                type: \"spring\",\n                stiffness: 200\n              },\n              className: \"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-2xl font-bold transition-all duration-300 ${isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'}`,\n                children: \"ShopHub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs font-medium transition-all duration-300 ${isScrolled ? 'text-light-orange-600' : 'text-white/80'}`,\n                children: \"Premium Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-2\",\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                y: -2\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${isActive(item.href) ? isScrolled ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25' : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm' : isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative z-10\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this), isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                  layoutId: \"activeNavBg\",\n                  className: \"absolute inset-0 rounded-xl\",\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 300,\n                    damping: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 23\n                }, this), !isActive(item.href) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)\n            }, item.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center flex-1 max-w-lg mx-8\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative w-full group\",\n              whileHover: {\n                scale: 1.02\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 transition-colors duration-300 ${isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search for products, brands, and more...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${isScrolled ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100' : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'} focus:outline-none shadow-lg hover:shadow-xl`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${isScrolled ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100' : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/wishlist\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: `relative p-3 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'}`,\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative user-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setShowUserDropdown(!showUserDropdown),\n                className: `relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg' : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'}`,\n                children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.profilePicture,\n                  alt: \"Profile\",\n                  className: \"w-8 h-8 rounded-full object-cover ring-2 ring-white/20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                    className: \"w-5 h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden md:block text-sm font-medium\",\n                  children: (user === null || user === void 0 ? void 0 : user.firstName) || 'Account'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                  className: `w-4 h-4 transition-transform duration-300 ${showUserDropdown ? 'rotate-180' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                children: showUserDropdown && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 10,\n                    scale: 0.95\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: 10,\n                    scale: 0.95\n                  },\n                  transition: {\n                    duration: 0.2\n                  },\n                  className: \"absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden z-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3\",\n                      children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: user.profilePicture,\n                        alt: \"Profile\",\n                        className: \"w-12 h-12 rounded-full object-cover ring-2 ring-white/30\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                          className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 240,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-semibold text-white\",\n                          children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 244,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-white/80\",\n                          children: user === null || user === void 0 ? void 0 : user.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 247,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"py-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/account\",\n                      onClick: () => setShowUserDropdown(false),\n                      className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"My Account\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/orders\",\n                      onClick: () => setShowUserDropdown(false),\n                      className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Order History\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/wishlist\",\n                      onClick: () => setShowUserDropdown(false),\n                      className: \"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Wishlist\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border-t border-gray-100 mt-2 pt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          logout();\n                          setShowUserDropdown(false);\n                        },\n                        className: \"flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\",\n                        children: [/*#__PURE__*/_jsxDEV(XMarkIcon, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 284,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Sign Out\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 285,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(!isOpen),\n              className: `lg:hidden p-2 rounded-md transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600' : 'text-white hover:text-yellow-300'}`,\n              children: isOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto'\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: \"lg:hidden backdrop-blur-md border-t bg-white/95 border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-6 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full pl-10 pr-4 py-3 rounded-lg bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                onClick: () => setIsOpen(false),\n                className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${isActive(item.href) ? 'bg-light-orange-100 text-light-orange-700' : 'text-gray-700 hover:bg-gray-100'}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this)]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-around pt-4 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-600\",\n                  children: \"Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 lg:h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navigation, \"EHd0Z8auy7azxY1ShWbP2rKcWRk=\", false, function () {\n  return [useLocation, useUser];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "ShoppingCart", "useUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Navigation", "_s", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "showUserDropdown", "setShowUserDropdown", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "console", "log", "handleScroll", "window", "scrollY", "handleClickOutside", "event", "target", "closest", "addEventListener", "document", "removeEventListener", "navigationItems", "name", "href", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "layoutId", "damping", "placeholder", "value", "onChange", "button", "whileTap", "onClick", "profilePicture", "src", "alt", "firstName", "opacity", "exit", "lastName", "email", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      console.log('Searching for:', searchQuery);\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    const handleClickOutside = (event) => {\n      if (!event.target.closest('.user-dropdown')) {\n        setShowUserDropdown(false);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    document.addEventListener('click', handleClickOutside);\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n          isScrolled\n            ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100'\n            : 'bg-white/10 backdrop-blur-sm'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-18 lg:h-22\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n              <motion.div\n                whileHover={{ rotate: 360, scale: 1.1 }}\n                transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n              >\n                <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n              </motion.div>\n              <div className=\"flex flex-col\">\n                <span className={`text-2xl font-bold transition-all duration-300 ${\n                  isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'\n                }`}>\n                  ShopHub\n                </span>\n                <span className={`text-xs font-medium transition-all duration-300 ${\n                  isScrolled ? 'text-light-orange-600' : 'text-white/80'\n                }`}>\n                  Premium Store\n                </span>\n              </div>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-2\">\n              {navigationItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ y: -2 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <Link\n                    to={item.href}\n                    className={`relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${\n                      isActive(item.href)\n                        ? isScrolled\n                          ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'\n                          : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm'\n                        : isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'\n                    }`}\n                  >\n                    <span className=\"relative z-10\">{item.name}</span>\n                    {isActive(item.href) && (\n                      <motion.div\n                        layoutId=\"activeNavBg\"\n                        className=\"absolute inset-0 rounded-xl\"\n                        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                      />\n                    )}\n                    {!isActive(item.href) && (\n                      <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"></div>\n                    )}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-lg mx-8\">\n              <motion.div\n                className=\"relative w-full group\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.2 }}\n              >\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${\n                    isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for products, brands, and more...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${\n                    isScrolled\n                      ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'\n                      : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'\n                  } focus:outline-none shadow-lg hover:shadow-xl`}\n                />\n                <div className={`absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${\n                  isScrolled\n                    ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100'\n                    : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'\n                }`}></div>\n              </motion.div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-3\">\n              <Link to=\"/wishlist\">\n                <motion.button\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                  className={`relative p-3 rounded-xl transition-all duration-300 group ${\n                    isScrolled\n                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                      : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'\n                  }`}\n                >\n                  <HeartIcon className=\"w-6 h-6\" />\n                  <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                </motion.button>\n              </Link>\n\n              <div className=\"relative\">\n                <ShoppingCart />\n              </div>\n\n              {/* User Account */}\n              {isAuthenticated ? (\n                <div className=\"relative user-dropdown\">\n                  <motion.button\n                    whileHover={{ scale: 1.05, y: -2 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowUserDropdown(!showUserDropdown)}\n                    className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                        : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'\n                    }`}\n                  >\n                    {user?.profilePicture ? (\n                      <img\n                        src={user.profilePicture}\n                        alt=\"Profile\"\n                        className=\"w-8 h-8 rounded-full object-cover ring-2 ring-white/20\"\n                      />\n                    ) : (\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\">\n                        <UserIcon className=\"w-5 h-5 text-white\" />\n                      </div>\n                    )}\n                    <span className=\"hidden md:block text-sm font-medium\">\n                      {user?.firstName || 'Account'}\n                    </span>\n                    <ChevronDownIcon className={`w-4 h-4 transition-transform duration-300 ${showUserDropdown ? 'rotate-180' : ''}`} />\n                  </motion.button>\n\n                  {/* User Dropdown */}\n                  <AnimatePresence>\n                    {showUserDropdown && (\n                      <motion.div\n                        initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                        animate={{ opacity: 1, y: 0, scale: 1 }}\n                        exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden z-50\"\n                      >\n                        <div className=\"p-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\">\n                          <div className=\"flex items-center space-x-3\">\n                            {user?.profilePicture ? (\n                              <img\n                                src={user.profilePicture}\n                                alt=\"Profile\"\n                                className=\"w-12 h-12 rounded-full object-cover ring-2 ring-white/30\"\n                              />\n                            ) : (\n                              <div className=\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\">\n                                <UserIcon className=\"w-6 h-6 text-white\" />\n                              </div>\n                            )}\n                            <div>\n                              <p className=\"font-semibold text-white\">\n                                {user?.firstName} {user?.lastName}\n                              </p>\n                              <p className=\"text-sm text-white/80\">{user?.email}</p>\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"py-2\">\n                          <Link\n                            to=\"/account\"\n                            onClick={() => setShowUserDropdown(false)}\n                            className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\"\n                          >\n                            <UserIcon className=\"w-5 h-5\" />\n                            <span>My Account</span>\n                          </Link>\n                          <Link\n                            to=\"/orders\"\n                            onClick={() => setShowUserDropdown(false)}\n                            className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\"\n                          >\n                            <ShoppingBagIcon className=\"w-5 h-5\" />\n                            <span>Order History</span>\n                          </Link>\n                          <Link\n                            to=\"/wishlist\"\n                            onClick={() => setShowUserDropdown(false)}\n                            className=\"flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200\"\n                          >\n                            <HeartIcon className=\"w-5 h-5\" />\n                            <span>Wishlist</span>\n                          </Link>\n                          <div className=\"border-t border-gray-100 mt-2 pt-2\">\n                            <button\n                              onClick={() => {\n                                logout();\n                                setShowUserDropdown(false);\n                              }}\n                              className=\"flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\"\n                            >\n                              <XMarkIcon className=\"w-5 h-5\" />\n                              <span>Sign Out</span>\n                            </button>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <Link to=\"/login\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                      }`}\n                    >\n                      Sign In\n                    </motion.button>\n                  </Link>\n                  <Link to=\"/register\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\"\n                    >\n                      Sign Up\n                    </motion.button>\n                  </Link>\n                </div>\n              )}\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600' \n                    : 'text-white hover:text-yellow-300'\n                }`}\n              >\n                {isOpen ? (\n                  <XMarkIcon className=\"w-6 h-6\" />\n                ) : (\n                  <Bars3Icon className=\"w-6 h-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden backdrop-blur-md border-t bg-white/95 border-gray-200\"\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {/* Mobile Search */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n                  />\n                </div>\n\n                {/* Mobile Navigation Links */}\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setIsOpen(false)}\n                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-light-orange-100 text-light-orange-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      }`}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ))}\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"flex items-center justify-around pt-4 border-t border-gray-200\">\n                  <button className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <HeartIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Wishlist</span>\n                  </button>\n                  <Link to=\"/account\" className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <UserIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Account</span>\n                  </Link>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ShoppingCart />\n                    <span className=\"text-xs text-gray-600\">Cart</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16 lg:h-20\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,EACrBC,eAAe,QACV,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMiC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+B,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnD,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIV,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEb,WAAW,CAAC;IAC5C;EACF,CAAC;EAED5B,SAAS,CAAC,MAAM;IACd,MAAM0C,YAAY,GAAGA,CAAA,KAAM;MACzBf,aAAa,CAACgB,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAED,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3CjB,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAEDY,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAC/CQ,QAAQ,CAACD,gBAAgB,CAAC,OAAO,EAAEJ,kBAAkB,CAAC;IAEtD,OAAO,MAAM;MACXF,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAET,YAAY,CAAC;MAClDQ,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEN,kBAAkB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE5C;EAAS,CAAC,EAC3C;IAAE0C,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE3C;EAAQ,CAAC,EACtD;IAAEyC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAE3C;EAAQ,CAAC,EAC7D;IAAEyC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAEzC;EAAsB,CAAC,EAC9D;IAAEuC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE1C;EAAU,CAAC,CACvD;EAED,MAAM2C,QAAQ,GAAIC,IAAI,IAAKzB,QAAQ,CAAC0B,QAAQ,KAAKD,IAAI;EAErD,oBACEtC,OAAA,CAAAE,SAAA;IAAAsC,QAAA,gBACExC,OAAA,CAAChB,MAAM,CAACyD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,SAAS,EAAE,+DACTtC,UAAU,GACN,iEAAiE,GACjE,8BAA8B,EACjC;MAAAiC,QAAA,gBAEHxC,OAAA;QAAK6C,SAAS,EAAC,wCAAwC;QAAAL,QAAA,eACrDxC,OAAA;UAAK6C,SAAS,EAAC,gDAAgD;UAAAL,QAAA,gBAE7DxC,OAAA,CAAClB,IAAI;YAACgE,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,mCAAmC;YAAAL,QAAA,gBACxDxC,OAAA,CAAChB,MAAM,CAAC+D,GAAG;cACTC,UAAU,EAAE;gBAAEC,MAAM,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cACxCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE;cAAI,CAAE;cAC9DT,SAAS,EAAC,2MAA2M;cAAAL,QAAA,gBAErNxC,OAAA,CAACZ,eAAe;gBAACyD,SAAS,EAAC;cAAoB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClD1D,OAAA;gBAAK6C,SAAS,EAAC;cAA6E;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACb1D,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAL,QAAA,gBAC5BxC,OAAA;gBAAM6C,SAAS,EAAE,kDACftC,UAAU,GAAG,eAAe,GAAG,2BAA2B,EACzD;gBAAAiC,QAAA,EAAC;cAEJ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP1D,OAAA;gBAAM6C,SAAS,EAAE,mDACftC,UAAU,GAAG,uBAAuB,GAAG,eAAe,EACrD;gBAAAiC,QAAA,EAAC;cAEJ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGP1D,OAAA;YAAK6C,SAAS,EAAC,uCAAuC;YAAAL,QAAA,EACnDP,eAAe,CAAC0B,GAAG,CAAEC,IAAI,iBACxB5D,OAAA,CAAChB,MAAM,CAAC+D,GAAG;cAETC,UAAU,EAAE;gBAAEL,CAAC,EAAE,CAAC;cAAE,CAAE;cACtBQ,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,eAE9BxC,OAAA,CAAClB,IAAI;gBACHgE,EAAE,EAAEc,IAAI,CAACzB,IAAK;gBACdU,SAAS,EAAE,2FACTR,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,GACf5B,UAAU,GACR,qEAAqE,GACrE,sDAAsD,GACxDA,UAAU,GACR,oEAAoE,GACpE,mEAAmE,EACxE;gBAAAiC,QAAA,gBAEHxC,OAAA;kBAAM6C,SAAS,EAAC,eAAe;kBAAAL,QAAA,EAAEoB,IAAI,CAAC1B;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACjDrB,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,iBAClBnC,OAAA,CAAChB,MAAM,CAAC+D,GAAG;kBACTc,QAAQ,EAAC,aAAa;kBACtBhB,SAAS,EAAC,6BAA6B;kBACvCM,UAAU,EAAE;oBAAEE,IAAI,EAAE,QAAQ;oBAAEC,SAAS,EAAE,GAAG;oBAAEQ,OAAO,EAAE;kBAAG;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CACF,EACA,CAACrB,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,iBACnBnC,OAAA;kBAAK6C,SAAS,EAAC;gBAAyJ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/K;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GA3BFE,IAAI,CAAC1B,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BJ,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1D,OAAA;YAAK6C,SAAS,EAAC,kDAAkD;YAAAL,QAAA,eAC/DxC,OAAA,CAAChB,MAAM,CAAC+D,GAAG;cACTF,SAAS,EAAC,uBAAuB;cACjCG,UAAU,EAAE;gBAAEE,KAAK,EAAE;cAAK,CAAE;cAC5BC,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAZ,QAAA,gBAE9BxC,OAAA;gBAAK6C,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFxC,OAAA,CAACX,mBAAmB;kBAACwD,SAAS,EAAE,0CAC9BtC,UAAU,GAAG,iDAAiD,GAAG,sCAAsC;gBACtG;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1D,OAAA;gBACEqD,IAAI,EAAC,MAAM;gBACXU,WAAW,EAAC,0CAA0C;gBACtDC,KAAK,EAAEvD,WAAY;gBACnBwD,QAAQ,EAAG/C,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACU,MAAM,CAACoC,KAAK,CAAE;gBAChDnB,SAAS,EAAE,2EACTtC,UAAU,GACN,qJAAqJ,GACrJ,uJAAuJ;cAC7G;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACF1D,OAAA;gBAAK6C,SAAS,EAAE,oFACdtC,UAAU,GACN,4FAA4F,GAC5F,6EAA6E;cAChF;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGN1D,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAC1CxC,OAAA,CAAClB,IAAI;cAACgE,EAAE,EAAC,WAAW;cAAAN,QAAA,eAClBxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;gBACZlB,UAAU,EAAE;kBAAEE,KAAK,EAAE,GAAG;kBAAEP,CAAC,EAAE,CAAC;gBAAE,CAAE;gBAClCwB,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAK,CAAE;gBAC1BL,SAAS,EAAE,6DACTtC,UAAU,GACN,oFAAoF,GACpF,mFAAmF,EACtF;gBAAAiC,QAAA,gBAEHxC,OAAA,CAACT,SAAS;kBAACsD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC1D,OAAA;kBAAK6C,SAAS,EAAC;gBAA4H;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEP1D,OAAA;cAAK6C,SAAS,EAAC,UAAU;cAAAL,QAAA,eACvBxC,OAAA,CAACH,YAAY;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EAGL3C,eAAe,gBACdf,OAAA;cAAK6C,SAAS,EAAC,wBAAwB;cAAAL,QAAA,gBACrCxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;gBACZlB,UAAU,EAAE;kBAAEE,KAAK,EAAE,IAAI;kBAAEP,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACnCwB,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAK,CAAE;gBAC1BkB,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBACtDkC,SAAS,EAAE,+FACTtC,UAAU,GACN,oFAAoF,GACpF,mFAAmF,EACtF;gBAAAiC,QAAA,GAEF1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuD,cAAc,gBACnBrE,OAAA;kBACEsE,GAAG,EAAExD,IAAI,CAACuD,cAAe;kBACzBE,GAAG,EAAC,SAAS;kBACb1B,SAAS,EAAC;gBAAwD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,gBAEF1D,OAAA;kBAAK6C,SAAS,EAAC,mHAAmH;kBAAAL,QAAA,eAChIxC,OAAA,CAACV,QAAQ;oBAACuD,SAAS,EAAC;kBAAoB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CACN,eACD1D,OAAA;kBAAM6C,SAAS,EAAC,qCAAqC;kBAAAL,QAAA,EAClD,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,SAAS,KAAI;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACP1D,OAAA,CAACJ,eAAe;kBAACiD,SAAS,EAAE,6CAA6ClC,gBAAgB,GAAG,YAAY,GAAG,EAAE;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CAAC,eAGhB1D,OAAA,CAACf,eAAe;gBAAAuD,QAAA,EACb7B,gBAAgB,iBACfX,OAAA,CAAChB,MAAM,CAAC+D,GAAG;kBACTL,OAAO,EAAE;oBAAE+B,OAAO,EAAE,CAAC;oBAAE9B,CAAC,EAAE,EAAE;oBAAEO,KAAK,EAAE;kBAAK,CAAE;kBAC5CN,OAAO,EAAE;oBAAE6B,OAAO,EAAE,CAAC;oBAAE9B,CAAC,EAAE,CAAC;oBAAEO,KAAK,EAAE;kBAAE,CAAE;kBACxCwB,IAAI,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAE9B,CAAC,EAAE,EAAE;oBAAEO,KAAK,EAAE;kBAAK,CAAE;kBACzCC,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAC9BP,SAAS,EAAC,wGAAwG;kBAAAL,QAAA,gBAElHxC,OAAA;oBAAK6C,SAAS,EAAC,2EAA2E;oBAAAL,QAAA,eACxFxC,OAAA;sBAAK6C,SAAS,EAAC,6BAA6B;sBAAAL,QAAA,GACzC1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuD,cAAc,gBACnBrE,OAAA;wBACEsE,GAAG,EAAExD,IAAI,CAACuD,cAAe;wBACzBE,GAAG,EAAC,SAAS;wBACb1B,SAAS,EAAC;sBAA0D;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrE,CAAC,gBAEF1D,OAAA;wBAAK6C,SAAS,EAAC,qEAAqE;wBAAAL,QAAA,eAClFxC,OAAA,CAACV,QAAQ;0BAACuD,SAAS,EAAC;wBAAoB;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACN,eACD1D,OAAA;wBAAAwC,QAAA,gBACExC,OAAA;0BAAG6C,SAAS,EAAC,0BAA0B;0BAAAL,QAAA,GACpC1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,SAAS,EAAC,GAAC,EAAC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,QAAQ;wBAAA;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACJ1D,OAAA;0BAAG6C,SAAS,EAAC,uBAAuB;0BAAAL,QAAA,EAAE1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D;wBAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1D,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBACnBxC,OAAA,CAAClB,IAAI;sBACHgE,EAAE,EAAC,UAAU;sBACbsB,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK,CAAE;sBAC1CiC,SAAS,EAAC,iJAAiJ;sBAAAL,QAAA,gBAE3JxC,OAAA,CAACV,QAAQ;wBAACuD,SAAS,EAAC;sBAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChC1D,OAAA;wBAAAwC,QAAA,EAAM;sBAAU;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACP1D,OAAA,CAAClB,IAAI;sBACHgE,EAAE,EAAC,SAAS;sBACZsB,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK,CAAE;sBAC1CiC,SAAS,EAAC,iJAAiJ;sBAAAL,QAAA,gBAE3JxC,OAAA,CAACZ,eAAe;wBAACyD,SAAS,EAAC;sBAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvC1D,OAAA;wBAAAwC,QAAA,EAAM;sBAAa;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACP1D,OAAA,CAAClB,IAAI;sBACHgE,EAAE,EAAC,WAAW;sBACdsB,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK,CAAE;sBAC1CiC,SAAS,EAAC,iJAAiJ;sBAAAL,QAAA,gBAE3JxC,OAAA,CAACT,SAAS;wBAACsD,SAAS,EAAC;sBAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjC1D,OAAA;wBAAAwC,QAAA,EAAM;sBAAQ;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACP1D,OAAA;sBAAK6C,SAAS,EAAC,oCAAoC;sBAAAL,QAAA,eACjDxC,OAAA;wBACEoE,OAAO,EAAEA,CAAA,KAAM;0BACbpD,MAAM,CAAC,CAAC;0BACRJ,mBAAmB,CAAC,KAAK,CAAC;wBAC5B,CAAE;wBACFiC,SAAS,EAAC,kHAAkH;wBAAAL,QAAA,gBAE5HxC,OAAA,CAACb,SAAS;0BAAC0D,SAAS,EAAC;wBAAS;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACjC1D,OAAA;0BAAAwC,QAAA,EAAM;wBAAQ;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,gBAEN1D,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAL,QAAA,gBAC1CxC,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACfxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;kBACZlB,UAAU,EAAE;oBAAEE,KAAK,EAAE;kBAAK,CAAE;kBAC5BiB,QAAQ,EAAE;oBAAEjB,KAAK,EAAE;kBAAK,CAAE;kBAC1BL,SAAS,EAAE,6EACTtC,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;kBAAAiC,QAAA,EACJ;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACP1D,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClBxC,OAAA,CAAChB,MAAM,CAACkF,MAAM;kBACZlB,UAAU,EAAE;oBAAEE,KAAK,EAAE;kBAAK,CAAE;kBAC5BiB,QAAQ,EAAE;oBAAEjB,KAAK,EAAE;kBAAK,CAAE;kBAC1BL,SAAS,EAAC,oIAAoI;kBAAAL,QAAA,EAC/I;gBAED;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGD1D,OAAA;cACEoE,OAAO,EAAEA,CAAA,KAAM9D,SAAS,CAAC,CAACD,MAAM,CAAE;cAClCwC,SAAS,EAAE,2DACTtC,UAAU,GACN,2CAA2C,GAC3C,kCAAkC,EACrC;cAAAiC,QAAA,EAEFnC,MAAM,gBACLL,OAAA,CAACb,SAAS;gBAAC0D,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjC1D,OAAA,CAACd,SAAS;gBAAC2D,SAAS,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA,CAACf,eAAe;QAAAuD,QAAA,EACbnC,MAAM,iBACLL,OAAA,CAAChB,MAAM,CAAC+D,GAAG;UACTL,OAAO,EAAE;YAAE+B,OAAO,EAAE,CAAC;YAAEI,MAAM,EAAE;UAAE,CAAE;UACnCjC,OAAO,EAAE;YAAE6B,OAAO,EAAE,CAAC;YAAEI,MAAM,EAAE;UAAO,CAAE;UACxCH,IAAI,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEI,MAAM,EAAE;UAAE,CAAE;UAChChC,SAAS,EAAC,iEAAiE;UAAAL,QAAA,eAE3ExC,OAAA;YAAK6C,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAElCxC,OAAA;cAAK6C,SAAS,EAAC,UAAU;cAAAL,QAAA,gBACvBxC,OAAA;gBAAK6C,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFxC,OAAA,CAACX,mBAAmB;kBAACwD,SAAS,EAAC;gBAAuB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACN1D,OAAA;gBACEqD,IAAI,EAAC,MAAM;gBACXU,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAEvD,WAAY;gBACnBwD,QAAQ,EAAG/C,CAAC,IAAKR,cAAc,CAACQ,CAAC,CAACU,MAAM,CAACoC,KAAK,CAAE;gBAChDnB,SAAS,EAAC;cAA0I;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1D,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAL,QAAA,EACvBP,eAAe,CAAC0B,GAAG,CAAEC,IAAI,iBACxB5D,OAAA,CAAClB,IAAI;gBAEHgE,EAAE,EAAEc,IAAI,CAACzB,IAAK;gBACdiC,OAAO,EAAEA,CAAA,KAAM9D,SAAS,CAAC,KAAK,CAAE;gBAChCuC,SAAS,EAAE,mFACTR,QAAQ,CAACuB,IAAI,CAACzB,IAAI,CAAC,GACf,2CAA2C,GAC3C,iCAAiC,EACpC;gBAAAK,QAAA,gBAEHxC,OAAA,CAAC4D,IAAI,CAACxB,IAAI;kBAACS,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC1D,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAL,QAAA,EAAEoB,IAAI,CAAC1B;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAV3CE,IAAI,CAAC1B,IAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1D,OAAA;cAAK6C,SAAS,EAAC,gEAAgE;cAAAL,QAAA,gBAC7ExC,OAAA;gBAAQ6C,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBAChGxC,OAAA,CAACT,SAAS;kBAACsD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC1D,OAAA;kBAAM6C,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACT1D,OAAA,CAAClB,IAAI;gBAACgE,EAAE,EAAC,UAAU;gBAACD,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBAC5GxC,OAAA,CAACV,QAAQ;kBAACuD,SAAS,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC1D,OAAA;kBAAM6C,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACP1D,OAAA;gBAAK6C,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnDxC,OAAA,CAACH,YAAY;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChB1D,OAAA;kBAAM6C,SAAS,EAAC,uBAAuB;kBAAAL,QAAA,EAAC;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGb1D,OAAA;MAAK6C,SAAS,EAAC;IAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAACtD,EAAA,CAnYID,UAAU;EAAA,QAKGpB,WAAW,EACce,OAAO;AAAA;AAAAgF,EAAA,GAN7C3E,UAAU;AAqYhB,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}