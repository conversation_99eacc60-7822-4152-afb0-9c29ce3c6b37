{"ast": null, "code": "import { getScrollParents as $a40c673dc9f6d9c7$export$94ed1c92c7beeb22 } from \"./getScrollParents.mjs\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $2f04cbc44ee30ce0$export$53a0910f038337bd(scrollView, element) {\n  let offsetX = $2f04cbc44ee30ce0$var$relativeOffset(scrollView, element, 'left');\n  let offsetY = $2f04cbc44ee30ce0$var$relativeOffset(scrollView, element, 'top');\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n  let x = scrollView.scrollLeft;\n  let y = scrollView.scrollTop;\n  // Account for top/left border offsetting the scroll top/Left + scroll padding\n  let {\n    borderTopWidth: borderTopWidth,\n    borderLeftWidth: borderLeftWidth,\n    scrollPaddingTop: scrollPaddingTop,\n    scrollPaddingRight: scrollPaddingRight,\n    scrollPaddingBottom: scrollPaddingBottom,\n    scrollPaddingLeft: scrollPaddingLeft\n  } = getComputedStyle(scrollView);\n  let borderAdjustedX = x + parseInt(borderLeftWidth, 10);\n  let borderAdjustedY = y + parseInt(borderTopWidth, 10);\n  // Ignore end/bottom border via clientHeight/Width instead of offsetHeight/Width\n  let maxX = borderAdjustedX + scrollView.clientWidth;\n  let maxY = borderAdjustedY + scrollView.clientHeight;\n  // Get scroll padding values as pixels - defaults to 0 if no scroll padding\n  // is used.\n  let scrollPaddingTopNumber = parseInt(scrollPaddingTop, 10) || 0;\n  let scrollPaddingBottomNumber = parseInt(scrollPaddingBottom, 10) || 0;\n  let scrollPaddingRightNumber = parseInt(scrollPaddingRight, 10) || 0;\n  let scrollPaddingLeftNumber = parseInt(scrollPaddingLeft, 10) || 0;\n  if (offsetX <= x + scrollPaddingLeftNumber) x = offsetX - parseInt(borderLeftWidth, 10) - scrollPaddingLeftNumber;else if (offsetX + width > maxX - scrollPaddingRightNumber) x += offsetX + width - maxX + scrollPaddingRightNumber;\n  if (offsetY <= borderAdjustedY + scrollPaddingTopNumber) y = offsetY - parseInt(borderTopWidth, 10) - scrollPaddingTopNumber;else if (offsetY + height > maxY - scrollPaddingBottomNumber) y += offsetY + height - maxY + scrollPaddingBottomNumber;\n  scrollView.scrollLeft = x;\n  scrollView.scrollTop = y;\n}\n/**\n * Computes the offset left or top from child to ancestor by accumulating\n * offsetLeft or offsetTop through intervening offsetParents.\n */\nfunction $2f04cbc44ee30ce0$var$relativeOffset(ancestor, child, axis) {\n  const prop = axis === 'left' ? 'offsetLeft' : 'offsetTop';\n  let sum = 0;\n  while (child.offsetParent) {\n    sum += child[prop];\n    if (child.offsetParent === ancestor) break;else if (child.offsetParent.contains(ancestor)) {\n      // If the ancestor is not `position:relative`, then we stop at\n      // _its_ offset parent, and we subtract off _its_ offset, so that\n      // we end up with the proper offset from child to ancestor.\n      sum -= ancestor[prop];\n      break;\n    }\n    child = child.offsetParent;\n  }\n  return sum;\n}\nfunction $2f04cbc44ee30ce0$export$c826860796309d1b(targetElement, opts) {\n  if (targetElement && document.contains(targetElement)) {\n    let root = document.scrollingElement || document.documentElement;\n    let isScrollPrevented = window.getComputedStyle(root).overflow === 'hidden';\n    // If scrolling is not currently prevented then we aren’t in a overlay nor is a overlay open, just use element.scrollIntoView to bring the element into view\n    if (!isScrollPrevented) {\n      var\n      // use scrollIntoView({block: 'nearest'}) instead of .focus to check if the element is fully in view or not since .focus()\n      // won't cause a scroll if the element is already focused and doesn't behave consistently when an element is partially out of view horizontally vs vertically\n      _targetElement_scrollIntoView;\n      let {\n        left: originalLeft,\n        top: originalTop\n      } = targetElement.getBoundingClientRect();\n      targetElement === null || targetElement === void 0 ? void 0 : (_targetElement_scrollIntoView = targetElement.scrollIntoView) === null || _targetElement_scrollIntoView === void 0 ? void 0 : _targetElement_scrollIntoView.call(targetElement, {\n        block: 'nearest'\n      });\n      let {\n        left: newLeft,\n        top: newTop\n      } = targetElement.getBoundingClientRect();\n      // Account for sub pixel differences from rounding\n      if (Math.abs(originalLeft - newLeft) > 1 || Math.abs(originalTop - newTop) > 1) {\n        var _opts_containingElement_scrollIntoView, _opts_containingElement, _targetElement_scrollIntoView1;\n        opts === null || opts === void 0 ? void 0 : (_opts_containingElement = opts.containingElement) === null || _opts_containingElement === void 0 ? void 0 : (_opts_containingElement_scrollIntoView = _opts_containingElement.scrollIntoView) === null || _opts_containingElement_scrollIntoView === void 0 ? void 0 : _opts_containingElement_scrollIntoView.call(_opts_containingElement, {\n          block: 'center',\n          inline: 'center'\n        });\n        (_targetElement_scrollIntoView1 = targetElement.scrollIntoView) === null || _targetElement_scrollIntoView1 === void 0 ? void 0 : _targetElement_scrollIntoView1.call(targetElement, {\n          block: 'nearest'\n        });\n      }\n    } else {\n      let scrollParents = (0, $a40c673dc9f6d9c7$export$94ed1c92c7beeb22)(targetElement);\n      // If scrolling is prevented, we don't want to scroll the body since it might move the overlay partially offscreen and the user can't scroll it back into view.\n      for (let scrollParent of scrollParents) $2f04cbc44ee30ce0$export$53a0910f038337bd(scrollParent, targetElement);\n    }\n  }\n}\nexport { $2f04cbc44ee30ce0$export$53a0910f038337bd as scrollIntoView, $2f04cbc44ee30ce0$export$c826860796309d1b as scrollIntoViewport };", "map": {"version": 3, "names": ["$2f04cbc44ee30ce0$export$53a0910f038337bd", "scrollView", "element", "offsetX", "$2f04cbc44ee30ce0$var$relativeOffset", "offsetY", "width", "offsetWidth", "height", "offsetHeight", "x", "scrollLeft", "y", "scrollTop", "borderTopWidth", "borderLeftWidth", "scrollPaddingTop", "scrollPaddingRight", "scrollPaddingBottom", "scrollPaddingLeft", "getComputedStyle", "borderAdjustedX", "parseInt", "borderAdjustedY", "maxX", "clientWidth", "maxY", "clientHeight", "scrollPaddingTopNumber", "scrollPaddingBottomNumber", "scrollPaddingRightNumber", "scrollPaddingLeftNumber", "ancestor", "child", "axis", "prop", "sum", "offsetParent", "contains", "$2f04cbc44ee30ce0$export$c826860796309d1b", "targetElement", "opts", "document", "root", "scrollingElement", "documentElement", "isScrollPrevented", "window", "overflow", "_targetElement_scrollIntoView", "left", "originalLeft", "top", "originalTop", "getBoundingClientRect", "scrollIntoView", "call", "block", "newLeft", "newTop", "Math", "abs", "_opts_containingElement_scrollIntoView", "_opts_containingElement", "_targetElement_scrollIntoView1", "containingElement", "inline", "scrollParents", "$a40c673dc9f6d9c7$export$94ed1c92c7beeb22", "scrollParent"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\scrollIntoView.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getScrollParents} from './getScrollParents';\n\ninterface ScrollIntoViewportOpts {\n  /** The optional containing element of the target to be centered in the viewport. */\n  containingElement?: Element | null\n}\n\n/**\n * Scrolls `scrollView` so that `element` is visible.\n * Similar to `element.scrollIntoView({block: 'nearest'})` (not supported in Edge),\n * but doesn't affect parents above `scrollView`.\n */\nexport function scrollIntoView(scrollView: HTMLElement, element: HTMLElement): void {\n  let offsetX = relativeOffset(scrollView, element, 'left');\n  let offsetY = relativeOffset(scrollView, element, 'top');\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n  let x = scrollView.scrollLeft;\n  let y = scrollView.scrollTop;\n\n  // Account for top/left border offsetting the scroll top/Left + scroll padding\n  let {\n    borderTopWidth,\n    borderLeftWidth,\n    scrollPaddingTop,\n    scrollPaddingRight,\n    scrollPaddingBottom,\n    scrollPaddingLeft\n  } = getComputedStyle(scrollView);\n\n  let borderAdjustedX = x + parseInt(borderLeftWidth, 10);\n  let borderAdjustedY = y + parseInt(borderTopWidth, 10);\n  // Ignore end/bottom border via clientHeight/Width instead of offsetHeight/Width\n  let maxX = borderAdjustedX + scrollView.clientWidth;\n  let maxY = borderAdjustedY + scrollView.clientHeight;\n\n  // Get scroll padding values as pixels - defaults to 0 if no scroll padding\n  // is used.\n  let scrollPaddingTopNumber = parseInt(scrollPaddingTop, 10) || 0;\n  let scrollPaddingBottomNumber = parseInt(scrollPaddingBottom, 10) || 0;\n  let scrollPaddingRightNumber = parseInt(scrollPaddingRight, 10) || 0;\n  let scrollPaddingLeftNumber = parseInt(scrollPaddingLeft, 10) || 0;\n\n  if (offsetX <= x + scrollPaddingLeftNumber) {\n    x = offsetX - parseInt(borderLeftWidth, 10) - scrollPaddingLeftNumber;\n  } else if (offsetX + width > maxX - scrollPaddingRightNumber) {\n    x += offsetX + width - maxX + scrollPaddingRightNumber;\n  }\n  if (offsetY <= borderAdjustedY + scrollPaddingTopNumber) {\n    y = offsetY - parseInt(borderTopWidth, 10) - scrollPaddingTopNumber;\n  } else if (offsetY + height > maxY - scrollPaddingBottomNumber) {\n    y += offsetY + height - maxY + scrollPaddingBottomNumber;\n  }\n\n  scrollView.scrollLeft = x;\n  scrollView.scrollTop = y;\n}\n\n/**\n * Computes the offset left or top from child to ancestor by accumulating\n * offsetLeft or offsetTop through intervening offsetParents.\n */\nfunction relativeOffset(ancestor: HTMLElement, child: HTMLElement, axis: 'left'|'top') {\n  const prop = axis === 'left' ? 'offsetLeft' : 'offsetTop';\n  let sum = 0;\n  while (child.offsetParent) {\n    sum += child[prop];\n    if (child.offsetParent === ancestor) {\n      // Stop once we have found the ancestor we are interested in.\n      break;\n    } else if (child.offsetParent.contains(ancestor)) {\n      // If the ancestor is not `position:relative`, then we stop at\n      // _its_ offset parent, and we subtract off _its_ offset, so that\n      // we end up with the proper offset from child to ancestor.\n      sum -= ancestor[prop];\n      break;\n    }\n    child = child.offsetParent as HTMLElement;\n  }\n  return sum;\n}\n\n/**\n * Scrolls the `targetElement` so it is visible in the viewport. Accepts an optional `opts.containingElement`\n * that will be centered in the viewport prior to scrolling the targetElement into view. If scrolling is prevented on\n * the body (e.g. targetElement is in a popover), this will only scroll the scroll parents of the targetElement up to but not including the body itself.\n */\nexport function scrollIntoViewport(targetElement: Element | null, opts?: ScrollIntoViewportOpts): void {\n  if (targetElement && document.contains(targetElement)) {\n    let root = document.scrollingElement || document.documentElement;\n    let isScrollPrevented = window.getComputedStyle(root).overflow === 'hidden';\n    // If scrolling is not currently prevented then we aren’t in a overlay nor is a overlay open, just use element.scrollIntoView to bring the element into view\n    if (!isScrollPrevented) {\n      let {left: originalLeft, top: originalTop} = targetElement.getBoundingClientRect();\n\n      // use scrollIntoView({block: 'nearest'}) instead of .focus to check if the element is fully in view or not since .focus()\n      // won't cause a scroll if the element is already focused and doesn't behave consistently when an element is partially out of view horizontally vs vertically\n      targetElement?.scrollIntoView?.({block: 'nearest'});\n      let {left: newLeft, top: newTop} = targetElement.getBoundingClientRect();\n      // Account for sub pixel differences from rounding\n      if ((Math.abs(originalLeft - newLeft) > 1) || (Math.abs(originalTop - newTop) > 1)) {\n        opts?.containingElement?.scrollIntoView?.({block: 'center', inline: 'center'});\n        targetElement.scrollIntoView?.({block: 'nearest'});\n      }\n    } else {\n      let scrollParents = getScrollParents(targetElement);\n      // If scrolling is prevented, we don't want to scroll the body since it might move the overlay partially offscreen and the user can't scroll it back into view.\n      for (let scrollParent of scrollParents) {\n        scrollIntoView(scrollParent as HTMLElement, targetElement as HTMLElement);\n      }\n    }\n  }\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAwBO,SAASA,0CAAeC,UAAuB,EAAEC,OAAoB;EAC1E,IAAIC,OAAA,GAAUC,oCAAA,CAAeH,UAAA,EAAYC,OAAA,EAAS;EAClD,IAAIG,OAAA,GAAUD,oCAAA,CAAeH,UAAA,EAAYC,OAAA,EAAS;EAClD,IAAII,KAAA,GAAQJ,OAAA,CAAQK,WAAW;EAC/B,IAAIC,MAAA,GAASN,OAAA,CAAQO,YAAY;EACjC,IAAIC,CAAA,GAAIT,UAAA,CAAWU,UAAU;EAC7B,IAAIC,CAAA,GAAIX,UAAA,CAAWY,SAAS;EAE5B;EACA,IAAI;IAAAC,cAAA,EACFA,cAAc;IAAAC,eAAA,EACdA,eAAe;IAAAC,gBAAA,EACfA,gBAAgB;IAAAC,kBAAA,EAChBA,kBAAkB;IAAAC,mBAAA,EAClBA,mBAAmB;IAAAC,iBAAA,EACnBA;EAAiB,CAClB,GAAGC,gBAAA,CAAiBnB,UAAA;EAErB,IAAIoB,eAAA,GAAkBX,CAAA,GAAIY,QAAA,CAASP,eAAA,EAAiB;EACpD,IAAIQ,eAAA,GAAkBX,CAAA,GAAIU,QAAA,CAASR,cAAA,EAAgB;EACnD;EACA,IAAIU,IAAA,GAAOH,eAAA,GAAkBpB,UAAA,CAAWwB,WAAW;EACnD,IAAIC,IAAA,GAAOH,eAAA,GAAkBtB,UAAA,CAAW0B,YAAY;EAEpD;EACA;EACA,IAAIC,sBAAA,GAAyBN,QAAA,CAASN,gBAAA,EAAkB,OAAO;EAC/D,IAAIa,yBAAA,GAA4BP,QAAA,CAASJ,mBAAA,EAAqB,OAAO;EACrE,IAAIY,wBAAA,GAA2BR,QAAA,CAASL,kBAAA,EAAoB,OAAO;EACnE,IAAIc,uBAAA,GAA0BT,QAAA,CAASH,iBAAA,EAAmB,OAAO;EAEjE,IAAIhB,OAAA,IAAWO,CAAA,GAAIqB,uBAAA,EACjBrB,CAAA,GAAIP,OAAA,GAAUmB,QAAA,CAASP,eAAA,EAAiB,MAAMgB,uBAAA,MACzC,IAAI5B,OAAA,GAAUG,KAAA,GAAQkB,IAAA,GAAOM,wBAAA,EAClCpB,CAAA,IAAKP,OAAA,GAAUG,KAAA,GAAQkB,IAAA,GAAOM,wBAAA;EAEhC,IAAIzB,OAAA,IAAWkB,eAAA,GAAkBK,sBAAA,EAC/BhB,CAAA,GAAIP,OAAA,GAAUiB,QAAA,CAASR,cAAA,EAAgB,MAAMc,sBAAA,MACxC,IAAIvB,OAAA,GAAUG,MAAA,GAASkB,IAAA,GAAOG,yBAAA,EACnCjB,CAAA,IAAKP,OAAA,GAAUG,MAAA,GAASkB,IAAA,GAAOG,yBAAA;EAGjC5B,UAAA,CAAWU,UAAU,GAAGD,CAAA;EACxBT,UAAA,CAAWY,SAAS,GAAGD,CAAA;AACzB;AAEA;;;;AAIA,SAASR,qCAAe4B,QAAqB,EAAEC,KAAkB,EAAEC,IAAkB;EACnF,MAAMC,IAAA,GAAOD,IAAA,KAAS,SAAS,eAAe;EAC9C,IAAIE,GAAA,GAAM;EACV,OAAOH,KAAA,CAAMI,YAAY,EAAE;IACzBD,GAAA,IAAOH,KAAK,CAACE,IAAA,CAAK;IAClB,IAAIF,KAAA,CAAMI,YAAY,KAAKL,QAAA,EAEzB,WACK,IAAIC,KAAA,CAAMI,YAAY,CAACC,QAAQ,CAACN,QAAA,GAAW;MAChD;MACA;MACA;MACAI,GAAA,IAAOJ,QAAQ,CAACG,IAAA,CAAK;MACrB;IACF;IACAF,KAAA,GAAQA,KAAA,CAAMI,YAAY;EAC5B;EACA,OAAOD,GAAA;AACT;AAOO,SAASG,0CAAmBC,aAA6B,EAAEC,IAA6B;EAC7F,IAAID,aAAA,IAAiBE,QAAA,CAASJ,QAAQ,CAACE,aAAA,GAAgB;IACrD,IAAIG,IAAA,GAAOD,QAAA,CAASE,gBAAgB,IAAIF,QAAA,CAASG,eAAe;IAChE,IAAIC,iBAAA,GAAoBC,MAAA,CAAO3B,gBAAgB,CAACuB,IAAA,EAAMK,QAAQ,KAAK;IACnE;IACA,IAAI,CAACF,iBAAA,EAAmB;;MAGtB;MACA;MACAG,6BAAA;MAJA,IAAI;QAACC,IAAA,EAAMC,YAAY;QAAEC,GAAA,EAAKC;MAAW,CAAC,GAAGb,aAAA,CAAcc,qBAAqB;MAIhFd,aAAA,aAAAA,aAAA,wBAAAS,6BAAA,GAAAT,aAAA,CAAee,cAAc,cAA7BN,6BAAA,uBAAAA,6BAAA,CAAAO,IAAA,CAAAhB,aAAA,EAAgC;QAACiB,KAAA,EAAO;MAAS;MACjD,IAAI;QAACP,IAAA,EAAMQ,OAAO;QAAEN,GAAA,EAAKO;MAAM,CAAC,GAAGnB,aAAA,CAAcc,qBAAqB;MACtE;MACA,IAAIM,IAAC,CAAKC,GAAG,CAACV,YAAA,GAAeO,OAAA,IAAW,KAAOE,IAAA,CAAKC,GAAG,CAACR,WAAA,GAAcM,MAAA,IAAU,GAAI;YAClFG,sCAAA,EAAAC,uBAAA,EACAC,8BAAA;QADAvB,IAAA,aAAAA,IAAA,wBAAAsB,uBAAA,GAAAtB,IAAA,CAAMwB,iBAAiB,cAAvBF,uBAAA,wBAAAD,sCAAA,GAAAC,uBAAA,CAAyBR,cAAc,cAAvCO,sCAAA,uBAAAA,sCAAA,CAAAN,IAAA,CAAAO,uBAAA,EAA0C;UAACN,KAAA,EAAO;UAAUS,MAAA,EAAQ;QAAQ;SAC5EF,8BAAA,GAAAxB,aAAA,CAAce,cAAc,cAA5BS,8BAAA,uBAAAA,8BAAA,CAAAR,IAAA,CAAAhB,aAAA,EAA+B;UAACiB,KAAA,EAAO;QAAS;MAClD;IACF,OAAO;MACL,IAAIU,aAAA,GAAgB,IAAAC,yCAAe,EAAE5B,aAAA;MACrC;MACA,KAAK,IAAI6B,YAAA,IAAgBF,aAAA,EACvBnE,yCAAA,CAAeqE,YAAA,EAA6B7B,aAAA;IAEhD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}