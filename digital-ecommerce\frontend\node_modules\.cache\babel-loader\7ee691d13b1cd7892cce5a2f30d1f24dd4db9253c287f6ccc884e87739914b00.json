{"ast": null, "code": "import * as n from './dom.js';\nfunction s(l) {\n  let e = l.parentElement,\n    t = null;\n  for (; e && !n.isHTMLFieldSetElement(e);) n.isHTMLLegendElement(e) && (t = e), e = e.parentElement;\n  let i = (e == null ? void 0 : e.get<PERSON>ttribute(\"disabled\")) === \"\";\n  return i && r(t) ? !1 : i;\n}\nfunction r(l) {\n  if (!l) return !1;\n  let e = l.previousElementSibling;\n  for (; e !== null;) {\n    if (n.isHTMLLegendElement(e)) return !1;\n    e = e.previousElementSibling;\n  }\n  return !0;\n}\nexport { s as isDisabledReactIssue7711 };", "map": {"version": 3, "names": ["n", "s", "l", "e", "parentElement", "t", "isHTMLFieldSetElement", "isHTMLLegendElement", "i", "getAttribute", "r", "previousElementSibling", "isDisabledReactIssue7711"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/bugs.js"], "sourcesContent": ["import*as n from'./dom.js';function s(l){let e=l.parentElement,t=null;for(;e&&!n.isHTMLFieldSetElement(e);)n.isHTMLLegendElement(e)&&(t=e),e=e.parentElement;let i=(e==null?void 0:e.get<PERSON>ttribute(\"disabled\"))===\"\";return i&&r(t)?!1:i}function r(l){if(!l)return!1;let e=l.previousElementSibling;for(;e!==null;){if(n.isHTMLLegendElement(e))return!1;e=e.previousElementSibling}return!0}export{s as isDisabledReactIssue7711};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,UAAU;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,aAAa;IAACC,CAAC,GAAC,IAAI;EAAC,OAAKF,CAAC,IAAE,CAACH,CAAC,CAACM,qBAAqB,CAACH,CAAC,CAAC,GAAEH,CAAC,CAACO,mBAAmB,CAACJ,CAAC,CAAC,KAAGE,CAAC,GAACF,CAAC,CAAC,EAACA,CAAC,GAACA,CAAC,CAACC,aAAa;EAAC,IAAII,CAAC,GAAC,CAACL,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACM,YAAY,CAAC,UAAU,CAAC,MAAI,EAAE;EAAC,OAAOD,CAAC,IAAEE,CAAC,CAACL,CAAC,CAAC,GAAC,CAAC,CAAC,GAACG,CAAC;AAAA;AAAC,SAASE,CAACA,CAACR,CAAC,EAAC;EAAC,IAAG,CAACA,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACS,sBAAsB;EAAC,OAAKR,CAAC,KAAG,IAAI,GAAE;IAAC,IAAGH,CAAC,CAACO,mBAAmB,CAACJ,CAAC,CAAC,EAAC,OAAM,CAAC,CAAC;IAACA,CAAC,GAACA,CAAC,CAACQ,sBAAsB;EAAA;EAAC,OAAM,CAAC,CAAC;AAAA;AAAC,SAAOV,CAAC,IAAIW,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}