{"ast": null, "code": "import { getOwnerDocument as $hpDQO$getOwnerDocument, getActiveElement as $hpDQO$getActiveElement } from \"@react-aria/utils\";\nfunction $55f9b1ae81f22853$export$76e4e37e5339496d(to) {\n  let from = $55f9b1ae81f22853$export$759df0d867455a91((0, $hpDQO$getOwnerDocument)(to));\n  if (from !== to) {\n    if (from) $55f9b1ae81f22853$export$6c5dc7e81d2cc29a(from, to);\n    if (to) $55f9b1ae81f22853$export$2b35b76d2e30e129(to, from);\n  }\n}\nfunction $55f9b1ae81f22853$export$6c5dc7e81d2cc29a(from, to) {\n  from.dispatchEvent(new FocusEvent('blur', {\n    relatedTarget: to\n  }));\n  from.dispatchEvent(new FocusEvent('focusout', {\n    bubbles: true,\n    relatedTarget: to\n  }));\n}\nfunction $55f9b1ae81f22853$export$2b35b76d2e30e129(to, from) {\n  to.dispatchEvent(new FocusEvent('focus', {\n    relatedTarget: from\n  }));\n  to.dispatchEvent(new FocusEvent('focusin', {\n    bubbles: true,\n    relatedTarget: from\n  }));\n}\nfunction $55f9b1ae81f22853$export$759df0d867455a91(document) {\n  let activeElement = (0, $hpDQO$getActiveElement)(document);\n  let activeDescendant = activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute('aria-activedescendant');\n  if (activeDescendant) return document.getElementById(activeDescendant) || activeElement;\n  return activeElement;\n}\nexport { $55f9b1ae81f22853$export$76e4e37e5339496d as moveVirtualFocus, $55f9b1ae81f22853$export$759df0d867455a91 as getVirtuallyFocusedElement, $55f9b1ae81f22853$export$6c5dc7e81d2cc29a as dispatchVirtualBlur, $55f9b1ae81f22853$export$2b35b76d2e30e129 as dispatchVirtualFocus };", "map": {"version": 3, "names": ["$55f9b1ae81f22853$export$76e4e37e5339496d", "to", "from", "$55f9b1ae81f22853$export$759df0d867455a91", "$hpDQO$getOwnerDocument", "$55f9b1ae81f22853$export$6c5dc7e81d2cc29a", "$55f9b1ae81f22853$export$2b35b76d2e30e129", "dispatchEvent", "FocusEvent", "relatedTarget", "bubbles", "document", "activeElement", "$hpDQO$getActiveElement", "activeDescendant", "getAttribute", "getElementById"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\focus\\dist\\packages\\@react-aria\\focus\\src\\virtualFocus.ts"], "sourcesContent": ["import {getActiveElement, getOwnerDocument} from '@react-aria/utils';\n\nexport function moveVirtualFocus(to: Element | null): void {\n  let from = getVirtuallyFocusedElement(getOwnerDocument(to));\n  if (from !== to) {\n    if (from) {\n      dispatchVirtualBlur(from, to);\n    }\n    if (to) {\n      dispatchVirtualFocus(to, from);\n    }\n  }\n}\n\nexport function dispatchVirtualBlur(from: Element, to: Element | null): void {\n  from.dispatchEvent(new FocusEvent('blur', {relatedTarget: to}));\n  from.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: to}));\n}\n\nexport function dispatchVirtualFocus(to: Element, from: Element | null): void {\n  to.dispatchEvent(new FocusEvent('focus', {relatedTarget: from}));\n  to.dispatchEvent(new FocusEvent('focusin', {bubbles: true, relatedTarget: from}));\n}\n\nexport function getVirtuallyFocusedElement(document: Document): Element | null {\n  let activeElement = getActiveElement(document);\n  let activeDescendant = activeElement?.getAttribute('aria-activedescendant');\n  if (activeDescendant) {\n    return document.getElementById(activeDescendant) || activeElement;\n  }\n\n  return activeElement;\n}\n"], "mappings": ";AAEO,SAASA,0CAAiBC,EAAkB;EACjD,IAAIC,IAAA,GAAOC,yCAAA,CAA2B,IAAAC,uBAAe,EAAEH,EAAA;EACvD,IAAIC,IAAA,KAASD,EAAA,EAAI;IACf,IAAIC,IAAA,EACFG,yCAAA,CAAoBH,IAAA,EAAMD,EAAA;IAE5B,IAAIA,EAAA,EACFK,yCAAA,CAAqBL,EAAA,EAAIC,IAAA;EAE7B;AACF;AAEO,SAASG,0CAAoBH,IAAa,EAAED,EAAkB;EACnEC,IAAA,CAAKK,aAAa,CAAC,IAAIC,UAAA,CAAW,QAAQ;IAACC,aAAA,EAAeR;EAAE;EAC5DC,IAAA,CAAKK,aAAa,CAAC,IAAIC,UAAA,CAAW,YAAY;IAACE,OAAA,EAAS;IAAMD,aAAA,EAAeR;EAAE;AACjF;AAEO,SAASK,0CAAqBL,EAAW,EAAEC,IAAoB;EACpED,EAAA,CAAGM,aAAa,CAAC,IAAIC,UAAA,CAAW,SAAS;IAACC,aAAA,EAAeP;EAAI;EAC7DD,EAAA,CAAGM,aAAa,CAAC,IAAIC,UAAA,CAAW,WAAW;IAACE,OAAA,EAAS;IAAMD,aAAA,EAAeP;EAAI;AAChF;AAEO,SAASC,0CAA2BQ,QAAkB;EAC3D,IAAIC,aAAA,GAAgB,IAAAC,uBAAe,EAAEF,QAAA;EACrC,IAAIG,gBAAA,GAAmBF,aAAA,aAAAA,aAAA,uBAAAA,aAAA,CAAeG,YAAY,CAAC;EACnD,IAAID,gBAAA,EACF,OAAOH,QAAA,CAASK,cAAc,CAACF,gBAAA,KAAqBF,aAAA;EAGtD,OAAOA,aAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}