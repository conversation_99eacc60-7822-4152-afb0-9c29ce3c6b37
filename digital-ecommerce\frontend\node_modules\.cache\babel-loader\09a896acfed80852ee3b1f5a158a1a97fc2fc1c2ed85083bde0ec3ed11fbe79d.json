{"ast": null, "code": "var T, b;\nimport { useRef as c, useState as S } from \"react\";\nimport { disposables as m } from '../utils/disposables.js';\nimport { useDisposables as g } from './use-disposables.js';\nimport { useFlags as y } from './use-flags.js';\nimport { useIsoMorphicEffect as A } from './use-iso-morphic-effect.js';\ntypeof process != \"undefined\" && typeof globalThis != \"undefined\" && typeof Element != \"undefined\" && ((T = process == null ? void 0 : process.env) == null ? void 0 : T[\"NODE_ENV\"]) === \"test\" && typeof ((b = Element == null ? void 0 : Element.prototype) == null ? void 0 : b.getAnimations) == \"undefined\" && (Element.prototype.getAnimations = function () {\n  return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\", \"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\", \"\", \"Example usage:\", \"```js\", \"import { mockAnimationsApi } from 'jsdom-testing-mocks'\", \"mockAnimationsApi()\", \"```\"].join(\"\\n\")), [];\n});\nvar L = (r => (r[r.None = 0] = \"None\", r[r.Closed = 1] = \"Closed\", r[r.Enter = 2] = \"Enter\", r[r.Leave = 4] = \"Leave\", r))(L || {});\nfunction R(t) {\n  let n = {};\n  for (let e in t) t[e] === !0 && (n[\"data-\".concat(e)] = \"\");\n  return n;\n}\nfunction x(t, n, e, i) {\n  let [r, o] = S(e),\n    {\n      hasFlag: s,\n      addFlag: a,\n      removeFlag: l\n    } = y(t && r ? 3 : 0),\n    u = c(!1),\n    f = c(!1),\n    E = g();\n  return A(() => {\n    var d;\n    if (t) {\n      if (e && o(!0), !n) {\n        e && a(3);\n        return;\n      }\n      return (d = i == null ? void 0 : i.start) == null || d.call(i, e), C(n, {\n        inFlight: u,\n        prepare() {\n          f.current ? f.current = !1 : f.current = u.current, u.current = !0, !f.current && (e ? (a(3), l(4)) : (a(4), l(2)));\n        },\n        run() {\n          f.current ? e ? (l(3), a(4)) : (l(4), a(3)) : e ? l(1) : a(1);\n        },\n        done() {\n          var p;\n          f.current && typeof n.getAnimations == \"function\" && n.getAnimations().length > 0 || (u.current = !1, l(7), e || o(!1), (p = i == null ? void 0 : i.end) == null || p.call(i, e));\n        }\n      });\n    }\n  }, [t, e, n, E]), t ? [r, {\n    closed: s(1),\n    enter: s(2),\n    leave: s(4),\n    transition: s(2) || s(4)\n  }] : [e, {\n    closed: void 0,\n    enter: void 0,\n    leave: void 0,\n    transition: void 0\n  }];\n}\nfunction C(t, _ref) {\n  let {\n    prepare: n,\n    run: e,\n    done: i,\n    inFlight: r\n  } = _ref;\n  let o = m();\n  return j(t, {\n    prepare: n,\n    inFlight: r\n  }), o.nextFrame(() => {\n    e(), o.requestAnimationFrame(() => {\n      o.add(M(t, i));\n    });\n  }), o.dispose;\n}\nfunction M(t, n) {\n  var o, s;\n  let e = m();\n  if (!t) return e.dispose;\n  let i = !1;\n  e.add(() => {\n    i = !0;\n  });\n  let r = (s = (o = t.getAnimations) == null ? void 0 : o.call(t).filter(a => a instanceof CSSTransition)) != null ? s : [];\n  return r.length === 0 ? (n(), e.dispose) : (Promise.allSettled(r.map(a => a.finished)).then(() => {\n    i || n();\n  }), e.dispose);\n}\nfunction j(t, _ref2) {\n  let {\n    inFlight: n,\n    prepare: e\n  } = _ref2;\n  if (n != null && n.current) {\n    e();\n    return;\n  }\n  let i = t.style.transition;\n  t.style.transition = \"none\", e(), t.offsetHeight, t.style.transition = i;\n}\nexport { R as transitionDataAttributes, x as useTransition };", "map": {"version": 3, "names": ["T", "b", "useRef", "c", "useState", "S", "disposables", "m", "useDisposables", "g", "useFlags", "y", "useIsoMorphicEffect", "A", "process", "globalThis", "Element", "env", "prototype", "getAnimations", "console", "warn", "join", "L", "r", "None", "Closed", "Enter", "Leave", "R", "t", "n", "e", "concat", "x", "i", "o", "hasFlag", "s", "addFlag", "a", "removeFlag", "l", "u", "f", "E", "d", "start", "call", "C", "inFlight", "prepare", "current", "run", "done", "p", "length", "end", "closed", "enter", "leave", "transition", "_ref", "j", "next<PERSON><PERSON><PERSON>", "requestAnimationFrame", "add", "M", "dispose", "filter", "CSSTransition", "Promise", "allSettled", "map", "finished", "then", "_ref2", "style", "offsetHeight", "transitionDataAttributes", "useTransition"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["var T,b;import{useRef as c,useState as S}from\"react\";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!=\"undefined\"&&typeof globalThis!=\"undefined\"&&typeof Element!=\"undefined\"&&((T=process==null?void 0:process.env)==null?void 0:T[\"NODE_ENV\"])===\"test\"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)==\"undefined\"&&(Element.prototype.getAnimations=function(){return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\",\"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\",\"\",\"Example usage:\",\"```js\",\"import { mockAnimationsApi } from 'jsdom-testing-mocks'\",\"mockAnimationsApi()\",\"```\"].join(`\n`)),[]});var L=(r=>(r[r.None=0]=\"None\",r[r.Closed=1]=\"Closed\",r[r.Enter=2]=\"Enter\",r[r.Leave=4]=\"Leave\",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]=\"\");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations==\"function\"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition=\"none\",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};\n"], "mappings": "AAAA,IAAIA,CAAC,EAACC,CAAC;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,UAAU,IAAE,WAAW,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,CAAC,CAAChB,CAAC,GAACc,OAAO,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,OAAO,CAACG,GAAG,KAAG,IAAI,GAAC,KAAK,CAAC,GAACjB,CAAC,CAAC,UAAU,CAAC,MAAI,MAAM,IAAE,QAAO,CAACC,CAAC,GAACe,OAAO,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,OAAO,CAACE,SAAS,KAAG,IAAI,GAAC,KAAK,CAAC,GAACjB,CAAC,CAACkB,aAAa,CAAC,IAAE,WAAW,KAAGH,OAAO,CAACE,SAAS,CAACC,aAAa,GAAC,YAAU;EAAC,OAAOC,OAAO,CAACC,IAAI,CAAC,CAAC,8EAA8E,EAAC,yFAAyF,EAAC,EAAE,EAAC,gBAAgB,EAAC,OAAO,EAAC,yDAAyD,EAAC,qBAAqB,EAAC,KAAK,CAAC,CAACC,IAAI,KACj3B,CAAC,CAAC,EAAC,EAAE;AAAA,CAAC,CAAC;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAACA,CAAC,CAACG,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACH,CAAC,CAACA,CAAC,CAACI,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACJ,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASM,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC,CAAC,CAAC;EAAC,KAAI,IAAIC,CAAC,IAAIF,CAAC,EAACA,CAAC,CAACE,CAAC,CAAC,KAAG,CAAC,CAAC,KAAGD,CAAC,SAAAE,MAAA,CAASD,CAAC,EAAG,GAAC,EAAE,CAAC;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASG,CAACA,CAACJ,CAAC,EAACC,CAAC,EAACC,CAAC,EAACG,CAAC,EAAC;EAAC,IAAG,CAACX,CAAC,EAACY,CAAC,CAAC,GAAC/B,CAAC,CAAC2B,CAAC,CAAC;IAAC;MAACK,OAAO,EAACC,CAAC;MAACC,OAAO,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC/B,CAAC,CAACmB,CAAC,IAAEN,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;IAACmB,CAAC,GAACxC,CAAC,CAAC,CAAC,CAAC,CAAC;IAACyC,CAAC,GAACzC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC0C,CAAC,GAACpC,CAAC,CAAC,CAAC;EAAC,OAAOI,CAAC,CAAC,MAAI;IAAC,IAAIiC,CAAC;IAAC,IAAGhB,CAAC,EAAC;MAAC,IAAGE,CAAC,IAAEI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAACL,CAAC,EAAC;QAACC,CAAC,IAAEQ,CAAC,CAAC,CAAC,CAAC;QAAC;MAAM;MAAC,OAAM,CAACM,CAAC,GAACX,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACY,KAAK,KAAG,IAAI,IAAED,CAAC,CAACE,IAAI,CAACb,CAAC,EAACH,CAAC,CAAC,EAACiB,CAAC,CAAClB,CAAC,EAAC;QAACmB,QAAQ,EAACP,CAAC;QAACQ,OAAOA,CAAA,EAAE;UAACP,CAAC,CAACQ,OAAO,GAACR,CAAC,CAACQ,OAAO,GAAC,CAAC,CAAC,GAACR,CAAC,CAACQ,OAAO,GAACT,CAAC,CAACS,OAAO,EAACT,CAAC,CAACS,OAAO,GAAC,CAAC,CAAC,EAAC,CAACR,CAAC,CAACQ,OAAO,KAAGpB,CAAC,IAAEQ,CAAC,CAAC,CAAC,CAAC,EAACE,CAAC,CAAC,CAAC,CAAC,KAAGF,CAAC,CAAC,CAAC,CAAC,EAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC;QAACW,GAAGA,CAAA,EAAE;UAACT,CAAC,CAACQ,OAAO,GAACpB,CAAC,IAAEU,CAAC,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC,CAAC,CAAC,KAAGE,CAAC,CAAC,CAAC,CAAC,EAACF,CAAC,CAAC,CAAC,CAAC,CAAC,GAACR,CAAC,GAACU,CAAC,CAAC,CAAC,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC;QAACc,IAAIA,CAAA,EAAE;UAAC,IAAIC,CAAC;UAACX,CAAC,CAACQ,OAAO,IAAE,OAAOrB,CAAC,CAACZ,aAAa,IAAE,UAAU,IAAEY,CAAC,CAACZ,aAAa,CAAC,CAAC,CAACqC,MAAM,GAAC,CAAC,KAAGb,CAAC,CAACS,OAAO,GAAC,CAAC,CAAC,EAACV,CAAC,CAAC,CAAC,CAAC,EAACV,CAAC,IAAEI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAACmB,CAAC,GAACpB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACsB,GAAG,KAAG,IAAI,IAAEF,CAAC,CAACP,IAAI,CAACb,CAAC,EAACH,CAAC,CAAC,CAAC;QAAA;MAAC,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,EAACD,CAAC,EAACc,CAAC,CAAC,CAAC,EAACf,CAAC,GAAC,CAACN,CAAC,EAAC;IAACkC,MAAM,EAACpB,CAAC,CAAC,CAAC,CAAC;IAACqB,KAAK,EAACrB,CAAC,CAAC,CAAC,CAAC;IAACsB,KAAK,EAACtB,CAAC,CAAC,CAAC,CAAC;IAACuB,UAAU,EAACvB,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC,CAAC,CAAC;EAAC,CAAC,CAAC,GAAC,CAACN,CAAC,EAAC;IAAC0B,MAAM,EAAC,KAAK,CAAC;IAACC,KAAK,EAAC,KAAK,CAAC;IAACC,KAAK,EAAC,KAAK,CAAC;IAACC,UAAU,EAAC,KAAK;EAAC,CAAC,CAAC;AAAA;AAAC,SAASZ,CAACA,CAACnB,CAAC,EAAAgC,IAAA,EAAqC;EAAA,IAApC;IAACX,OAAO,EAACpB,CAAC;IAACsB,GAAG,EAACrB,CAAC;IAACsB,IAAI,EAACnB,CAAC;IAACe,QAAQ,EAAC1B;EAAC,CAAC,GAAAsC,IAAA;EAAE,IAAI1B,CAAC,GAAC7B,CAAC,CAAC,CAAC;EAAC,OAAOwD,CAAC,CAACjC,CAAC,EAAC;IAACqB,OAAO,EAACpB,CAAC;IAACmB,QAAQ,EAAC1B;EAAC,CAAC,CAAC,EAACY,CAAC,CAAC4B,SAAS,CAAC,MAAI;IAAChC,CAAC,CAAC,CAAC,EAACI,CAAC,CAAC6B,qBAAqB,CAAC,MAAI;MAAC7B,CAAC,CAAC8B,GAAG,CAACC,CAAC,CAACrC,CAAC,EAACK,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,CAAC,EAACC,CAAC,CAACgC,OAAO;AAAA;AAAC,SAASD,CAACA,CAACrC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIK,CAAC,EAACE,CAAC;EAAC,IAAIN,CAAC,GAACzB,CAAC,CAAC,CAAC;EAAC,IAAG,CAACuB,CAAC,EAAC,OAAOE,CAAC,CAACoC,OAAO;EAAC,IAAIjC,CAAC,GAAC,CAAC,CAAC;EAACH,CAAC,CAACkC,GAAG,CAAC,MAAI;IAAC/B,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,CAAC;EAAC,IAAIX,CAAC,GAAC,CAACc,CAAC,GAAC,CAACF,CAAC,GAACN,CAAC,CAACX,aAAa,KAAG,IAAI,GAAC,KAAK,CAAC,GAACiB,CAAC,CAACY,IAAI,CAAClB,CAAC,CAAC,CAACuC,MAAM,CAAC7B,CAAC,IAAEA,CAAC,YAAY8B,aAAa,CAAC,KAAG,IAAI,GAAChC,CAAC,GAAC,EAAE;EAAC,OAAOd,CAAC,CAACgC,MAAM,KAAG,CAAC,IAAEzB,CAAC,CAAC,CAAC,EAACC,CAAC,CAACoC,OAAO,KAAGG,OAAO,CAACC,UAAU,CAAChD,CAAC,CAACiD,GAAG,CAACjC,CAAC,IAAEA,CAAC,CAACkC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,MAAI;IAACxC,CAAC,IAAEJ,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACC,CAAC,CAACoC,OAAO,CAAC;AAAA;AAAC,SAASL,CAACA,CAACjC,CAAC,EAAA8C,KAAA,EAAwB;EAAA,IAAvB;IAAC1B,QAAQ,EAACnB,CAAC;IAACoB,OAAO,EAACnB;EAAC,CAAC,GAAA4C,KAAA;EAAE,IAAG7C,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACqB,OAAO,EAAC;IAACpB,CAAC,CAAC,CAAC;IAAC;EAAM;EAAC,IAAIG,CAAC,GAACL,CAAC,CAAC+C,KAAK,CAAChB,UAAU;EAAC/B,CAAC,CAAC+C,KAAK,CAAChB,UAAU,GAAC,MAAM,EAAC7B,CAAC,CAAC,CAAC,EAACF,CAAC,CAACgD,YAAY,EAAChD,CAAC,CAAC+C,KAAK,CAAChB,UAAU,GAAC1B,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIkD,wBAAwB,EAAC7C,CAAC,IAAI8C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}