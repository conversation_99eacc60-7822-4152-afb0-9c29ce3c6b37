{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar S = Object.defineProperty;\nvar I = (t, i, e) => i in t ? S(t, i, {\n  enumerable: !0,\n  configurable: !0,\n  writable: !0,\n  value: e\n}) : t[i] = e;\nvar c = (t, i, e) => (I(t, typeof i != \"symbol\" ? i + \"\" : i, e), e);\nimport { Machine as h } from '../../machine.js';\nimport { ActionTypes as R, stackMachines as A } from '../../machines/stack-machine.js';\nimport { Focus as f, calculateActiveIndex as x } from '../../utils/calculate-active-index.js';\nimport { sortByDomNode as E } from '../../utils/focus-management.js';\nimport { match as g } from '../../utils/match.js';\nvar C = (e => (e[e.Open = 0] = \"Open\", e[e.Closed = 1] = \"Closed\", e))(C || {}),\n  M = (e => (e[e.Single = 0] = \"Single\", e[e.Multi = 1] = \"Multi\", e))(M || {}),\n  F = (n => (n[n.Pointer = 0] = \"Pointer\", n[n.Focus = 1] = \"Focus\", n[n.Other = 2] = \"Other\", n))(F || {}),\n  _ = (l => (l[l.OpenCombobox = 0] = \"OpenCombobox\", l[l.CloseCombobox = 1] = \"CloseCombobox\", l[l.GoToOption = 2] = \"GoToOption\", l[l.SetTyping = 3] = \"SetTyping\", l[l.RegisterOption = 4] = \"RegisterOption\", l[l.UnregisterOption = 5] = \"UnregisterOption\", l[l.DefaultToFirstOption = 6] = \"DefaultToFirstOption\", l[l.SetActivationTrigger = 7] = \"SetActivationTrigger\", l[l.UpdateVirtualConfiguration = 8] = \"UpdateVirtualConfiguration\", l[l.SetInputElement = 9] = \"SetInputElement\", l[l.SetButtonElement = 10] = \"SetButtonElement\", l[l.SetOptionsElement = 11] = \"SetOptionsElement\", l))(_ || {});\nfunction T(t) {\n  let i = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : e => e;\n  let e = t.activeOptionIndex !== null ? t.options[t.activeOptionIndex] : null,\n    n = i(t.options.slice()),\n    o = n.length > 0 && n[0].dataRef.current.order !== null ? n.sort((u, a) => u.dataRef.current.order - a.dataRef.current.order) : E(n, u => u.dataRef.current.domRef.current),\n    r = e ? o.indexOf(e) : null;\n  return r === -1 && (r = null), {\n    options: o,\n    activeOptionIndex: r\n  };\n}\nlet D = {\n  [1](t) {\n    var i;\n    return (i = t.dataRef.current) != null && i.disabled || t.comboboxState === 1 ? t : _objectSpread(_objectSpread({}, t), {}, {\n      activeOptionIndex: null,\n      comboboxState: 1,\n      isTyping: !1,\n      activationTrigger: 2,\n      __demoMode: !1\n    });\n  },\n  [0](t) {\n    var i, e;\n    if ((i = t.dataRef.current) != null && i.disabled || t.comboboxState === 0) return t;\n    if ((e = t.dataRef.current) != null && e.value) {\n      let n = t.dataRef.current.calculateIndex(t.dataRef.current.value);\n      if (n !== -1) return _objectSpread(_objectSpread({}, t), {}, {\n        activeOptionIndex: n,\n        comboboxState: 0,\n        __demoMode: !1\n      });\n    }\n    return _objectSpread(_objectSpread({}, t), {}, {\n      comboboxState: 0,\n      __demoMode: !1\n    });\n  },\n  [3](t, i) {\n    return t.isTyping === i.isTyping ? t : _objectSpread(_objectSpread({}, t), {}, {\n      isTyping: i.isTyping\n    });\n  },\n  [2](t, i) {\n    var r, u, a, d;\n    if ((r = t.dataRef.current) != null && r.disabled || t.optionsElement && !((u = t.dataRef.current) != null && u.optionsPropsRef.current.static) && t.comboboxState === 1) return t;\n    if (t.virtual) {\n      let {\n          options: p,\n          disabled: s\n        } = t.virtual,\n        b = i.focus === f.Specific ? i.idx : x(i, {\n          resolveItems: () => p,\n          resolveActiveIndex: () => {\n            var v, m;\n            return (m = (v = t.activeOptionIndex) != null ? v : p.findIndex(y => !s(y))) != null ? m : null;\n          },\n          resolveDisabled: s,\n          resolveId() {\n            throw new Error(\"Function not implemented.\");\n          }\n        }),\n        l = (a = i.trigger) != null ? a : 2;\n      return t.activeOptionIndex === b && t.activationTrigger === l ? t : _objectSpread(_objectSpread({}, t), {}, {\n        activeOptionIndex: b,\n        activationTrigger: l,\n        isTyping: !1,\n        __demoMode: !1\n      });\n    }\n    let e = T(t);\n    if (e.activeOptionIndex === null) {\n      let p = e.options.findIndex(s => !s.dataRef.current.disabled);\n      p !== -1 && (e.activeOptionIndex = p);\n    }\n    let n = i.focus === f.Specific ? i.idx : x(i, {\n        resolveItems: () => e.options,\n        resolveActiveIndex: () => e.activeOptionIndex,\n        resolveId: p => p.id,\n        resolveDisabled: p => p.dataRef.current.disabled\n      }),\n      o = (d = i.trigger) != null ? d : 2;\n    return t.activeOptionIndex === n && t.activationTrigger === o ? t : _objectSpread(_objectSpread(_objectSpread({}, t), e), {}, {\n      isTyping: !1,\n      activeOptionIndex: n,\n      activationTrigger: o,\n      __demoMode: !1\n    });\n  },\n  [4]: (t, i) => {\n    var r, u, a, d;\n    if ((r = t.dataRef.current) != null && r.virtual) return _objectSpread(_objectSpread({}, t), {}, {\n      options: [...t.options, i.payload]\n    });\n    let e = i.payload,\n      n = T(t, p => (p.push(e), p));\n    t.activeOptionIndex === null && (a = (u = t.dataRef.current).isSelected) != null && a.call(u, i.payload.dataRef.current.value) && (n.activeOptionIndex = n.options.indexOf(e));\n    let o = _objectSpread(_objectSpread(_objectSpread({}, t), n), {}, {\n      activationTrigger: 2\n    });\n    return (d = t.dataRef.current) != null && d.__demoMode && t.dataRef.current.value === void 0 && (o.activeOptionIndex = 0), o;\n  },\n  [5]: (t, i) => {\n    var n;\n    if ((n = t.dataRef.current) != null && n.virtual) return _objectSpread(_objectSpread({}, t), {}, {\n      options: t.options.filter(o => o.id !== i.id)\n    });\n    let e = T(t, o => {\n      let r = o.findIndex(u => u.id === i.id);\n      return r !== -1 && o.splice(r, 1), o;\n    });\n    return _objectSpread(_objectSpread(_objectSpread({}, t), e), {}, {\n      activationTrigger: 2\n    });\n  },\n  [6]: (t, i) => t.defaultToFirstOption === i.value ? t : _objectSpread(_objectSpread({}, t), {}, {\n    defaultToFirstOption: i.value\n  }),\n  [7]: (t, i) => t.activationTrigger === i.trigger ? t : _objectSpread(_objectSpread({}, t), {}, {\n    activationTrigger: i.trigger\n  }),\n  [8]: (t, i) => {\n    var n, o;\n    if (t.virtual === null) return _objectSpread(_objectSpread({}, t), {}, {\n      virtual: {\n        options: i.options,\n        disabled: (n = i.disabled) != null ? n : () => !1\n      }\n    });\n    if (t.virtual.options === i.options && t.virtual.disabled === i.disabled) return t;\n    let e = t.activeOptionIndex;\n    if (t.activeOptionIndex !== null) {\n      let r = i.options.indexOf(t.virtual.options[t.activeOptionIndex]);\n      r !== -1 ? e = r : e = null;\n    }\n    return _objectSpread(_objectSpread({}, t), {}, {\n      activeOptionIndex: e,\n      virtual: {\n        options: i.options,\n        disabled: (o = i.disabled) != null ? o : () => !1\n      }\n    });\n  },\n  [9]: (t, i) => t.inputElement === i.element ? t : _objectSpread(_objectSpread({}, t), {}, {\n    inputElement: i.element\n  }),\n  [10]: (t, i) => t.buttonElement === i.element ? t : _objectSpread(_objectSpread({}, t), {}, {\n    buttonElement: i.element\n  }),\n  [11]: (t, i) => t.optionsElement === i.element ? t : _objectSpread(_objectSpread({}, t), {}, {\n    optionsElement: i.element\n  })\n};\nclass O extends h {\n  constructor(e) {\n    super(e);\n    c(this, \"actions\", {\n      onChange: e => {\n        let {\n          onChange: n,\n          compare: o,\n          mode: r,\n          value: u\n        } = this.state.dataRef.current;\n        return g(r, {\n          [0]: () => n == null ? void 0 : n(e),\n          [1]: () => {\n            let a = u.slice(),\n              d = a.findIndex(p => o(p, e));\n            return d === -1 ? a.push(e) : a.splice(d, 1), n == null ? void 0 : n(a);\n          }\n        });\n      },\n      registerOption: (e, n) => (this.send({\n        type: 4,\n        payload: {\n          id: e,\n          dataRef: n\n        }\n      }), () => {\n        this.state.activeOptionIndex === this.state.dataRef.current.calculateIndex(n.current.value) && this.send({\n          type: 6,\n          value: !0\n        }), this.send({\n          type: 5,\n          id: e\n        });\n      }),\n      goToOption: (e, n) => (this.send({\n        type: 6,\n        value: !1\n      }), this.send(_objectSpread(_objectSpread({\n        type: 2\n      }, e), {}, {\n        trigger: n\n      }))),\n      setIsTyping: e => {\n        this.send({\n          type: 3,\n          isTyping: e\n        });\n      },\n      closeCombobox: () => {\n        var e, n;\n        this.send({\n          type: 1\n        }), this.send({\n          type: 6,\n          value: !1\n        }), (n = (e = this.state.dataRef.current).onClose) == null || n.call(e);\n      },\n      openCombobox: () => {\n        this.send({\n          type: 0\n        }), this.send({\n          type: 6,\n          value: !0\n        });\n      },\n      setActivationTrigger: e => {\n        this.send({\n          type: 7,\n          trigger: e\n        });\n      },\n      selectActiveOption: () => {\n        let e = this.selectors.activeOptionIndex(this.state);\n        if (e !== null) {\n          if (this.actions.setIsTyping(!1), this.state.virtual) this.actions.onChange(this.state.virtual.options[e]);else {\n            let {\n              dataRef: n\n            } = this.state.options[e];\n            this.actions.onChange(n.current.value);\n          }\n          this.actions.goToOption({\n            focus: f.Specific,\n            idx: e\n          });\n        }\n      },\n      setInputElement: e => {\n        this.send({\n          type: 9,\n          element: e\n        });\n      },\n      setButtonElement: e => {\n        this.send({\n          type: 10,\n          element: e\n        });\n      },\n      setOptionsElement: e => {\n        this.send({\n          type: 11,\n          element: e\n        });\n      }\n    });\n    c(this, \"selectors\", {\n      activeDescendantId: e => {\n        var o, r;\n        let n = this.selectors.activeOptionIndex(e);\n        if (n !== null) return e.virtual ? (r = e.options.find(u => !u.dataRef.current.disabled && e.dataRef.current.compare(u.dataRef.current.value, e.virtual.options[n]))) == null ? void 0 : r.id : (o = e.options[n]) == null ? void 0 : o.id;\n      },\n      activeOptionIndex: e => {\n        if (e.defaultToFirstOption && e.activeOptionIndex === null && (e.virtual ? e.virtual.options.length > 0 : e.options.length > 0)) {\n          if (e.virtual) {\n            let {\n                options: o,\n                disabled: r\n              } = e.virtual,\n              u = o.findIndex(a => {\n                var d;\n                return !((d = r == null ? void 0 : r(a)) != null && d);\n              });\n            if (u !== -1) return u;\n          }\n          let n = e.options.findIndex(o => !o.dataRef.current.disabled);\n          if (n !== -1) return n;\n        }\n        return e.activeOptionIndex;\n      },\n      activeOption: e => {\n        var o, r;\n        let n = this.selectors.activeOptionIndex(e);\n        return n === null ? null : e.virtual ? e.virtual.options[n != null ? n : 0] : (r = (o = e.options[n]) == null ? void 0 : o.dataRef.current.value) != null ? r : null;\n      },\n      isActive: (e, n, o) => {\n        var u;\n        let r = this.selectors.activeOptionIndex(e);\n        return r === null ? !1 : e.virtual ? r === e.dataRef.current.calculateIndex(n) : ((u = e.options[r]) == null ? void 0 : u.id) === o;\n      },\n      shouldScrollIntoView: (e, n, o) => !(e.virtual || e.__demoMode || e.comboboxState !== 0 || e.activationTrigger === 0 || !this.selectors.isActive(e, n, o))\n    });\n    {\n      let n = this.state.id,\n        o = A.get(null);\n      this.disposables.add(o.on(R.Push, r => {\n        !o.selectors.isTop(r, n) && this.state.comboboxState === 0 && this.actions.closeCombobox();\n      })), this.on(0, () => o.actions.push(n)), this.on(1, () => o.actions.pop(n));\n    }\n  }\n  static new(_ref) {\n    let {\n      id: e,\n      virtual: n = null,\n      __demoMode: o = !1\n    } = _ref;\n    var r;\n    return new O({\n      id: e,\n      dataRef: {\n        current: {}\n      },\n      comboboxState: o ? 0 : 1,\n      isTyping: !1,\n      options: [],\n      virtual: n ? {\n        options: n.options,\n        disabled: (r = n.disabled) != null ? r : () => !1\n      } : null,\n      activeOptionIndex: null,\n      activationTrigger: 2,\n      inputElement: null,\n      buttonElement: null,\n      optionsElement: null,\n      __demoMode: o\n    });\n  }\n  reduce(e, n) {\n    return g(n.type, D, e, n);\n  }\n}\nexport { _ as ActionTypes, F as ActivationTrigger, O as ComboboxMachine, C as ComboboxState, M as ValueMode };", "map": {"version": 3, "names": ["S", "Object", "defineProperty", "I", "t", "i", "e", "enumerable", "configurable", "writable", "value", "c", "Machine", "h", "ActionTypes", "R", "stackMachines", "A", "Focus", "f", "calculateActiveIndex", "x", "sortByDomNode", "E", "match", "g", "C", "Open", "Closed", "M", "Single", "Multi", "F", "n", "Pointer", "Other", "_", "l", "OpenCombobox", "CloseCombobox", "GoToOption", "SetTyping", "RegisterOption", "UnregisterOption", "DefaultToFirstOption", "SetActivationTrigger", "UpdateVirtualConfiguration", "SetInputElement", "SetButtonElement", "SetOptionsElement", "T", "arguments", "length", "undefined", "activeOptionIndex", "options", "slice", "o", "dataRef", "current", "order", "sort", "u", "a", "domRef", "r", "indexOf", "D", "disabled", "comboboxState", "_objectSpread", "isTyping", "activationTrigger", "__demoMode", "calculateIndex", "d", "optionsElement", "optionsPropsRef", "static", "virtual", "p", "s", "b", "focus", "Specific", "idx", "resolveItems", "resolveActiveIndex", "v", "m", "findIndex", "y", "resolveDisabled", "resolveId", "Error", "trigger", "id", "payload", "push", "isSelected", "call", "filter", "splice", "defaultToFirstOption", "inputElement", "element", "buttonElement", "O", "constructor", "onChange", "compare", "mode", "state", "registerOption", "send", "type", "goToOption", "setIsTyping", "closeCombobox", "onClose", "openCombobox", "setActivationTrigger", "selectActiveOption", "selectors", "actions", "setInputElement", "setButtonElement", "setOptionsElement", "activeDescendantId", "find", "activeOption", "isActive", "shouldScrollIntoView", "get", "disposables", "add", "on", "<PERSON><PERSON>", "isTop", "pop", "new", "_ref", "reduce", "ActivationTrigger", "ComboboxMachine", "ComboboxState", "ValueMode"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/combobox/combobox-machine.js"], "sourcesContent": ["var S=Object.defineProperty;var I=(t,i,e)=>i in t?S(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var c=(t,i,e)=>(I(t,typeof i!=\"symbol\"?i+\"\":i,e),e);import{Machine as h}from'../../machine.js';import{ActionTypes as R,stackMachines as A}from'../../machines/stack-machine.js';import{Focus as f,calculateActiveIndex as x}from'../../utils/calculate-active-index.js';import{sortByDomNode as E}from'../../utils/focus-management.js';import{match as g}from'../../utils/match.js';var C=(e=>(e[e.Open=0]=\"Open\",e[e.Closed=1]=\"Closed\",e))(C||{}),M=(e=>(e[e.Single=0]=\"Single\",e[e.Multi=1]=\"Multi\",e))(M||{}),F=(n=>(n[n.Pointer=0]=\"Pointer\",n[n.Focus=1]=\"Focus\",n[n.Other=2]=\"Other\",n))(F||{}),_=(l=>(l[l.OpenCombobox=0]=\"OpenCombobox\",l[l.CloseCombobox=1]=\"CloseCombobox\",l[l.GoToOption=2]=\"GoToOption\",l[l.SetTyping=3]=\"SetTyping\",l[l.RegisterOption=4]=\"RegisterOption\",l[l.UnregisterOption=5]=\"UnregisterOption\",l[l.DefaultToFirstOption=6]=\"DefaultToFirstOption\",l[l.SetActivationTrigger=7]=\"SetActivationTrigger\",l[l.UpdateVirtualConfiguration=8]=\"UpdateVirtualConfiguration\",l[l.SetInputElement=9]=\"SetInputElement\",l[l.SetButtonElement=10]=\"SetButtonElement\",l[l.SetOptionsElement=11]=\"SetOptionsElement\",l))(_||{});function T(t,i=e=>e){let e=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,n=i(t.options.slice()),o=n.length>0&&n[0].dataRef.current.order!==null?n.sort((u,a)=>u.dataRef.current.order-a.dataRef.current.order):E(n,u=>u.dataRef.current.domRef.current),r=e?o.indexOf(e):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}let D={[1](t){var i;return(i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](t){var i,e;if((i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===0)return t;if((e=t.dataRef.current)!=null&&e.value){let n=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(n!==-1)return{...t,activeOptionIndex:n,comboboxState:0,__demoMode:!1}}return{...t,comboboxState:0,__demoMode:!1}},[3](t,i){return t.isTyping===i.isTyping?t:{...t,isTyping:i.isTyping}},[2](t,i){var r,u,a,d;if((r=t.dataRef.current)!=null&&r.disabled||t.optionsElement&&!((u=t.dataRef.current)!=null&&u.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let{options:p,disabled:s}=t.virtual,b=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>p,resolveActiveIndex:()=>{var v,m;return(m=(v=t.activeOptionIndex)!=null?v:p.findIndex(y=>!s(y)))!=null?m:null},resolveDisabled:s,resolveId(){throw new Error(\"Function not implemented.\")}}),l=(a=i.trigger)!=null?a:2;return t.activeOptionIndex===b&&t.activationTrigger===l?t:{...t,activeOptionIndex:b,activationTrigger:l,isTyping:!1,__demoMode:!1}}let e=T(t);if(e.activeOptionIndex===null){let p=e.options.findIndex(s=>!s.dataRef.current.disabled);p!==-1&&(e.activeOptionIndex=p)}let n=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled}),o=(d=i.trigger)!=null?d:2;return t.activeOptionIndex===n&&t.activationTrigger===o?t:{...t,...e,isTyping:!1,activeOptionIndex:n,activationTrigger:o,__demoMode:!1}},[4]:(t,i)=>{var r,u,a,d;if((r=t.dataRef.current)!=null&&r.virtual)return{...t,options:[...t.options,i.payload]};let e=i.payload,n=T(t,p=>(p.push(e),p));t.activeOptionIndex===null&&(a=(u=t.dataRef.current).isSelected)!=null&&a.call(u,i.payload.dataRef.current.value)&&(n.activeOptionIndex=n.options.indexOf(e));let o={...t,...n,activationTrigger:2};return(d=t.dataRef.current)!=null&&d.__demoMode&&t.dataRef.current.value===void 0&&(o.activeOptionIndex=0),o},[5]:(t,i)=>{var n;if((n=t.dataRef.current)!=null&&n.virtual)return{...t,options:t.options.filter(o=>o.id!==i.id)};let e=T(t,o=>{let r=o.findIndex(u=>u.id===i.id);return r!==-1&&o.splice(r,1),o});return{...t,...e,activationTrigger:2}},[6]:(t,i)=>t.defaultToFirstOption===i.value?t:{...t,defaultToFirstOption:i.value},[7]:(t,i)=>t.activationTrigger===i.trigger?t:{...t,activationTrigger:i.trigger},[8]:(t,i)=>{var n,o;if(t.virtual===null)return{...t,virtual:{options:i.options,disabled:(n=i.disabled)!=null?n:()=>!1}};if(t.virtual.options===i.options&&t.virtual.disabled===i.disabled)return t;let e=t.activeOptionIndex;if(t.activeOptionIndex!==null){let r=i.options.indexOf(t.virtual.options[t.activeOptionIndex]);r!==-1?e=r:e=null}return{...t,activeOptionIndex:e,virtual:{options:i.options,disabled:(o=i.disabled)!=null?o:()=>!1}}},[9]:(t,i)=>t.inputElement===i.element?t:{...t,inputElement:i.element},[10]:(t,i)=>t.buttonElement===i.element?t:{...t,buttonElement:i.element},[11]:(t,i)=>t.optionsElement===i.element?t:{...t,optionsElement:i.element}};class O extends h{constructor(e){super(e);c(this,\"actions\",{onChange:e=>{let{onChange:n,compare:o,mode:r,value:u}=this.state.dataRef.current;return g(r,{[0]:()=>n==null?void 0:n(e),[1]:()=>{let a=u.slice(),d=a.findIndex(p=>o(p,e));return d===-1?a.push(e):a.splice(d,1),n==null?void 0:n(a)}})},registerOption:(e,n)=>(this.send({type:4,payload:{id:e,dataRef:n}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(n.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:e})}),goToOption:(e,n)=>(this.send({type:6,value:!1}),this.send({type:2,...e,trigger:n})),setIsTyping:e=>{this.send({type:3,isTyping:e})},closeCombobox:()=>{var e,n;this.send({type:1}),this.send({type:6,value:!1}),(n=(e=this.state.dataRef.current).onClose)==null||n.call(e)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:e=>{this.send({type:7,trigger:e})},selectActiveOption:()=>{let e=this.selectors.activeOptionIndex(this.state);if(e!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[e]);else{let{dataRef:n}=this.state.options[e];this.actions.onChange(n.current.value)}this.actions.goToOption({focus:f.Specific,idx:e})}},setInputElement:e=>{this.send({type:9,element:e})},setButtonElement:e=>{this.send({type:10,element:e})},setOptionsElement:e=>{this.send({type:11,element:e})}});c(this,\"selectors\",{activeDescendantId:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);if(n!==null)return e.virtual?(r=e.options.find(u=>!u.dataRef.current.disabled&&e.dataRef.current.compare(u.dataRef.current.value,e.virtual.options[n])))==null?void 0:r.id:(o=e.options[n])==null?void 0:o.id},activeOptionIndex:e=>{if(e.defaultToFirstOption&&e.activeOptionIndex===null&&(e.virtual?e.virtual.options.length>0:e.options.length>0)){if(e.virtual){let{options:o,disabled:r}=e.virtual,u=o.findIndex(a=>{var d;return!((d=r==null?void 0:r(a))!=null&&d)});if(u!==-1)return u}let n=e.options.findIndex(o=>!o.dataRef.current.disabled);if(n!==-1)return n}return e.activeOptionIndex},activeOption:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);return n===null?null:e.virtual?e.virtual.options[n!=null?n:0]:(r=(o=e.options[n])==null?void 0:o.dataRef.current.value)!=null?r:null},isActive:(e,n,o)=>{var u;let r=this.selectors.activeOptionIndex(e);return r===null?!1:e.virtual?r===e.dataRef.current.calculateIndex(n):((u=e.options[r])==null?void 0:u.id)===o},shouldScrollIntoView:(e,n,o)=>!(e.virtual||e.__demoMode||e.comboboxState!==0||e.activationTrigger===0||!this.selectors.isActive(e,n,o))});{let n=this.state.id,o=A.get(null);this.disposables.add(o.on(R.Push,r=>{!o.selectors.isTop(r,n)&&this.state.comboboxState===0&&this.actions.closeCombobox()})),this.on(0,()=>o.actions.push(n)),this.on(1,()=>o.actions.pop(n))}}static new({id:e,virtual:n=null,__demoMode:o=!1}){var r;return new O({id:e,dataRef:{current:{}},comboboxState:o?0:1,isTyping:!1,options:[],virtual:n?{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:o})}reduce(e,n){return g(n.type,D,e,n)}}export{_ as ActionTypes,F as ActivationTrigger,O as ComboboxMachine,C as ComboboxState,M as ValueMode};\n"], "mappings": ";AAAA,IAAIA,CAAC,GAACC,MAAM,CAACC,cAAc;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,IAAID,CAAC,GAACJ,CAAC,CAACI,CAAC,EAACC,CAAC,EAAC;EAACE,UAAU,EAAC,CAAC,CAAC;EAACC,YAAY,EAAC,CAAC,CAAC;EAACC,QAAQ,EAAC,CAAC,CAAC;EAACC,KAAK,EAACJ;AAAC,CAAC,CAAC,GAACF,CAAC,CAACC,CAAC,CAAC,GAACC,CAAC;AAAC,IAAIK,CAAC,GAACA,CAACP,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIH,CAAC,CAACC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,GAACA,CAAC,GAAC,EAAE,GAACA,CAAC,EAACC,CAAC,CAAC,EAACA,CAAC,CAAC;AAAC,SAAOM,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,CAAC,EAACC,oBAAoB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,sBAAsB;AAAC,IAAIC,CAAC,GAAC,CAACpB,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACqB,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACrB,CAAC,CAACA,CAAC,CAACsB,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACtB,CAAC,CAAC,EAAEoB,CAAC,IAAE,CAAC,CAAC,CAAC;EAACG,CAAC,GAAC,CAACvB,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACwB,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACxB,CAAC,CAACA,CAAC,CAACyB,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACzB,CAAC,CAAC,EAAEuB,CAAC,IAAE,CAAC,CAAC,CAAC;EAACG,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,GAAC,SAAS,EAACD,CAAC,CAACA,CAAC,CAACf,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACe,CAAC,CAACA,CAAC,CAACE,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;EAACI,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,YAAY,GAAC,CAAC,CAAC,GAAC,cAAc,EAACD,CAAC,CAACA,CAAC,CAACE,aAAa,GAAC,CAAC,CAAC,GAAC,eAAe,EAACF,CAAC,CAACA,CAAC,CAACG,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACH,CAAC,CAACA,CAAC,CAACI,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACJ,CAAC,CAACA,CAAC,CAACK,cAAc,GAAC,CAAC,CAAC,GAAC,gBAAgB,EAACL,CAAC,CAACA,CAAC,CAACM,gBAAgB,GAAC,CAAC,CAAC,GAAC,kBAAkB,EAACN,CAAC,CAACA,CAAC,CAACO,oBAAoB,GAAC,CAAC,CAAC,GAAC,sBAAsB,EAACP,CAAC,CAACA,CAAC,CAACQ,oBAAoB,GAAC,CAAC,CAAC,GAAC,sBAAsB,EAACR,CAAC,CAACA,CAAC,CAACS,0BAA0B,GAAC,CAAC,CAAC,GAAC,4BAA4B,EAACT,CAAC,CAACA,CAAC,CAACU,eAAe,GAAC,CAAC,CAAC,GAAC,iBAAiB,EAACV,CAAC,CAACA,CAAC,CAACW,gBAAgB,GAAC,EAAE,CAAC,GAAC,kBAAkB,EAACX,CAAC,CAACA,CAAC,CAACY,iBAAiB,GAAC,EAAE,CAAC,GAAC,mBAAmB,EAACZ,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASc,CAACA,CAAC9C,CAAC,EAAQ;EAAA,IAAPC,CAAC,GAAA8C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC7C,CAAC,IAAEA,CAAC;EAAE,IAAIA,CAAC,GAACF,CAAC,CAACkD,iBAAiB,KAAG,IAAI,GAAClD,CAAC,CAACmD,OAAO,CAACnD,CAAC,CAACkD,iBAAiB,CAAC,GAAC,IAAI;IAACrB,CAAC,GAAC5B,CAAC,CAACD,CAAC,CAACmD,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;IAACC,CAAC,GAACxB,CAAC,CAACmB,MAAM,GAAC,CAAC,IAAEnB,CAAC,CAAC,CAAC,CAAC,CAACyB,OAAO,CAACC,OAAO,CAACC,KAAK,KAAG,IAAI,GAAC3B,CAAC,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACC,KAAK,GAACG,CAAC,CAACL,OAAO,CAACC,OAAO,CAACC,KAAK,CAAC,GAACrC,CAAC,CAACU,CAAC,EAAC6B,CAAC,IAAEA,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACK,MAAM,CAACL,OAAO,CAAC;IAACM,CAAC,GAAC3D,CAAC,GAACmD,CAAC,CAACS,OAAO,CAAC5D,CAAC,CAAC,GAAC,IAAI;EAAC,OAAO2D,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAAC,IAAI,CAAC,EAAC;IAACV,OAAO,EAACE,CAAC;IAACH,iBAAiB,EAACW;EAAC,CAAC;AAAA;AAAC,IAAIE,CAAC,GAAC;EAAC,CAAC,CAAC,EAAE/D,CAAC,EAAC;IAAC,IAAIC,CAAC;IAAC,OAAM,CAACA,CAAC,GAACD,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEtD,CAAC,CAAC+D,QAAQ,IAAEhE,CAAC,CAACiE,aAAa,KAAG,CAAC,GAACjE,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;MAACkD,iBAAiB,EAAC,IAAI;MAACe,aAAa,EAAC,CAAC;MAACE,QAAQ,EAAC,CAAC,CAAC;MAACC,iBAAiB,EAAC,CAAC;MAACC,UAAU,EAAC,CAAC;IAAC,EAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAErE,CAAC,EAAC;IAAC,IAAIC,CAAC,EAACC,CAAC;IAAC,IAAG,CAACD,CAAC,GAACD,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEtD,CAAC,CAAC+D,QAAQ,IAAEhE,CAAC,CAACiE,aAAa,KAAG,CAAC,EAAC,OAAOjE,CAAC;IAAC,IAAG,CAACE,CAAC,GAACF,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAErD,CAAC,CAACI,KAAK,EAAC;MAAC,IAAIuB,CAAC,GAAC7B,CAAC,CAACsD,OAAO,CAACC,OAAO,CAACe,cAAc,CAACtE,CAAC,CAACsD,OAAO,CAACC,OAAO,CAACjD,KAAK,CAAC;MAAC,IAAGuB,CAAC,KAAG,CAAC,CAAC,EAAC,OAAAqC,aAAA,CAAAA,aAAA,KAAUlE,CAAC;QAACkD,iBAAiB,EAACrB,CAAC;QAACoC,aAAa,EAAC,CAAC;QAACI,UAAU,EAAC,CAAC;MAAC;IAAC;IAAC,OAAAH,aAAA,CAAAA,aAAA,KAAUlE,CAAC;MAACiE,aAAa,EAAC,CAAC;MAACI,UAAU,EAAC,CAAC;IAAC;EAAC,CAAC;EAAC,CAAC,CAAC,EAAErE,CAAC,EAACC,CAAC,EAAC;IAAC,OAAOD,CAAC,CAACmE,QAAQ,KAAGlE,CAAC,CAACkE,QAAQ,GAACnE,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;MAACmE,QAAQ,EAAClE,CAAC,CAACkE;IAAQ,EAAC;EAAA,CAAC;EAAC,CAAC,CAAC,EAAEnE,CAAC,EAACC,CAAC,EAAC;IAAC,IAAI4D,CAAC,EAACH,CAAC,EAACC,CAAC,EAACY,CAAC;IAAC,IAAG,CAACV,CAAC,GAAC7D,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEM,CAAC,CAACG,QAAQ,IAAEhE,CAAC,CAACwE,cAAc,IAAE,EAAE,CAACd,CAAC,GAAC1D,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEG,CAAC,CAACe,eAAe,CAAClB,OAAO,CAACmB,MAAM,CAAC,IAAE1E,CAAC,CAACiE,aAAa,KAAG,CAAC,EAAC,OAAOjE,CAAC;IAAC,IAAGA,CAAC,CAAC2E,OAAO,EAAC;MAAC,IAAG;UAACxB,OAAO,EAACyB,CAAC;UAACZ,QAAQ,EAACa;QAAC,CAAC,GAAC7E,CAAC,CAAC2E,OAAO;QAACG,CAAC,GAAC7E,CAAC,CAAC8E,KAAK,KAAGhE,CAAC,CAACiE,QAAQ,GAAC/E,CAAC,CAACgF,GAAG,GAAChE,CAAC,CAAChB,CAAC,EAAC;UAACiF,YAAY,EAACA,CAAA,KAAIN,CAAC;UAACO,kBAAkB,EAACA,CAAA,KAAI;YAAC,IAAIC,CAAC,EAACC,CAAC;YAAC,OAAM,CAACA,CAAC,GAAC,CAACD,CAAC,GAACpF,CAAC,CAACkD,iBAAiB,KAAG,IAAI,GAACkC,CAAC,GAACR,CAAC,CAACU,SAAS,CAACC,CAAC,IAAE,CAACV,CAAC,CAACU,CAAC,CAAC,CAAC,KAAG,IAAI,GAACF,CAAC,GAAC,IAAI;UAAA,CAAC;UAACG,eAAe,EAACX,CAAC;UAACY,SAASA,CAAA,EAAE;YAAC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;UAAA;QAAC,CAAC,CAAC;QAACzD,CAAC,GAAC,CAAC0B,CAAC,GAAC1D,CAAC,CAAC0F,OAAO,KAAG,IAAI,GAAChC,CAAC,GAAC,CAAC;MAAC,OAAO3D,CAAC,CAACkD,iBAAiB,KAAG4B,CAAC,IAAE9E,CAAC,CAACoE,iBAAiB,KAAGnC,CAAC,GAACjC,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;QAACkD,iBAAiB,EAAC4B,CAAC;QAACV,iBAAiB,EAACnC,CAAC;QAACkC,QAAQ,EAAC,CAAC,CAAC;QAACE,UAAU,EAAC,CAAC;MAAC,EAAC;IAAA;IAAC,IAAInE,CAAC,GAAC4C,CAAC,CAAC9C,CAAC,CAAC;IAAC,IAAGE,CAAC,CAACgD,iBAAiB,KAAG,IAAI,EAAC;MAAC,IAAI0B,CAAC,GAAC1E,CAAC,CAACiD,OAAO,CAACmC,SAAS,CAACT,CAAC,IAAE,CAACA,CAAC,CAACvB,OAAO,CAACC,OAAO,CAACS,QAAQ,CAAC;MAACY,CAAC,KAAG,CAAC,CAAC,KAAG1E,CAAC,CAACgD,iBAAiB,GAAC0B,CAAC,CAAC;IAAA;IAAC,IAAI/C,CAAC,GAAC5B,CAAC,CAAC8E,KAAK,KAAGhE,CAAC,CAACiE,QAAQ,GAAC/E,CAAC,CAACgF,GAAG,GAAChE,CAAC,CAAChB,CAAC,EAAC;QAACiF,YAAY,EAACA,CAAA,KAAIhF,CAAC,CAACiD,OAAO;QAACgC,kBAAkB,EAACA,CAAA,KAAIjF,CAAC,CAACgD,iBAAiB;QAACuC,SAAS,EAACb,CAAC,IAAEA,CAAC,CAACgB,EAAE;QAACJ,eAAe,EAACZ,CAAC,IAAEA,CAAC,CAACtB,OAAO,CAACC,OAAO,CAACS;MAAQ,CAAC,CAAC;MAACX,CAAC,GAAC,CAACkB,CAAC,GAACtE,CAAC,CAAC0F,OAAO,KAAG,IAAI,GAACpB,CAAC,GAAC,CAAC;IAAC,OAAOvE,CAAC,CAACkD,iBAAiB,KAAGrB,CAAC,IAAE7B,CAAC,CAACoE,iBAAiB,KAAGf,CAAC,GAACrD,CAAC,GAAAkE,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAKlE,CAAC,GAAIE,CAAC;MAACiE,QAAQ,EAAC,CAAC,CAAC;MAACjB,iBAAiB,EAACrB,CAAC;MAACuC,iBAAiB,EAACf,CAAC;MAACgB,UAAU,EAAC,CAAC;IAAC,EAAC;EAAA,CAAC;EAAC,CAAC,CAAC,GAAE,CAACrE,CAAC,EAACC,CAAC,KAAG;IAAC,IAAI4D,CAAC,EAACH,CAAC,EAACC,CAAC,EAACY,CAAC;IAAC,IAAG,CAACV,CAAC,GAAC7D,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEM,CAAC,CAACc,OAAO,EAAC,OAAAT,aAAA,CAAAA,aAAA,KAAUlE,CAAC;MAACmD,OAAO,EAAC,CAAC,GAAGnD,CAAC,CAACmD,OAAO,EAAClD,CAAC,CAAC4F,OAAO;IAAC;IAAE,IAAI3F,CAAC,GAACD,CAAC,CAAC4F,OAAO;MAAChE,CAAC,GAACiB,CAAC,CAAC9C,CAAC,EAAC4E,CAAC,KAAGA,CAAC,CAACkB,IAAI,CAAC5F,CAAC,CAAC,EAAC0E,CAAC,CAAC,CAAC;IAAC5E,CAAC,CAACkD,iBAAiB,KAAG,IAAI,IAAE,CAACS,CAAC,GAAC,CAACD,CAAC,GAAC1D,CAAC,CAACsD,OAAO,CAACC,OAAO,EAAEwC,UAAU,KAAG,IAAI,IAAEpC,CAAC,CAACqC,IAAI,CAACtC,CAAC,EAACzD,CAAC,CAAC4F,OAAO,CAACvC,OAAO,CAACC,OAAO,CAACjD,KAAK,CAAC,KAAGuB,CAAC,CAACqB,iBAAiB,GAACrB,CAAC,CAACsB,OAAO,CAACW,OAAO,CAAC5D,CAAC,CAAC,CAAC;IAAC,IAAImD,CAAC,GAAAa,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAKlE,CAAC,GAAI6B,CAAC;MAACuC,iBAAiB,EAAC;IAAC,EAAC;IAAC,OAAM,CAACG,CAAC,GAACvE,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEgB,CAAC,CAACF,UAAU,IAAErE,CAAC,CAACsD,OAAO,CAACC,OAAO,CAACjD,KAAK,KAAG,KAAK,CAAC,KAAG+C,CAAC,CAACH,iBAAiB,GAAC,CAAC,CAAC,EAACG,CAAC;EAAA,CAAC;EAAC,CAAC,CAAC,GAAE,CAACrD,CAAC,EAACC,CAAC,KAAG;IAAC,IAAI4B,CAAC;IAAC,IAAG,CAACA,CAAC,GAAC7B,CAAC,CAACsD,OAAO,CAACC,OAAO,KAAG,IAAI,IAAE1B,CAAC,CAAC8C,OAAO,EAAC,OAAAT,aAAA,CAAAA,aAAA,KAAUlE,CAAC;MAACmD,OAAO,EAACnD,CAAC,CAACmD,OAAO,CAAC8C,MAAM,CAAC5C,CAAC,IAAEA,CAAC,CAACuC,EAAE,KAAG3F,CAAC,CAAC2F,EAAE;IAAC;IAAE,IAAI1F,CAAC,GAAC4C,CAAC,CAAC9C,CAAC,EAACqD,CAAC,IAAE;MAAC,IAAIQ,CAAC,GAACR,CAAC,CAACiC,SAAS,CAAC5B,CAAC,IAAEA,CAAC,CAACkC,EAAE,KAAG3F,CAAC,CAAC2F,EAAE,CAAC;MAAC,OAAO/B,CAAC,KAAG,CAAC,CAAC,IAAER,CAAC,CAAC6C,MAAM,CAACrC,CAAC,EAAC,CAAC,CAAC,EAACR,CAAC;IAAA,CAAC,CAAC;IAAC,OAAAa,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAUlE,CAAC,GAAIE,CAAC;MAACkE,iBAAiB,EAAC;IAAC;EAAC,CAAC;EAAC,CAAC,CAAC,GAAE,CAACpE,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACmG,oBAAoB,KAAGlG,CAAC,CAACK,KAAK,GAACN,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;IAACmG,oBAAoB,EAAClG,CAAC,CAACK;EAAK,EAAC;EAAC,CAAC,CAAC,GAAE,CAACN,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACoE,iBAAiB,KAAGnE,CAAC,CAAC0F,OAAO,GAAC3F,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;IAACoE,iBAAiB,EAACnE,CAAC,CAAC0F;EAAO,EAAC;EAAC,CAAC,CAAC,GAAE,CAAC3F,CAAC,EAACC,CAAC,KAAG;IAAC,IAAI4B,CAAC,EAACwB,CAAC;IAAC,IAAGrD,CAAC,CAAC2E,OAAO,KAAG,IAAI,EAAC,OAAAT,aAAA,CAAAA,aAAA,KAAUlE,CAAC;MAAC2E,OAAO,EAAC;QAACxB,OAAO,EAAClD,CAAC,CAACkD,OAAO;QAACa,QAAQ,EAAC,CAACnC,CAAC,GAAC5B,CAAC,CAAC+D,QAAQ,KAAG,IAAI,GAACnC,CAAC,GAAC,MAAI,CAAC;MAAC;IAAC;IAAE,IAAG7B,CAAC,CAAC2E,OAAO,CAACxB,OAAO,KAAGlD,CAAC,CAACkD,OAAO,IAAEnD,CAAC,CAAC2E,OAAO,CAACX,QAAQ,KAAG/D,CAAC,CAAC+D,QAAQ,EAAC,OAAOhE,CAAC;IAAC,IAAIE,CAAC,GAACF,CAAC,CAACkD,iBAAiB;IAAC,IAAGlD,CAAC,CAACkD,iBAAiB,KAAG,IAAI,EAAC;MAAC,IAAIW,CAAC,GAAC5D,CAAC,CAACkD,OAAO,CAACW,OAAO,CAAC9D,CAAC,CAAC2E,OAAO,CAACxB,OAAO,CAACnD,CAAC,CAACkD,iBAAiB,CAAC,CAAC;MAACW,CAAC,KAAG,CAAC,CAAC,GAAC3D,CAAC,GAAC2D,CAAC,GAAC3D,CAAC,GAAC,IAAI;IAAA;IAAC,OAAAgE,aAAA,CAAAA,aAAA,KAAUlE,CAAC;MAACkD,iBAAiB,EAAChD,CAAC;MAACyE,OAAO,EAAC;QAACxB,OAAO,EAAClD,CAAC,CAACkD,OAAO;QAACa,QAAQ,EAAC,CAACX,CAAC,GAACpD,CAAC,CAAC+D,QAAQ,KAAG,IAAI,GAACX,CAAC,GAAC,MAAI,CAAC;MAAC;IAAC;EAAC,CAAC;EAAC,CAAC,CAAC,GAAE,CAACrD,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACoG,YAAY,KAAGnG,CAAC,CAACoG,OAAO,GAACrG,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;IAACoG,YAAY,EAACnG,CAAC,CAACoG;EAAO,EAAC;EAAC,CAAC,EAAE,GAAE,CAACrG,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACsG,aAAa,KAAGrG,CAAC,CAACoG,OAAO,GAACrG,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;IAACsG,aAAa,EAACrG,CAAC,CAACoG;EAAO,EAAC;EAAC,CAAC,EAAE,GAAE,CAACrG,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACwE,cAAc,KAAGvE,CAAC,CAACoG,OAAO,GAACrG,CAAC,GAAAkE,aAAA,CAAAA,aAAA,KAAKlE,CAAC;IAACwE,cAAc,EAACvE,CAAC,CAACoG;EAAO;AAAC,CAAC;AAAC,MAAME,CAAC,SAAS9F,CAAC;EAAC+F,WAAWA,CAACtG,CAAC,EAAC;IAAC,KAAK,CAACA,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,SAAS,EAAC;MAACkG,QAAQ,EAACvG,CAAC,IAAE;QAAC,IAAG;UAACuG,QAAQ,EAAC5E,CAAC;UAAC6E,OAAO,EAACrD,CAAC;UAACsD,IAAI,EAAC9C,CAAC;UAACvD,KAAK,EAACoD;QAAC,CAAC,GAAC,IAAI,CAACkD,KAAK,CAACtD,OAAO,CAACC,OAAO;QAAC,OAAOlC,CAAC,CAACwC,CAAC,EAAC;UAAC,CAAC,CAAC,GAAE,MAAIhC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC3B,CAAC,CAAC;UAAC,CAAC,CAAC,GAAE,MAAI;YAAC,IAAIyD,CAAC,GAACD,CAAC,CAACN,KAAK,CAAC,CAAC;cAACmB,CAAC,GAACZ,CAAC,CAAC2B,SAAS,CAACV,CAAC,IAAEvB,CAAC,CAACuB,CAAC,EAAC1E,CAAC,CAAC,CAAC;YAAC,OAAOqE,CAAC,KAAG,CAAC,CAAC,GAACZ,CAAC,CAACmC,IAAI,CAAC5F,CAAC,CAAC,GAACyD,CAAC,CAACuC,MAAM,CAAC3B,CAAC,EAAC,CAAC,CAAC,EAAC1C,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC8B,CAAC,CAAC;UAAA;QAAC,CAAC,CAAC;MAAA,CAAC;MAACkD,cAAc,EAACA,CAAC3G,CAAC,EAAC2B,CAAC,MAAI,IAAI,CAACiF,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAAClB,OAAO,EAAC;UAACD,EAAE,EAAC1F,CAAC;UAACoD,OAAO,EAACzB;QAAC;MAAC,CAAC,CAAC,EAAC,MAAI;QAAC,IAAI,CAAC+E,KAAK,CAAC1D,iBAAiB,KAAG,IAAI,CAAC0D,KAAK,CAACtD,OAAO,CAACC,OAAO,CAACe,cAAc,CAACzC,CAAC,CAAC0B,OAAO,CAACjD,KAAK,CAAC,IAAE,IAAI,CAACwG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACzG,KAAK,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACwG,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACnB,EAAE,EAAC1F;QAAC,CAAC,CAAC;MAAA,CAAC,CAAC;MAAC8G,UAAU,EAACA,CAAC9G,CAAC,EAAC2B,CAAC,MAAI,IAAI,CAACiF,IAAI,CAAC;QAACC,IAAI,EAAC,CAAC;QAACzG,KAAK,EAAC,CAAC;MAAC,CAAC,CAAC,EAAC,IAAI,CAACwG,IAAI,CAAA5C,aAAA,CAAAA,aAAA;QAAE6C,IAAI,EAAC;MAAC,GAAI7G,CAAC;QAACyF,OAAO,EAAC9D;MAAC,EAAC,CAAC,CAAC;MAACoF,WAAW,EAAC/G,CAAC,IAAE;QAAC,IAAI,CAAC4G,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAAC5C,QAAQ,EAACjE;QAAC,CAAC,CAAC;MAAA,CAAC;MAACgH,aAAa,EAACA,CAAA,KAAI;QAAC,IAAIhH,CAAC,EAAC2B,CAAC;QAAC,IAAI,CAACiF,IAAI,CAAC;UAACC,IAAI,EAAC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACD,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACzG,KAAK,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC,CAACuB,CAAC,GAAC,CAAC3B,CAAC,GAAC,IAAI,CAAC0G,KAAK,CAACtD,OAAO,CAACC,OAAO,EAAE4D,OAAO,KAAG,IAAI,IAAEtF,CAAC,CAACmE,IAAI,CAAC9F,CAAC,CAAC;MAAA,CAAC;MAACkH,YAAY,EAACA,CAAA,KAAI;QAAC,IAAI,CAACN,IAAI,CAAC;UAACC,IAAI,EAAC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACD,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACzG,KAAK,EAAC,CAAC;QAAC,CAAC,CAAC;MAAA,CAAC;MAAC+G,oBAAoB,EAACnH,CAAC,IAAE;QAAC,IAAI,CAAC4G,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACpB,OAAO,EAACzF;QAAC,CAAC,CAAC;MAAA,CAAC;MAACoH,kBAAkB,EAACA,CAAA,KAAI;QAAC,IAAIpH,CAAC,GAAC,IAAI,CAACqH,SAAS,CAACrE,iBAAiB,CAAC,IAAI,CAAC0D,KAAK,CAAC;QAAC,IAAG1G,CAAC,KAAG,IAAI,EAAC;UAAC,IAAG,IAAI,CAACsH,OAAO,CAACP,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACL,KAAK,CAACjC,OAAO,EAAC,IAAI,CAAC6C,OAAO,CAACf,QAAQ,CAAC,IAAI,CAACG,KAAK,CAACjC,OAAO,CAACxB,OAAO,CAACjD,CAAC,CAAC,CAAC,CAAC,KAAI;YAAC,IAAG;cAACoD,OAAO,EAACzB;YAAC,CAAC,GAAC,IAAI,CAAC+E,KAAK,CAACzD,OAAO,CAACjD,CAAC,CAAC;YAAC,IAAI,CAACsH,OAAO,CAACf,QAAQ,CAAC5E,CAAC,CAAC0B,OAAO,CAACjD,KAAK,CAAC;UAAA;UAAC,IAAI,CAACkH,OAAO,CAACR,UAAU,CAAC;YAACjC,KAAK,EAAChE,CAAC,CAACiE,QAAQ;YAACC,GAAG,EAAC/E;UAAC,CAAC,CAAC;QAAA;MAAC,CAAC;MAACuH,eAAe,EAACvH,CAAC,IAAE;QAAC,IAAI,CAAC4G,IAAI,CAAC;UAACC,IAAI,EAAC,CAAC;UAACV,OAAO,EAACnG;QAAC,CAAC,CAAC;MAAA,CAAC;MAACwH,gBAAgB,EAACxH,CAAC,IAAE;QAAC,IAAI,CAAC4G,IAAI,CAAC;UAACC,IAAI,EAAC,EAAE;UAACV,OAAO,EAACnG;QAAC,CAAC,CAAC;MAAA,CAAC;MAACyH,iBAAiB,EAACzH,CAAC,IAAE;QAAC,IAAI,CAAC4G,IAAI,CAAC;UAACC,IAAI,EAAC,EAAE;UAACV,OAAO,EAACnG;QAAC,CAAC,CAAC;MAAA;IAAC,CAAC,CAAC;IAACK,CAAC,CAAC,IAAI,EAAC,WAAW,EAAC;MAACqH,kBAAkB,EAAC1H,CAAC,IAAE;QAAC,IAAImD,CAAC,EAACQ,CAAC;QAAC,IAAIhC,CAAC,GAAC,IAAI,CAAC0F,SAAS,CAACrE,iBAAiB,CAAChD,CAAC,CAAC;QAAC,IAAG2B,CAAC,KAAG,IAAI,EAAC,OAAO3B,CAAC,CAACyE,OAAO,GAAC,CAACd,CAAC,GAAC3D,CAAC,CAACiD,OAAO,CAAC0E,IAAI,CAACnE,CAAC,IAAE,CAACA,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACS,QAAQ,IAAE9D,CAAC,CAACoD,OAAO,CAACC,OAAO,CAACmD,OAAO,CAAChD,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACjD,KAAK,EAACJ,CAAC,CAACyE,OAAO,CAACxB,OAAO,CAACtB,CAAC,CAAC,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACgC,CAAC,CAAC+B,EAAE,GAAC,CAACvC,CAAC,GAACnD,CAAC,CAACiD,OAAO,CAACtB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACwB,CAAC,CAACuC,EAAE;MAAA,CAAC;MAAC1C,iBAAiB,EAAChD,CAAC,IAAE;QAAC,IAAGA,CAAC,CAACiG,oBAAoB,IAAEjG,CAAC,CAACgD,iBAAiB,KAAG,IAAI,KAAGhD,CAAC,CAACyE,OAAO,GAACzE,CAAC,CAACyE,OAAO,CAACxB,OAAO,CAACH,MAAM,GAAC,CAAC,GAAC9C,CAAC,CAACiD,OAAO,CAACH,MAAM,GAAC,CAAC,CAAC,EAAC;UAAC,IAAG9C,CAAC,CAACyE,OAAO,EAAC;YAAC,IAAG;gBAACxB,OAAO,EAACE,CAAC;gBAACW,QAAQ,EAACH;cAAC,CAAC,GAAC3D,CAAC,CAACyE,OAAO;cAACjB,CAAC,GAACL,CAAC,CAACiC,SAAS,CAAC3B,CAAC,IAAE;gBAAC,IAAIY,CAAC;gBAAC,OAAM,EAAE,CAACA,CAAC,GAACV,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACF,CAAC,CAAC,KAAG,IAAI,IAAEY,CAAC,CAAC;cAAA,CAAC,CAAC;YAAC,IAAGb,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOA,CAAC;UAAA;UAAC,IAAI7B,CAAC,GAAC3B,CAAC,CAACiD,OAAO,CAACmC,SAAS,CAACjC,CAAC,IAAE,CAACA,CAAC,CAACC,OAAO,CAACC,OAAO,CAACS,QAAQ,CAAC;UAAC,IAAGnC,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOA,CAAC;QAAA;QAAC,OAAO3B,CAAC,CAACgD,iBAAiB;MAAA,CAAC;MAAC4E,YAAY,EAAC5H,CAAC,IAAE;QAAC,IAAImD,CAAC,EAACQ,CAAC;QAAC,IAAIhC,CAAC,GAAC,IAAI,CAAC0F,SAAS,CAACrE,iBAAiB,CAAChD,CAAC,CAAC;QAAC,OAAO2B,CAAC,KAAG,IAAI,GAAC,IAAI,GAAC3B,CAAC,CAACyE,OAAO,GAACzE,CAAC,CAACyE,OAAO,CAACxB,OAAO,CAACtB,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,CAAC,GAAC,CAACgC,CAAC,GAAC,CAACR,CAAC,GAACnD,CAAC,CAACiD,OAAO,CAACtB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACwB,CAAC,CAACC,OAAO,CAACC,OAAO,CAACjD,KAAK,KAAG,IAAI,GAACuD,CAAC,GAAC,IAAI;MAAA,CAAC;MAACkE,QAAQ,EAACA,CAAC7H,CAAC,EAAC2B,CAAC,EAACwB,CAAC,KAAG;QAAC,IAAIK,CAAC;QAAC,IAAIG,CAAC,GAAC,IAAI,CAAC0D,SAAS,CAACrE,iBAAiB,CAAChD,CAAC,CAAC;QAAC,OAAO2D,CAAC,KAAG,IAAI,GAAC,CAAC,CAAC,GAAC3D,CAAC,CAACyE,OAAO,GAACd,CAAC,KAAG3D,CAAC,CAACoD,OAAO,CAACC,OAAO,CAACe,cAAc,CAACzC,CAAC,CAAC,GAAC,CAAC,CAAC6B,CAAC,GAACxD,CAAC,CAACiD,OAAO,CAACU,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACH,CAAC,CAACkC,EAAE,MAAIvC,CAAC;MAAA,CAAC;MAAC2E,oBAAoB,EAACA,CAAC9H,CAAC,EAAC2B,CAAC,EAACwB,CAAC,KAAG,EAAEnD,CAAC,CAACyE,OAAO,IAAEzE,CAAC,CAACmE,UAAU,IAAEnE,CAAC,CAAC+D,aAAa,KAAG,CAAC,IAAE/D,CAAC,CAACkE,iBAAiB,KAAG,CAAC,IAAE,CAAC,IAAI,CAACmD,SAAS,CAACQ,QAAQ,CAAC7H,CAAC,EAAC2B,CAAC,EAACwB,CAAC,CAAC;IAAC,CAAC,CAAC;IAAC;MAAC,IAAIxB,CAAC,GAAC,IAAI,CAAC+E,KAAK,CAAChB,EAAE;QAACvC,CAAC,GAACxC,CAAC,CAACoH,GAAG,CAAC,IAAI,CAAC;MAAC,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC9E,CAAC,CAAC+E,EAAE,CAACzH,CAAC,CAAC0H,IAAI,EAACxE,CAAC,IAAE;QAAC,CAACR,CAAC,CAACkE,SAAS,CAACe,KAAK,CAACzE,CAAC,EAAChC,CAAC,CAAC,IAAE,IAAI,CAAC+E,KAAK,CAAC3C,aAAa,KAAG,CAAC,IAAE,IAAI,CAACuD,OAAO,CAACN,aAAa,CAAC,CAAC;MAAA,CAAC,CAAC,CAAC,EAAC,IAAI,CAACkB,EAAE,CAAC,CAAC,EAAC,MAAI/E,CAAC,CAACmE,OAAO,CAAC1B,IAAI,CAACjE,CAAC,CAAC,CAAC,EAAC,IAAI,CAACuG,EAAE,CAAC,CAAC,EAAC,MAAI/E,CAAC,CAACmE,OAAO,CAACe,GAAG,CAAC1G,CAAC,CAAC,CAAC;IAAA;EAAC;EAAC,OAAO2G,GAAGA,CAAAC,IAAA,EAAuC;IAAA,IAAtC;MAAC7C,EAAE,EAAC1F,CAAC;MAACyE,OAAO,EAAC9C,CAAC,GAAC,IAAI;MAACwC,UAAU,EAAChB,CAAC,GAAC,CAAC;IAAC,CAAC,GAAAoF,IAAA;IAAE,IAAI5E,CAAC;IAAC,OAAO,IAAI0C,CAAC,CAAC;MAACX,EAAE,EAAC1F,CAAC;MAACoD,OAAO,EAAC;QAACC,OAAO,EAAC,CAAC;MAAC,CAAC;MAACU,aAAa,EAACZ,CAAC,GAAC,CAAC,GAAC,CAAC;MAACc,QAAQ,EAAC,CAAC,CAAC;MAAChB,OAAO,EAAC,EAAE;MAACwB,OAAO,EAAC9C,CAAC,GAAC;QAACsB,OAAO,EAACtB,CAAC,CAACsB,OAAO;QAACa,QAAQ,EAAC,CAACH,CAAC,GAAChC,CAAC,CAACmC,QAAQ,KAAG,IAAI,GAACH,CAAC,GAAC,MAAI,CAAC;MAAC,CAAC,GAAC,IAAI;MAACX,iBAAiB,EAAC,IAAI;MAACkB,iBAAiB,EAAC,CAAC;MAACgC,YAAY,EAAC,IAAI;MAACE,aAAa,EAAC,IAAI;MAAC9B,cAAc,EAAC,IAAI;MAACH,UAAU,EAAChB;IAAC,CAAC,CAAC;EAAA;EAACqF,MAAMA,CAACxI,CAAC,EAAC2B,CAAC,EAAC;IAAC,OAAOR,CAAC,CAACQ,CAAC,CAACkF,IAAI,EAAChD,CAAC,EAAC7D,CAAC,EAAC2B,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOG,CAAC,IAAItB,WAAW,EAACkB,CAAC,IAAI+G,iBAAiB,EAACpC,CAAC,IAAIqC,eAAe,EAACtH,CAAC,IAAIuH,aAAa,EAACpH,CAAC,IAAIqH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}