{"ast": null, "code": "\"use client\";\n\nimport r, { createContext as n, useContext as i } from \"react\";\nlet e = n(() => {});\nfunction u() {\n  return i(e);\n}\nfunction C(_ref) {\n  let {\n    value: t,\n    children: o\n  } = _ref;\n  return r.createElement(e.Provider, {\n    value: t\n  }, o);\n}\nexport { C as CloseProvider, u as useClose };", "map": {"version": 3, "names": ["r", "createContext", "n", "useContext", "i", "e", "u", "C", "_ref", "value", "t", "children", "o", "createElement", "Provider", "Close<PERSON>rovider", "useClose"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/close-provider.js"], "sourcesContent": ["\"use client\";import r,{createContext as n,useContext as i}from\"react\";let e=n(()=>{});function u(){return i(e)}function C({value:t,children:o}){return r.createElement(e.Provider,{value:t},o)}export{C as CloseProvider,u as useClose};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAAAC,IAAA,EAAsB;EAAA,IAArB;IAACC,KAAK,EAACC,CAAC;IAACC,QAAQ,EAACC;EAAC,CAAC,GAAAJ,IAAA;EAAE,OAAOR,CAAC,CAACa,aAAa,CAACR,CAAC,CAACS,QAAQ,EAAC;IAACL,KAAK,EAACC;EAAC,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIQ,aAAa,EAACT,CAAC,IAAIU,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}