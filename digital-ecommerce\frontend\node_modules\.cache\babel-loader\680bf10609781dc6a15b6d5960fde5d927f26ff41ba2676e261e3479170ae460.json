{"ast": null, "code": "import { createSyntheticEvent as $8a9cb279dc87e130$export$525bc4921d56d4a, preventFocus as $8a9cb279dc87e130$export$cabe61c495ee3649, setEventTarget as $8a9cb279dc87e130$export$c2b7abe5d61ec696 } from \"./utils.mjs\";\nimport { disableTextSelection as $14c0b72509d70225$export$16a4697467175487, restoreTextSelection as $14c0b72509d70225$export$b0d6fa1ab32e3295 } from \"./textSelection.mjs\";\nimport { PressResponderContext as $ae1eeba8b9eafd08$export$5165eccb35aaadb5 } from \"./context.mjs\";\nimport { _ as $7mdmh$_ } from \"@swc/helpers/_/_class_private_field_get\";\nimport { _ as $7mdmh$_1 } from \"@swc/helpers/_/_class_private_field_init\";\nimport { _ as $7mdmh$_2 } from \"@swc/helpers/_/_class_private_field_set\";\nimport { mergeProps as $7mdmh$mergeProps, useSyncRef as $7mdmh$useSyncRef, useGlobalListeners as $7mdmh$useGlobalListeners, useEffectEvent as $7mdmh$useEffectEvent, nodeContains as $7mdmh$nodeContains, getEventTarget as $7mdmh$getEventTarget, getOwnerDocument as $7mdmh$getOwnerDocument, chain as $7mdmh$chain, isMac as $7mdmh$isMac, openLink as $7mdmh$openLink, isVirtualClick as $7mdmh$isVirtualClick, isVirtualPointerEvent as $7mdmh$isVirtualPointerEvent, focusWithoutScrolling as $7mdmh$focusWithoutScrolling, getOwnerWindow as $7mdmh$getOwnerWindow } from \"@react-aria/utils\";\nimport { flushSync as $7mdmh$flushSync } from \"react-dom\";\nimport { useContext as $7mdmh$useContext, useState as $7mdmh$useState, useRef as $7mdmh$useRef, useMemo as $7mdmh$useMemo, useEffect as $7mdmh$useEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nfunction $f6c31cce2adf654f$var$usePressResponderContext(props) {\n  // Consume context from <PressResponder> and merge with props.\n  let context = (0, $7mdmh$useContext)((0, $ae1eeba8b9eafd08$export$5165eccb35aaadb5));\n  if (context) {\n    let {\n      register: register,\n      ...contextProps\n    } = context;\n    props = (0, $7mdmh$mergeProps)(contextProps, props);\n    register();\n  }\n  (0, $7mdmh$useSyncRef)(context, props.ref);\n  return props;\n}\nvar $f6c31cce2adf654f$var$_shouldStopPropagation = /*#__PURE__*/new WeakMap();\nclass $f6c31cce2adf654f$var$PressEvent {\n  continuePropagation() {\n    (0, $7mdmh$_2)(this, $f6c31cce2adf654f$var$_shouldStopPropagation, false);\n  }\n  get shouldStopPropagation() {\n    return (0, $7mdmh$_)(this, $f6c31cce2adf654f$var$_shouldStopPropagation);\n  }\n  constructor(type, pointerType, originalEvent, state) {\n    (0, $7mdmh$_1)(this, $f6c31cce2adf654f$var$_shouldStopPropagation, {\n      writable: true,\n      value: void 0\n    });\n    (0, $7mdmh$_2)(this, $f6c31cce2adf654f$var$_shouldStopPropagation, true);\n    var _state_target;\n    let currentTarget = (_state_target = state === null || state === void 0 ? void 0 : state.target) !== null && _state_target !== void 0 ? _state_target : originalEvent.currentTarget;\n    const rect = currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.getBoundingClientRect();\n    let x,\n      y = 0;\n    let clientX,\n      clientY = null;\n    if (originalEvent.clientX != null && originalEvent.clientY != null) {\n      clientX = originalEvent.clientX;\n      clientY = originalEvent.clientY;\n    }\n    if (rect) {\n      if (clientX != null && clientY != null) {\n        x = clientX - rect.left;\n        y = clientY - rect.top;\n      } else {\n        x = rect.width / 2;\n        y = rect.height / 2;\n      }\n    }\n    this.type = type;\n    this.pointerType = pointerType;\n    this.target = originalEvent.currentTarget;\n    this.shiftKey = originalEvent.shiftKey;\n    this.metaKey = originalEvent.metaKey;\n    this.ctrlKey = originalEvent.ctrlKey;\n    this.altKey = originalEvent.altKey;\n    this.x = x;\n    this.y = y;\n  }\n}\nconst $f6c31cce2adf654f$var$LINK_CLICKED = Symbol('linkClicked');\nconst $f6c31cce2adf654f$var$STYLE_ID = 'react-aria-pressable-style';\nconst $f6c31cce2adf654f$var$PRESSABLE_ATTRIBUTE = 'data-react-aria-pressable';\nfunction $f6c31cce2adf654f$export$45712eceda6fad21(props) {\n  let {\n    onPress: onPress,\n    onPressChange: onPressChange,\n    onPressStart: onPressStart,\n    onPressEnd: onPressEnd,\n    onPressUp: onPressUp,\n    onClick: onClick,\n    isDisabled: isDisabled,\n    isPressed: isPressedProp,\n    preventFocusOnPress: preventFocusOnPress,\n    shouldCancelOnPointerExit: shouldCancelOnPointerExit,\n    allowTextSelectionOnPress: allowTextSelectionOnPress,\n    ref: domRef,\n    ...domProps\n  } = $f6c31cce2adf654f$var$usePressResponderContext(props);\n  let [isPressed, setPressed] = (0, $7mdmh$useState)(false);\n  let ref = (0, $7mdmh$useRef)({\n    isPressed: false,\n    ignoreEmulatedMouseEvents: false,\n    didFirePressStart: false,\n    isTriggeringEvent: false,\n    activePointerId: null,\n    target: null,\n    isOverTarget: false,\n    pointerType: null,\n    disposables: []\n  });\n  let {\n    addGlobalListener: addGlobalListener,\n    removeAllGlobalListeners: removeAllGlobalListeners\n  } = (0, $7mdmh$useGlobalListeners)();\n  let triggerPressStart = (0, $7mdmh$useEffectEvent)((originalEvent, pointerType) => {\n    let state = ref.current;\n    if (isDisabled || state.didFirePressStart) return false;\n    let shouldStopPropagation = true;\n    state.isTriggeringEvent = true;\n    if (onPressStart) {\n      let event = new $f6c31cce2adf654f$var$PressEvent('pressstart', pointerType, originalEvent);\n      onPressStart(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n    if (onPressChange) onPressChange(true);\n    state.isTriggeringEvent = false;\n    state.didFirePressStart = true;\n    setPressed(true);\n    return shouldStopPropagation;\n  });\n  let triggerPressEnd = (0, $7mdmh$useEffectEvent)((originalEvent, pointerType, wasPressed = true) => {\n    let state = ref.current;\n    if (!state.didFirePressStart) return false;\n    state.didFirePressStart = false;\n    state.isTriggeringEvent = true;\n    let shouldStopPropagation = true;\n    if (onPressEnd) {\n      let event = new $f6c31cce2adf654f$var$PressEvent('pressend', pointerType, originalEvent);\n      onPressEnd(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n    if (onPressChange) onPressChange(false);\n    setPressed(false);\n    if (onPress && wasPressed && !isDisabled) {\n      let event = new $f6c31cce2adf654f$var$PressEvent('press', pointerType, originalEvent);\n      onPress(event);\n      shouldStopPropagation && (shouldStopPropagation = event.shouldStopPropagation);\n    }\n    state.isTriggeringEvent = false;\n    return shouldStopPropagation;\n  });\n  let triggerPressUp = (0, $7mdmh$useEffectEvent)((originalEvent, pointerType) => {\n    let state = ref.current;\n    if (isDisabled) return false;\n    if (onPressUp) {\n      state.isTriggeringEvent = true;\n      let event = new $f6c31cce2adf654f$var$PressEvent('pressup', pointerType, originalEvent);\n      onPressUp(event);\n      state.isTriggeringEvent = false;\n      return event.shouldStopPropagation;\n    }\n    return true;\n  });\n  let cancel = (0, $7mdmh$useEffectEvent)(e => {\n    let state = ref.current;\n    if (state.isPressed && state.target) {\n      if (state.didFirePressStart && state.pointerType != null) triggerPressEnd($f6c31cce2adf654f$var$createEvent(state.target, e), state.pointerType, false);\n      state.isPressed = false;\n      state.isOverTarget = false;\n      state.activePointerId = null;\n      state.pointerType = null;\n      removeAllGlobalListeners();\n      if (!allowTextSelectionOnPress) (0, $14c0b72509d70225$export$b0d6fa1ab32e3295)(state.target);\n      for (let dispose of state.disposables) dispose();\n      state.disposables = [];\n    }\n  });\n  let cancelOnPointerExit = (0, $7mdmh$useEffectEvent)(e => {\n    if (shouldCancelOnPointerExit) cancel(e);\n  });\n  let triggerClick = (0, $7mdmh$useEffectEvent)(e => {\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  });\n  let triggerSyntheticClick = (0, $7mdmh$useEffectEvent)((e, target) => {\n    // Some third-party libraries pass in onClick instead of onPress.\n    // Create a fake mouse event and trigger onClick as well.\n    // This matches the browser's native activation behavior for certain elements (e.g. button).\n    // https://html.spec.whatwg.org/#activation\n    // https://html.spec.whatwg.org/#fire-a-synthetic-pointer-event\n    if (onClick) {\n      let event = new MouseEvent('click', e);\n      (0, $8a9cb279dc87e130$export$c2b7abe5d61ec696)(event, target);\n      onClick((0, $8a9cb279dc87e130$export$525bc4921d56d4a)(event));\n    }\n  });\n  let pressProps = (0, $7mdmh$useMemo)(() => {\n    let state = ref.current;\n    let pressProps = {\n      onKeyDown(e) {\n        if ($f6c31cce2adf654f$var$isValidKeyboardEvent(e.nativeEvent, e.currentTarget) && (0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) {\n          var _state_metaKeyEvents;\n          if ($f6c31cce2adf654f$var$shouldPreventDefaultKeyboard((0, $7mdmh$getEventTarget)(e.nativeEvent), e.key)) e.preventDefault();\n          // If the event is repeating, it may have started on a different element\n          // after which focus moved to the current element. Ignore these events and\n          // only handle the first key down event.\n          let shouldStopPropagation = true;\n          if (!state.isPressed && !e.repeat) {\n            state.target = e.currentTarget;\n            state.isPressed = true;\n            state.pointerType = 'keyboard';\n            shouldStopPropagation = triggerPressStart(e, 'keyboard');\n            // Focus may move before the key up event, so register the event on the document\n            // instead of the same element where the key down event occurred. Make it capturing so that it will trigger\n            // before stopPropagation from useKeyboard on a child element may happen and thus we can still call triggerPress for the parent element.\n            let originalTarget = e.currentTarget;\n            let pressUp = e => {\n              if ($f6c31cce2adf654f$var$isValidKeyboardEvent(e, originalTarget) && !e.repeat && (0, $7mdmh$nodeContains)(originalTarget, (0, $7mdmh$getEventTarget)(e)) && state.target) triggerPressUp($f6c31cce2adf654f$var$createEvent(state.target, e), 'keyboard');\n            };\n            addGlobalListener((0, $7mdmh$getOwnerDocument)(e.currentTarget), 'keyup', (0, $7mdmh$chain)(pressUp, onKeyUp), true);\n          }\n          if (shouldStopPropagation) e.stopPropagation();\n          // Keep track of the keydown events that occur while the Meta (e.g. Command) key is held.\n          // macOS has a bug where keyup events are not fired while the Meta key is down.\n          // When the Meta key itself is released we will get an event for that, and we'll act as if\n          // all of these other keys were released as well.\n          // https://bugs.chromium.org/p/chromium/issues/detail?id=1393524\n          // https://bugs.webkit.org/show_bug.cgi?id=55291\n          // https://bugzilla.mozilla.org/show_bug.cgi?id=1299553\n          if (e.metaKey && (0, $7mdmh$isMac)()) (_state_metaKeyEvents = state.metaKeyEvents) === null || _state_metaKeyEvents === void 0 ? void 0 : _state_metaKeyEvents.set(e.key, e.nativeEvent);\n        } else if (e.key === 'Meta') state.metaKeyEvents = new Map();\n      },\n      onClick(e) {\n        if (e && !(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        if (e && e.button === 0 && !state.isTriggeringEvent && !(0, $7mdmh$openLink).isOpening) {\n          let shouldStopPropagation = true;\n          if (isDisabled) e.preventDefault();\n          // If triggered from a screen reader or by using element.click(),\n          // trigger as if it were a keyboard click.\n          if (!state.ignoreEmulatedMouseEvents && !state.isPressed && (state.pointerType === 'virtual' || (0, $7mdmh$isVirtualClick)(e.nativeEvent))) {\n            let stopPressStart = triggerPressStart(e, 'virtual');\n            let stopPressUp = triggerPressUp(e, 'virtual');\n            let stopPressEnd = triggerPressEnd(e, 'virtual');\n            triggerClick(e);\n            shouldStopPropagation = stopPressStart && stopPressUp && stopPressEnd;\n          } else if (state.isPressed && state.pointerType !== 'keyboard') {\n            let pointerType = state.pointerType || e.nativeEvent.pointerType || 'virtual';\n            let stopPressUp = triggerPressUp($f6c31cce2adf654f$var$createEvent(e.currentTarget, e), pointerType);\n            let stopPressEnd = triggerPressEnd($f6c31cce2adf654f$var$createEvent(e.currentTarget, e), pointerType, true);\n            shouldStopPropagation = stopPressUp && stopPressEnd;\n            state.isOverTarget = false;\n            triggerClick(e);\n            cancel(e);\n          }\n          state.ignoreEmulatedMouseEvents = false;\n          if (shouldStopPropagation) e.stopPropagation();\n        }\n      }\n    };\n    let onKeyUp = e => {\n      var _state_metaKeyEvents;\n      if (state.isPressed && state.target && $f6c31cce2adf654f$var$isValidKeyboardEvent(e, state.target)) {\n        var _state_metaKeyEvents1;\n        if ($f6c31cce2adf654f$var$shouldPreventDefaultKeyboard((0, $7mdmh$getEventTarget)(e), e.key)) e.preventDefault();\n        let target = (0, $7mdmh$getEventTarget)(e);\n        let wasPressed = (0, $7mdmh$nodeContains)(state.target, (0, $7mdmh$getEventTarget)(e));\n        triggerPressEnd($f6c31cce2adf654f$var$createEvent(state.target, e), 'keyboard', wasPressed);\n        if (wasPressed) triggerSyntheticClick(e, state.target);\n        removeAllGlobalListeners();\n        // If a link was triggered with a key other than Enter, open the URL ourselves.\n        // This means the link has a role override, and the default browser behavior\n        // only applies when using the Enter key.\n        if (e.key !== 'Enter' && $f6c31cce2adf654f$var$isHTMLAnchorLink(state.target) && (0, $7mdmh$nodeContains)(state.target, target) && !e[$f6c31cce2adf654f$var$LINK_CLICKED]) {\n          // Store a hidden property on the event so we only trigger link click once,\n          // even if there are multiple usePress instances attached to the element.\n          e[$f6c31cce2adf654f$var$LINK_CLICKED] = true;\n          (0, $7mdmh$openLink)(state.target, e, false);\n        }\n        state.isPressed = false;\n        (_state_metaKeyEvents1 = state.metaKeyEvents) === null || _state_metaKeyEvents1 === void 0 ? void 0 : _state_metaKeyEvents1.delete(e.key);\n      } else if (e.key === 'Meta' && ((_state_metaKeyEvents = state.metaKeyEvents) === null || _state_metaKeyEvents === void 0 ? void 0 : _state_metaKeyEvents.size)) {\n        var _state_target;\n        // If we recorded keydown events that occurred while the Meta key was pressed,\n        // and those haven't received keyup events already, fire keyup events ourselves.\n        // See comment above for more info about the macOS bug causing this.\n        let events = state.metaKeyEvents;\n        state.metaKeyEvents = undefined;\n        for (let event of events.values()) (_state_target = state.target) === null || _state_target === void 0 ? void 0 : _state_target.dispatchEvent(new KeyboardEvent('keyup', event));\n      }\n    };\n    if (typeof PointerEvent !== 'undefined') {\n      pressProps.onPointerDown = e => {\n        // Only handle left clicks, and ignore events that bubbled through portals.\n        if (e.button !== 0 || !(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        // iOS safari fires pointer events from VoiceOver with incorrect coordinates/target.\n        // Ignore and let the onClick handler take care of it instead.\n        // https://bugs.webkit.org/show_bug.cgi?id=222627\n        // https://bugs.webkit.org/show_bug.cgi?id=223202\n        if ((0, $7mdmh$isVirtualPointerEvent)(e.nativeEvent)) {\n          state.pointerType = 'virtual';\n          return;\n        }\n        state.pointerType = e.pointerType;\n        let shouldStopPropagation = true;\n        if (!state.isPressed) {\n          state.isPressed = true;\n          state.isOverTarget = true;\n          state.activePointerId = e.pointerId;\n          state.target = e.currentTarget;\n          if (!allowTextSelectionOnPress) (0, $14c0b72509d70225$export$16a4697467175487)(state.target);\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n          // Release pointer capture so that touch interactions can leave the original target.\n          // This enables onPointerLeave and onPointerEnter to fire.\n          let target = (0, $7mdmh$getEventTarget)(e.nativeEvent);\n          if ('releasePointerCapture' in target) target.releasePointerCapture(e.pointerId);\n          addGlobalListener((0, $7mdmh$getOwnerDocument)(e.currentTarget), 'pointerup', onPointerUp, false);\n          addGlobalListener((0, $7mdmh$getOwnerDocument)(e.currentTarget), 'pointercancel', onPointerCancel, false);\n        }\n        if (shouldStopPropagation) e.stopPropagation();\n      };\n      pressProps.onMouseDown = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        if (e.button === 0) {\n          if (preventFocusOnPress) {\n            let dispose = (0, $8a9cb279dc87e130$export$cabe61c495ee3649)(e.target);\n            if (dispose) state.disposables.push(dispose);\n          }\n          e.stopPropagation();\n        }\n      };\n      pressProps.onPointerUp = e => {\n        // iOS fires pointerup with zero width and height, so check the pointerType recorded during pointerdown.\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent)) || state.pointerType === 'virtual') return;\n        // Only handle left clicks. If isPressed is true, delay until onClick.\n        if (e.button === 0 && !state.isPressed) triggerPressUp(e, state.pointerType || e.pointerType);\n      };\n      pressProps.onPointerEnter = e => {\n        if (e.pointerId === state.activePointerId && state.target && !state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = true;\n          triggerPressStart($f6c31cce2adf654f$var$createEvent(state.target, e), state.pointerType);\n        }\n      };\n      pressProps.onPointerLeave = e => {\n        if (e.pointerId === state.activePointerId && state.target && state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          triggerPressEnd($f6c31cce2adf654f$var$createEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n      };\n      let onPointerUp = e => {\n        if (e.pointerId === state.activePointerId && state.isPressed && e.button === 0 && state.target) {\n          if ((0, $7mdmh$nodeContains)(state.target, (0, $7mdmh$getEventTarget)(e)) && state.pointerType != null) {\n            // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n            // is mutated between onPointerUp and onClick, and is more compatible with third party libraries.\n            // https://github.com/adobe/react-spectrum/issues/1513\n            // https://issues.chromium.org/issues/40732224\n            // However, iOS and Android do not focus or fire onClick after a long press.\n            // We work around this by triggering a click ourselves after a timeout.\n            // This timeout is canceled during the click event in case the real one fires first.\n            // The timeout must be at least 32ms, because Safari on iOS delays the click event on\n            // non-form elements without certain ARIA roles (for hover emulation).\n            // https://github.com/WebKit/WebKit/blob/dccfae42bb29bd4bdef052e469f604a9387241c0/Source/WebKit/WebProcess/WebPage/ios/WebPageIOS.mm#L875-L892\n            let clicked = false;\n            let timeout = setTimeout(() => {\n              if (state.isPressed && state.target instanceof HTMLElement) {\n                if (clicked) cancel(e);else {\n                  (0, $7mdmh$focusWithoutScrolling)(state.target);\n                  state.target.click();\n                }\n              }\n            }, 80);\n            // Use a capturing listener to track if a click occurred.\n            // If stopPropagation is called it may never reach our handler.\n            addGlobalListener(e.currentTarget, 'click', () => clicked = true, true);\n            state.disposables.push(() => clearTimeout(timeout));\n          } else cancel(e);\n          // Ignore subsequent onPointerLeave event before onClick on touch devices.\n          state.isOverTarget = false;\n        }\n      };\n      let onPointerCancel = e => {\n        cancel(e);\n      };\n      pressProps.onDragStart = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        // Safari does not call onPointerCancel when a drag starts, whereas Chrome and Firefox do.\n        cancel(e);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      // NOTE: this fallback branch is entirely used by unit tests.\n      // All browsers now support pointer events, but JSDOM still does not.\n      pressProps.onMouseDown = e => {\n        // Only handle left clicks\n        if (e.button !== 0 || !(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        if (state.ignoreEmulatedMouseEvents) {\n          e.stopPropagation();\n          return;\n        }\n        state.isPressed = true;\n        state.isOverTarget = true;\n        state.target = e.currentTarget;\n        state.pointerType = (0, $7mdmh$isVirtualClick)(e.nativeEvent) ? 'virtual' : 'mouse';\n        // Flush sync so that focus moved during react re-renders occurs before we yield back to the browser.\n        let shouldStopPropagation = (0, $7mdmh$flushSync)(() => triggerPressStart(e, state.pointerType));\n        if (shouldStopPropagation) e.stopPropagation();\n        if (preventFocusOnPress) {\n          let dispose = (0, $8a9cb279dc87e130$export$cabe61c495ee3649)(e.target);\n          if (dispose) state.disposables.push(dispose);\n        }\n        addGlobalListener((0, $7mdmh$getOwnerDocument)(e.currentTarget), 'mouseup', onMouseUp, false);\n      };\n      pressProps.onMouseEnter = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = true;\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n        }\n        if (shouldStopPropagation) e.stopPropagation();\n      };\n      pressProps.onMouseLeave = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(e, state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n        if (shouldStopPropagation) e.stopPropagation();\n      };\n      pressProps.onMouseUp = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        if (!state.ignoreEmulatedMouseEvents && e.button === 0 && !state.isPressed) triggerPressUp(e, state.pointerType || 'mouse');\n      };\n      let onMouseUp = e => {\n        // Only handle left clicks\n        if (e.button !== 0) return;\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n          return;\n        }\n        if (state.target && state.target.contains(e.target) && state.pointerType != null) ;else cancel(e);\n        state.isOverTarget = false;\n      };\n      pressProps.onTouchStart = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        let touch = $f6c31cce2adf654f$var$getTouchFromEvent(e.nativeEvent);\n        if (!touch) return;\n        state.activePointerId = touch.identifier;\n        state.ignoreEmulatedMouseEvents = true;\n        state.isOverTarget = true;\n        state.isPressed = true;\n        state.target = e.currentTarget;\n        state.pointerType = 'touch';\n        if (!allowTextSelectionOnPress) (0, $14c0b72509d70225$export$16a4697467175487)(state.target);\n        let shouldStopPropagation = triggerPressStart($f6c31cce2adf654f$var$createTouchEvent(state.target, e), state.pointerType);\n        if (shouldStopPropagation) e.stopPropagation();\n        addGlobalListener((0, $7mdmh$getOwnerWindow)(e.currentTarget), 'scroll', onScroll, true);\n      };\n      pressProps.onTouchMove = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n        let touch = $f6c31cce2adf654f$var$getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && $f6c31cce2adf654f$var$isOverTarget(touch, e.currentTarget)) {\n          if (!state.isOverTarget && state.pointerType != null) {\n            state.isOverTarget = true;\n            shouldStopPropagation = triggerPressStart($f6c31cce2adf654f$var$createTouchEvent(state.target, e), state.pointerType);\n          }\n        } else if (state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd($f6c31cce2adf654f$var$createTouchEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit($f6c31cce2adf654f$var$createTouchEvent(state.target, e));\n        }\n        if (shouldStopPropagation) e.stopPropagation();\n      };\n      pressProps.onTouchEnd = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n        let touch = $f6c31cce2adf654f$var$getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && $f6c31cce2adf654f$var$isOverTarget(touch, e.currentTarget) && state.pointerType != null) {\n          triggerPressUp($f6c31cce2adf654f$var$createTouchEvent(state.target, e), state.pointerType);\n          shouldStopPropagation = triggerPressEnd($f6c31cce2adf654f$var$createTouchEvent(state.target, e), state.pointerType);\n          triggerSyntheticClick(e.nativeEvent, state.target);\n        } else if (state.isOverTarget && state.pointerType != null) shouldStopPropagation = triggerPressEnd($f6c31cce2adf654f$var$createTouchEvent(state.target, e), state.pointerType, false);\n        if (shouldStopPropagation) e.stopPropagation();\n        state.isPressed = false;\n        state.activePointerId = null;\n        state.isOverTarget = false;\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.target && !allowTextSelectionOnPress) (0, $14c0b72509d70225$export$b0d6fa1ab32e3295)(state.target);\n        removeAllGlobalListeners();\n      };\n      pressProps.onTouchCancel = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        e.stopPropagation();\n        if (state.isPressed) cancel($f6c31cce2adf654f$var$createTouchEvent(state.target, e));\n      };\n      let onScroll = e => {\n        if (state.isPressed && (0, $7mdmh$nodeContains)((0, $7mdmh$getEventTarget)(e), state.target)) cancel({\n          currentTarget: state.target,\n          shiftKey: false,\n          ctrlKey: false,\n          metaKey: false,\n          altKey: false\n        });\n      };\n      pressProps.onDragStart = e => {\n        if (!(0, $7mdmh$nodeContains)(e.currentTarget, (0, $7mdmh$getEventTarget)(e.nativeEvent))) return;\n        cancel(e);\n      };\n    }\n    return pressProps;\n  }, [addGlobalListener, isDisabled, preventFocusOnPress, removeAllGlobalListeners, allowTextSelectionOnPress, cancel, cancelOnPointerExit, triggerPressEnd, triggerPressStart, triggerPressUp, triggerClick, triggerSyntheticClick]);\n  // Avoid onClick delay for double tap to zoom by default.\n  (0, $7mdmh$useEffect)(() => {\n    if (!domRef || process.env.NODE_ENV === 'test') return;\n    const ownerDocument = (0, $7mdmh$getOwnerDocument)(domRef.current);\n    if (!ownerDocument || !ownerDocument.head || ownerDocument.getElementById($f6c31cce2adf654f$var$STYLE_ID)) return;\n    const style = ownerDocument.createElement('style');\n    style.id = $f6c31cce2adf654f$var$STYLE_ID;\n    // touchAction: 'manipulation' is supposed to be equivalent, but in\n    // Safari it causes onPointerCancel not to fire on scroll.\n    // https://bugs.webkit.org/show_bug.cgi?id=240917\n    style.textContent = `\n@layer {\n  [${$f6c31cce2adf654f$var$PRESSABLE_ATTRIBUTE}] {\n    touch-action: pan-x pan-y pinch-zoom;\n  }\n}\n    `.trim();\n    ownerDocument.head.prepend(style);\n  }, [domRef]);\n  // Remove user-select: none in case component unmounts immediately after pressStart\n  (0, $7mdmh$useEffect)(() => {\n    let state = ref.current;\n    return () => {\n      var _state_target;\n      if (!allowTextSelectionOnPress) (0, $14c0b72509d70225$export$b0d6fa1ab32e3295)((_state_target = state.target) !== null && _state_target !== void 0 ? _state_target : undefined);\n      for (let dispose of state.disposables) dispose();\n      state.disposables = [];\n    };\n  }, [allowTextSelectionOnPress]);\n  return {\n    isPressed: isPressedProp || isPressed,\n    pressProps: (0, $7mdmh$mergeProps)(domProps, pressProps, {\n      [$f6c31cce2adf654f$var$PRESSABLE_ATTRIBUTE]: true\n    })\n  };\n}\nfunction $f6c31cce2adf654f$var$isHTMLAnchorLink(target) {\n  return target.tagName === 'A' && target.hasAttribute('href');\n}\nfunction $f6c31cce2adf654f$var$isValidKeyboardEvent(event, currentTarget) {\n  const {\n    key: key,\n    code: code\n  } = event;\n  const element = currentTarget;\n  const role = element.getAttribute('role');\n  // Accessibility for keyboards. Space and Enter only.\n  // \"Spacebar\" is for IE 11\n  return (key === 'Enter' || key === ' ' || key === 'Spacebar' || code === 'Space') && !(element instanceof (0, $7mdmh$getOwnerWindow)(element).HTMLInputElement && !$f6c31cce2adf654f$var$isValidInputKey(element, key) || element instanceof (0, $7mdmh$getOwnerWindow)(element).HTMLTextAreaElement || element.isContentEditable) &&\n  // Links should only trigger with Enter key\n  !((role === 'link' || !role && $f6c31cce2adf654f$var$isHTMLAnchorLink(element)) && key !== 'Enter');\n}\nfunction $f6c31cce2adf654f$var$getTouchFromEvent(event) {\n  const {\n    targetTouches: targetTouches\n  } = event;\n  if (targetTouches.length > 0) return targetTouches[0];\n  return null;\n}\nfunction $f6c31cce2adf654f$var$getTouchById(event, pointerId) {\n  const changedTouches = event.changedTouches;\n  for (let i = 0; i < changedTouches.length; i++) {\n    const touch = changedTouches[i];\n    if (touch.identifier === pointerId) return touch;\n  }\n  return null;\n}\nfunction $f6c31cce2adf654f$var$createTouchEvent(target, e) {\n  let clientX = 0;\n  let clientY = 0;\n  if (e.targetTouches && e.targetTouches.length === 1) {\n    clientX = e.targetTouches[0].clientX;\n    clientY = e.targetTouches[0].clientY;\n  }\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX: clientX,\n    clientY: clientY\n  };\n}\nfunction $f6c31cce2adf654f$var$createEvent(target, e) {\n  let clientX = e.clientX;\n  let clientY = e.clientY;\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX: clientX,\n    clientY: clientY\n  };\n}\nfunction $f6c31cce2adf654f$var$getPointClientRect(point) {\n  let offsetX = 0;\n  let offsetY = 0;\n  if (point.width !== undefined) offsetX = point.width / 2;else if (point.radiusX !== undefined) offsetX = point.radiusX;\n  if (point.height !== undefined) offsetY = point.height / 2;else if (point.radiusY !== undefined) offsetY = point.radiusY;\n  return {\n    top: point.clientY - offsetY,\n    right: point.clientX + offsetX,\n    bottom: point.clientY + offsetY,\n    left: point.clientX - offsetX\n  };\n}\nfunction $f6c31cce2adf654f$var$areRectanglesOverlapping(a, b) {\n  // check if they cannot overlap on x axis\n  if (a.left > b.right || b.left > a.right) return false;\n  // check if they cannot overlap on y axis\n  if (a.top > b.bottom || b.top > a.bottom) return false;\n  return true;\n}\nfunction $f6c31cce2adf654f$var$isOverTarget(point, target) {\n  let rect = target.getBoundingClientRect();\n  let pointRect = $f6c31cce2adf654f$var$getPointClientRect(point);\n  return $f6c31cce2adf654f$var$areRectanglesOverlapping(rect, pointRect);\n}\nfunction $f6c31cce2adf654f$var$shouldPreventDefaultUp(target) {\n  if (target instanceof HTMLInputElement) return false;\n  if (target instanceof HTMLButtonElement) return target.type !== 'submit' && target.type !== 'reset';\n  if ($f6c31cce2adf654f$var$isHTMLAnchorLink(target)) return false;\n  return true;\n}\nfunction $f6c31cce2adf654f$var$shouldPreventDefaultKeyboard(target, key) {\n  if (target instanceof HTMLInputElement) return !$f6c31cce2adf654f$var$isValidInputKey(target, key);\n  return $f6c31cce2adf654f$var$shouldPreventDefaultUp(target);\n}\nconst $f6c31cce2adf654f$var$nonTextInputTypes = new Set(['checkbox', 'radio', 'range', 'color', 'file', 'image', 'button', 'submit', 'reset']);\nfunction $f6c31cce2adf654f$var$isValidInputKey(target, key) {\n  // Only space should toggle checkboxes and radios, not enter.\n  return target.type === 'checkbox' || target.type === 'radio' ? key === ' ' : $f6c31cce2adf654f$var$nonTextInputTypes.has(target.type);\n}\nexport { $f6c31cce2adf654f$export$45712eceda6fad21 as usePress };", "map": {"version": 3, "names": ["$f6c31cce2adf654f$var$usePressResponderContext", "props", "context", "$7mdmh$useContext", "$ae1eeba8b9eafd08$export$5165eccb35aaadb5", "register", "contextProps", "$7mdmh$mergeProps", "$7mdmh$useSyncRef", "ref", "$f6c31cce2adf654f$var$_shouldStopPropagation", "WeakMap", "$f6c31cce2adf654f$var$PressEvent", "continuePropagation", "shouldStopPropagation", "$7mdmh$_", "constructor", "type", "pointerType", "originalEvent", "state", "$7mdmh$_1", "_state_target", "currentTarget", "target", "rect", "getBoundingClientRect", "x", "y", "clientX", "clientY", "left", "top", "width", "height", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "altKey", "$f6c31cce2adf654f$var$LINK_CLICKED", "Symbol", "$f6c31cce2adf654f$var$STYLE_ID", "$f6c31cce2adf654f$var$PRESSABLE_ATTRIBUTE", "$f6c31cce2adf654f$export$45712eceda6fad21", "onPress", "onPressChange", "onPressStart", "onPressEnd", "onPressUp", "onClick", "isDisabled", "isPressed", "isPressedProp", "preventFocusOnPress", "shouldCancelOnPointerExit", "allowTextSelectionOnPress", "domRef", "domProps", "setPressed", "$7mdmh$useState", "$7mdmh$useRef", "ignoreEmulatedMouseEvents", "didFirePressStart", "isTriggeringEvent", "activePointerId", "isOverTarget", "disposables", "addGlobalListener", "removeAllGlobalListeners", "$7mdmh$useGlobalListeners", "triggerPressStart", "$7mdmh$useEffectEvent", "current", "event", "triggerPressEnd", "wasPressed", "triggerPressUp", "cancel", "e", "$f6c31cce2adf654f$var$createEvent", "$14c0b72509d70225$export$b0d6fa1ab32e3295", "dispose", "cancelOnPointerExit", "triggerClick", "triggerSyntheticClick", "MouseEvent", "$8a9cb279dc87e130$export$c2b7abe5d61ec696", "$8a9cb279dc87e130$export$525bc4921d56d4a", "pressProps", "$7mdmh$useMemo", "onKeyDown", "$f6c31cce2adf654f$var$isValidKeyboardEvent", "nativeEvent", "$7mdmh$nodeContains", "$7mdmh$getEventTarget", "_state_metaKeyEvents", "$f6c31cce2adf654f$var$shouldPreventDefaultKeyboard", "key", "preventDefault", "repeat", "originalTarget", "pressUp", "$7mdmh$getOwnerDocument", "$7mdmh$chain", "onKeyUp", "stopPropagation", "$7mdmh$isMac", "metaKeyEvents", "set", "Map", "button", "$7mdmh$openLink", "isOpening", "$7mdmh$isVirtualClick", "stopPressStart", "stopPressUp", "stopPressEnd", "_state_metaKeyEvents1", "$f6c31cce2adf654f$var$isHTMLAnchorLink", "delete", "size", "events", "undefined", "values", "dispatchEvent", "KeyboardEvent", "PointerEvent", "onPointerDown", "$7mdmh$isVirtualPointerEvent", "pointerId", "$14c0b72509d70225$export$16a4697467175487", "releasePointerCapture", "onPointerUp", "onPointerCancel", "onMouseDown", "$8a9cb279dc87e130$export$cabe61c495ee3649", "push", "onPointerEnter", "onPointerLeave", "clicked", "timeout", "setTimeout", "HTMLElement", "$7mdmh$focusWithoutScrolling", "click", "clearTimeout", "onDragStart", "process", "env", "NODE_ENV", "$7mdmh$flushSync", "onMouseUp", "onMouseEnter", "onMouseLeave", "contains", "onTouchStart", "touch", "$f6c31cce2adf654f$var$getTouchFromEvent", "identifier", "$f6c31cce2adf654f$var$createTouchEvent", "$7mdmh$getOwnerWindow", "onScroll", "onTouchMove", "$f6c31cce2adf654f$var$getTouchById", "$f6c31cce2adf654f$var$isOverTarget", "onTouchEnd", "onTouchCancel", "$7mdmh$useEffect", "ownerDocument", "head", "getElementById", "style", "createElement", "id", "textContent", "trim", "prepend", "tagName", "hasAttribute", "code", "element", "role", "getAttribute", "HTMLInputElement", "$f6c31cce2adf654f$var$isValidInputKey", "HTMLTextAreaElement", "isContentEditable", "targetTouches", "length", "changedTouches", "i", "$f6c31cce2adf654f$var$getPointClientRect", "point", "offsetX", "offsetY", "radiusX", "radiusY", "right", "bottom", "$f6c31cce2adf654f$var$areRectanglesOverlapping", "a", "b", "pointRect", "$f6c31cce2adf654f$var$shouldPreventDefaultUp", "HTMLButtonElement", "$f6c31cce2adf654f$var$nonTextInputTypes", "Set", "has"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\usePress.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {\n  chain,\n  focusWithoutScrolling,\n  getEventTarget,\n  getOwnerDocument,\n  getOwnerWindow,\n  isMac,\n  isVirtualClick,\n  isVirtualPointerEvent,\n  mergeProps,\n  nodeContains,\n  openLink,\n  useEffectEvent,\n  useGlobalListeners,\n  useSyncRef\n} from '@react-aria/utils';\nimport {createSyntheticEvent, preventFocus, setEventTarget} from './utils';\nimport {disableTextSelection, restoreTextSelection} from './textSelection';\nimport {DOMAttributes, FocusableElement, PressEvent as IPressEvent, PointerType, PressEvents, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {PressResponderContext} from './context';\nimport {MouseEvent as RMouseEvent, TouchEvent as RTouchEvent, useContext, useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface PressProps extends PressEvents {\n  /** Whether the target is in a controlled press state (e.g. an overlay it triggers is open). */\n  isPressed?: boolean,\n  /** Whether the press events should be disabled. */\n  isDisabled?: boolean,\n  /** Whether the target should not receive focus on press. */\n  preventFocusOnPress?: boolean,\n  /**\n   * Whether press events should be canceled when the pointer leaves the target while pressed.\n   * By default, this is `false`, which means if the pointer returns back over the target while\n   * still pressed, onPressStart will be fired again. If set to `true`, the press is canceled\n   * when the pointer leaves the target and onPressStart will not be fired if the pointer returns.\n   */\n  shouldCancelOnPointerExit?: boolean,\n  /** Whether text selection should be enabled on the pressable element. */\n  allowTextSelectionOnPress?: boolean\n}\n\nexport interface PressHookProps extends PressProps {\n  /** A ref to the target element. */\n  ref?: RefObject<Element | null>\n}\n\ninterface PressState {\n  isPressed: boolean,\n  ignoreEmulatedMouseEvents: boolean,\n  didFirePressStart: boolean,\n  isTriggeringEvent: boolean,\n  activePointerId: any,\n  target: FocusableElement | null,\n  isOverTarget: boolean,\n  pointerType: PointerType | null,\n  userSelect?: string,\n  metaKeyEvents?: Map<string, KeyboardEvent>,\n  disposables: Array<() => void>\n}\n\ninterface EventBase {\n  currentTarget: EventTarget | null,\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean,\n  clientX?: number,\n  clientY?: number,\n  targetTouches?: Array<{clientX?: number, clientY?: number}>\n}\n\nexport interface PressResult {\n  /** Whether the target is currently pressed. */\n  isPressed: boolean,\n  /** Props to spread on the target element. */\n  pressProps: DOMAttributes\n}\n\nfunction usePressResponderContext(props: PressHookProps): PressHookProps {\n  // Consume context from <PressResponder> and merge with props.\n  let context = useContext(PressResponderContext);\n  if (context) {\n    let {register, ...contextProps} = context;\n    props = mergeProps(contextProps, props) as PressHookProps;\n    register();\n  }\n  useSyncRef(context, props.ref);\n\n  return props;\n}\n\nclass PressEvent implements IPressEvent {\n  type: IPressEvent['type'];\n  pointerType: PointerType;\n  target: Element;\n  shiftKey: boolean;\n  ctrlKey: boolean;\n  metaKey: boolean;\n  altKey: boolean;\n  x: number;\n  y: number;\n  #shouldStopPropagation = true;\n\n  constructor(type: IPressEvent['type'], pointerType: PointerType, originalEvent: EventBase, state?: PressState) {\n    let currentTarget = state?.target ?? originalEvent.currentTarget;\n    const rect: DOMRect | undefined = (currentTarget as Element)?.getBoundingClientRect();\n    let x, y = 0;\n    let clientX, clientY: number | null = null;\n    if (originalEvent.clientX != null && originalEvent.clientY != null) {\n      clientX = originalEvent.clientX;\n      clientY = originalEvent.clientY;\n    }\n    if (rect) {\n      if (clientX != null && clientY != null) {\n        x = clientX - rect.left;\n        y = clientY - rect.top;\n      } else {\n        x = rect.width / 2;\n        y = rect.height / 2;\n      }\n    }\n    this.type = type;\n    this.pointerType = pointerType;\n    this.target = originalEvent.currentTarget as Element;\n    this.shiftKey = originalEvent.shiftKey;\n    this.metaKey = originalEvent.metaKey;\n    this.ctrlKey = originalEvent.ctrlKey;\n    this.altKey = originalEvent.altKey;\n    this.x = x;\n    this.y = y;\n  }\n\n  continuePropagation() {\n    this.#shouldStopPropagation = false;\n  }\n\n  get shouldStopPropagation() {\n    return this.#shouldStopPropagation;\n  }\n}\n\nconst LINK_CLICKED = Symbol('linkClicked');\nconst STYLE_ID = 'react-aria-pressable-style';\nconst PRESSABLE_ATTRIBUTE = 'data-react-aria-pressable';\n\n/**\n * Handles press interactions across mouse, touch, keyboard, and screen readers.\n * It normalizes behavior across browsers and platforms, and handles many nuances\n * of dealing with pointer and keyboard events.\n */\nexport function usePress(props: PressHookProps): PressResult {\n  let {\n    onPress,\n    onPressChange,\n    onPressStart,\n    onPressEnd,\n    onPressUp,\n    onClick,\n    isDisabled,\n    isPressed: isPressedProp,\n    preventFocusOnPress,\n    shouldCancelOnPointerExit,\n    allowTextSelectionOnPress,\n    ref: domRef,\n    ...domProps\n  } = usePressResponderContext(props);\n\n  let [isPressed, setPressed] = useState(false);\n  let ref = useRef<PressState>({\n    isPressed: false,\n    ignoreEmulatedMouseEvents: false,\n    didFirePressStart: false,\n    isTriggeringEvent: false,\n    activePointerId: null,\n    target: null,\n    isOverTarget: false,\n    pointerType: null,\n    disposables: []\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let triggerPressStart = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled || state.didFirePressStart) {\n      return false;\n    }\n\n    let shouldStopPropagation = true;\n    state.isTriggeringEvent = true;\n    if (onPressStart) {\n      let event = new PressEvent('pressstart', pointerType, originalEvent);\n      onPressStart(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(true);\n    }\n\n    state.isTriggeringEvent = false;\n    state.didFirePressStart = true;\n    setPressed(true);\n    return shouldStopPropagation;\n  });\n\n  let triggerPressEnd = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, wasPressed = true) => {\n    let state = ref.current;\n    if (!state.didFirePressStart) {\n      return false;\n    }\n\n    state.didFirePressStart = false;\n    state.isTriggeringEvent = true;\n\n    let shouldStopPropagation = true;\n    if (onPressEnd) {\n      let event = new PressEvent('pressend', pointerType, originalEvent);\n      onPressEnd(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(false);\n    }\n\n    setPressed(false);\n\n    if (onPress && wasPressed && !isDisabled) {\n      let event = new PressEvent('press', pointerType, originalEvent);\n      onPress(event);\n      shouldStopPropagation &&= event.shouldStopPropagation;\n    }\n\n    state.isTriggeringEvent = false;\n    return shouldStopPropagation;\n  });\n\n  let triggerPressUp = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled) {\n      return false;\n    }\n\n    if (onPressUp) {\n      state.isTriggeringEvent = true;\n      let event = new PressEvent('pressup', pointerType, originalEvent);\n      onPressUp(event);\n      state.isTriggeringEvent = false;\n      return event.shouldStopPropagation;\n    }\n\n    return true;\n  });\n\n  let cancel = useEffectEvent((e: EventBase) => {\n    let state = ref.current;\n    if (state.isPressed && state.target) {\n      if (state.didFirePressStart && state.pointerType != null) {\n        triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n      }\n      state.isPressed = false;\n      state.isOverTarget = false;\n      state.activePointerId = null;\n      state.pointerType = null;\n      removeAllGlobalListeners();\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    }\n  });\n\n  let cancelOnPointerExit = useEffectEvent((e: EventBase) => {\n    if (shouldCancelOnPointerExit) {\n      cancel(e);\n    }\n  });\n\n  let triggerClick = useEffectEvent((e: RMouseEvent<FocusableElement>) => {\n    onClick?.(e);\n  });\n\n  let triggerSyntheticClick = useEffectEvent((e: KeyboardEvent | TouchEvent, target: FocusableElement) => {\n    // Some third-party libraries pass in onClick instead of onPress.\n    // Create a fake mouse event and trigger onClick as well.\n    // This matches the browser's native activation behavior for certain elements (e.g. button).\n    // https://html.spec.whatwg.org/#activation\n    // https://html.spec.whatwg.org/#fire-a-synthetic-pointer-event\n    if (onClick) {\n      let event = new MouseEvent('click', e);\n      setEventTarget(event, target);\n      onClick(createSyntheticEvent(event));\n    }\n  });\n\n  let pressProps = useMemo(() => {\n    let state = ref.current;\n    let pressProps: DOMAttributes = {\n      onKeyDown(e) {\n        if (isValidKeyboardEvent(e.nativeEvent, e.currentTarget) && nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          if (shouldPreventDefaultKeyboard(getEventTarget(e.nativeEvent), e.key)) {\n            e.preventDefault();\n          }\n\n          // If the event is repeating, it may have started on a different element\n          // after which focus moved to the current element. Ignore these events and\n          // only handle the first key down event.\n          let shouldStopPropagation = true;\n          if (!state.isPressed && !e.repeat) {\n            state.target = e.currentTarget;\n            state.isPressed = true;\n            state.pointerType = 'keyboard';\n            shouldStopPropagation = triggerPressStart(e, 'keyboard');\n\n            // Focus may move before the key up event, so register the event on the document\n            // instead of the same element where the key down event occurred. Make it capturing so that it will trigger\n            // before stopPropagation from useKeyboard on a child element may happen and thus we can still call triggerPress for the parent element.\n            let originalTarget = e.currentTarget;\n            let pressUp = (e) => {\n              if (isValidKeyboardEvent(e, originalTarget) && !e.repeat && nodeContains(originalTarget, getEventTarget(e)) && state.target) {\n                triggerPressUp(createEvent(state.target, e), 'keyboard');\n              }\n            };\n\n            addGlobalListener(getOwnerDocument(e.currentTarget), 'keyup', chain(pressUp, onKeyUp), true);\n          }\n\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n\n          // Keep track of the keydown events that occur while the Meta (e.g. Command) key is held.\n          // macOS has a bug where keyup events are not fired while the Meta key is down.\n          // When the Meta key itself is released we will get an event for that, and we'll act as if\n          // all of these other keys were released as well.\n          // https://bugs.chromium.org/p/chromium/issues/detail?id=1393524\n          // https://bugs.webkit.org/show_bug.cgi?id=55291\n          // https://bugzilla.mozilla.org/show_bug.cgi?id=1299553\n          if (e.metaKey && isMac()) {\n            state.metaKeyEvents?.set(e.key, e.nativeEvent);\n          }\n        } else if (e.key === 'Meta') {\n          state.metaKeyEvents = new Map();\n        }\n      },\n      onClick(e) {\n        if (e && !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e && e.button === 0 && !state.isTriggeringEvent && !(openLink as any).isOpening) {\n          let shouldStopPropagation = true;\n          if (isDisabled) {\n            e.preventDefault();\n          }\n          \n          // If triggered from a screen reader or by using element.click(),\n          // trigger as if it were a keyboard click.\n          if (!state.ignoreEmulatedMouseEvents && !state.isPressed && (state.pointerType === 'virtual' || isVirtualClick(e.nativeEvent))) {\n            let stopPressStart = triggerPressStart(e, 'virtual');\n            let stopPressUp = triggerPressUp(e, 'virtual');\n            let stopPressEnd = triggerPressEnd(e, 'virtual');\n            triggerClick(e);\n            shouldStopPropagation = stopPressStart && stopPressUp && stopPressEnd;\n          } else if (state.isPressed && state.pointerType !== 'keyboard') {\n            let pointerType = state.pointerType || (e.nativeEvent as PointerEvent).pointerType as PointerType || 'virtual';\n            let stopPressUp = triggerPressUp(createEvent(e.currentTarget, e), pointerType);\n            let stopPressEnd =  triggerPressEnd(createEvent(e.currentTarget, e), pointerType, true);\n            shouldStopPropagation = stopPressUp && stopPressEnd;\n            state.isOverTarget = false;\n            triggerClick(e);\n            cancel(e);\n          }\n\n          state.ignoreEmulatedMouseEvents = false;\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n        }\n      }\n    };\n\n    let onKeyUp = (e: KeyboardEvent) => {\n      if (state.isPressed && state.target && isValidKeyboardEvent(e, state.target)) {\n        if (shouldPreventDefaultKeyboard(getEventTarget(e), e.key)) {\n          e.preventDefault();\n        }\n\n        let target = getEventTarget(e);\n        let wasPressed = nodeContains(state.target, getEventTarget(e));\n        triggerPressEnd(createEvent(state.target, e), 'keyboard', wasPressed);\n        if (wasPressed) {\n          triggerSyntheticClick(e, state.target);\n        }\n        removeAllGlobalListeners();\n\n        // If a link was triggered with a key other than Enter, open the URL ourselves.\n        // This means the link has a role override, and the default browser behavior\n        // only applies when using the Enter key.\n        if (e.key !== 'Enter' && isHTMLAnchorLink(state.target) && nodeContains(state.target, target) && !e[LINK_CLICKED]) {\n          // Store a hidden property on the event so we only trigger link click once,\n          // even if there are multiple usePress instances attached to the element.\n          e[LINK_CLICKED] = true;\n          openLink(state.target, e, false);\n        }\n\n        state.isPressed = false;\n        state.metaKeyEvents?.delete(e.key);\n      } else if (e.key === 'Meta' && state.metaKeyEvents?.size) {\n        // If we recorded keydown events that occurred while the Meta key was pressed,\n        // and those haven't received keyup events already, fire keyup events ourselves.\n        // See comment above for more info about the macOS bug causing this.\n        let events = state.metaKeyEvents;\n        state.metaKeyEvents = undefined;\n        for (let event of events.values()) {\n          state.target?.dispatchEvent(new KeyboardEvent('keyup', event));\n        }\n      }\n    };\n\n    if (typeof PointerEvent !== 'undefined') {\n      pressProps.onPointerDown = (e) => {\n        // Only handle left clicks, and ignore events that bubbled through portals.\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // iOS safari fires pointer events from VoiceOver with incorrect coordinates/target.\n        // Ignore and let the onClick handler take care of it instead.\n        // https://bugs.webkit.org/show_bug.cgi?id=222627\n        // https://bugs.webkit.org/show_bug.cgi?id=223202\n        if (isVirtualPointerEvent(e.nativeEvent)) {\n          state.pointerType = 'virtual';\n          return;\n        }\n\n        state.pointerType = e.pointerType;\n\n        let shouldStopPropagation = true;\n        if (!state.isPressed) {\n          state.isPressed = true;\n          state.isOverTarget = true;\n          state.activePointerId = e.pointerId;\n          state.target = e.currentTarget as FocusableElement;\n\n          if (!allowTextSelectionOnPress) {\n            disableTextSelection(state.target);\n          }\n\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n\n          // Release pointer capture so that touch interactions can leave the original target.\n          // This enables onPointerLeave and onPointerEnter to fire.\n          let target = getEventTarget(e.nativeEvent);\n          if ('releasePointerCapture' in target) {\n            target.releasePointerCapture(e.pointerId);\n          }\n\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointerup', onPointerUp, false);\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointercancel', onPointerCancel, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseDown = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e.button === 0) {\n          if (preventFocusOnPress) {\n            let dispose = preventFocus(e.target as FocusableElement);\n            if (dispose) {\n              state.disposables.push(dispose);\n            }\n          }\n\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onPointerUp = (e) => {\n        // iOS fires pointerup with zero width and height, so check the pointerType recorded during pointerdown.\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent)) || state.pointerType === 'virtual') {\n          return;\n        }\n\n        // Only handle left clicks. If isPressed is true, delay until onClick.\n        if (e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || e.pointerType);\n        }\n      };\n\n      pressProps.onPointerEnter = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && !state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = true;\n          triggerPressStart(createEvent(state.target, e), state.pointerType);\n        }\n      };\n\n      pressProps.onPointerLeave = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.activePointerId && state.isPressed && e.button === 0 && state.target) {\n          if (nodeContains(state.target, getEventTarget(e)) && state.pointerType != null) {\n            // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n            // is mutated between onPointerUp and onClick, and is more compatible with third party libraries.\n            // https://github.com/adobe/react-spectrum/issues/1513\n            // https://issues.chromium.org/issues/40732224\n            // However, iOS and Android do not focus or fire onClick after a long press.\n            // We work around this by triggering a click ourselves after a timeout.\n            // This timeout is canceled during the click event in case the real one fires first.\n            // The timeout must be at least 32ms, because Safari on iOS delays the click event on\n            // non-form elements without certain ARIA roles (for hover emulation).\n            // https://github.com/WebKit/WebKit/blob/dccfae42bb29bd4bdef052e469f604a9387241c0/Source/WebKit/WebProcess/WebPage/ios/WebPageIOS.mm#L875-L892\n            let clicked = false;\n            let timeout = setTimeout(() => {\n              if (state.isPressed && state.target instanceof HTMLElement) {\n                if (clicked) {\n                  cancel(e);\n                } else {\n                  focusWithoutScrolling(state.target);\n                  state.target.click();\n                }\n              }\n            }, 80);\n            // Use a capturing listener to track if a click occurred.\n            // If stopPropagation is called it may never reach our handler.\n            addGlobalListener(e.currentTarget as Document, 'click', () => clicked = true, true);\n            state.disposables.push(() => clearTimeout(timeout));\n          } else {\n            cancel(e);\n          }\n\n          // Ignore subsequent onPointerLeave event before onClick on touch devices.\n          state.isOverTarget = false;\n        }\n      };\n\n      let onPointerCancel = (e: PointerEvent) => {\n        cancel(e);\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // Safari does not call onPointerCancel when a drag starts, whereas Chrome and Firefox do.\n        cancel(e);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      // NOTE: this fallback branch is entirely used by unit tests.\n      // All browsers now support pointer events, but JSDOM still does not.\n\n      pressProps.onMouseDown = (e) => {\n        // Only handle left clicks\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          e.stopPropagation();\n          return;\n        }\n\n        state.isPressed = true;\n        state.isOverTarget = true;\n        state.target = e.currentTarget;\n        state.pointerType = isVirtualClick(e.nativeEvent) ? 'virtual' : 'mouse';\n\n        // Flush sync so that focus moved during react re-renders occurs before we yield back to the browser.\n        let shouldStopPropagation = flushSync(() => triggerPressStart(e, state.pointerType!));\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        if (preventFocusOnPress) {\n          let dispose = preventFocus(e.target as FocusableElement);\n          if (dispose) {\n            state.disposables.push(dispose);\n          }\n        }\n\n        addGlobalListener(getOwnerDocument(e.currentTarget), 'mouseup', onMouseUp, false);\n      };\n\n      pressProps.onMouseEnter = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = true;\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseLeave = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(e, state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseUp = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.ignoreEmulatedMouseEvents && e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || 'mouse');\n        }\n      };\n\n      let onMouseUp = (e: MouseEvent) => {\n        // Only handle left clicks\n        if (e.button !== 0) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n          return;\n        }\n\n        if (state.target && state.target.contains(e.target as Element) && state.pointerType != null) {\n          // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n          // is mutated between onMouseUp and onClick, and is more compatible with third party libraries.\n        } else {\n          cancel(e);\n        }\n\n        state.isOverTarget = false;\n      };\n\n      pressProps.onTouchStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let touch = getTouchFromEvent(e.nativeEvent);\n        if (!touch) {\n          return;\n        }\n        state.activePointerId = touch.identifier;\n        state.ignoreEmulatedMouseEvents = true;\n        state.isOverTarget = true;\n        state.isPressed = true;\n        state.target = e.currentTarget;\n        state.pointerType = 'touch';\n\n        if (!allowTextSelectionOnPress) {\n          disableTextSelection(state.target);\n        }\n\n        let shouldStopPropagation = triggerPressStart(createTouchEvent(state.target, e), state.pointerType);\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        addGlobalListener(getOwnerWindow(e.currentTarget), 'scroll', onScroll, true);\n      };\n\n      pressProps.onTouchMove = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget)) {\n          if (!state.isOverTarget && state.pointerType != null) {\n            state.isOverTarget = true;\n            shouldStopPropagation = triggerPressStart(createTouchEvent(state.target!, e), state.pointerType);\n          }\n        } else if (state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n          cancelOnPointerExit(createTouchEvent(state.target!, e));\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onTouchEnd = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget) && state.pointerType != null) {\n          triggerPressUp(createTouchEvent(state.target!, e), state.pointerType);\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType);\n          triggerSyntheticClick(e.nativeEvent, state.target!);\n        } else if (state.isOverTarget && state.pointerType != null) {\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        state.isPressed = false;\n        state.activePointerId = null;\n        state.isOverTarget = false;\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.target && !allowTextSelectionOnPress) {\n          restoreTextSelection(state.target);\n        }\n        removeAllGlobalListeners();\n      };\n\n      pressProps.onTouchCancel = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        e.stopPropagation();\n        if (state.isPressed) {\n          cancel(createTouchEvent(state.target!, e));\n        }\n      };\n\n      let onScroll = (e: Event) => {\n        if (state.isPressed && nodeContains(getEventTarget(e), state.target)) {\n          cancel({\n            currentTarget: state.target,\n            shiftKey: false,\n            ctrlKey: false,\n            metaKey: false,\n            altKey: false\n          });\n        }\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        cancel(e);\n      };\n    }\n\n    return pressProps;\n  }, [\n    addGlobalListener,\n    isDisabled,\n    preventFocusOnPress,\n    removeAllGlobalListeners,\n    allowTextSelectionOnPress,\n    cancel,\n    cancelOnPointerExit,\n    triggerPressEnd,\n    triggerPressStart,\n    triggerPressUp,\n    triggerClick,\n    triggerSyntheticClick\n  ]);\n\n  // Avoid onClick delay for double tap to zoom by default.\n  useEffect(() => {\n    if (!domRef || process.env.NODE_ENV === 'test') {\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(domRef.current);\n    if (!ownerDocument || !ownerDocument.head || ownerDocument.getElementById(STYLE_ID)) {\n      return;\n    }\n\n    const style = ownerDocument.createElement('style');\n    style.id = STYLE_ID;\n    // touchAction: 'manipulation' is supposed to be equivalent, but in\n    // Safari it causes onPointerCancel not to fire on scroll.\n    // https://bugs.webkit.org/show_bug.cgi?id=240917\n    style.textContent = `\n@layer {\n  [${PRESSABLE_ATTRIBUTE}] {\n    touch-action: pan-x pan-y pinch-zoom;\n  }\n}\n    `.trim();\n    ownerDocument.head.prepend(style);\n  }, [domRef]);\n\n  // Remove user-select: none in case component unmounts immediately after pressStart\n  useEffect(() => {\n    let state = ref.current;\n    return () => {\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target ?? undefined);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    };\n  }, [allowTextSelectionOnPress]);\n\n  return {\n    isPressed: isPressedProp || isPressed,\n    pressProps: mergeProps(domProps, pressProps, {[PRESSABLE_ATTRIBUTE]: true})\n  };\n}\n\nfunction isHTMLAnchorLink(target: Element): target is HTMLAnchorElement {\n  return target.tagName === 'A' && target.hasAttribute('href');\n}\n\nfunction isValidKeyboardEvent(event: KeyboardEvent, currentTarget: Element): boolean {\n  const {key, code} = event;\n  const element = currentTarget as HTMLElement;\n  const role = element.getAttribute('role');\n  // Accessibility for keyboards. Space and Enter only.\n  // \"Spacebar\" is for IE 11\n  return (\n    (key === 'Enter' || key === ' ' || key === 'Spacebar' || code === 'Space') &&\n    !((element instanceof getOwnerWindow(element).HTMLInputElement && !isValidInputKey(element, key)) ||\n      element instanceof getOwnerWindow(element).HTMLTextAreaElement ||\n      element.isContentEditable) &&\n    // Links should only trigger with Enter key\n    !((role === 'link' || (!role && isHTMLAnchorLink(element))) && key !== 'Enter')\n  );\n}\n\nfunction getTouchFromEvent(event: TouchEvent): Touch | null {\n  const {targetTouches} = event;\n  if (targetTouches.length > 0) {\n    return targetTouches[0];\n  }\n  return null;\n}\n\nfunction getTouchById(\n  event: TouchEvent,\n  pointerId: null | number\n): null | Touch {\n  const changedTouches = event.changedTouches;\n  for (let i = 0; i < changedTouches.length; i++) {\n    const touch = changedTouches[i];\n    if (touch.identifier === pointerId) {\n      return touch;\n    }\n  }\n  return null;\n}\n\nfunction createTouchEvent(target: FocusableElement, e: RTouchEvent<FocusableElement>): EventBase {\n  let clientX = 0;\n  let clientY = 0;\n  if (e.targetTouches && e.targetTouches.length === 1) {\n    clientX = e.targetTouches[0].clientX;\n    clientY = e.targetTouches[0].clientY;\n  }\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\nfunction createEvent(target: FocusableElement, e: EventBase): EventBase {\n  let clientX = e.clientX;\n  let clientY = e.clientY;\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\ninterface Rect {\n  top: number,\n  right: number,\n  bottom: number,\n  left: number\n}\n\ninterface EventPoint {\n  clientX: number,\n  clientY: number,\n  width?: number,\n  height?: number,\n  radiusX?: number,\n  radiusY?: number\n}\n\nfunction getPointClientRect(point: EventPoint): Rect {\n  let offsetX = 0;\n  let offsetY = 0;\n  if (point.width !== undefined) {\n    offsetX = (point.width / 2);\n  } else if (point.radiusX !== undefined) {\n    offsetX = point.radiusX;\n  }\n  if (point.height !== undefined) {\n    offsetY = (point.height / 2);\n  } else if (point.radiusY !== undefined) {\n    offsetY = point.radiusY;\n  }\n\n  return {\n    top: point.clientY - offsetY,\n    right: point.clientX + offsetX,\n    bottom: point.clientY + offsetY,\n    left: point.clientX - offsetX\n  };\n}\n\nfunction areRectanglesOverlapping(a: Rect, b: Rect) {\n  // check if they cannot overlap on x axis\n  if (a.left > b.right || b.left > a.right) {\n    return false;\n  }\n  // check if they cannot overlap on y axis\n  if (a.top > b.bottom || b.top > a.bottom) {\n    return false;\n  }\n  return true;\n}\n\nfunction isOverTarget(point: EventPoint, target: Element) {\n  let rect = target.getBoundingClientRect();\n  let pointRect = getPointClientRect(point);\n  return areRectanglesOverlapping(rect, pointRect);\n}\n\nfunction shouldPreventDefaultUp(target: Element) {\n  if (target instanceof HTMLInputElement) {\n    return false;\n  }\n\n  if (target instanceof HTMLButtonElement) {\n    return target.type !== 'submit' && target.type !== 'reset';\n  }\n\n  if (isHTMLAnchorLink(target)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction shouldPreventDefaultKeyboard(target: Element, key: string) {\n  if (target instanceof HTMLInputElement) {\n    return !isValidInputKey(target, key);\n  }\n\n  return shouldPreventDefaultUp(target);\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\nfunction isValidInputKey(target: HTMLInputElement, key: string) {\n  // Only space should toggle checkboxes and radios, not enter.\n  return target.type === 'checkbox' || target.type === 'radio'\n    ? key === ' '\n    : nonTextInputTypes.has(target.type);\n}\n"], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;;AAgFA,SAASA,+CAAyBC,KAAqB;EACrD;EACA,IAAIC,OAAA,GAAU,IAAAC,iBAAS,GAAE,GAAAC,yCAAoB;EAC7C,IAAIF,OAAA,EAAS;IACX,IAAI;MAAAG,QAAA,EAACA,QAAQ;MAAE,GAAGC;IAAA,CAAa,GAAGJ,OAAA;IAClCD,KAAA,GAAQ,IAAAM,iBAAS,EAAED,YAAA,EAAcL,KAAA;IACjCI,QAAA;EACF;EACA,IAAAG,iBAAS,EAAEN,OAAA,EAASD,KAAA,CAAMQ,GAAG;EAE7B,OAAOR,KAAA;AACT;IAYES,4CAAA,oBAAAC,OAAA;AAVF,MAAMC,gCAAA;EAyCJC,oBAAA,EAAsB;yBACfH,4CAAA,EAAyB;EAChC;EAEA,IAAII,sBAAA,EAAwB;IAC1B,WAAAC,QAAA,EAAO,IAAI,EAACL,4CAAA;EACd;EAnCAM,YAAYC,IAAyB,EAAEC,WAAwB,EAAEC,aAAwB,EAAEC,KAAkB,EAAE;IAF/G,IAAAC,SAAA,QAAAX,4CAAA;;aAAA;;yBAAAA,4CAAA,EAAyB;QAGHY,aAAA;IAApB,IAAIC,aAAA,GAAgB,CAAAD,aAAA,GAAAF,KAAA,aAAAA,KAAA,uBAAAA,KAAA,CAAOI,MAAM,cAAbF,aAAA,cAAAA,aAAA,GAAiBH,aAAA,CAAcI,aAAa;IAChE,MAAME,IAAA,GAA6BF,aAAA,aAAAA,aAAA,uBAADA,aAAC,CAA2BG,qBAAqB;IACnF,IAAIC,CAAA;MAAGC,CAAA,GAAI;IACX,IAAIC,OAAA;MAASC,OAAA,GAAyB;IACtC,IAAIX,aAAA,CAAcU,OAAO,IAAI,QAAQV,aAAA,CAAcW,OAAO,IAAI,MAAM;MAClED,OAAA,GAAUV,aAAA,CAAcU,OAAO;MAC/BC,OAAA,GAAUX,aAAA,CAAcW,OAAO;IACjC;IACA,IAAIL,IAAA;MACF,IAAII,OAAA,IAAW,QAAQC,OAAA,IAAW,MAAM;QACtCH,CAAA,GAAIE,OAAA,GAAUJ,IAAA,CAAKM,IAAI;QACvBH,CAAA,GAAIE,OAAA,GAAUL,IAAA,CAAKO,GAAG;MACxB,OAAO;QACLL,CAAA,GAAIF,IAAA,CAAKQ,KAAK,GAAG;QACjBL,CAAA,GAAIH,IAAA,CAAKS,MAAM,GAAG;MACpB;;IAEF,IAAI,CAACjB,IAAI,GAAGA,IAAA;IACZ,IAAI,CAACC,WAAW,GAAGA,WAAA;IACnB,IAAI,CAACM,MAAM,GAAGL,aAAA,CAAcI,aAAa;IACzC,IAAI,CAACY,QAAQ,GAAGhB,aAAA,CAAcgB,QAAQ;IACtC,IAAI,CAACC,OAAO,GAAGjB,aAAA,CAAciB,OAAO;IACpC,IAAI,CAACC,OAAO,GAAGlB,aAAA,CAAckB,OAAO;IACpC,IAAI,CAACC,MAAM,GAAGnB,aAAA,CAAcmB,MAAM;IAClC,IAAI,CAACX,CAAC,GAAGA,CAAA;IACT,IAAI,CAACC,CAAC,GAAGA,CAAA;EACX;AASF;AAEA,MAAMW,kCAAA,GAAeC,MAAA,CAAO;AAC5B,MAAMC,8BAAA,GAAW;AACjB,MAAMC,yCAAA,GAAsB;AAOrB,SAASC,0CAAS1C,KAAqB;EAC5C,IAAI;IAAA2C,OAAA,EACFA,OAAO;IAAAC,aAAA,EACPA,aAAa;IAAAC,YAAA,EACbA,YAAY;IAAAC,UAAA,EACZA,UAAU;IAAAC,SAAA,EACVA,SAAS;IAAAC,OAAA,EACTA,OAAO;IAAAC,UAAA,EACPA,UAAU;IACVC,SAAA,EAAWC,aAAa;IAAAC,mBAAA,EACxBA,mBAAmB;IAAAC,yBAAA,EACnBA,yBAAyB;IAAAC,yBAAA,EACzBA,yBAAyB;IACzB9C,GAAA,EAAK+C,MAAM;IACX,GAAGC;EAAA,CACJ,GAAGzD,8CAAA,CAAyBC,KAAA;EAE7B,IAAI,CAACkD,SAAA,EAAWO,UAAA,CAAW,GAAG,IAAAC,eAAO,EAAE;EACvC,IAAIlD,GAAA,GAAM,IAAAmD,aAAK,EAAc;IAC3BT,SAAA,EAAW;IACXU,yBAAA,EAA2B;IAC3BC,iBAAA,EAAmB;IACnBC,iBAAA,EAAmB;IACnBC,eAAA,EAAiB;IACjBxC,MAAA,EAAQ;IACRyC,YAAA,EAAc;IACd/C,WAAA,EAAa;IACbgD,WAAA,EAAa;EACf;EAEA,IAAI;IAAAC,iBAAA,EAACA,iBAAiB;IAAAC,wBAAA,EAAEA;EAAwB,CAAC,GAAG,IAAAC,yBAAiB;EAErE,IAAIC,iBAAA,GAAoB,IAAAC,qBAAa,EAAE,CAACpD,aAAA,EAA0BD,WAAA;IAChE,IAAIE,KAAA,GAAQX,GAAA,CAAI+D,OAAO;IACvB,IAAItB,UAAA,IAAc9B,KAAA,CAAM0C,iBAAiB,EACvC,OAAO;IAGT,IAAIhD,qBAAA,GAAwB;IAC5BM,KAAA,CAAM2C,iBAAiB,GAAG;IAC1B,IAAIjB,YAAA,EAAc;MAChB,IAAI2B,KAAA,GAAQ,IAAI7D,gCAAA,CAAW,cAAcM,WAAA,EAAaC,aAAA;MACtD2B,YAAA,CAAa2B,KAAA;MACb3D,qBAAA,GAAwB2D,KAAA,CAAM3D,qBAAqB;IACrD;IAEA,IAAI+B,aAAA,EACFA,aAAA,CAAc;IAGhBzB,KAAA,CAAM2C,iBAAiB,GAAG;IAC1B3C,KAAA,CAAM0C,iBAAiB,GAAG;IAC1BJ,UAAA,CAAW;IACX,OAAO5C,qBAAA;EACT;EAEA,IAAI4D,eAAA,GAAkB,IAAAH,qBAAa,EAAE,CAACpD,aAAA,EAA0BD,WAAA,EAA0ByD,UAAA,GAAa,IAAI;IACzG,IAAIvD,KAAA,GAAQX,GAAA,CAAI+D,OAAO;IACvB,IAAI,CAACpD,KAAA,CAAM0C,iBAAiB,EAC1B,OAAO;IAGT1C,KAAA,CAAM0C,iBAAiB,GAAG;IAC1B1C,KAAA,CAAM2C,iBAAiB,GAAG;IAE1B,IAAIjD,qBAAA,GAAwB;IAC5B,IAAIiC,UAAA,EAAY;MACd,IAAI0B,KAAA,GAAQ,IAAI7D,gCAAA,CAAW,YAAYM,WAAA,EAAaC,aAAA;MACpD4B,UAAA,CAAW0B,KAAA;MACX3D,qBAAA,GAAwB2D,KAAA,CAAM3D,qBAAqB;IACrD;IAEA,IAAI+B,aAAA,EACFA,aAAA,CAAc;IAGhBa,UAAA,CAAW;IAEX,IAAId,OAAA,IAAW+B,UAAA,IAAc,CAACzB,UAAA,EAAY;MACxC,IAAIuB,KAAA,GAAQ,IAAI7D,gCAAA,CAAW,SAASM,WAAA,EAAaC,aAAA;MACjDyB,OAAA,CAAQ6B,KAAA;MACR3D,qBAAA,KAAAA,qBAAA,GAA0B2D,KAAA,CAAM3D,qBAAqB;IACvD;IAEAM,KAAA,CAAM2C,iBAAiB,GAAG;IAC1B,OAAOjD,qBAAA;EACT;EAEA,IAAI8D,cAAA,GAAiB,IAAAL,qBAAa,EAAE,CAACpD,aAAA,EAA0BD,WAAA;IAC7D,IAAIE,KAAA,GAAQX,GAAA,CAAI+D,OAAO;IACvB,IAAItB,UAAA,EACF,OAAO;IAGT,IAAIF,SAAA,EAAW;MACb5B,KAAA,CAAM2C,iBAAiB,GAAG;MAC1B,IAAIU,KAAA,GAAQ,IAAI7D,gCAAA,CAAW,WAAWM,WAAA,EAAaC,aAAA;MACnD6B,SAAA,CAAUyB,KAAA;MACVrD,KAAA,CAAM2C,iBAAiB,GAAG;MAC1B,OAAOU,KAAA,CAAM3D,qBAAqB;IACpC;IAEA,OAAO;EACT;EAEA,IAAI+D,MAAA,GAAS,IAAAN,qBAAa,EAAGO,CAAA;IAC3B,IAAI1D,KAAA,GAAQX,GAAA,CAAI+D,OAAO;IACvB,IAAIpD,KAAA,CAAM+B,SAAS,IAAI/B,KAAA,CAAMI,MAAM,EAAE;MACnC,IAAIJ,KAAA,CAAM0C,iBAAiB,IAAI1C,KAAA,CAAMF,WAAW,IAAI,MAClDwD,eAAA,CAAgBK,iCAAA,CAAY3D,KAAA,CAAMI,MAAM,EAAEsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW,EAAE;MAEnEE,KAAA,CAAM+B,SAAS,GAAG;MAClB/B,KAAA,CAAM6C,YAAY,GAAG;MACrB7C,KAAA,CAAM4C,eAAe,GAAG;MACxB5C,KAAA,CAAMF,WAAW,GAAG;MACpBkD,wBAAA;MACA,IAAI,CAACb,yBAAA,EACH,IAAAyB,yCAAmB,EAAE5D,KAAA,CAAMI,MAAM;MAEnC,KAAK,IAAIyD,OAAA,IAAW7D,KAAA,CAAM8C,WAAW,EACnCe,OAAA;MAEF7D,KAAA,CAAM8C,WAAW,GAAG,EAAE;IACxB;EACF;EAEA,IAAIgB,mBAAA,GAAsB,IAAAX,qBAAa,EAAGO,CAAA;IACxC,IAAIxB,yBAAA,EACFuB,MAAA,CAAOC,CAAA;EAEX;EAEA,IAAIK,YAAA,GAAe,IAAAZ,qBAAa,EAAGO,CAAA;IACjC7B,OAAA,aAAAA,OAAA,uBAAAA,OAAA,CAAU6B,CAAA;EACZ;EAEA,IAAIM,qBAAA,GAAwB,IAAAb,qBAAa,EAAE,CAACO,CAAA,EAA+BtD,MAAA;IACzE;IACA;IACA;IACA;IACA;IACA,IAAIyB,OAAA,EAAS;MACX,IAAIwB,KAAA,GAAQ,IAAIY,UAAA,CAAW,SAASP,CAAA;MACpC,IAAAQ,yCAAa,EAAEb,KAAA,EAAOjD,MAAA;MACtByB,OAAA,CAAQ,IAAAsC,wCAAmB,EAAEd,KAAA;IAC/B;EACF;EAEA,IAAIe,UAAA,GAAa,IAAAC,cAAM,EAAE;IACvB,IAAIrE,KAAA,GAAQX,GAAA,CAAI+D,OAAO;IACvB,IAAIgB,UAAA,GAA4B;MAC9BE,UAAUZ,CAAC;QACT,IAAIa,0CAAA,CAAqBb,CAAA,CAAEc,WAAW,EAAEd,CAAA,CAAEvD,aAAa,KAAK,IAAAsE,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAAI;cAwCtHG,oBAAA;UAvCF,IAAIC,kDAAA,CAA6B,IAAAF,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,GAAGd,CAAA,CAAEmB,GAAG,GACnEnB,CAAA,CAAEoB,cAAc;UAGlB;UACA;UACA;UACA,IAAIpF,qBAAA,GAAwB;UAC5B,IAAI,CAACM,KAAA,CAAM+B,SAAS,IAAI,CAAC2B,CAAA,CAAEqB,MAAM,EAAE;YACjC/E,KAAA,CAAMI,MAAM,GAAGsD,CAAA,CAAEvD,aAAa;YAC9BH,KAAA,CAAM+B,SAAS,GAAG;YAClB/B,KAAA,CAAMF,WAAW,GAAG;YACpBJ,qBAAA,GAAwBwD,iBAAA,CAAkBQ,CAAA,EAAG;YAE7C;YACA;YACA;YACA,IAAIsB,cAAA,GAAiBtB,CAAA,CAAEvD,aAAa;YACpC,IAAI8E,OAAA,GAAWvB,CAAA;cACb,IAAIa,0CAAA,CAAqBb,CAAA,EAAGsB,cAAA,KAAmB,CAACtB,CAAA,CAAEqB,MAAM,IAAI,IAAAN,mBAAW,EAAEO,cAAA,EAAgB,IAAAN,qBAAa,EAAEhB,CAAA,MAAO1D,KAAA,CAAMI,MAAM,EACzHoD,cAAA,CAAeG,iCAAA,CAAY3D,KAAA,CAAMI,MAAM,EAAEsD,CAAA,GAAI;YAEjD;YAEAX,iBAAA,CAAkB,IAAAmC,uBAAe,EAAExB,CAAA,CAAEvD,aAAa,GAAG,SAAS,IAAAgF,YAAI,EAAEF,OAAA,EAASG,OAAA,GAAU;UACzF;UAEA,IAAI1F,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;UAGnB;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI3B,CAAA,CAAE1C,OAAO,IAAI,IAAAsE,YAAI,MACnBX,oBAAA,GAAA3E,KAAA,CAAMuF,aAAa,cAAnBZ,oBAAA,uBAAAA,oBAAA,CAAqBa,GAAG,CAAC9B,CAAA,CAAEmB,GAAG,EAAEnB,CAAA,CAAEc,WAAW;QAEjD,OAAO,IAAId,CAAA,CAAEmB,GAAG,KAAK,QACnB7E,KAAA,CAAMuF,aAAa,GAAG,IAAIE,GAAA;MAE9B;MACA5D,QAAQ6B,CAAC;QACP,IAAIA,CAAA,IAAK,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAClE;QAGF,IAAId,CAAA,IAAKA,CAAA,CAAEgC,MAAM,KAAK,KAAK,CAAC1F,KAAA,CAAM2C,iBAAiB,IAAI,CAAC,CAAC,GAAAgD,eAAO,EAAUC,SAAS,EAAE;UACnF,IAAIlG,qBAAA,GAAwB;UAC5B,IAAIoC,UAAA,EACF4B,CAAA,CAAEoB,cAAc;UAGlB;UACA;UACA,IAAI,CAAC9E,KAAA,CAAMyC,yBAAyB,IAAI,CAACzC,KAAA,CAAM+B,SAAS,KAAK/B,KAAA,CAAMF,WAAW,KAAK,aAAa,IAAA+F,qBAAa,EAAEnC,CAAA,CAAEc,WAAW,IAAI;YAC9H,IAAIsB,cAAA,GAAiB5C,iBAAA,CAAkBQ,CAAA,EAAG;YAC1C,IAAIqC,WAAA,GAAcvC,cAAA,CAAeE,CAAA,EAAG;YACpC,IAAIsC,YAAA,GAAe1C,eAAA,CAAgBI,CAAA,EAAG;YACtCK,YAAA,CAAaL,CAAA;YACbhE,qBAAA,GAAwBoG,cAAA,IAAkBC,WAAA,IAAeC,YAAA;UAC3D,OAAO,IAAIhG,KAAA,CAAM+B,SAAS,IAAI/B,KAAA,CAAMF,WAAW,KAAK,YAAY;YAC9D,IAAIA,WAAA,GAAcE,KAAA,CAAMF,WAAW,IAAI4D,CAAC,CAAEc,WAAW,CAAkB1E,WAAW,IAAmB;YACrG,IAAIiG,WAAA,GAAcvC,cAAA,CAAeG,iCAAA,CAAYD,CAAA,CAAEvD,aAAa,EAAEuD,CAAA,GAAI5D,WAAA;YAClE,IAAIkG,YAAA,GAAgB1C,eAAA,CAAgBK,iCAAA,CAAYD,CAAA,CAAEvD,aAAa,EAAEuD,CAAA,GAAI5D,WAAA,EAAa;YAClFJ,qBAAA,GAAwBqG,WAAA,IAAeC,YAAA;YACvChG,KAAA,CAAM6C,YAAY,GAAG;YACrBkB,YAAA,CAAaL,CAAA;YACbD,MAAA,CAAOC,CAAA;UACT;UAEA1D,KAAA,CAAMyC,yBAAyB,GAAG;UAClC,IAAI/C,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;QAErB;MACF;IACF;IAEA,IAAID,OAAA,GAAW1B,CAAA;UA0BkBiB,oBAAA;MAzB/B,IAAI3E,KAAA,CAAM+B,SAAS,IAAI/B,KAAA,CAAMI,MAAM,IAAImE,0CAAA,CAAqBb,CAAA,EAAG1D,KAAA,CAAMI,MAAM,GAAG;YAwB5E6F,qBAAA;QAvBA,IAAIrB,kDAAA,CAA6B,IAAAF,qBAAa,EAAEhB,CAAA,GAAIA,CAAA,CAAEmB,GAAG,GACvDnB,CAAA,CAAEoB,cAAc;QAGlB,IAAI1E,MAAA,GAAS,IAAAsE,qBAAa,EAAEhB,CAAA;QAC5B,IAAIH,UAAA,GAAa,IAAAkB,mBAAW,EAAEzE,KAAA,CAAMI,MAAM,EAAE,IAAAsE,qBAAa,EAAEhB,CAAA;QAC3DJ,eAAA,CAAgBK,iCAAA,CAAY3D,KAAA,CAAMI,MAAM,EAAEsD,CAAA,GAAI,YAAYH,UAAA;QAC1D,IAAIA,UAAA,EACFS,qBAAA,CAAsBN,CAAA,EAAG1D,KAAA,CAAMI,MAAM;QAEvC4C,wBAAA;QAEA;QACA;QACA;QACA,IAAIU,CAAA,CAAEmB,GAAG,KAAK,WAAWqB,sCAAA,CAAiBlG,KAAA,CAAMI,MAAM,KAAK,IAAAqE,mBAAW,EAAEzE,KAAA,CAAMI,MAAM,EAAEA,MAAA,KAAW,CAACsD,CAAC,CAACvC,kCAAA,CAAa,EAAE;UACjH;UACA;UACAuC,CAAC,CAACvC,kCAAA,CAAa,GAAG;UAClB,IAAAwE,eAAO,EAAE3F,KAAA,CAAMI,MAAM,EAAEsD,CAAA,EAAG;QAC5B;QAEA1D,KAAA,CAAM+B,SAAS,GAAG;SAClBkE,qBAAA,GAAAjG,KAAA,CAAMuF,aAAa,cAAnBU,qBAAA,uBAAAA,qBAAA,CAAqBE,MAAM,CAACzC,CAAA,CAAEmB,GAAG;MACnC,OAAO,IAAInB,CAAA,CAAEmB,GAAG,KAAK,YAAUF,oBAAA,GAAA3E,KAAA,CAAMuF,aAAa,cAAnBZ,oBAAA,uBAAAA,oBAAA,CAAqByB,IAAI,GAAE;YAOtDlG,aAAA;QANF;QACA;QACA;QACA,IAAImG,MAAA,GAASrG,KAAA,CAAMuF,aAAa;QAChCvF,KAAA,CAAMuF,aAAa,GAAGe,SAAA;QACtB,KAAK,IAAIjD,KAAA,IAASgD,MAAA,CAAOE,MAAM,KAC7BrG,aAAA,GAAAF,KAAA,CAAMI,MAAM,cAAZF,aAAA,uBAAAA,aAAA,CAAcsG,aAAa,CAAC,IAAIC,aAAA,CAAc,SAASpD,KAAA;MAE3D;IACF;IAEA,IAAI,OAAOqD,YAAA,KAAiB,aAAa;MACvCtC,UAAA,CAAWuC,aAAa,GAAIjD,CAAA;QAC1B;QACA,IAAIA,CAAA,CAAEgC,MAAM,KAAK,KAAK,CAAC,IAAAjB,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC/E;QAGF;QACA;QACA;QACA;QACA,IAAI,IAAAoC,4BAAoB,EAAElD,CAAA,CAAEc,WAAW,GAAG;UACxCxE,KAAA,CAAMF,WAAW,GAAG;UACpB;QACF;QAEAE,KAAA,CAAMF,WAAW,GAAG4D,CAAA,CAAE5D,WAAW;QAEjC,IAAIJ,qBAAA,GAAwB;QAC5B,IAAI,CAACM,KAAA,CAAM+B,SAAS,EAAE;UACpB/B,KAAA,CAAM+B,SAAS,GAAG;UAClB/B,KAAA,CAAM6C,YAAY,GAAG;UACrB7C,KAAA,CAAM4C,eAAe,GAAGc,CAAA,CAAEmD,SAAS;UACnC7G,KAAA,CAAMI,MAAM,GAAGsD,CAAA,CAAEvD,aAAa;UAE9B,IAAI,CAACgC,yBAAA,EACH,IAAA2E,yCAAmB,EAAE9G,KAAA,CAAMI,MAAM;UAGnCV,qBAAA,GAAwBwD,iBAAA,CAAkBQ,CAAA,EAAG1D,KAAA,CAAMF,WAAW;UAE9D;UACA;UACA,IAAIM,MAAA,GAAS,IAAAsE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW;UACzC,IAAI,2BAA2BpE,MAAA,EAC7BA,MAAA,CAAO2G,qBAAqB,CAACrD,CAAA,CAAEmD,SAAS;UAG1C9D,iBAAA,CAAkB,IAAAmC,uBAAe,EAAExB,CAAA,CAAEvD,aAAa,GAAG,aAAa6G,WAAA,EAAa;UAC/EjE,iBAAA,CAAkB,IAAAmC,uBAAe,EAAExB,CAAA,CAAEvD,aAAa,GAAG,iBAAiB8G,eAAA,EAAiB;QACzF;QAEA,IAAIvH,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;MAErB;MAEAjB,UAAA,CAAW8C,WAAW,GAAIxD,CAAA;QACxB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAId,CAAA,CAAEgC,MAAM,KAAK,GAAG;UAClB,IAAIzD,mBAAA,EAAqB;YACvB,IAAI4B,OAAA,GAAU,IAAAsD,yCAAW,EAAEzD,CAAA,CAAEtD,MAAM;YACnC,IAAIyD,OAAA,EACF7D,KAAA,CAAM8C,WAAW,CAACsE,IAAI,CAACvD,OAAA;UAE3B;UAEAH,CAAA,CAAE2B,eAAe;QACnB;MACF;MAEAjB,UAAA,CAAW4C,WAAW,GAAItD,CAAA;QACxB;QACA,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,MAAMxE,KAAA,CAAMF,WAAW,KAAK,WACzF;QAGF;QACA,IAAI4D,CAAA,CAAEgC,MAAM,KAAK,KAAK,CAAC1F,KAAA,CAAM+B,SAAS,EACpCyB,cAAA,CAAeE,CAAA,EAAG1D,KAAA,CAAMF,WAAW,IAAI4D,CAAA,CAAE5D,WAAW;MAExD;MAEAsE,UAAA,CAAWiD,cAAc,GAAI3D,CAAA;QAC3B,IAAIA,CAAA,CAAEmD,SAAS,KAAK7G,KAAA,CAAM4C,eAAe,IAAI5C,KAAA,CAAMI,MAAM,IAAI,CAACJ,KAAA,CAAM6C,YAAY,IAAI7C,KAAA,CAAMF,WAAW,IAAI,MAAM;UAC7GE,KAAA,CAAM6C,YAAY,GAAG;UACrBK,iBAAA,CAAkBS,iCAAA,CAAY3D,KAAA,CAAMI,MAAM,EAAEsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW;QACnE;MACF;MAEAsE,UAAA,CAAWkD,cAAc,GAAI5D,CAAA;QAC3B,IAAIA,CAAA,CAAEmD,SAAS,KAAK7G,KAAA,CAAM4C,eAAe,IAAI5C,KAAA,CAAMI,MAAM,IAAIJ,KAAA,CAAM6C,YAAY,IAAI7C,KAAA,CAAMF,WAAW,IAAI,MAAM;UAC5GE,KAAA,CAAM6C,YAAY,GAAG;UACrBS,eAAA,CAAgBK,iCAAA,CAAY3D,KAAA,CAAMI,MAAM,EAAEsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW,EAAE;UACjEgE,mBAAA,CAAoBJ,CAAA;QACtB;MACF;MAEA,IAAIsD,WAAA,GAAetD,CAAA;QACjB,IAAIA,CAAA,CAAEmD,SAAS,KAAK7G,KAAA,CAAM4C,eAAe,IAAI5C,KAAA,CAAM+B,SAAS,IAAI2B,CAAA,CAAEgC,MAAM,KAAK,KAAK1F,KAAA,CAAMI,MAAM,EAAE;UAC9F,IAAI,IAAAqE,mBAAW,EAAEzE,KAAA,CAAMI,MAAM,EAAE,IAAAsE,qBAAa,EAAEhB,CAAA,MAAO1D,KAAA,CAAMF,WAAW,IAAI,MAAM;YAC9E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIyH,OAAA,GAAU;YACd,IAAIC,OAAA,GAAUC,UAAA,CAAW;cACvB,IAAIzH,KAAA,CAAM+B,SAAS,IAAI/B,KAAA,CAAMI,MAAM,YAAYsH,WAAA;gBAC7C,IAAIH,OAAA,EACF9D,MAAA,CAAOC,CAAA,OACF;kBACL,IAAAiE,4BAAoB,EAAE3H,KAAA,CAAMI,MAAM;kBAClCJ,KAAA,CAAMI,MAAM,CAACwH,KAAK;gBACpB;;YAEJ,GAAG;YACH;YACA;YACA7E,iBAAA,CAAkBW,CAAA,CAAEvD,aAAa,EAAc,SAAS,MAAMoH,OAAA,GAAU,MAAM;YAC9EvH,KAAA,CAAM8C,WAAW,CAACsE,IAAI,CAAC,MAAMS,YAAA,CAAaL,OAAA;UAC5C,OACE/D,MAAA,CAAOC,CAAA;UAGT;UACA1D,KAAA,CAAM6C,YAAY,GAAG;QACvB;MACF;MAEA,IAAIoE,eAAA,GAAmBvD,CAAA;QACrBD,MAAA,CAAOC,CAAA;MACT;MAEAU,UAAA,CAAW0D,WAAW,GAAIpE,CAAA;QACxB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF;QACAf,MAAA,CAAOC,CAAA;MACT;IACF,OAAO,IAAIqE,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;MAC1C;MACA;MAEA7D,UAAA,CAAW8C,WAAW,GAAIxD,CAAA;QACxB;QACA,IAAIA,CAAA,CAAEgC,MAAM,KAAK,KAAK,CAAC,IAAAjB,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC/E;QAGF,IAAIxE,KAAA,CAAMyC,yBAAyB,EAAE;UACnCiB,CAAA,CAAE2B,eAAe;UACjB;QACF;QAEArF,KAAA,CAAM+B,SAAS,GAAG;QAClB/B,KAAA,CAAM6C,YAAY,GAAG;QACrB7C,KAAA,CAAMI,MAAM,GAAGsD,CAAA,CAAEvD,aAAa;QAC9BH,KAAA,CAAMF,WAAW,GAAG,IAAA+F,qBAAa,EAAEnC,CAAA,CAAEc,WAAW,IAAI,YAAY;QAEhE;QACA,IAAI9E,qBAAA,GAAwB,IAAAwI,gBAAQ,EAAE,MAAMhF,iBAAA,CAAkBQ,CAAA,EAAG1D,KAAA,CAAMF,WAAW;QAClF,IAAIJ,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;QAGnB,IAAIpD,mBAAA,EAAqB;UACvB,IAAI4B,OAAA,GAAU,IAAAsD,yCAAW,EAAEzD,CAAA,CAAEtD,MAAM;UACnC,IAAIyD,OAAA,EACF7D,KAAA,CAAM8C,WAAW,CAACsE,IAAI,CAACvD,OAAA;QAE3B;QAEAd,iBAAA,CAAkB,IAAAmC,uBAAe,EAAExB,CAAA,CAAEvD,aAAa,GAAG,WAAWgI,SAAA,EAAW;MAC7E;MAEA/D,UAAA,CAAWgE,YAAY,GAAI1E,CAAA;QACzB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAI9E,qBAAA,GAAwB;QAC5B,IAAIM,KAAA,CAAM+B,SAAS,IAAI,CAAC/B,KAAA,CAAMyC,yBAAyB,IAAIzC,KAAA,CAAMF,WAAW,IAAI,MAAM;UACpFE,KAAA,CAAM6C,YAAY,GAAG;UACrBnD,qBAAA,GAAwBwD,iBAAA,CAAkBQ,CAAA,EAAG1D,KAAA,CAAMF,WAAW;QAChE;QAEA,IAAIJ,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;MAErB;MAEAjB,UAAA,CAAWiE,YAAY,GAAI3E,CAAA;QACzB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAI9E,qBAAA,GAAwB;QAC5B,IAAIM,KAAA,CAAM+B,SAAS,IAAI,CAAC/B,KAAA,CAAMyC,yBAAyB,IAAIzC,KAAA,CAAMF,WAAW,IAAI,MAAM;UACpFE,KAAA,CAAM6C,YAAY,GAAG;UACrBnD,qBAAA,GAAwB4D,eAAA,CAAgBI,CAAA,EAAG1D,KAAA,CAAMF,WAAW,EAAE;UAC9DgE,mBAAA,CAAoBJ,CAAA;QACtB;QAEA,IAAIhE,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;MAErB;MAEAjB,UAAA,CAAW+D,SAAS,GAAIzE,CAAA;QACtB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAI,CAACxE,KAAA,CAAMyC,yBAAyB,IAAIiB,CAAA,CAAEgC,MAAM,KAAK,KAAK,CAAC1F,KAAA,CAAM+B,SAAS,EACxEyB,cAAA,CAAeE,CAAA,EAAG1D,KAAA,CAAMF,WAAW,IAAI;MAE3C;MAEA,IAAIqI,SAAA,GAAazE,CAAA;QACf;QACA,IAAIA,CAAA,CAAEgC,MAAM,KAAK,GACf;QAGF,IAAI1F,KAAA,CAAMyC,yBAAyB,EAAE;UACnCzC,KAAA,CAAMyC,yBAAyB,GAAG;UAClC;QACF;QAEA,IAAIzC,KAAA,CAAMI,MAAM,IAAIJ,KAAA,CAAMI,MAAM,CAACkI,QAAQ,CAAC5E,CAAA,CAAEtD,MAAM,KAAgBJ,KAAA,CAAMF,WAAW,IAAI,YAIrF2D,MAAA,CAAOC,CAAA;QAGT1D,KAAA,CAAM6C,YAAY,GAAG;MACvB;MAEAuB,UAAA,CAAWmE,YAAY,GAAI7E,CAAA;QACzB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAIgE,KAAA,GAAQC,uCAAA,CAAkB/E,CAAA,CAAEc,WAAW;QAC3C,IAAI,CAACgE,KAAA,EACH;QAEFxI,KAAA,CAAM4C,eAAe,GAAG4F,KAAA,CAAME,UAAU;QACxC1I,KAAA,CAAMyC,yBAAyB,GAAG;QAClCzC,KAAA,CAAM6C,YAAY,GAAG;QACrB7C,KAAA,CAAM+B,SAAS,GAAG;QAClB/B,KAAA,CAAMI,MAAM,GAAGsD,CAAA,CAAEvD,aAAa;QAC9BH,KAAA,CAAMF,WAAW,GAAG;QAEpB,IAAI,CAACqC,yBAAA,EACH,IAAA2E,yCAAmB,EAAE9G,KAAA,CAAMI,MAAM;QAGnC,IAAIV,qBAAA,GAAwBwD,iBAAA,CAAkByF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAEsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW;QAClG,IAAIJ,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;QAGnBtC,iBAAA,CAAkB,IAAA6F,qBAAa,EAAElF,CAAA,CAAEvD,aAAa,GAAG,UAAU0I,QAAA,EAAU;MACzE;MAEAzE,UAAA,CAAW0E,WAAW,GAAIpF,CAAA;QACxB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAI,CAACxE,KAAA,CAAM+B,SAAS,EAAE;UACpB2B,CAAA,CAAE2B,eAAe;UACjB;QACF;QAEA,IAAImD,KAAA,GAAQO,kCAAA,CAAarF,CAAA,CAAEc,WAAW,EAAExE,KAAA,CAAM4C,eAAe;QAC7D,IAAIlD,qBAAA,GAAwB;QAC5B,IAAI8I,KAAA,IAASQ,kCAAA,CAAaR,KAAA,EAAO9E,CAAA,CAAEvD,aAAa,GAC9C;UAAA,IAAI,CAACH,KAAA,CAAM6C,YAAY,IAAI7C,KAAA,CAAMF,WAAW,IAAI,MAAM;YACpDE,KAAA,CAAM6C,YAAY,GAAG;YACrBnD,qBAAA,GAAwBwD,iBAAA,CAAkByF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW;UACjG;QAAA,OACK,IAAIE,KAAA,CAAM6C,YAAY,IAAI7C,KAAA,CAAMF,WAAW,IAAI,MAAM;UAC1DE,KAAA,CAAM6C,YAAY,GAAG;UACrBnD,qBAAA,GAAwB4D,eAAA,CAAgBqF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW,EAAE;UAC/FgE,mBAAA,CAAoB6E,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA;QACtD;QAEA,IAAIhE,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;MAErB;MAEAjB,UAAA,CAAW6E,UAAU,GAAIvF,CAAA;QACvB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGF,IAAI,CAACxE,KAAA,CAAM+B,SAAS,EAAE;UACpB2B,CAAA,CAAE2B,eAAe;UACjB;QACF;QAEA,IAAImD,KAAA,GAAQO,kCAAA,CAAarF,CAAA,CAAEc,WAAW,EAAExE,KAAA,CAAM4C,eAAe;QAC7D,IAAIlD,qBAAA,GAAwB;QAC5B,IAAI8I,KAAA,IAASQ,kCAAA,CAAaR,KAAA,EAAO9E,CAAA,CAAEvD,aAAa,KAAKH,KAAA,CAAMF,WAAW,IAAI,MAAM;UAC9E0D,cAAA,CAAemF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW;UACpEJ,qBAAA,GAAwB4D,eAAA,CAAgBqF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW;UAC7FkE,qBAAA,CAAsBN,CAAA,CAAEc,WAAW,EAAExE,KAAA,CAAMI,MAAM;QACnD,OAAO,IAAIJ,KAAA,CAAM6C,YAAY,IAAI7C,KAAA,CAAMF,WAAW,IAAI,MACpDJ,qBAAA,GAAwB4D,eAAA,CAAgBqF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA,GAAI1D,KAAA,CAAMF,WAAW,EAAE;QAGjG,IAAIJ,qBAAA,EACFgE,CAAA,CAAE2B,eAAe;QAGnBrF,KAAA,CAAM+B,SAAS,GAAG;QAClB/B,KAAA,CAAM4C,eAAe,GAAG;QACxB5C,KAAA,CAAM6C,YAAY,GAAG;QACrB7C,KAAA,CAAMyC,yBAAyB,GAAG;QAClC,IAAIzC,KAAA,CAAMI,MAAM,IAAI,CAAC+B,yBAAA,EACnB,IAAAyB,yCAAmB,EAAE5D,KAAA,CAAMI,MAAM;QAEnC4C,wBAAA;MACF;MAEAoB,UAAA,CAAW8E,aAAa,GAAIxF,CAAA;QAC1B,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGFd,CAAA,CAAE2B,eAAe;QACjB,IAAIrF,KAAA,CAAM+B,SAAS,EACjB0B,MAAA,CAAOkF,sCAAA,CAAiB3I,KAAA,CAAMI,MAAM,EAAGsD,CAAA;MAE3C;MAEA,IAAImF,QAAA,GAAYnF,CAAA;QACd,IAAI1D,KAAA,CAAM+B,SAAS,IAAI,IAAA0C,mBAAW,EAAE,IAAAC,qBAAa,EAAEhB,CAAA,GAAI1D,KAAA,CAAMI,MAAM,GACjEqD,MAAA,CAAO;UACLtD,aAAA,EAAeH,KAAA,CAAMI,MAAM;UAC3BW,QAAA,EAAU;UACVE,OAAA,EAAS;UACTD,OAAA,EAAS;UACTE,MAAA,EAAQ;QACV;MAEJ;MAEAkD,UAAA,CAAW0D,WAAW,GAAIpE,CAAA;QACxB,IAAI,CAAC,IAAAe,mBAAW,EAAEf,CAAA,CAAEvD,aAAa,EAAE,IAAAuE,qBAAa,EAAEhB,CAAA,CAAEc,WAAW,IAC7D;QAGFf,MAAA,CAAOC,CAAA;MACT;IACF;IAEA,OAAOU,UAAA;EACT,GAAG,CACDrB,iBAAA,EACAjB,UAAA,EACAG,mBAAA,EACAe,wBAAA,EACAb,yBAAA,EACAsB,MAAA,EACAK,mBAAA,EACAR,eAAA,EACAJ,iBAAA,EACAM,cAAA,EACAO,YAAA,EACAC,qBAAA,CACD;EAED;EACA,IAAAmF,gBAAQ,EAAE;IACR,IAAI,CAAC/G,MAAA,IAAU2F,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QACtC;IAGF,MAAMmB,aAAA,GAAgB,IAAAlE,uBAAe,EAAE9C,MAAA,CAAOgB,OAAO;IACrD,IAAI,CAACgG,aAAA,IAAiB,CAACA,aAAA,CAAcC,IAAI,IAAID,aAAA,CAAcE,cAAc,CAACjI,8BAAA,GACxE;IAGF,MAAMkI,KAAA,GAAQH,aAAA,CAAcI,aAAa,CAAC;IAC1CD,KAAA,CAAME,EAAE,GAAGpI,8BAAA;IACX;IACA;IACA;IACAkI,KAAA,CAAMG,WAAW,GAAG;;KAEnBpI,yCAAA;;;;KAIA,CAACqI,IAAI;IACNP,aAAA,CAAcC,IAAI,CAACO,OAAO,CAACL,KAAA;EAC7B,GAAG,CAACnH,MAAA,CAAO;EAEX;EACA,IAAA+G,gBAAQ,EAAE;IACR,IAAInJ,KAAA,GAAQX,GAAA,CAAI+D,OAAO;IACvB,OAAO;UAEkBlD,aAAA;MADvB,IAAI,CAACiC,yBAAA,EACH,IAAAyB,yCAAmB,EAAE,CAAA1D,aAAA,GAAAF,KAAA,CAAMI,MAAM,cAAZF,aAAA,cAAAA,aAAA,GAAgBoG,SAAA;MAEvC,KAAK,IAAIzC,OAAA,IAAW7D,KAAA,CAAM8C,WAAW,EACnCe,OAAA;MAEF7D,KAAA,CAAM8C,WAAW,GAAG,EAAE;IACxB;EACF,GAAG,CAACX,yBAAA,CAA0B;EAE9B,OAAO;IACLJ,SAAA,EAAWC,aAAA,IAAiBD,SAAA;IAC5BqC,UAAA,EAAY,IAAAjF,iBAAS,EAAEkD,QAAA,EAAU+B,UAAA,EAAY;MAAC,CAAC9C,yCAAA,GAAsB;IAAI;EAC3E;AACF;AAEA,SAAS4E,uCAAiB9F,MAAe;EACvC,OAAOA,MAAA,CAAOyJ,OAAO,KAAK,OAAOzJ,MAAA,CAAO0J,YAAY,CAAC;AACvD;AAEA,SAASvF,2CAAqBlB,KAAoB,EAAElD,aAAsB;EACxE,MAAM;IAAA0E,GAAA,EAACA,GAAG;IAAAkF,IAAA,EAAEA;EAAI,CAAC,GAAG1G,KAAA;EACpB,MAAM2G,OAAA,GAAU7J,aAAA;EAChB,MAAM8J,IAAA,GAAOD,OAAA,CAAQE,YAAY,CAAC;EAClC;EACA;EACA,OACE,CAACrF,GAAA,KAAQ,WAAWA,GAAA,KAAQ,OAAOA,GAAA,KAAQ,cAAckF,IAAA,KAAS,OAAM,KACxE,EAAEC,OAAC,YAAmB,IAAApB,qBAAa,EAAEoB,OAAA,EAASG,gBAAgB,IAAI,CAACC,qCAAA,CAAgBJ,OAAA,EAASnF,GAAA,KAC1FmF,OAAA,YAAmB,IAAApB,qBAAa,EAAEoB,OAAA,EAASK,mBAAmB,IAC9DL,OAAA,CAAQM,iBAAiB,CAAD;EAC1B;EACA,EAAE,CAACL,IAAA,KAAS,UAAW,CAACA,IAAA,IAAQ/D,sCAAA,CAAiB8D,OAAA,CAAQ,KAAMnF,GAAA,KAAQ,OAAM;AAEjF;AAEA,SAAS4D,wCAAkBpF,KAAiB;EAC1C,MAAM;IAAAkH,aAAA,EAACA;EAAa,CAAC,GAAGlH,KAAA;EACxB,IAAIkH,aAAA,CAAcC,MAAM,GAAG,GACzB,OAAOD,aAAa,CAAC,EAAE;EAEzB,OAAO;AACT;AAEA,SAASxB,mCACP1F,KAAiB,EACjBwD,SAAwB;EAExB,MAAM4D,cAAA,GAAiBpH,KAAA,CAAMoH,cAAc;EAC3C,KAAK,IAAIC,CAAA,GAAI,GAAGA,CAAA,GAAID,cAAA,CAAeD,MAAM,EAAEE,CAAA,IAAK;IAC9C,MAAMlC,KAAA,GAAQiC,cAAc,CAACC,CAAA,CAAE;IAC/B,IAAIlC,KAAA,CAAME,UAAU,KAAK7B,SAAA,EACvB,OAAO2B,KAAA;EAEX;EACA,OAAO;AACT;AAEA,SAASG,uCAAiBvI,MAAwB,EAAEsD,CAAgC;EAClF,IAAIjD,OAAA,GAAU;EACd,IAAIC,OAAA,GAAU;EACd,IAAIgD,CAAA,CAAE6G,aAAa,IAAI7G,CAAA,CAAE6G,aAAa,CAACC,MAAM,KAAK,GAAG;IACnD/J,OAAA,GAAUiD,CAAA,CAAE6G,aAAa,CAAC,EAAE,CAAC9J,OAAO;IACpCC,OAAA,GAAUgD,CAAA,CAAE6G,aAAa,CAAC,EAAE,CAAC7J,OAAO;EACtC;EACA,OAAO;IACLP,aAAA,EAAeC,MAAA;IACfW,QAAA,EAAU2C,CAAA,CAAE3C,QAAQ;IACpBE,OAAA,EAASyC,CAAA,CAAEzC,OAAO;IAClBD,OAAA,EAAS0C,CAAA,CAAE1C,OAAO;IAClBE,MAAA,EAAQwC,CAAA,CAAExC,MAAM;aAChBT,OAAA;aACAC;EACF;AACF;AAEA,SAASiD,kCAAYvD,MAAwB,EAAEsD,CAAY;EACzD,IAAIjD,OAAA,GAAUiD,CAAA,CAAEjD,OAAO;EACvB,IAAIC,OAAA,GAAUgD,CAAA,CAAEhD,OAAO;EACvB,OAAO;IACLP,aAAA,EAAeC,MAAA;IACfW,QAAA,EAAU2C,CAAA,CAAE3C,QAAQ;IACpBE,OAAA,EAASyC,CAAA,CAAEzC,OAAO;IAClBD,OAAA,EAAS0C,CAAA,CAAE1C,OAAO;IAClBE,MAAA,EAAQwC,CAAA,CAAExC,MAAM;aAChBT,OAAA;aACAC;EACF;AACF;AAkBA,SAASiK,yCAAmBC,KAAiB;EAC3C,IAAIC,OAAA,GAAU;EACd,IAAIC,OAAA,GAAU;EACd,IAAIF,KAAA,CAAM/J,KAAK,KAAKyF,SAAA,EAClBuE,OAAA,GAAWD,KAAA,CAAM/J,KAAK,GAAG,OACpB,IAAI+J,KAAA,CAAMG,OAAO,KAAKzE,SAAA,EAC3BuE,OAAA,GAAUD,KAAA,CAAMG,OAAO;EAEzB,IAAIH,KAAA,CAAM9J,MAAM,KAAKwF,SAAA,EACnBwE,OAAA,GAAWF,KAAA,CAAM9J,MAAM,GAAG,OACrB,IAAI8J,KAAA,CAAMI,OAAO,KAAK1E,SAAA,EAC3BwE,OAAA,GAAUF,KAAA,CAAMI,OAAO;EAGzB,OAAO;IACLpK,GAAA,EAAKgK,KAAA,CAAMlK,OAAO,GAAGoK,OAAA;IACrBG,KAAA,EAAOL,KAAA,CAAMnK,OAAO,GAAGoK,OAAA;IACvBK,MAAA,EAAQN,KAAA,CAAMlK,OAAO,GAAGoK,OAAA;IACxBnK,IAAA,EAAMiK,KAAA,CAAMnK,OAAO,GAAGoK;EACxB;AACF;AAEA,SAASM,+CAAyBC,CAAO,EAAEC,CAAO;EAChD;EACA,IAAID,CAAA,CAAEzK,IAAI,GAAG0K,CAAA,CAAEJ,KAAK,IAAII,CAAA,CAAE1K,IAAI,GAAGyK,CAAA,CAAEH,KAAK,EACtC,OAAO;EAET;EACA,IAAIG,CAAA,CAAExK,GAAG,GAAGyK,CAAA,CAAEH,MAAM,IAAIG,CAAA,CAAEzK,GAAG,GAAGwK,CAAA,CAAEF,MAAM,EACtC,OAAO;EAET,OAAO;AACT;AAEA,SAASlC,mCAAa4B,KAAiB,EAAExK,MAAe;EACtD,IAAIC,IAAA,GAAOD,MAAA,CAAOE,qBAAqB;EACvC,IAAIgL,SAAA,GAAYX,wCAAA,CAAmBC,KAAA;EACnC,OAAOO,8CAAA,CAAyB9K,IAAA,EAAMiL,SAAA;AACxC;AAEA,SAASC,6CAAuBnL,MAAe;EAC7C,IAAIA,MAAA,YAAkB+J,gBAAA,EACpB,OAAO;EAGT,IAAI/J,MAAA,YAAkBoL,iBAAA,EACpB,OAAOpL,MAAA,CAAOP,IAAI,KAAK,YAAYO,MAAA,CAAOP,IAAI,KAAK;EAGrD,IAAIqG,sCAAA,CAAiB9F,MAAA,GACnB,OAAO;EAGT,OAAO;AACT;AAEA,SAASwE,mDAA6BxE,MAAe,EAAEyE,GAAW;EAChE,IAAIzE,MAAA,YAAkB+J,gBAAA,EACpB,OAAO,CAACC,qCAAA,CAAgBhK,MAAA,EAAQyE,GAAA;EAGlC,OAAO0G,4CAAA,CAAuBnL,MAAA;AAChC;AAEA,MAAMqL,uCAAA,GAAoB,IAAIC,GAAA,CAAI,CAChC,YACA,SACA,SACA,SACA,QACA,SACA,UACA,UACA,QACD;AAED,SAAStB,sCAAgBhK,MAAwB,EAAEyE,GAAW;EAC5D;EACA,OAAOzE,MAAA,CAAOP,IAAI,KAAK,cAAcO,MAAA,CAAOP,IAAI,KAAK,UACjDgF,GAAA,KAAQ,MACR4G,uCAAA,CAAkBE,GAAG,CAACvL,MAAA,CAAOP,IAAI;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}