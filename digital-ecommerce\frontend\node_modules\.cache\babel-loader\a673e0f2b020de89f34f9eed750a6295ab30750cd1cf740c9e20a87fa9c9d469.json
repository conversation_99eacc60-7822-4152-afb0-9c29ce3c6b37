{"ast": null, "code": "function t(e) {\n  typeof queueMicrotask == \"function\" ? queueMicrotask(e) : Promise.resolve().then(e).catch(o => setTimeout(() => {\n    throw o;\n  }));\n}\nexport { t as microTask };", "map": {"version": 3, "names": ["t", "e", "queueMicrotask", "Promise", "resolve", "then", "catch", "o", "setTimeout", "microTask"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/micro-task.js"], "sourcesContent": ["function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAC;EAAC,OAAOC,cAAc,IAAE,UAAU,GAACA,cAAc,CAACD,CAAC,CAAC,GAACE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACJ,CAAC,CAAC,CAACK,KAAK,CAACC,CAAC,IAAEC,UAAU,CAAC,MAAI;IAAC,MAAMD,CAAC;EAAA,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIS,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}