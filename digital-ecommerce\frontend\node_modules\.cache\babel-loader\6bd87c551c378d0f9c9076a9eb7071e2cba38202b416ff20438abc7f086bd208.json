{"ast": null, "code": "var i = Object.defineProperty;\nvar d = (t, e, n) => e in t ? i(t, e, {\n  enumerable: !0,\n  configurable: !0,\n  writable: !0,\n  value: n\n}) : t[e] = n;\nvar r = (t, e, n) => (d(t, typeof e != \"symbol\" ? e + \"\" : e, n), n);\nclass o {\n  constructor() {\n    r(this, \"current\", this.detect());\n    r(this, \"handoffState\", \"pending\");\n    r(this, \"currentId\", 0);\n  }\n  set(e) {\n    this.current !== e && (this.handoffState = \"pending\", this.currentId = 0, this.current = e);\n  }\n  reset() {\n    this.set(this.detect());\n  }\n  nextId() {\n    return ++this.currentId;\n  }\n  get isServer() {\n    return this.current === \"server\";\n  }\n  get isClient() {\n    return this.current === \"client\";\n  }\n  detect() {\n    return typeof window == \"undefined\" || typeof document == \"undefined\" ? \"server\" : \"client\";\n  }\n  handoff() {\n    this.handoffState === \"pending\" && (this.handoffState = \"complete\");\n  }\n  get isHandoffComplete() {\n    return this.handoffState === \"complete\";\n  }\n}\nlet s = new o();\nexport { s as env };", "map": {"version": 3, "names": ["i", "Object", "defineProperty", "d", "t", "e", "n", "enumerable", "configurable", "writable", "value", "r", "o", "constructor", "detect", "set", "current", "handoffState", "currentId", "reset", "nextId", "isServer", "isClient", "window", "document", "handoff", "isHandoffComplete", "s", "env"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/env.js"], "sourcesContent": ["var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n"], "mappings": "AAAA,IAAIA,CAAC,GAACC,MAAM,CAACC,cAAc;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,IAAID,CAAC,GAACJ,CAAC,CAACI,CAAC,EAACC,CAAC,EAAC;EAACE,UAAU,EAAC,CAAC,CAAC;EAACC,YAAY,EAAC,CAAC,CAAC;EAACC,QAAQ,EAAC,CAAC,CAAC;EAACC,KAAK,EAACJ;AAAC,CAAC,CAAC,GAACF,CAAC,CAACC,CAAC,CAAC,GAACC,CAAC;AAAC,IAAIK,CAAC,GAACA,CAACP,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIH,CAAC,CAACC,CAAC,EAAC,OAAOC,CAAC,IAAE,QAAQ,GAACA,CAAC,GAAC,EAAE,GAACA,CAAC,EAACC,CAAC,CAAC,EAACA,CAAC,CAAC;AAAC,MAAMM,CAAC;EAACC,WAAWA,CAAA,EAAE;IAACF,CAAC,CAAC,IAAI,EAAC,SAAS,EAAC,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC;IAACH,CAAC,CAAC,IAAI,EAAC,cAAc,EAAC,SAAS,CAAC;IAACA,CAAC,CAAC,IAAI,EAAC,WAAW,EAAC,CAAC,CAAC;EAAA;EAACI,GAAGA,CAACV,CAAC,EAAC;IAAC,IAAI,CAACW,OAAO,KAAGX,CAAC,KAAG,IAAI,CAACY,YAAY,GAAC,SAAS,EAAC,IAAI,CAACC,SAAS,GAAC,CAAC,EAAC,IAAI,CAACF,OAAO,GAACX,CAAC,CAAC;EAAA;EAACc,KAAKA,CAAA,EAAE;IAAC,IAAI,CAACJ,GAAG,CAAC,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;EAAA;EAACM,MAAMA,CAAA,EAAE;IAAC,OAAM,EAAE,IAAI,CAACF,SAAS;EAAA;EAAC,IAAIG,QAAQA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACL,OAAO,KAAG,QAAQ;EAAA;EAAC,IAAIM,QAAQA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACN,OAAO,KAAG,QAAQ;EAAA;EAACF,MAAMA,CAAA,EAAE;IAAC,OAAO,OAAOS,MAAM,IAAE,WAAW,IAAE,OAAOC,QAAQ,IAAE,WAAW,GAAC,QAAQ,GAAC,QAAQ;EAAA;EAACC,OAAOA,CAAA,EAAE;IAAC,IAAI,CAACR,YAAY,KAAG,SAAS,KAAG,IAAI,CAACA,YAAY,GAAC,UAAU,CAAC;EAAA;EAAC,IAAIS,iBAAiBA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACT,YAAY,KAAG,UAAU;EAAA;AAAC;AAAC,IAAIU,CAAC,GAAC,IAAIf,CAAC,CAAD,CAAC;AAAC,SAAOe,CAAC,IAAIC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}