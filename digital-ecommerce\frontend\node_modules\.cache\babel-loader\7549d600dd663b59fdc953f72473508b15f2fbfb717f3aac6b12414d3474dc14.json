{"ast": null, "code": "import { useRef as o } from \"react\";\nfunction t(e) {\n  return [e.screenX, e.screenY];\n}\nfunction u() {\n  let e = o([-1, -1]);\n  return {\n    wasMoved(r) {\n      let n = t(r);\n      return e.current[0] === n[0] && e.current[1] === n[1] ? !1 : (e.current = n, !0);\n    },\n    update(r) {\n      e.current = t(r);\n    }\n  };\n}\nexport { u as useTrackedPointer };", "map": {"version": 3, "names": ["useRef", "o", "t", "e", "screenX", "screenY", "u", "wasMoved", "r", "n", "current", "update", "useTrackedPointer"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-tracked-pointer.js"], "sourcesContent": ["import{useRef as o}from\"react\";function t(e){return[e.screenX,e.screenY]}function u(){let e=o([-1,-1]);return{wasMoved(r){let n=t(r);return e.current[0]===n[0]&&e.current[1]===n[1]?!1:(e.current=n,!0)},update(r){e.current=t(r)}}}export{u as useTrackedPointer};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,OAAM,CAACA,CAAC,CAACC,OAAO,EAACD,CAAC,CAACE,OAAO,CAAC;AAAA;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIH,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;EAAC,OAAM;IAACM,QAAQA,CAACC,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACP,CAAC,CAACM,CAAC,CAAC;MAAC,OAAOL,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,KAAGD,CAAC,CAAC,CAAC,CAAC,IAAEN,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,KAAGD,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAEN,CAAC,CAACO,OAAO,GAACD,CAAC,EAAC,CAAC,CAAC,CAAC;IAAA,CAAC;IAACE,MAAMA,CAACH,CAAC,EAAC;MAACL,CAAC,CAACO,OAAO,GAACR,CAAC,CAACM,CAAC,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}