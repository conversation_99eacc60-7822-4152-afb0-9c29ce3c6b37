import React, { useState } from 'react';
import { MagnifyingGlassIcon as SearchIcon, FunnelIcon as FilterIcon, XMarkIcon as XIcon } from '@heroicons/react/24/outline';

const SearchFilters = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [selectedRating, setSelectedRating] = useState(0);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'audio', name: 'Audio' },
    { id: 'wearables', name: 'Wearables' },
    { id: 'electronics', name: 'Electronics' },
    { id: 'accessories', name: 'Accessories' }
  ];

  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
    setPriceRange([0, 1000]);
    setSelectedRating(0);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-light-orange-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-white flex items-center">
            <SearchIcon className="w-6 h-6 mr-2" />
            Search & Filters
          </h2>
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="md:hidden bg-white bg-opacity-20 p-2 rounded-lg hover:bg-opacity-30 transition-colors"
          >
            <FilterIcon className="w-5 h-5 text-white" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className={`p-6 space-y-6 ${isFilterOpen ? 'block' : 'hidden md:block'}`}>
        {/* Search Bar */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon className="h-5 w-5 text-light-orange-400" />
          </div>
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors"
          />
        </div>

        {/* Category Filter */}
        <div>
          <label className="block text-sm font-semibold text-light-orange-800 mb-3">
            Category
          </label>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full p-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors bg-white"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Price Range */}
        <div>
          <label className="block text-sm font-semibold text-light-orange-800 mb-3">
            Price Range: ${priceRange[0]} - ${priceRange[1]}
          </label>
          <div className="space-y-3">
            <input
              type="range"
              min="0"
              max="1000"
              value={priceRange[1]}
              onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
              className="w-full h-2 bg-light-orange-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-light-orange-600">
              <span>$0</span>
              <span>$1000+</span>
            </div>
          </div>
        </div>

        {/* Rating Filter */}
        <div>
          <label className="block text-sm font-semibold text-light-orange-800 mb-3">
            Minimum Rating
          </label>
          <div className="flex space-x-2">
            {[1, 2, 3, 4, 5].map((rating) => (
              <button
                key={rating}
                onClick={() => setSelectedRating(rating === selectedRating ? 0 : rating)}
                className={`flex items-center space-x-1 px-3 py-2 rounded-lg border transition-colors ${
                  selectedRating >= rating
                    ? 'bg-light-orange-100 border-light-orange-300 text-light-orange-800'
                    : 'bg-white border-light-orange-200 text-light-orange-600 hover:bg-light-orange-50'
                }`}
              >
                <svg className="w-4 h-4 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span className="text-sm">{rating}+</span>
              </button>
            ))}
          </div>
        </div>

        {/* Clear Filters Button */}
        <button
          onClick={handleClearFilters}
          className="w-full flex items-center justify-center space-x-2 bg-light-orange-100 text-light-orange-700 py-3 px-4 rounded-lg hover:bg-light-orange-200 transition-colors border border-light-orange-200"
        >
          <XIcon className="w-4 h-4" />
          <span>Clear All Filters</span>
        </button>
      </div>
    </div>
  );
};

export default SearchFilters;
