{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */function $5dc95899b306f630$export$c9058316764c140e() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  if (refs.length === 1 && refs[0]) return refs[0];\n  return value => {\n    let hasCleanup = false;\n    const cleanups = refs.map(ref => {\n      const cleanup = $5dc95899b306f630$var$setRef(ref, value);\n      hasCleanup || (hasCleanup = typeof cleanup == 'function');\n      return cleanup;\n    });\n    if (hasCleanup) return () => {\n      cleanups.forEach((cleanup, i) => {\n        if (typeof cleanup === 'function') cleanup();else $5dc95899b306f630$var$setRef(refs[i], null);\n      });\n    };\n  };\n}\nfunction $5dc95899b306f630$var$setRef(ref, value) {\n  if (typeof ref === 'function') return ref(value);else if (ref != null) ref.current = value;\n}\nexport { $5dc95899b306f630$export$c9058316764c140e as mergeRefs };", "map": {"version": 3, "names": ["$5dc95899b306f630$export$c9058316764c140e", "_len", "arguments", "length", "refs", "Array", "_key", "value", "hasCleanup", "cleanups", "map", "ref", "cleanup", "$5dc95899b306f630$var$setRef", "for<PERSON>ach", "i", "current"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\mergeRefs.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, Ref} from 'react';\n\n/**\n * Merges multiple refs into one. Works with either callback or object refs.\n */\nexport function mergeRefs<T>(...refs: Array<Ref<T> | MutableRefObject<T> | null | undefined>): Ref<T> {\n  if (refs.length === 1 && refs[0]) {\n    return refs[0];\n  }\n\n  return (value: T | null) => {\n    let hasCleanup = false;\n\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, value);\n      hasCleanup ||= typeof cleanup == 'function';\n      return cleanup;\n    });\n\n    if (hasCleanup) {\n      return () => {\n        cleanups.forEach((cleanup, i) => {\n          if (typeof cleanup === 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        });\n      };\n    }\n  };\n}\n\nfunction setRef<T>(ref: Ref<T> | MutableRefObject<T> | null | undefined, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref != null) {\n    ref.current = value;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;GAiBO,SAASA,0CAAA,EAA4E;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAA5DC,IAA4D,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAA5DF,IAA4D,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC1F,IAAIF,IAAA,CAAKD,MAAM,KAAK,KAAKC,IAAI,CAAC,EAAE,EAC9B,OAAOA,IAAI,CAAC,EAAE;EAGhB,OAAQG,KAAA;IACN,IAAIC,UAAA,GAAa;IAEjB,MAAMC,QAAA,GAAWL,IAAA,CAAKM,GAAG,CAACC,GAAA;MACxB,MAAMC,OAAA,GAAUC,4BAAA,CAAOF,GAAA,EAAKJ,KAAA;MAC5BC,UAAA,KAAAA,UAAA,GAAe,OAAOI,OAAA,IAAW;MACjC,OAAOA,OAAA;IACT;IAEA,IAAIJ,UAAA,EACF,OAAO;MACLC,QAAA,CAASK,OAAO,CAAC,CAACF,OAAA,EAASG,CAAA;QACzB,IAAI,OAAOH,OAAA,KAAY,YACrBA,OAAA,QAEAC,4BAAA,CAAOT,IAAI,CAACW,CAAA,CAAE,EAAE;MAEpB;IACF;EAEJ;AACF;AAEA,SAASF,6BAAUF,GAAoD,EAAEJ,KAAQ;EAC/E,IAAI,OAAOI,GAAA,KAAQ,YACjB,OAAOA,GAAA,CAAIJ,KAAA,OACN,IAAII,GAAA,IAAO,MAChBA,GAAA,CAAIK,OAAO,GAAGT,KAAA;AAElB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}