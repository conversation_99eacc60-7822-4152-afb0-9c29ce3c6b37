{"ast": null, "code": "import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\nfunction _class_private_field_set(receiver, privateMap, value) {\n  var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n  _class_apply_descriptor_set(receiver, descriptor, value);\n  return value;\n}\nexport { _class_private_field_set as _ };", "map": {"version": 3, "names": ["_", "_class_apply_descriptor_set", "_class_extract_field_descriptor", "_class_private_field_set", "receiver", "privateMap", "value", "descriptor"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,2BAA2B,QAAQ,kCAAkC;AACnF,SAASD,CAAC,IAAIE,+BAA+B,QAAQ,sCAAsC;AAE3F,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAC3D,IAAIC,UAAU,GAAGL,+BAA+B,CAACE,QAAQ,EAAEC,UAAU,EAAE,KAAK,CAAC;EAC7EJ,2BAA2B,CAACG,QAAQ,EAAEG,UAAU,EAAED,KAAK,CAAC;EACxD,OAAOA,KAAK;AAChB;AACA,SAASH,wBAAwB,IAAIH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}