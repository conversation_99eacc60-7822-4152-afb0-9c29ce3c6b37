{"ast": null, "code": "\"use client\";\n\nimport T, { Fragment as E, createContext as A, useContext as d, useEffect as G, useMemo as x, useRef as L, useState as c } from \"react\";\nimport { createPortal as h } from \"react-dom\";\nimport { useEvent as _ } from '../../hooks/use-event.js';\nimport { useIsoMorphicEffect as C } from '../../hooks/use-iso-morphic-effect.js';\nimport { useOnUnmount as F } from '../../hooks/use-on-unmount.js';\nimport { useOwnerDocument as U } from '../../hooks/use-owner.js';\nimport { useServerHandoffComplete as N } from '../../hooks/use-server-handoff-complete.js';\nimport { optionalRef as S, useSyncRefs as m } from '../../hooks/use-sync-refs.js';\nimport { usePortalRoot as W } from '../../internal/portal-force-root.js';\nimport * as j from '../../utils/dom.js';\nimport { env as v } from '../../utils/env.js';\nimport { forwardRefWithAs as y, useRender as R } from '../../utils/render.js';\nfunction I(e) {\n  let l = W(),\n    o = d(H),\n    [r, u] = c(() => {\n      var i;\n      if (!l && o !== null) return (i = o.current) != null ? i : null;\n      if (v.isServer) return null;\n      let t = e == null ? void 0 : e.getElementById(\"headlessui-portal-root\");\n      if (t) return t;\n      if (e === null) return null;\n      let a = e.createElement(\"div\");\n      return a.setAttribute(\"id\", \"headlessui-portal-root\"), e.body.appendChild(a);\n    });\n  return G(() => {\n    r !== null && (e != null && e.body.contains(r) || e == null || e.body.appendChild(r));\n  }, [r, e]), G(() => {\n    l || o !== null && u(o.current);\n  }, [o, u, l]), r;\n}\nlet M = E,\n  D = y(function (l, o) {\n    let {\n        ownerDocument: r = null,\n        ...u\n      } = l,\n      t = L(null),\n      a = m(S(s => {\n        t.current = s;\n      }), o),\n      i = U(t),\n      f = r != null ? r : i,\n      p = I(f),\n      [n] = c(() => {\n        var s;\n        return v.isServer ? null : (s = f == null ? void 0 : f.createElement(\"div\")) != null ? s : null;\n      }),\n      P = d(g),\n      O = N();\n    C(() => {\n      !p || !n || p.contains(n) || (n.setAttribute(\"data-headlessui-portal\", \"\"), p.appendChild(n));\n    }, [p, n]), C(() => {\n      if (n && P) return P.register(n);\n    }, [P, n]), F(() => {\n      var s;\n      !p || !n || (j.isNode(n) && p.contains(n) && p.removeChild(n), p.childNodes.length <= 0 && ((s = p.parentElement) == null || s.removeChild(p)));\n    });\n    let b = R();\n    return O ? !p || !n ? null : h(b({\n      ourProps: {\n        ref: a\n      },\n      theirProps: u,\n      slot: {},\n      defaultTag: M,\n      name: \"Portal\"\n    }), n) : null;\n  });\nfunction J(e, l) {\n  let o = m(l),\n    {\n      enabled: r = !0,\n      ownerDocument: u,\n      ...t\n    } = e,\n    a = R();\n  return r ? T.createElement(D, {\n    ...t,\n    ownerDocument: u,\n    ref: o\n  }) : a({\n    ourProps: {\n      ref: o\n    },\n    theirProps: t,\n    slot: {},\n    defaultTag: M,\n    name: \"Portal\"\n  });\n}\nlet X = E,\n  H = A(null);\nfunction k(e, l) {\n  let {\n      target: o,\n      ...r\n    } = e,\n    t = {\n      ref: m(l)\n    },\n    a = R();\n  return T.createElement(H.Provider, {\n    value: o\n  }, a({\n    ourProps: t,\n    theirProps: r,\n    defaultTag: X,\n    name: \"Popover.Group\"\n  }));\n}\nlet g = A(null);\nfunction oe() {\n  let e = d(g),\n    l = L([]),\n    o = _(t => (l.current.push(t), e && e.register(t), () => r(t))),\n    r = _(t => {\n      let a = l.current.indexOf(t);\n      a !== -1 && l.current.splice(a, 1), e && e.unregister(t);\n    }),\n    u = x(() => ({\n      register: o,\n      unregister: r,\n      portals: l\n    }), [o, r, l]);\n  return [l, x(() => function ({\n    children: a\n  }) {\n    return T.createElement(g.Provider, {\n      value: u\n    }, a);\n  }, [u])];\n}\nlet B = y(J),\n  q = y(k),\n  ne = Object.assign(B, {\n    Group: q\n  });\nexport { ne as Portal, q as PortalGroup, oe as useNestedPortals };", "map": {"version": 3, "names": ["T", "Fragment", "E", "createContext", "A", "useContext", "d", "useEffect", "G", "useMemo", "x", "useRef", "L", "useState", "c", "createPortal", "h", "useEvent", "_", "useIsoMorphicEffect", "C", "useOnUnmount", "F", "useOwnerDocument", "U", "useServerHandoffComplete", "N", "optionalRef", "S", "useSyncRefs", "m", "usePortalRoot", "W", "j", "env", "v", "forwardRefWithAs", "y", "useRender", "R", "I", "e", "l", "o", "H", "r", "u", "i", "current", "isServer", "t", "getElementById", "a", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "contains", "M", "D", "ownerDocument", "s", "f", "p", "n", "P", "g", "O", "register", "isNode", "<PERSON><PERSON><PERSON><PERSON>", "childNodes", "length", "parentElement", "b", "ourProps", "ref", "theirProps", "slot", "defaultTag", "name", "J", "enabled", "X", "k", "target", "Provider", "value", "oe", "push", "indexOf", "splice", "unregister", "portals", "children", "B", "q", "ne", "Object", "assign", "Group", "Portal", "PortalGroup", "useNestedPortals"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/portal/portal.js"], "sourcesContent": ["\"use client\";import T,{Fragment as E,createContext as A,useContext as d,useEffect as G,useMemo as x,useRef as L,useState as c}from\"react\";import{createPortal as h}from\"react-dom\";import{useEvent as _}from'../../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as F}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as U}from'../../hooks/use-owner.js';import{useServerHandoffComplete as N}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as S,useSyncRefs as m}from'../../hooks/use-sync-refs.js';import{usePortalRoot as W}from'../../internal/portal-force-root.js';import*as j from'../../utils/dom.js';import{env as v}from'../../utils/env.js';import{forwardRefWithAs as y,useRender as R}from'../../utils/render.js';function I(e){let l=W(),o=d(H),[r,u]=c(()=>{var i;if(!l&&o!==null)return(i=o.current)!=null?i:null;if(v.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let a=e.createElement(\"div\");return a.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(a)});return G(()=>{r!==null&&(e!=null&&e.body.contains(r)||e==null||e.body.appendChild(r))},[r,e]),G(()=>{l||o!==null&&u(o.current)},[o,u,l]),r}let M=E,D=y(function(l,o){let{ownerDocument:r=null,...u}=l,t=L(null),a=m(S(s=>{t.current=s}),o),i=U(t),f=r!=null?r:i,p=I(f),[n]=c(()=>{var s;return v.isServer?null:(s=f==null?void 0:f.createElement(\"div\"))!=null?s:null}),P=d(g),O=N();C(()=>{!p||!n||p.contains(n)||(n.setAttribute(\"data-headlessui-portal\",\"\"),p.appendChild(n))},[p,n]),C(()=>{if(n&&P)return P.register(n)},[P,n]),F(()=>{var s;!p||!n||(j.isNode(n)&&p.contains(n)&&p.removeChild(n),p.childNodes.length<=0&&((s=p.parentElement)==null||s.removeChild(p)))});let b=R();return O?!p||!n?null:h(b({ourProps:{ref:a},theirProps:u,slot:{},defaultTag:M,name:\"Portal\"}),n):null});function J(e,l){let o=m(l),{enabled:r=!0,ownerDocument:u,...t}=e,a=R();return r?T.createElement(D,{...t,ownerDocument:u,ref:o}):a({ourProps:{ref:o},theirProps:t,slot:{},defaultTag:M,name:\"Portal\"})}let X=E,H=A(null);function k(e,l){let{target:o,...r}=e,t={ref:m(l)},a=R();return T.createElement(H.Provider,{value:o},a({ourProps:t,theirProps:r,defaultTag:X,name:\"Popover.Group\"}))}let g=A(null);function oe(){let e=d(g),l=L([]),o=_(t=>(l.current.push(t),e&&e.register(t),()=>r(t))),r=_(t=>{let a=l.current.indexOf(t);a!==-1&&l.current.splice(a,1),e&&e.unregister(t)}),u=x(()=>({register:o,unregister:r,portals:l}),[o,r,l]);return[l,x(()=>function({children:a}){return T.createElement(g.Provider,{value:u},a)},[u])]}let B=y(J),q=y(k),ne=Object.assign(B,{Group:q});export{ne as Portal,q as PortalGroup,oe as useNestedPortals};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAOA,CAAC,IAAEC,QAAQ,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,WAAW;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,wBAAwB,IAAIC,CAAC,QAAK,4CAA4C;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,qCAAqC;AAAC,OAAM,KAAIC,CAAC,MAAK,oBAAoB;AAAC,SAAOC,GAAG,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAAC,CAAC;IAACW,CAAC,GAACrC,CAAC,CAACsC,CAAC,CAAC;IAAC,CAACC,CAAC,EAACC,CAAC,CAAC,GAAChC,CAAC,CAAC,MAAI;MAAC,IAAIiC,CAAC;MAAC,IAAG,CAACL,CAAC,IAAEC,CAAC,KAAG,IAAI,EAAC,OAAM,CAACI,CAAC,GAACJ,CAAC,CAACK,OAAO,KAAG,IAAI,GAACD,CAAC,GAAC,IAAI;MAAC,IAAGZ,CAAC,CAACc,QAAQ,EAAC,OAAO,IAAI;MAAC,IAAIC,CAAC,GAACT,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACU,cAAc,CAAC,wBAAwB,CAAC;MAAC,IAAGD,CAAC,EAAC,OAAOA,CAAC;MAAC,IAAGT,CAAC,KAAG,IAAI,EAAC,OAAO,IAAI;MAAC,IAAIW,CAAC,GAACX,CAAC,CAACY,aAAa,CAAC,KAAK,CAAC;MAAC,OAAOD,CAAC,CAACE,YAAY,CAAC,IAAI,EAAC,wBAAwB,CAAC,EAACb,CAAC,CAACc,IAAI,CAACC,WAAW,CAACJ,CAAC,CAAC;IAAA,CAAC,CAAC;EAAC,OAAO5C,CAAC,CAAC,MAAI;IAACqC,CAAC,KAAG,IAAI,KAAGJ,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACc,IAAI,CAACE,QAAQ,CAACZ,CAAC,CAAC,IAAEJ,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACc,IAAI,CAACC,WAAW,CAACX,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,EAACJ,CAAC,CAAC,CAAC,EAACjC,CAAC,CAAC,MAAI;IAACkC,CAAC,IAAEC,CAAC,KAAG,IAAI,IAAEG,CAAC,CAACH,CAAC,CAACK,OAAO,CAAC;EAAA,CAAC,EAAC,CAACL,CAAC,EAACG,CAAC,EAACJ,CAAC,CAAC,CAAC,EAACG,CAAC;AAAA;AAAC,IAAIa,CAAC,GAACxD,CAAC;EAACyD,CAAC,GAACtB,CAAC,CAAC,UAASK,CAAC,EAACC,CAAC,EAAC;IAAC,IAAG;QAACiB,aAAa,EAACf,CAAC,GAAC,IAAI;QAAC,GAAGC;MAAC,CAAC,GAACJ,CAAC;MAACQ,CAAC,GAACtC,CAAC,CAAC,IAAI,CAAC;MAACwC,CAAC,GAACtB,CAAC,CAACF,CAAC,CAACiC,CAAC,IAAE;QAACX,CAAC,CAACF,OAAO,GAACa,CAAC;MAAA,CAAC,CAAC,EAAClB,CAAC,CAAC;MAACI,CAAC,GAACvB,CAAC,CAAC0B,CAAC,CAAC;MAACY,CAAC,GAACjB,CAAC,IAAE,IAAI,GAACA,CAAC,GAACE,CAAC;MAACgB,CAAC,GAACvB,CAAC,CAACsB,CAAC,CAAC;MAAC,CAACE,CAAC,CAAC,GAAClD,CAAC,CAAC,MAAI;QAAC,IAAI+C,CAAC;QAAC,OAAO1B,CAAC,CAACc,QAAQ,GAAC,IAAI,GAAC,CAACY,CAAC,GAACC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACT,aAAa,CAAC,KAAK,CAAC,KAAG,IAAI,GAACQ,CAAC,GAAC,IAAI;MAAA,CAAC,CAAC;MAACI,CAAC,GAAC3D,CAAC,CAAC4D,CAAC,CAAC;MAACC,CAAC,GAACzC,CAAC,CAAC,CAAC;IAACN,CAAC,CAAC,MAAI;MAAC,CAAC2C,CAAC,IAAE,CAACC,CAAC,IAAED,CAAC,CAACN,QAAQ,CAACO,CAAC,CAAC,KAAGA,CAAC,CAACV,YAAY,CAAC,wBAAwB,EAAC,EAAE,CAAC,EAACS,CAAC,CAACP,WAAW,CAACQ,CAAC,CAAC,CAAC;IAAA,CAAC,EAAC,CAACD,CAAC,EAACC,CAAC,CAAC,CAAC,EAAC5C,CAAC,CAAC,MAAI;MAAC,IAAG4C,CAAC,IAAEC,CAAC,EAAC,OAAOA,CAAC,CAACG,QAAQ,CAACJ,CAAC,CAAC;IAAA,CAAC,EAAC,CAACC,CAAC,EAACD,CAAC,CAAC,CAAC,EAAC1C,CAAC,CAAC,MAAI;MAAC,IAAIuC,CAAC;MAAC,CAACE,CAAC,IAAE,CAACC,CAAC,KAAG/B,CAAC,CAACoC,MAAM,CAACL,CAAC,CAAC,IAAED,CAAC,CAACN,QAAQ,CAACO,CAAC,CAAC,IAAED,CAAC,CAACO,WAAW,CAACN,CAAC,CAAC,EAACD,CAAC,CAACQ,UAAU,CAACC,MAAM,IAAE,CAAC,KAAG,CAACX,CAAC,GAACE,CAAC,CAACU,aAAa,KAAG,IAAI,IAAEZ,CAAC,CAACS,WAAW,CAACP,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC,IAAIW,CAAC,GAACnC,CAAC,CAAC,CAAC;IAAC,OAAO4B,CAAC,GAAC,CAACJ,CAAC,IAAE,CAACC,CAAC,GAAC,IAAI,GAAChD,CAAC,CAAC0D,CAAC,CAAC;MAACC,QAAQ,EAAC;QAACC,GAAG,EAACxB;MAAC,CAAC;MAACyB,UAAU,EAAC/B,CAAC;MAACgC,IAAI,EAAC,CAAC,CAAC;MAACC,UAAU,EAACrB,CAAC;MAACsB,IAAI,EAAC;IAAQ,CAAC,CAAC,EAAChB,CAAC,CAAC,GAAC,IAAI;EAAA,CAAC,CAAC;AAAC,SAASiB,CAACA,CAACxC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACb,CAAC,CAACY,CAAC,CAAC;IAAC;MAACwC,OAAO,EAACrC,CAAC,GAAC,CAAC,CAAC;MAACe,aAAa,EAACd,CAAC;MAAC,GAAGI;IAAC,CAAC,GAACT,CAAC;IAACW,CAAC,GAACb,CAAC,CAAC,CAAC;EAAC,OAAOM,CAAC,GAAC7C,CAAC,CAACqD,aAAa,CAACM,CAAC,EAAC;IAAC,GAAGT,CAAC;IAACU,aAAa,EAACd,CAAC;IAAC8B,GAAG,EAACjC;EAAC,CAAC,CAAC,GAACS,CAAC,CAAC;IAACuB,QAAQ,EAAC;MAACC,GAAG,EAACjC;IAAC,CAAC;IAACkC,UAAU,EAAC3B,CAAC;IAAC4B,IAAI,EAAC,CAAC,CAAC;IAACC,UAAU,EAACrB,CAAC;IAACsB,IAAI,EAAC;EAAQ,CAAC,CAAC;AAAA;AAAC,IAAIG,CAAC,GAACjF,CAAC;EAAC0C,CAAC,GAACxC,CAAC,CAAC,IAAI,CAAC;AAAC,SAASgF,CAACA,CAAC3C,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG;MAAC2C,MAAM,EAAC1C,CAAC;MAAC,GAAGE;IAAC,CAAC,GAACJ,CAAC;IAACS,CAAC,GAAC;MAAC0B,GAAG,EAAC9C,CAAC,CAACY,CAAC;IAAC,CAAC;IAACU,CAAC,GAACb,CAAC,CAAC,CAAC;EAAC,OAAOvC,CAAC,CAACqD,aAAa,CAACT,CAAC,CAAC0C,QAAQ,EAAC;IAACC,KAAK,EAAC5C;EAAC,CAAC,EAACS,CAAC,CAAC;IAACuB,QAAQ,EAACzB,CAAC;IAAC2B,UAAU,EAAChC,CAAC;IAACkC,UAAU,EAACI,CAAC;IAACH,IAAI,EAAC;EAAe,CAAC,CAAC,CAAC;AAAA;AAAC,IAAId,CAAC,GAAC9D,CAAC,CAAC,IAAI,CAAC;AAAC,SAASoF,EAAEA,CAAA,EAAE;EAAC,IAAI/C,CAAC,GAACnC,CAAC,CAAC4D,CAAC,CAAC;IAACxB,CAAC,GAAC9B,CAAC,CAAC,EAAE,CAAC;IAAC+B,CAAC,GAACzB,CAAC,CAACgC,CAAC,KAAGR,CAAC,CAACM,OAAO,CAACyC,IAAI,CAACvC,CAAC,CAAC,EAACT,CAAC,IAAEA,CAAC,CAAC2B,QAAQ,CAAClB,CAAC,CAAC,EAAC,MAAIL,CAAC,CAACK,CAAC,CAAC,CAAC,CAAC;IAACL,CAAC,GAAC3B,CAAC,CAACgC,CAAC,IAAE;MAAC,IAAIE,CAAC,GAACV,CAAC,CAACM,OAAO,CAAC0C,OAAO,CAACxC,CAAC,CAAC;MAACE,CAAC,KAAG,CAAC,CAAC,IAAEV,CAAC,CAACM,OAAO,CAAC2C,MAAM,CAACvC,CAAC,EAAC,CAAC,CAAC,EAACX,CAAC,IAAEA,CAAC,CAACmD,UAAU,CAAC1C,CAAC,CAAC;IAAA,CAAC,CAAC;IAACJ,CAAC,GAACpC,CAAC,CAAC,OAAK;MAAC0D,QAAQ,EAACzB,CAAC;MAACiD,UAAU,EAAC/C,CAAC;MAACgD,OAAO,EAACnD;IAAC,CAAC,CAAC,EAAC,CAACC,CAAC,EAACE,CAAC,EAACH,CAAC,CAAC,CAAC;EAAC,OAAM,CAACA,CAAC,EAAChC,CAAC,CAAC,MAAI,UAAS;IAACoF,QAAQ,EAAC1C;EAAC,CAAC,EAAC;IAAC,OAAOpD,CAAC,CAACqD,aAAa,CAACa,CAAC,CAACoB,QAAQ,EAAC;MAACC,KAAK,EAACzC;IAAC,CAAC,EAACM,CAAC,CAAC;EAAA,CAAC,EAAC,CAACN,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIiD,CAAC,GAAC1D,CAAC,CAAC4C,CAAC,CAAC;EAACe,CAAC,GAAC3D,CAAC,CAAC+C,CAAC,CAAC;EAACa,EAAE,GAACC,MAAM,CAACC,MAAM,CAACJ,CAAC,EAAC;IAACK,KAAK,EAACJ;EAAC,CAAC,CAAC;AAAC,SAAOC,EAAE,IAAII,MAAM,EAACL,CAAC,IAAIM,WAAW,EAACd,EAAE,IAAIe,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}