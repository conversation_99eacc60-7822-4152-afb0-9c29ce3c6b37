{"ast": null, "code": "import { useEffect as c } from \"react\";\nimport { useLatestValue as a } from './use-latest-value.js';\nfunction i(t, e, o, n) {\n  let u = a(o);\n  c(() => {\n    if (!t) return;\n    function r(m) {\n      u.current(m);\n    }\n    return document.addEventListener(e, r, n), () => document.removeEventListener(e, r, n);\n  }, [t, e, n]);\n}\nexport { i as useDocumentEvent };", "map": {"version": 3, "names": ["useEffect", "c", "useLatestValue", "a", "i", "t", "e", "o", "n", "u", "r", "m", "current", "document", "addEventListener", "removeEventListener", "useDocumentEvent"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-document-event.js"], "sourcesContent": ["import{useEffect as c}from\"react\";import{useLatestValue as a}from'./use-latest-value.js';function i(t,e,o,n){let u=a(o);c(()=>{if(!t)return;function r(m){u.current(m)}return document.addEventListener(e,r,n),()=>document.removeEventListener(e,r,n)},[t,e,n])}export{i as useDocumentEvent};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAACI,CAAC,CAAC;EAACN,CAAC,CAAC,MAAI;IAAC,IAAG,CAACI,CAAC,EAAC;IAAO,SAASK,CAACA,CAACC,CAAC,EAAC;MAACF,CAAC,CAACG,OAAO,CAACD,CAAC,CAAC;IAAA;IAAC,OAAOE,QAAQ,CAACC,gBAAgB,CAACR,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC,EAAC,MAAIK,QAAQ,CAACE,mBAAmB,CAACT,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC;EAAA,CAAC,EAAC,CAACH,CAAC,EAACC,CAAC,EAACE,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIY,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}