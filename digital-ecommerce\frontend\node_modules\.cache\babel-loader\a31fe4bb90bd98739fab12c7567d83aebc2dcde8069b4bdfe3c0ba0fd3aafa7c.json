{"ast": null, "code": "import * as l from \"react\";\nconst s = l.createContext(null);\nfunction a() {\n  return {\n    groups: new Map(),\n    get(o, e) {\n      var i;\n      let t = this.groups.get(o);\n      t || (t = new Map(), this.groups.set(o, t));\n      let n = (i = t.get(e)) != null ? i : 0;\n      t.set(e, n + 1);\n      let r = Array.from(t.keys()).indexOf(e);\n      function u() {\n        let c = t.get(e);\n        c > 1 ? t.set(e, c - 1) : t.delete(e);\n      }\n      return [r, u];\n    }\n  };\n}\nfunction f({\n  children: o\n}) {\n  let e = l.useRef(a());\n  return l.createElement(s.Provider, {\n    value: e\n  }, o);\n}\nfunction C(o) {\n  let e = l.useContext(s);\n  if (!e) throw new Error(\"You must wrap your component in a <StableCollection>\");\n  let t = l.useId(),\n    [n, r] = e.current.get(o, t);\n  return l.useEffect(() => r, []), n;\n}\nexport { f as StableCollection, C as useStableCollectionIndex };", "map": {"version": 3, "names": ["l", "s", "createContext", "a", "groups", "Map", "get", "o", "e", "i", "t", "set", "n", "r", "Array", "from", "keys", "indexOf", "u", "c", "delete", "f", "children", "useRef", "createElement", "Provider", "value", "C", "useContext", "Error", "useId", "current", "useEffect", "StableCollection", "useStableCollectionIndex"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/stable-collection.js"], "sourcesContent": ["import*as l from\"react\";const s=l.createContext(null);function a(){return{groups:new Map,get(o,e){var i;let t=this.groups.get(o);t||(t=new Map,this.groups.set(o,t));let n=(i=t.get(e))!=null?i:0;t.set(e,n+1);let r=Array.from(t.keys()).indexOf(e);function u(){let c=t.get(e);c>1?t.set(e,c-1):t.delete(e)}return[r,u]}}}function f({children:o}){let e=l.useRef(a());return l.createElement(s.Provider,{value:e},o)}function C(o){let e=l.useContext(s);if(!e)throw new Error(\"You must wrap your component in a <StableCollection>\");let t=l.useId(),[n,r]=e.current.get(o,t);return l.useEffect(()=>r,[]),n}export{f as StableCollection,C as useStableCollectionIndex};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,OAAO;AAAC,MAAMC,CAAC,GAACD,CAAC,CAACE,aAAa,CAAC,IAAI,CAAC;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM;IAACC,MAAM,EAAC,IAAIC,GAAG,CAAD,CAAC;IAACC,GAAGA,CAACC,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIC,CAAC;MAAC,IAAIC,CAAC,GAAC,IAAI,CAACN,MAAM,CAACE,GAAG,CAACC,CAAC,CAAC;MAACG,CAAC,KAAGA,CAAC,GAAC,IAAIL,GAAG,CAAD,CAAC,EAAC,IAAI,CAACD,MAAM,CAACO,GAAG,CAACJ,CAAC,EAACG,CAAC,CAAC,CAAC;MAAC,IAAIE,CAAC,GAAC,CAACH,CAAC,GAACC,CAAC,CAACJ,GAAG,CAACE,CAAC,CAAC,KAAG,IAAI,GAACC,CAAC,GAAC,CAAC;MAACC,CAAC,CAACC,GAAG,CAACH,CAAC,EAACI,CAAC,GAAC,CAAC,CAAC;MAAC,IAAIC,CAAC,GAACC,KAAK,CAACC,IAAI,CAACL,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC,CAACC,OAAO,CAACT,CAAC,CAAC;MAAC,SAASU,CAACA,CAAA,EAAE;QAAC,IAAIC,CAAC,GAACT,CAAC,CAACJ,GAAG,CAACE,CAAC,CAAC;QAACW,CAAC,GAAC,CAAC,GAACT,CAAC,CAACC,GAAG,CAACH,CAAC,EAACW,CAAC,GAAC,CAAC,CAAC,GAACT,CAAC,CAACU,MAAM,CAACZ,CAAC,CAAC;MAAA;MAAC,OAAM,CAACK,CAAC,EAACK,CAAC,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAAC;EAACC,QAAQ,EAACf;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAACuB,MAAM,CAACpB,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOH,CAAC,CAACwB,aAAa,CAACvB,CAAC,CAACwB,QAAQ,EAAC;IAACC,KAAK,EAAClB;EAAC,CAAC,EAACD,CAAC,CAAC;AAAA;AAAC,SAASoB,CAACA,CAACpB,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAAC4B,UAAU,CAAC3B,CAAC,CAAC;EAAC,IAAG,CAACO,CAAC,EAAC,MAAM,IAAIqB,KAAK,CAAC,sDAAsD,CAAC;EAAC,IAAInB,CAAC,GAACV,CAAC,CAAC8B,KAAK,CAAC,CAAC;IAAC,CAAClB,CAAC,EAACC,CAAC,CAAC,GAACL,CAAC,CAACuB,OAAO,CAACzB,GAAG,CAACC,CAAC,EAACG,CAAC,CAAC;EAAC,OAAOV,CAAC,CAACgC,SAAS,CAAC,MAAInB,CAAC,EAAC,EAAE,CAAC,EAACD,CAAC;AAAA;AAAC,SAAOS,CAAC,IAAIY,gBAAgB,EAACN,CAAC,IAAIO,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}