{"ast": null, "code": "import { useRef as d } from \"react\";\nimport * as l from '../utils/dom.js';\nimport { useDocumentEvent as c } from './use-document-event.js';\nvar m = (e => (e[e.Ignore = 0] = \"Ignore\", e[e.Select = 1] = \"Select\", e[e.Close = 2] = \"Close\", e))(m || {});\nconst g = {\n    Ignore: {\n      kind: 0\n    },\n    Select: r => ({\n      kind: 1,\n      target: r\n    }),\n    Close: {\n      kind: 2\n    }\n  },\n  E = 200;\nfunction k(r, {\n  trigger: n,\n  action: s,\n  close: e,\n  select: a\n}) {\n  let o = d(null);\n  c(r && n !== null, \"pointerdown\", t => {\n    l.isNode(t == null ? void 0 : t.target) && n != null && n.contains(t.target) && (o.current = new Date());\n  }), c(r && n !== null, \"pointerup\", t => {\n    if (o.current === null || !l.isHTMLorSVGElement(t.target)) return;\n    let i = s(t),\n      u = new Date().getTime() - o.current.getTime();\n    switch (o.current = null, i.kind) {\n      case 0:\n        return;\n      case 1:\n        {\n          u > E && (a(i.target), e());\n          break;\n        }\n      case 2:\n        {\n          e();\n          break;\n        }\n    }\n  }, {\n    capture: !0\n  });\n}\nexport { g as Action, k as useQuickRelease };", "map": {"version": 3, "names": ["useRef", "d", "l", "useDocumentEvent", "c", "m", "e", "Ignore", "Select", "Close", "g", "kind", "r", "target", "E", "k", "trigger", "n", "action", "s", "close", "select", "a", "o", "t", "isNode", "contains", "current", "Date", "isHTMLorSVGElement", "i", "u", "getTime", "capture", "Action", "useQuickRelease"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-quick-release.js"], "sourcesContent": ["import{useRef as d}from\"react\";import*as l from'../utils/dom.js';import{useDocumentEvent as c}from'./use-document-event.js';var m=(e=>(e[e.Ignore=0]=\"Ignore\",e[e.Select=1]=\"Select\",e[e.Close=2]=\"Close\",e))(m||{});const g={Ignore:{kind:0},Select:r=>({kind:1,target:r}),Close:{kind:2}},E=200;function k(r,{trigger:n,action:s,close:e,select:a}){let o=d(null);c(r&&n!==null,\"pointerdown\",t=>{l.isNode(t==null?void 0:t.target)&&n!=null&&n.contains(t.target)&&(o.current=new Date)}),c(r&&n!==null,\"pointerup\",t=>{if(o.current===null||!l.isHTMLorSVGElement(t.target))return;let i=s(t),u=new Date().getTime()-o.current.getTime();switch(o.current=null,i.kind){case 0:return;case 1:{u>E&&(a(i.target),e());break}case 2:{e();break}}},{capture:!0})}export{g as Action,k as useQuickRelease};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,OAAM,KAAIC,CAAC,MAAK,iBAAiB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAACA,CAAC,CAACG,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACH,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,MAAMK,CAAC,GAAC;IAACH,MAAM,EAAC;MAACI,IAAI,EAAC;IAAC,CAAC;IAACH,MAAM,EAACI,CAAC,KAAG;MAACD,IAAI,EAAC,CAAC;MAACE,MAAM,EAACD;IAAC,CAAC,CAAC;IAACH,KAAK,EAAC;MAACE,IAAI,EAAC;IAAC;EAAC,CAAC;EAACG,CAAC,GAAC,GAAG;AAAC,SAASC,CAACA,CAACH,CAAC,EAAC;EAACI,OAAO,EAACC,CAAC;EAACC,MAAM,EAACC,CAAC;EAACC,KAAK,EAACd,CAAC;EAACe,MAAM,EAACC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACtB,CAAC,CAAC,IAAI,CAAC;EAACG,CAAC,CAACQ,CAAC,IAAEK,CAAC,KAAG,IAAI,EAAC,aAAa,EAACO,CAAC,IAAE;IAACtB,CAAC,CAACuB,MAAM,CAACD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACX,MAAM,CAAC,IAAEI,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACS,QAAQ,CAACF,CAAC,CAACX,MAAM,CAAC,KAAGU,CAAC,CAACI,OAAO,GAAC,IAAIC,IAAI,CAAD,CAAC,CAAC;EAAA,CAAC,CAAC,EAACxB,CAAC,CAACQ,CAAC,IAAEK,CAAC,KAAG,IAAI,EAAC,WAAW,EAACO,CAAC,IAAE;IAAC,IAAGD,CAAC,CAACI,OAAO,KAAG,IAAI,IAAE,CAACzB,CAAC,CAAC2B,kBAAkB,CAACL,CAAC,CAACX,MAAM,CAAC,EAAC;IAAO,IAAIiB,CAAC,GAACX,CAAC,CAACK,CAAC,CAAC;MAACO,CAAC,GAAC,IAAIH,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAACT,CAAC,CAACI,OAAO,CAACK,OAAO,CAAC,CAAC;IAAC,QAAOT,CAAC,CAACI,OAAO,GAAC,IAAI,EAACG,CAAC,CAACnB,IAAI;MAAE,KAAK,CAAC;QAAC;MAAO,KAAK,CAAC;QAAC;UAACoB,CAAC,GAACjB,CAAC,KAAGQ,CAAC,CAACQ,CAAC,CAACjB,MAAM,CAAC,EAACP,CAAC,CAAC,CAAC,CAAC;UAAC;QAAK;MAAC,KAAK,CAAC;QAAC;UAACA,CAAC,CAAC,CAAC;UAAC;QAAK;IAAC;EAAC,CAAC,EAAC;IAAC2B,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC;AAAA;AAAC,SAAOvB,CAAC,IAAIwB,MAAM,EAACnB,CAAC,IAAIoB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}