{"ast": null, "code": "import { nodeContains as $d4ee10de306f2510$export$4282f70798064fe0 } from \"./DOMFunctions.mjs\";\nimport { shadowDOM as $bJKXg$shadowDOM } from \"@react-stately/flags\";\n\n// https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/ShadowTreeWalker.ts\n\nclass $dfc540311bf7f109$export$63eb3ababa9c55c4 {\n  get currentNode() {\n    return this._currentNode;\n  }\n  set currentNode(node) {\n    if (!(0, $d4ee10de306f2510$export$4282f70798064fe0)(this.root, node)) throw new Error('Cannot set currentNode to a node that is not contained by the root node.');\n    const walkers = [];\n    let curNode = node;\n    let currentWalkerCurrentNode = node;\n    this._currentNode = node;\n    while (curNode && curNode !== this.root) if (curNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n      const shadowRoot = curNode;\n      const walker = this._doc.createTreeWalker(shadowRoot, this.whatToShow, {\n        acceptNode: this._acceptNode\n      });\n      walkers.push(walker);\n      walker.currentNode = currentWalkerCurrentNode;\n      this._currentSetFor.add(walker);\n      curNode = currentWalkerCurrentNode = shadowRoot.host;\n    } else curNode = curNode.parentNode;\n    const walker = this._doc.createTreeWalker(this.root, this.whatToShow, {\n      acceptNode: this._acceptNode\n    });\n    walkers.push(walker);\n    walker.currentNode = currentWalkerCurrentNode;\n    this._currentSetFor.add(walker);\n    this._walkerStack = walkers;\n  }\n  get doc() {\n    return this._doc;\n  }\n  firstChild() {\n    let currentNode = this.currentNode;\n    let newNode = this.nextNode();\n    if (!(0, $d4ee10de306f2510$export$4282f70798064fe0)(currentNode, newNode)) {\n      this.currentNode = currentNode;\n      return null;\n    }\n    if (newNode) this.currentNode = newNode;\n    return newNode;\n  }\n  lastChild() {\n    let walker = this._walkerStack[0];\n    let newNode = walker.lastChild();\n    if (newNode) this.currentNode = newNode;\n    return newNode;\n  }\n  nextNode() {\n    const nextNode = this._walkerStack[0].nextNode();\n    if (nextNode) {\n      const shadowRoot = nextNode.shadowRoot;\n      if (shadowRoot) {\n        var _this_filter;\n        let nodeResult;\n        if (typeof this.filter === 'function') nodeResult = this.filter(nextNode);else if ((_this_filter = this.filter) === null || _this_filter === void 0 ? void 0 : _this_filter.acceptNode) nodeResult = this.filter.acceptNode(nextNode);\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          this.currentNode = nextNode;\n          return nextNode;\n        }\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.nextNode();\n        if (newNode) this.currentNode = newNode;\n        return newNode;\n      }\n      if (nextNode) this.currentNode = nextNode;\n      return nextNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n        let newNode = this.nextNode();\n        if (newNode) this.currentNode = newNode;\n        return newNode;\n      } else return null;\n    }\n  }\n  previousNode() {\n    const currentWalker = this._walkerStack[0];\n    if (currentWalker.currentNode === currentWalker.root) {\n      if (this._currentSetFor.has(currentWalker)) {\n        this._currentSetFor.delete(currentWalker);\n        if (this._walkerStack.length > 1) {\n          this._walkerStack.shift();\n          let newNode = this.previousNode();\n          if (newNode) this.currentNode = newNode;\n          return newNode;\n        } else return null;\n      }\n      return null;\n    }\n    const previousNode = currentWalker.previousNode();\n    if (previousNode) {\n      const shadowRoot = previousNode.shadowRoot;\n      if (shadowRoot) {\n        var _this_filter;\n        let nodeResult;\n        if (typeof this.filter === 'function') nodeResult = this.filter(previousNode);else if ((_this_filter = this.filter) === null || _this_filter === void 0 ? void 0 : _this_filter.acceptNode) nodeResult = this.filter.acceptNode(previousNode);\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          if (previousNode) this.currentNode = previousNode;\n          return previousNode;\n        }\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.lastChild();\n        if (newNode) this.currentNode = newNode;\n        return newNode;\n      }\n      if (previousNode) this.currentNode = previousNode;\n      return previousNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n        let newNode = this.previousNode();\n        if (newNode) this.currentNode = newNode;\n        return newNode;\n      } else return null;\n    }\n  }\n  /**\n   * @deprecated\n   */\n  nextSibling() {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n    return null;\n  }\n  /**\n   * @deprecated\n   */\n  previousSibling() {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n    return null;\n  }\n  /**\n   * @deprecated\n   */\n  parentNode() {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n    return null;\n  }\n  constructor(doc, root, whatToShow, filter) {\n    this._walkerStack = [];\n    this._currentSetFor = new Set();\n    this._acceptNode = node => {\n      if (node.nodeType === Node.ELEMENT_NODE) {\n        const shadowRoot = node.shadowRoot;\n        if (shadowRoot) {\n          const walker = this._doc.createTreeWalker(shadowRoot, this.whatToShow, {\n            acceptNode: this._acceptNode\n          });\n          this._walkerStack.unshift(walker);\n          return NodeFilter.FILTER_ACCEPT;\n        } else {\n          var _this_filter;\n          if (typeof this.filter === 'function') return this.filter(node);else if ((_this_filter = this.filter) === null || _this_filter === void 0 ? void 0 : _this_filter.acceptNode) return this.filter.acceptNode(node);else if (this.filter === null) return NodeFilter.FILTER_ACCEPT;\n        }\n      }\n      return NodeFilter.FILTER_SKIP;\n    };\n    this._doc = doc;\n    this.root = root;\n    this.filter = filter !== null && filter !== void 0 ? filter : null;\n    this.whatToShow = whatToShow !== null && whatToShow !== void 0 ? whatToShow : NodeFilter.SHOW_ALL;\n    this._currentNode = root;\n    this._walkerStack.unshift(doc.createTreeWalker(root, whatToShow, this._acceptNode));\n    const shadowRoot = root.shadowRoot;\n    if (shadowRoot) {\n      const walker = this._doc.createTreeWalker(shadowRoot, this.whatToShow, {\n        acceptNode: this._acceptNode\n      });\n      this._walkerStack.unshift(walker);\n    }\n  }\n}\nfunction $dfc540311bf7f109$export$4d0f8be8b12a7ef6(doc, root, whatToShow, filter) {\n  if ((0, $bJKXg$shadowDOM)()) return new $dfc540311bf7f109$export$63eb3ababa9c55c4(doc, root, whatToShow, filter);\n  return doc.createTreeWalker(root, whatToShow, filter);\n}\nexport { $dfc540311bf7f109$export$63eb3ababa9c55c4 as ShadowTreeWalker, $dfc540311bf7f109$export$4d0f8be8b12a7ef6 as createShadowTreeWalker };", "map": {"version": 3, "names": ["$dfc540311bf7f109$export$63eb3ababa9c55c4", "currentNode", "_currentNode", "node", "$d4ee10de306f2510$export$4282f70798064fe0", "root", "Error", "walkers", "curNode", "currentWalkerCurrentNode", "nodeType", "Node", "DOCUMENT_FRAGMENT_NODE", "shadowRoot", "walker", "_doc", "createTreeWalker", "whatToShow", "acceptNode", "_acceptNode", "push", "_currentSetFor", "add", "host", "parentNode", "_walker<PERSON><PERSON>ck", "doc", "<PERSON><PERSON><PERSON><PERSON>", "newNode", "nextNode", "<PERSON><PERSON><PERSON><PERSON>", "_this_filter", "nodeResult", "filter", "Node<PERSON><PERSON><PERSON>", "FILTER_ACCEPT", "length", "shift", "previousNode", "<PERSON><PERSON><PERSON><PERSON>", "has", "delete", "nextS<PERSON>ling", "previousSibling", "constructor", "Set", "ELEMENT_NODE", "unshift", "FILTER_SKIP", "SHOW_ALL", "$dfc540311bf7f109$export$4d0f8be8b12a7ef6", "$bJKXg$shadowDOM"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\shadowdom\\ShadowTreeWalker.ts"], "sourcesContent": ["// https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/ShadowTreeWalker.ts\n\nimport {nodeContains} from './DOMFunctions';\nimport {shadowDOM} from '@react-stately/flags';\n\nexport class ShadowTreeWalker implements TreeWalker {\n  public readonly filter: NodeFilter | null;\n  public readonly root: Node;\n  public readonly whatToShow: number;\n\n  private _doc: Document;\n  private _walkerStack: Array<TreeWalker> = [];\n  private _currentNode: Node;\n  private _currentSetFor: Set<TreeWalker> = new Set();\n\n  constructor(\n      doc: Document,\n      root: Node,\n      whatToShow?: number,\n      filter?: NodeFilter | null\n    ) {\n    this._doc = doc;\n    this.root = root;\n    this.filter = filter ?? null;\n    this.whatToShow = whatToShow ?? NodeFilter.SHOW_ALL;\n    this._currentNode = root;\n\n    this._walkerStack.unshift(\n      doc.createTreeWalker(root, whatToShow, this._acceptNode)\n    );\n\n    const shadowRoot = (root as Element).shadowRoot;\n\n    if (shadowRoot) {\n      const walker = this._doc.createTreeWalker(\n        shadowRoot,\n        this.whatToShow,\n        {acceptNode: this._acceptNode}\n      );\n\n      this._walkerStack.unshift(walker);\n    }\n  }\n\n  private _acceptNode = (node: Node): number => {\n    if (node.nodeType === Node.ELEMENT_NODE) {\n      const shadowRoot = (node as Element).shadowRoot;\n\n      if (shadowRoot) {\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        this._walkerStack.unshift(walker);\n\n        return NodeFilter.FILTER_ACCEPT;\n      } else {\n        if (typeof this.filter === 'function') {\n          return this.filter(node);\n        } else if (this.filter?.acceptNode) {\n          return this.filter.acceptNode(node);\n        } else if (this.filter === null) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n      }\n    }\n\n    return NodeFilter.FILTER_SKIP;\n  };\n\n  public get currentNode(): Node {\n    return this._currentNode;\n  }\n\n  public set currentNode(node: Node) {\n    if (!nodeContains(this.root, node)) {\n      throw new Error(\n        'Cannot set currentNode to a node that is not contained by the root node.'\n      );\n    }\n\n    const walkers: TreeWalker[] = [];\n    let curNode: Node | null | undefined = node;\n    let currentWalkerCurrentNode = node;\n\n    this._currentNode = node;\n\n    while (curNode && curNode !== this.root) {\n      if (curNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n        const shadowRoot = curNode as ShadowRoot;\n\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        walkers.push(walker);\n\n        walker.currentNode = currentWalkerCurrentNode;\n\n        this._currentSetFor.add(walker);\n\n        curNode = currentWalkerCurrentNode = shadowRoot.host;\n      } else {\n        curNode = curNode.parentNode;\n      }\n    }\n\n    const walker = this._doc.createTreeWalker(\n      this.root,\n      this.whatToShow,\n      {acceptNode: this._acceptNode}\n    );\n\n    walkers.push(walker);\n\n    walker.currentNode = currentWalkerCurrentNode;\n\n    this._currentSetFor.add(walker);\n\n    this._walkerStack = walkers;\n  }\n\n  public get doc(): Document {\n    return this._doc;\n  }\n\n  public firstChild(): Node | null {\n    let currentNode = this.currentNode;\n    let newNode = this.nextNode();\n    if (!nodeContains(currentNode, newNode)) {\n      this.currentNode = currentNode;\n      return null;\n    }\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public lastChild(): Node | null {\n    let walker = this._walkerStack[0];\n    let newNode = walker.lastChild();\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public nextNode(): Node | null {\n    const nextNode = this._walkerStack[0].nextNode();\n\n    if (nextNode) {\n      const shadowRoot = (nextNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(nextNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(nextNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          this.currentNode = nextNode;\n          return nextNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (nextNode) {\n        this.currentNode = nextNode;\n      }\n      return nextNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n  public previousNode(): Node | null {\n    const currentWalker = this._walkerStack[0];\n\n    if (currentWalker.currentNode === currentWalker.root) {\n      if (this._currentSetFor.has(currentWalker)) {\n        this._currentSetFor.delete(currentWalker);\n\n        if (this._walkerStack.length > 1) {\n          this._walkerStack.shift();\n          let newNode = this.previousNode();\n          if (newNode) {\n            this.currentNode = newNode;\n          }\n          return newNode;\n        } else {\n          return null;\n        }\n      }\n\n      return null;\n    }\n\n    const previousNode = currentWalker.previousNode();\n\n    if (previousNode) {\n      const shadowRoot = (previousNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(previousNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(previousNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          if (previousNode) {\n            this.currentNode = previousNode;\n          }\n          return previousNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.lastChild();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (previousNode) {\n        this.currentNode = previousNode;\n      }\n      return previousNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.previousNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n    /**\n     * @deprecated\n     */\n  public nextSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public previousSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public parentNode(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n}\n\n/**\n * ShadowDOM safe version of document.createTreeWalker.\n */\nexport function createShadowTreeWalker(\n    doc: Document,\n    root: Node,\n    whatToShow?: number,\n    filter?: NodeFilter | null\n): TreeWalker {\n  if (shadowDOM()) {\n    return new ShadowTreeWalker(doc, root, whatToShow, filter);\n  }\n  return doc.createTreeWalker(root, whatToShow, filter);\n}\n"], "mappings": ";;;AAAA;;AAKO,MAAMA,yCAAA;EAmEX,IAAWC,YAAA,EAAoB;IAC7B,OAAO,IAAI,CAACC,YAAY;EAC1B;EAEA,IAAWD,YAAYE,IAAU,EAAE;IACjC,IAAI,CAAC,IAAAC,yCAAW,EAAE,IAAI,CAACC,IAAI,EAAEF,IAAA,GAC3B,MAAM,IAAIG,KAAA,CACR;IAIJ,MAAMC,OAAA,GAAwB,EAAE;IAChC,IAAIC,OAAA,GAAmCL,IAAA;IACvC,IAAIM,wBAAA,GAA2BN,IAAA;IAE/B,IAAI,CAACD,YAAY,GAAGC,IAAA;IAEpB,OAAOK,OAAA,IAAWA,OAAA,KAAY,IAAI,CAACH,IAAI,EACrC,IAAIG,OAAA,CAAQE,QAAQ,KAAKC,IAAA,CAAKC,sBAAsB,EAAE;MACpD,MAAMC,UAAA,GAAaL,OAAA;MAEnB,MAAMM,MAAA,GAAS,IAAI,CAACC,IAAI,CAACC,gBAAgB,CACvCH,UAAA,EACA,IAAI,CAACI,UAAU,EACf;QAACC,UAAA,EAAY,IAAI,CAACC;MAAW;MAG/BZ,OAAA,CAAQa,IAAI,CAACN,MAAA;MAEbA,MAAA,CAAOb,WAAW,GAAGQ,wBAAA;MAErB,IAAI,CAACY,cAAc,CAACC,GAAG,CAACR,MAAA;MAExBN,OAAA,GAAUC,wBAAA,GAA2BI,UAAA,CAAWU,IAAI;IACtD,OACEf,OAAA,GAAUA,OAAA,CAAQgB,UAAU;IAIhC,MAAMV,MAAA,GAAS,IAAI,CAACC,IAAI,CAACC,gBAAgB,CACvC,IAAI,CAACX,IAAI,EACT,IAAI,CAACY,UAAU,EACf;MAACC,UAAA,EAAY,IAAI,CAACC;IAAW;IAG/BZ,OAAA,CAAQa,IAAI,CAACN,MAAA;IAEbA,MAAA,CAAOb,WAAW,GAAGQ,wBAAA;IAErB,IAAI,CAACY,cAAc,CAACC,GAAG,CAACR,MAAA;IAExB,IAAI,CAACW,YAAY,GAAGlB,OAAA;EACtB;EAEA,IAAWmB,IAAA,EAAgB;IACzB,OAAO,IAAI,CAACX,IAAI;EAClB;EAEOY,WAAA,EAA0B;IAC/B,IAAI1B,WAAA,GAAc,IAAI,CAACA,WAAW;IAClC,IAAI2B,OAAA,GAAU,IAAI,CAACC,QAAQ;IAC3B,IAAI,CAAC,IAAAzB,yCAAW,EAAEH,WAAA,EAAa2B,OAAA,GAAU;MACvC,IAAI,CAAC3B,WAAW,GAAGA,WAAA;MACnB,OAAO;IACT;IACA,IAAI2B,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;IAErB,OAAOA,OAAA;EACT;EAEOE,UAAA,EAAyB;IAC9B,IAAIhB,MAAA,GAAS,IAAI,CAACW,YAAY,CAAC,EAAE;IACjC,IAAIG,OAAA,GAAUd,MAAA,CAAOgB,SAAS;IAC9B,IAAIF,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;IAErB,OAAOA,OAAA;EACT;EAEOC,SAAA,EAAwB;IAC7B,MAAMA,QAAA,GAAW,IAAI,CAACJ,YAAY,CAAC,EAAE,CAACI,QAAQ;IAE9C,IAAIA,QAAA,EAAU;MACZ,MAAMhB,UAAA,GAAagB,QAAC,CAAqBhB,UAAU;MAEnD,IAAIA,UAAA,EAAY;YAKHkB,YAAA;QAJX,IAAIC,UAAA;QAEJ,IAAI,OAAO,IAAI,CAACC,MAAM,KAAK,YACzBD,UAAA,GAAa,IAAI,CAACC,MAAM,CAACJ,QAAA,OACpB,KAAIE,YAAA,OAAI,CAACE,MAAM,cAAXF,YAAA,uBAAAA,YAAA,CAAab,UAAU,EAChCc,UAAA,GAAa,IAAI,CAACC,MAAM,CAACf,UAAU,CAACW,QAAA;QAGtC,IAAIG,UAAA,KAAeE,UAAA,CAAWC,aAAa,EAAE;UAC3C,IAAI,CAAClC,WAAW,GAAG4B,QAAA;UACnB,OAAOA,QAAA;QACT;QAEA;QACA;QACA,IAAID,OAAA,GAAU,IAAI,CAACC,QAAQ;QAC3B,IAAID,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;QAErB,OAAOA,OAAA;MACT;MAEA,IAAIC,QAAA,EACF,IAAI,CAAC5B,WAAW,GAAG4B,QAAA;MAErB,OAAOA,QAAA;IACT,OAAO;MACL,IAAI,IAAI,CAACJ,YAAY,CAACW,MAAM,GAAG,GAAG;QAChC,IAAI,CAACX,YAAY,CAACY,KAAK;QAEvB,IAAIT,OAAA,GAAU,IAAI,CAACC,QAAQ;QAC3B,IAAID,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;QAErB,OAAOA,OAAA;MACT,OACE,OAAO;IAEX;EACF;EAEOU,aAAA,EAA4B;IACjC,MAAMC,aAAA,GAAgB,IAAI,CAACd,YAAY,CAAC,EAAE;IAE1C,IAAIc,aAAA,CAActC,WAAW,KAAKsC,aAAA,CAAclC,IAAI,EAAE;MACpD,IAAI,IAAI,CAACgB,cAAc,CAACmB,GAAG,CAACD,aAAA,GAAgB;QAC1C,IAAI,CAAClB,cAAc,CAACoB,MAAM,CAACF,aAAA;QAE3B,IAAI,IAAI,CAACd,YAAY,CAACW,MAAM,GAAG,GAAG;UAChC,IAAI,CAACX,YAAY,CAACY,KAAK;UACvB,IAAIT,OAAA,GAAU,IAAI,CAACU,YAAY;UAC/B,IAAIV,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;UAErB,OAAOA,OAAA;QACT,OACE,OAAO;MAEX;MAEA,OAAO;IACT;IAEA,MAAMU,YAAA,GAAeC,aAAA,CAAcD,YAAY;IAE/C,IAAIA,YAAA,EAAc;MAChB,MAAMzB,UAAA,GAAayB,YAAC,CAAyBzB,UAAU;MAEvD,IAAIA,UAAA,EAAY;YAKHkB,YAAA;QAJX,IAAIC,UAAA;QAEJ,IAAI,OAAO,IAAI,CAACC,MAAM,KAAK,YACzBD,UAAA,GAAa,IAAI,CAACC,MAAM,CAACK,YAAA,OACpB,KAAIP,YAAA,OAAI,CAACE,MAAM,cAAXF,YAAA,uBAAAA,YAAA,CAAab,UAAU,EAChCc,UAAA,GAAa,IAAI,CAACC,MAAM,CAACf,UAAU,CAACoB,YAAA;QAGtC,IAAIN,UAAA,KAAeE,UAAA,CAAWC,aAAa,EAAE;UAC3C,IAAIG,YAAA,EACF,IAAI,CAACrC,WAAW,GAAGqC,YAAA;UAErB,OAAOA,YAAA;QACT;QAEA;QACA;QACA,IAAIV,OAAA,GAAU,IAAI,CAACE,SAAS;QAC5B,IAAIF,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;QAErB,OAAOA,OAAA;MACT;MAEA,IAAIU,YAAA,EACF,IAAI,CAACrC,WAAW,GAAGqC,YAAA;MAErB,OAAOA,YAAA;IACT,OAAO;MACL,IAAI,IAAI,CAACb,YAAY,CAACW,MAAM,GAAG,GAAG;QAChC,IAAI,CAACX,YAAY,CAACY,KAAK;QAEvB,IAAIT,OAAA,GAAU,IAAI,CAACU,YAAY;QAC/B,IAAIV,OAAA,EACF,IAAI,CAAC3B,WAAW,GAAG2B,OAAA;QAErB,OAAOA,OAAA;MACT,OACE,OAAO;IAEX;EACF;EAEE;;;EAGFc,WAAOA,CAAA,EAA2B;IAChC;IACA;IACA;IAEA,OAAO;EACT;EAEE;;;EAGFC,eAAOA,CAAA,EAA+B;IACpC;IACA;IACA;IAEA,OAAO;EACT;EAEE;;;EAGFnB,UAAOA,CAAA,EAA0B;IAC/B;IACA;IACA;IAEA,OAAO;EACT;EA/RAoB,YACIlB,GAAa,EACbrB,IAAU,EACVY,UAAmB,EACnBgB,MAA0B,EAC1B;SATIR,YAAA,GAAkC,EAAE;SAEpCJ,cAAA,GAAkC,IAAIwB,GAAA;SA+BtC1B,WAAA,GAAehB,IAAA;MACrB,IAAIA,IAAA,CAAKO,QAAQ,KAAKC,IAAA,CAAKmC,YAAY,EAAE;QACvC,MAAMjC,UAAA,GAAaV,IAAC,CAAiBU,UAAU;QAE/C,IAAIA,UAAA,EAAY;UACd,MAAMC,MAAA,GAAS,IAAI,CAACC,IAAI,CAACC,gBAAgB,CACvCH,UAAA,EACA,IAAI,CAACI,UAAU,EACf;YAACC,UAAA,EAAY,IAAI,CAACC;UAAW;UAG/B,IAAI,CAACM,YAAY,CAACsB,OAAO,CAACjC,MAAA;UAE1B,OAAOoB,UAAA,CAAWC,aAAa;QACjC,OAAO;cAGMJ,YAAA;UAFX,IAAI,OAAO,IAAI,CAACE,MAAM,KAAK,YACzB,OAAO,IAAI,CAACA,MAAM,CAAC9B,IAAA,OACd,KAAI4B,YAAA,OAAI,CAACE,MAAM,cAAXF,YAAA,uBAAAA,YAAA,CAAab,UAAU,EAChC,OAAO,IAAI,CAACe,MAAM,CAACf,UAAU,CAACf,IAAA,OACzB,IAAI,IAAI,CAAC8B,MAAM,KAAK,MACzB,OAAOC,UAAA,CAAWC,aAAa;QAEnC;MACF;MAEA,OAAOD,UAAA,CAAWc,WAAW;IAC/B;IAjDE,IAAI,CAACjC,IAAI,GAAGW,GAAA;IACZ,IAAI,CAACrB,IAAI,GAAGA,IAAA;IACZ,IAAI,CAAC4B,MAAM,GAAGA,MAAA,aAAAA,MAAA,cAAAA,MAAA,GAAU;IACxB,IAAI,CAAChB,UAAU,GAAGA,UAAA,aAAAA,UAAA,cAAAA,UAAA,GAAciB,UAAA,CAAWe,QAAQ;IACnD,IAAI,CAAC/C,YAAY,GAAGG,IAAA;IAEpB,IAAI,CAACoB,YAAY,CAACsB,OAAO,CACvBrB,GAAA,CAAIV,gBAAgB,CAACX,IAAA,EAAMY,UAAA,EAAY,IAAI,CAACE,WAAW;IAGzD,MAAMN,UAAA,GAAaR,IAAC,CAAiBQ,UAAU;IAE/C,IAAIA,UAAA,EAAY;MACd,MAAMC,MAAA,GAAS,IAAI,CAACC,IAAI,CAACC,gBAAgB,CACvCH,UAAA,EACA,IAAI,CAACI,UAAU,EACf;QAACC,UAAA,EAAY,IAAI,CAACC;MAAW;MAG/B,IAAI,CAACM,YAAY,CAACsB,OAAO,CAACjC,MAAA;IAC5B;EACF;AAqQF;AAKO,SAASoC,0CACZxB,GAAa,EACbrB,IAAU,EACVY,UAAmB,EACnBgB,MAA0B;EAE5B,IAAI,IAAAkB,gBAAQ,KACV,OAAO,IAAInD,yCAAA,CAAiB0B,GAAA,EAAKrB,IAAA,EAAMY,UAAA,EAAYgB,MAAA;EAErD,OAAOP,GAAA,CAAIV,gBAAgB,CAACX,IAAA,EAAMY,UAAA,EAAYgB,MAAA;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}