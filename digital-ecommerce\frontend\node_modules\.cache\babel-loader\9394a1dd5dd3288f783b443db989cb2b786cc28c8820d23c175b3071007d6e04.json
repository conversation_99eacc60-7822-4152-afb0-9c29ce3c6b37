{"ast": null, "code": "import { useState as $3whtM$useState, useRef as $3whtM$useRef, useEffect as $3whtM$useEffect, useCallback as $3whtM$useCallback } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $458b0a5536c1a7cf$export$40bfa8c7b0832715(value, defaultValue, onChange) {\n  let [stateValue, setStateValue] = (0, $3whtM$useState)(value || defaultValue);\n  let isControlledRef = (0, $3whtM$useRef)(value !== undefined);\n  let isControlled = value !== undefined;\n  (0, $3whtM$useEffect)(() => {\n    let wasControlled = isControlledRef.current;\n    if (wasControlled !== isControlled && process.env.NODE_ENV !== 'production') console.warn(`WARN: A component changed from ${wasControlled ? 'controlled' : 'uncontrolled'} to ${isControlled ? 'controlled' : 'uncontrolled'}.`);\n    isControlledRef.current = isControlled;\n  }, [isControlled]);\n  let currentValue = isControlled ? value : stateValue;\n  let setValue = (0, $3whtM$useCallback)((value, ...args) => {\n    let onChangeCaller = (value, ...onChangeArgs) => {\n      if (onChange) {\n        if (!Object.is(currentValue, value)) onChange(value, ...onChangeArgs);\n      }\n      if (!isControlled)\n        // If uncontrolled, mutate the currentValue local variable so that\n        // calling setState multiple times with the same value only emits onChange once.\n        // We do not use a ref for this because we specifically _do_ want the value to\n        // reset every render, and assigning to a ref in render breaks aborted suspended renders.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        currentValue = value;\n    };\n    if (typeof value === 'function') {\n      if (process.env.NODE_ENV !== 'production') console.warn('We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320');\n      // this supports functional updates https://reactjs.org/docs/hooks-reference.html#functional-updates\n      // when someone using useControlledState calls setControlledState(myFunc)\n      // this will call our useState setState with a function as well which invokes myFunc and calls onChange with the value from myFunc\n      // if we're in an uncontrolled state, then we also return the value of myFunc which to setState looks as though it was just called with myFunc from the beginning\n      // otherwise we just return the controlled value, which won't cause a rerender because React knows to bail out when the value is the same\n      let updateFunction = (oldValue, ...functionArgs) => {\n        let interceptedValue = value(isControlled ? currentValue : oldValue, ...functionArgs);\n        onChangeCaller(interceptedValue, ...args);\n        if (!isControlled) return interceptedValue;\n        return oldValue;\n      };\n      setStateValue(updateFunction);\n    } else {\n      if (!isControlled) setStateValue(value);\n      onChangeCaller(value, ...args);\n    }\n  }, [isControlled, currentValue, onChange]);\n  return [currentValue, setValue];\n}\nexport { $458b0a5536c1a7cf$export$40bfa8c7b0832715 as useControlledState };", "map": {"version": 3, "names": ["$458b0a5536c1a7cf$export$40bfa8c7b0832715", "value", "defaultValue", "onChange", "stateValue", "setStateValue", "$3whtM$useState", "isControlledRef", "$3whtM$useRef", "undefined", "isControlled", "$3whtM$useEffect", "wasControlled", "current", "process", "env", "NODE_ENV", "console", "warn", "currentValue", "setValue", "$3whtM$useCallback", "args", "onChangeCaller", "onChangeArgs", "Object", "is", "updateFunction", "oldValue", "functionArgs", "interceptedValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-stately\\utils\\dist\\packages\\@react-stately\\utils\\src\\useControlledState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\n\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined>, defaultValue: Exclude<T, undefined> | undefined, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined> | undefined, defaultValue: Exclude<T, undefined>, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: T, defaultValue: T, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void] {\n  let [stateValue, setStateValue] = useState(value || defaultValue);\n\n  let isControlledRef = useRef(value !== undefined);\n  let isControlled = value !== undefined;\n  useEffect(() => {\n    let wasControlled = isControlledRef.current;\n    if (wasControlled !== isControlled && process.env.NODE_ENV !== 'production') {\n      console.warn(`WARN: A component changed from ${wasControlled ? 'controlled' : 'uncontrolled'} to ${isControlled ? 'controlled' : 'uncontrolled'}.`);\n    }\n    isControlledRef.current = isControlled;\n  }, [isControlled]);\n\n  let currentValue = isControlled ? value : stateValue;\n  let setValue = useCallback((value, ...args) => {\n    let onChangeCaller = (value, ...onChangeArgs) => {\n      if (onChange) {\n        if (!Object.is(currentValue, value)) {\n          onChange(value, ...onChangeArgs);\n        }\n      }\n      if (!isControlled) {\n        // If uncontrolled, mutate the currentValue local variable so that\n        // calling setState multiple times with the same value only emits onChange once.\n        // We do not use a ref for this because we specifically _do_ want the value to\n        // reset every render, and assigning to a ref in render breaks aborted suspended renders.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        currentValue = value;\n      }\n    };\n\n    if (typeof value === 'function') {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320');\n      }\n      // this supports functional updates https://reactjs.org/docs/hooks-reference.html#functional-updates\n      // when someone using useControlledState calls setControlledState(myFunc)\n      // this will call our useState setState with a function as well which invokes myFunc and calls onChange with the value from myFunc\n      // if we're in an uncontrolled state, then we also return the value of myFunc which to setState looks as though it was just called with myFunc from the beginning\n      // otherwise we just return the controlled value, which won't cause a rerender because React knows to bail out when the value is the same\n      let updateFunction = (oldValue, ...functionArgs) => {\n        let interceptedValue = value(isControlled ? currentValue : oldValue, ...functionArgs);\n        onChangeCaller(interceptedValue, ...args);\n        if (!isControlled) {\n          return interceptedValue;\n        }\n        return oldValue;\n      };\n      setStateValue(updateFunction);\n    } else {\n      if (!isControlled) {\n        setStateValue(value);\n      }\n      onChangeCaller(value, ...args);\n    }\n  }, [isControlled, currentValue, onChange]);\n\n  return [currentValue, setValue];\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAgBO,SAASA,0CAA6BC,KAAQ,EAAEC,YAAe,EAAEC,QAAyC;EAC/G,IAAI,CAACC,UAAA,EAAYC,aAAA,CAAc,GAAG,IAAAC,eAAO,EAAEL,KAAA,IAASC,YAAA;EAEpD,IAAIK,eAAA,GAAkB,IAAAC,aAAK,EAAEP,KAAA,KAAUQ,SAAA;EACvC,IAAIC,YAAA,GAAeT,KAAA,KAAUQ,SAAA;EAC7B,IAAAE,gBAAQ,EAAE;IACR,IAAIC,aAAA,GAAgBL,eAAA,CAAgBM,OAAO;IAC3C,IAAID,aAAA,KAAkBF,YAAA,IAAgBI,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cAC7DC,OAAA,CAAQC,IAAI,CAAC,kCAAkCN,aAAA,GAAgB,eAAe,qBAAqBF,YAAA,GAAe,eAAe,iBAAiB;IAEpJH,eAAA,CAAgBM,OAAO,GAAGH,YAAA;EAC5B,GAAG,CAACA,YAAA,CAAa;EAEjB,IAAIS,YAAA,GAAeT,YAAA,GAAeT,KAAA,GAAQG,UAAA;EAC1C,IAAIgB,QAAA,GAAW,IAAAC,kBAAU,EAAE,CAACpB,KAAA,EAAO,GAAGqB,IAAA;IACpC,IAAIC,cAAA,GAAiBA,CAACtB,KAAA,EAAO,GAAGuB,YAAA;MAC9B,IAAIrB,QAAA,EACF;QAAA,IAAI,CAACsB,MAAA,CAAOC,EAAE,CAACP,YAAA,EAAclB,KAAA,GAC3BE,QAAA,CAASF,KAAA,KAAUuB,YAAA;MACrB;MAEF,IAAI,CAACd,YAAA;QACH;QACA;QACA;QACA;QACA;QACAS,YAAA,GAAelB,KAAA;IAEnB;IAEA,IAAI,OAAOA,KAAA,KAAU,YAAY;MAC/B,IAAIa,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cAC3BC,OAAA,CAAQC,IAAI,CAAC;MAEf;MACA;MACA;MACA;MACA;MACA,IAAIS,cAAA,GAAiBA,CAACC,QAAA,EAAU,GAAGC,YAAA;QACjC,IAAIC,gBAAA,GAAmB7B,KAAA,CAAMS,YAAA,GAAeS,YAAA,GAAeS,QAAA,KAAaC,YAAA;QACxEN,cAAA,CAAeO,gBAAA,KAAqBR,IAAA;QACpC,IAAI,CAACZ,YAAA,EACH,OAAOoB,gBAAA;QAET,OAAOF,QAAA;MACT;MACAvB,aAAA,CAAcsB,cAAA;IAChB,OAAO;MACL,IAAI,CAACjB,YAAA,EACHL,aAAA,CAAcJ,KAAA;MAEhBsB,cAAA,CAAetB,KAAA,KAAUqB,IAAA;IAC3B;EACF,GAAG,CAACZ,YAAA,EAAcS,YAAA,EAAchB,QAAA,CAAS;EAEzC,OAAO,CAACgB,YAAA,EAAcC,QAAA,CAAS;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}