{"ast": null, "code": "\"use client\";\n\nimport D, { createContext as k, useContext as v, useMemo as T, useState as _ } from \"react\";\nimport { useEvent as P } from '../../hooks/use-event.js';\nimport { useId as A } from '../../hooks/use-id.js';\nimport { useIsoMorphicEffect as B } from '../../hooks/use-iso-morphic-effect.js';\nimport { useSyncRefs as F } from '../../hooks/use-sync-refs.js';\nimport { useDisabled as M } from '../../internal/disabled.js';\nimport { useProvidedId as S } from '../../internal/id.js';\nimport * as m from '../../utils/dom.js';\nimport { forwardRefWithAs as I, useRender as H } from '../../utils/render.js';\nlet L = k(null);\nL.displayName = \"LabelContext\";\nfunction C() {\n  let n = v(L);\n  if (n === null) {\n    let l = new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");\n    throw Error.captureStackTrace && Error.captureStackTrace(l, C), l;\n  }\n  return n;\n}\nfunction N(n) {\n  var a, e, o;\n  let l = (e = (a = v(L)) == null ? void 0 : a.value) != null ? e : void 0;\n  return ((o = n == null ? void 0 : n.length) != null ? o : 0) > 0 ? [l, ...n].filter(Boolean).join(\" \") : l;\n}\nfunction Q({\n  inherit: n = !1\n} = {}) {\n  let l = N(),\n    [a, e] = _([]),\n    o = n ? [l, ...a].filter(Boolean) : a;\n  return [o.length > 0 ? o.join(\" \") : void 0, T(() => function (t) {\n    let p = P(i => (e(u => [...u, i]), () => e(u => {\n        let d = u.slice(),\n          f = d.indexOf(i);\n        return f !== -1 && d.splice(f, 1), d;\n      }))),\n      b = T(() => ({\n        register: p,\n        slot: t.slot,\n        name: t.name,\n        props: t.props,\n        value: t.value\n      }), [p, t.slot, t.name, t.props, t.value]);\n    return D.createElement(L.Provider, {\n      value: b\n    }, t.children);\n  }, [e])];\n}\nlet G = \"label\";\nfunction U(n, l) {\n  var E;\n  let a = A(),\n    e = C(),\n    o = S(),\n    y = M(),\n    {\n      id: t = `headlessui-label-${a}`,\n      htmlFor: p = o != null ? o : (E = e.props) == null ? void 0 : E.htmlFor,\n      passive: b = !1,\n      ...i\n    } = n,\n    u = F(l);\n  B(() => e.register(t), [t, e.register]);\n  let d = P(s => {\n      let g = s.currentTarget;\n      if (!(s.target !== s.currentTarget && m.isInteractiveElement(s.target)) && (m.isHTMLLabelElement(g) && s.preventDefault(), e.props && \"onClick\" in e.props && typeof e.props.onClick == \"function\" && e.props.onClick(s), m.isHTMLLabelElement(g))) {\n        let r = document.getElementById(g.htmlFor);\n        if (r) {\n          let x = r.getAttribute(\"disabled\");\n          if (x === \"true\" || x === \"\") return;\n          let h = r.getAttribute(\"aria-disabled\");\n          if (h === \"true\" || h === \"\") return;\n          (m.isHTMLInputElement(r) && (r.type === \"file\" || r.type === \"radio\" || r.type === \"checkbox\") || r.role === \"radio\" || r.role === \"checkbox\" || r.role === \"switch\") && r.click(), r.focus({\n            preventScroll: !0\n          });\n        }\n      }\n    }),\n    f = y || !1,\n    R = T(() => ({\n      ...e.slot,\n      disabled: f\n    }), [e.slot, f]),\n    c = {\n      ref: u,\n      ...e.props,\n      id: t,\n      htmlFor: p,\n      onClick: d\n    };\n  return b && (\"onClick\" in c && (delete c.htmlFor, delete c.onClick), \"onClick\" in i && delete i.onClick), H()({\n    ourProps: c,\n    theirProps: i,\n    slot: R,\n    defaultTag: p ? G : \"div\",\n    name: e.name || \"Label\"\n  });\n}\nlet j = I(U),\n  V = Object.assign(j, {});\nexport { V as Label, C as useLabelContext, N as useLabelledBy, Q as useLabels };", "map": {"version": 3, "names": ["D", "createContext", "k", "useContext", "v", "useMemo", "T", "useState", "_", "useEvent", "P", "useId", "A", "useIsoMorphicEffect", "B", "useSyncRefs", "F", "useDisabled", "M", "useProvidedId", "S", "m", "forwardRefWithAs", "I", "useRender", "H", "L", "displayName", "C", "n", "l", "Error", "captureStackTrace", "N", "a", "e", "o", "value", "length", "filter", "Boolean", "join", "Q", "inherit", "t", "p", "i", "u", "d", "slice", "f", "indexOf", "splice", "b", "register", "slot", "name", "props", "createElement", "Provider", "children", "G", "U", "E", "y", "id", "htmlFor", "passive", "s", "g", "currentTarget", "target", "isInteractiveElement", "isHTMLLabelElement", "preventDefault", "onClick", "r", "document", "getElementById", "x", "getAttribute", "h", "isHTMLInputElement", "type", "role", "click", "focus", "preventScroll", "R", "disabled", "c", "ref", "ourProps", "theirProps", "defaultTag", "j", "V", "Object", "assign", "Label", "useLabelContext", "useLabelledBy", "useLabels"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/label/label.js"], "sourcesContent": ["\"use client\";import D,{createContext as k,useContext as v,useMemo as T,useState as _}from\"react\";import{useEvent as P}from'../../hooks/use-event.js';import{useId as A}from'../../hooks/use-id.js';import{useIsoMorphicEffect as B}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as F}from'../../hooks/use-sync-refs.js';import{useDisabled as M}from'../../internal/disabled.js';import{useProvidedId as S}from'../../internal/id.js';import*as m from'../../utils/dom.js';import{forwardRefWithAs as I,useRender as H}from'../../utils/render.js';let L=k(null);L.displayName=\"LabelContext\";function C(){let n=v(L);if(n===null){let l=new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(l,C),l}return n}function N(n){var a,e,o;let l=(e=(a=v(L))==null?void 0:a.value)!=null?e:void 0;return((o=n==null?void 0:n.length)!=null?o:0)>0?[l,...n].filter(Boolean).join(\" \"):l}function Q({inherit:n=!1}={}){let l=N(),[a,e]=_([]),o=n?[l,...a].filter(Boolean):a;return[o.length>0?o.join(\" \"):void 0,T(()=>function(t){let p=P(i=>(e(u=>[...u,i]),()=>e(u=>{let d=u.slice(),f=d.indexOf(i);return f!==-1&&d.splice(f,1),d}))),b=T(()=>({register:p,slot:t.slot,name:t.name,props:t.props,value:t.value}),[p,t.slot,t.name,t.props,t.value]);return D.createElement(L.Provider,{value:b},t.children)},[e])]}let G=\"label\";function U(n,l){var E;let a=A(),e=C(),o=S(),y=M(),{id:t=`headlessui-label-${a}`,htmlFor:p=o!=null?o:(E=e.props)==null?void 0:E.htmlFor,passive:b=!1,...i}=n,u=F(l);B(()=>e.register(t),[t,e.register]);let d=P(s=>{let g=s.currentTarget;if(!(s.target!==s.currentTarget&&m.isInteractiveElement(s.target))&&(m.isHTMLLabelElement(g)&&s.preventDefault(),e.props&&\"onClick\"in e.props&&typeof e.props.onClick==\"function\"&&e.props.onClick(s),m.isHTMLLabelElement(g))){let r=document.getElementById(g.htmlFor);if(r){let x=r.getAttribute(\"disabled\");if(x===\"true\"||x===\"\")return;let h=r.getAttribute(\"aria-disabled\");if(h===\"true\"||h===\"\")return;(m.isHTMLInputElement(r)&&(r.type===\"file\"||r.type===\"radio\"||r.type===\"checkbox\")||r.role===\"radio\"||r.role===\"checkbox\"||r.role===\"switch\")&&r.click(),r.focus({preventScroll:!0})}}}),f=y||!1,R=T(()=>({...e.slot,disabled:f}),[e.slot,f]),c={ref:u,...e.props,id:t,htmlFor:p,onClick:d};return b&&(\"onClick\"in c&&(delete c.htmlFor,delete c.onClick),\"onClick\"in i&&delete i.onClick),H()({ourProps:c,theirProps:i,slot:R,defaultTag:p?G:\"div\",name:e.name||\"Label\"})}let j=I(U),V=Object.assign(j,{});export{V as Label,C as useLabelContext,N as useLabelledBy,Q as useLabels};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,sBAAsB;AAAC,OAAM,KAAIC,CAAC,MAAK,oBAAoB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAACxB,CAAC,CAAC,IAAI,CAAC;AAACwB,CAAC,CAACC,WAAW,GAAC,cAAc;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACzB,CAAC,CAACsB,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,yEAAyE,CAAC;IAAC,MAAMA,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACF,CAAC,CAAC,EAACE,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAACJ,CAAC,EAAC;EAAC,IAAIK,CAAC,EAACC,CAAC,EAACC,CAAC;EAAC,IAAIN,CAAC,GAAC,CAACK,CAAC,GAAC,CAACD,CAAC,GAAC9B,CAAC,CAACsB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACQ,CAAC,CAACG,KAAK,KAAG,IAAI,GAACF,CAAC,GAAC,KAAK,CAAC;EAAC,OAAM,CAAC,CAACC,CAAC,GAACP,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACS,MAAM,KAAG,IAAI,GAACF,CAAC,GAAC,CAAC,IAAE,CAAC,GAAC,CAACN,CAAC,EAAC,GAAGD,CAAC,CAAC,CAACU,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAACX,CAAC;AAAA;AAAC,SAASY,CAACA,CAAC;EAACC,OAAO,EAACd,CAAC,GAAC,CAAC;AAAC,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACG,CAAC,CAAC,CAAC;IAAC,CAACC,CAAC,EAACC,CAAC,CAAC,GAAC3B,CAAC,CAAC,EAAE,CAAC;IAAC4B,CAAC,GAACP,CAAC,GAAC,CAACC,CAAC,EAAC,GAAGI,CAAC,CAAC,CAACK,MAAM,CAACC,OAAO,CAAC,GAACN,CAAC;EAAC,OAAM,CAACE,CAAC,CAACE,MAAM,GAAC,CAAC,GAACF,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,GAAC,KAAK,CAAC,EAACnC,CAAC,CAAC,MAAI,UAASsC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACnC,CAAC,CAACoC,CAAC,KAAGX,CAAC,CAACY,CAAC,IAAE,CAAC,GAAGA,CAAC,EAACD,CAAC,CAAC,CAAC,EAAC,MAAIX,CAAC,CAACY,CAAC,IAAE;QAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,KAAK,CAAC,CAAC;UAACC,CAAC,GAACF,CAAC,CAACG,OAAO,CAACL,CAAC,CAAC;QAAC,OAAOI,CAAC,KAAG,CAAC,CAAC,IAAEF,CAAC,CAACI,MAAM,CAACF,CAAC,EAAC,CAAC,CAAC,EAACF,CAAC;MAAA,CAAC,CAAC,CAAC,CAAC;MAACK,CAAC,GAAC/C,CAAC,CAAC,OAAK;QAACgD,QAAQ,EAACT,CAAC;QAACU,IAAI,EAACX,CAAC,CAACW,IAAI;QAACC,IAAI,EAACZ,CAAC,CAACY,IAAI;QAACC,KAAK,EAACb,CAAC,CAACa,KAAK;QAACpB,KAAK,EAACO,CAAC,CAACP;MAAK,CAAC,CAAC,EAAC,CAACQ,CAAC,EAACD,CAAC,CAACW,IAAI,EAACX,CAAC,CAACY,IAAI,EAACZ,CAAC,CAACa,KAAK,EAACb,CAAC,CAACP,KAAK,CAAC,CAAC;IAAC,OAAOrC,CAAC,CAAC0D,aAAa,CAAChC,CAAC,CAACiC,QAAQ,EAAC;MAACtB,KAAK,EAACgB;IAAC,CAAC,EAACT,CAAC,CAACgB,QAAQ,CAAC;EAAA,CAAC,EAAC,CAACzB,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAI0B,CAAC,GAAC,OAAO;AAAC,SAASC,CAACA,CAACjC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIiC,CAAC;EAAC,IAAI7B,CAAC,GAACtB,CAAC,CAAC,CAAC;IAACuB,CAAC,GAACP,CAAC,CAAC,CAAC;IAACQ,CAAC,GAAChB,CAAC,CAAC,CAAC;IAAC4C,CAAC,GAAC9C,CAAC,CAAC,CAAC;IAAC;MAAC+C,EAAE,EAACrB,CAAC,GAAC,oBAAoBV,CAAC,EAAE;MAACgC,OAAO,EAACrB,CAAC,GAACT,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC2B,CAAC,GAAC5B,CAAC,CAACsB,KAAK,KAAG,IAAI,GAAC,KAAK,CAAC,GAACM,CAAC,CAACG,OAAO;MAACC,OAAO,EAACd,CAAC,GAAC,CAAC,CAAC;MAAC,GAAGP;IAAC,CAAC,GAACjB,CAAC;IAACkB,CAAC,GAAC/B,CAAC,CAACc,CAAC,CAAC;EAAChB,CAAC,CAAC,MAAIqB,CAAC,CAACmB,QAAQ,CAACV,CAAC,CAAC,EAAC,CAACA,CAAC,EAACT,CAAC,CAACmB,QAAQ,CAAC,CAAC;EAAC,IAAIN,CAAC,GAACtC,CAAC,CAAC0D,CAAC,IAAE;MAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,aAAa;MAAC,IAAG,EAAEF,CAAC,CAACG,MAAM,KAAGH,CAAC,CAACE,aAAa,IAAEjD,CAAC,CAACmD,oBAAoB,CAACJ,CAAC,CAACG,MAAM,CAAC,CAAC,KAAGlD,CAAC,CAACoD,kBAAkB,CAACJ,CAAC,CAAC,IAAED,CAAC,CAACM,cAAc,CAAC,CAAC,EAACvC,CAAC,CAACsB,KAAK,IAAE,SAAS,IAAGtB,CAAC,CAACsB,KAAK,IAAE,OAAOtB,CAAC,CAACsB,KAAK,CAACkB,OAAO,IAAE,UAAU,IAAExC,CAAC,CAACsB,KAAK,CAACkB,OAAO,CAACP,CAAC,CAAC,EAAC/C,CAAC,CAACoD,kBAAkB,CAACJ,CAAC,CAAC,CAAC,EAAC;QAAC,IAAIO,CAAC,GAACC,QAAQ,CAACC,cAAc,CAACT,CAAC,CAACH,OAAO,CAAC;QAAC,IAAGU,CAAC,EAAC;UAAC,IAAIG,CAAC,GAACH,CAAC,CAACI,YAAY,CAAC,UAAU,CAAC;UAAC,IAAGD,CAAC,KAAG,MAAM,IAAEA,CAAC,KAAG,EAAE,EAAC;UAAO,IAAIE,CAAC,GAACL,CAAC,CAACI,YAAY,CAAC,eAAe,CAAC;UAAC,IAAGC,CAAC,KAAG,MAAM,IAAEA,CAAC,KAAG,EAAE,EAAC;UAAO,CAAC5D,CAAC,CAAC6D,kBAAkB,CAACN,CAAC,CAAC,KAAGA,CAAC,CAACO,IAAI,KAAG,MAAM,IAAEP,CAAC,CAACO,IAAI,KAAG,OAAO,IAAEP,CAAC,CAACO,IAAI,KAAG,UAAU,CAAC,IAAEP,CAAC,CAACQ,IAAI,KAAG,OAAO,IAAER,CAAC,CAACQ,IAAI,KAAG,UAAU,IAAER,CAAC,CAACQ,IAAI,KAAG,QAAQ,KAAGR,CAAC,CAACS,KAAK,CAAC,CAAC,EAACT,CAAC,CAACU,KAAK,CAAC;YAACC,aAAa,EAAC,CAAC;UAAC,CAAC,CAAC;QAAA;MAAC;IAAC,CAAC,CAAC;IAACrC,CAAC,GAACc,CAAC,IAAE,CAAC,CAAC;IAACwB,CAAC,GAAClF,CAAC,CAAC,OAAK;MAAC,GAAG6B,CAAC,CAACoB,IAAI;MAACkC,QAAQ,EAACvC;IAAC,CAAC,CAAC,EAAC,CAACf,CAAC,CAACoB,IAAI,EAACL,CAAC,CAAC,CAAC;IAACwC,CAAC,GAAC;MAACC,GAAG,EAAC5C,CAAC;MAAC,GAAGZ,CAAC,CAACsB,KAAK;MAACQ,EAAE,EAACrB,CAAC;MAACsB,OAAO,EAACrB,CAAC;MAAC8B,OAAO,EAAC3B;IAAC,CAAC;EAAC,OAAOK,CAAC,KAAG,SAAS,IAAGqC,CAAC,KAAG,OAAOA,CAAC,CAACxB,OAAO,EAAC,OAAOwB,CAAC,CAACf,OAAO,CAAC,EAAC,SAAS,IAAG7B,CAAC,IAAE,OAAOA,CAAC,CAAC6B,OAAO,CAAC,EAAClD,CAAC,CAAC,CAAC,CAAC;IAACmE,QAAQ,EAACF,CAAC;IAACG,UAAU,EAAC/C,CAAC;IAACS,IAAI,EAACiC,CAAC;IAACM,UAAU,EAACjD,CAAC,GAACgB,CAAC,GAAC,KAAK;IAACL,IAAI,EAACrB,CAAC,CAACqB,IAAI,IAAE;EAAO,CAAC,CAAC;AAAA;AAAC,IAAIuC,CAAC,GAACxE,CAAC,CAACuC,CAAC,CAAC;EAACkC,CAAC,GAACC,MAAM,CAACC,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC,CAAC;AAAC,SAAOC,CAAC,IAAIG,KAAK,EAACvE,CAAC,IAAIwE,eAAe,EAACnE,CAAC,IAAIoE,aAAa,EAAC3D,CAAC,IAAI4D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}