{"ast": null, "code": "function d() {\n  let r;\n  return {\n    before(_ref) {\n      let {\n        doc: e\n      } = _ref;\n      var l;\n      let o = e.documentElement,\n        t = (l = e.defaultView) != null ? l : window;\n      r = Math.max(0, t.innerWidth - o.clientWidth);\n    },\n    after(_ref2) {\n      let {\n        doc: e,\n        d: o\n      } = _ref2;\n      let t = e.documentElement,\n        l = Math.max(0, t.clientWidth - t.offsetWidth),\n        n = Math.max(0, r - l);\n      o.style(t, \"paddingRight\", \"\".concat(n, \"px\"));\n    }\n  };\n}\nexport { d as adjustScrollbarPadding };", "map": {"version": 3, "names": ["d", "r", "before", "_ref", "doc", "e", "l", "o", "documentElement", "t", "defaultView", "window", "Math", "max", "innerWidth", "clientWidth", "after", "_ref2", "offsetWidth", "n", "style", "concat", "adjustScrollbarPadding"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js"], "sourcesContent": ["function d(){let r;return{before({doc:e}){var l;let o=e.documentElement,t=(l=e.defaultView)!=null?l:window;r=Math.max(0,t.innerWidth-o.clientWidth)},after({doc:e,d:o}){let t=e.documentElement,l=Math.max(0,t.clientWidth-t.offsetWidth),n=Math.max(0,r-l);o.style(t,\"paddingRight\",`${n}px`)}}}export{d as adjustScrollbarPadding};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC;EAAC,OAAM;IAACC,MAAMA,CAAAC,IAAA,EAAS;MAAA,IAAR;QAACC,GAAG,EAACC;MAAC,CAAC,GAAAF,IAAA;MAAE,IAAIG,CAAC;MAAC,IAAIC,CAAC,GAACF,CAAC,CAACG,eAAe;QAACC,CAAC,GAAC,CAACH,CAAC,GAACD,CAAC,CAACK,WAAW,KAAG,IAAI,GAACJ,CAAC,GAACK,MAAM;MAACV,CAAC,GAACW,IAAI,CAACC,GAAG,CAAC,CAAC,EAACJ,CAAC,CAACK,UAAU,GAACP,CAAC,CAACQ,WAAW,CAAC;IAAA,CAAC;IAACC,KAAKA,CAAAC,KAAA,EAAa;MAAA,IAAZ;QAACb,GAAG,EAACC,CAAC;QAACL,CAAC,EAACO;MAAC,CAAC,GAAAU,KAAA;MAAE,IAAIR,CAAC,GAACJ,CAAC,CAACG,eAAe;QAACF,CAAC,GAACM,IAAI,CAACC,GAAG,CAAC,CAAC,EAACJ,CAAC,CAACM,WAAW,GAACN,CAAC,CAACS,WAAW,CAAC;QAACC,CAAC,GAACP,IAAI,CAACC,GAAG,CAAC,CAAC,EAACZ,CAAC,GAACK,CAAC,CAAC;MAACC,CAAC,CAACa,KAAK,CAACX,CAAC,EAAC,cAAc,KAAAY,MAAA,CAAIF,CAAC,OAAI,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOnB,CAAC,IAAIsB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}