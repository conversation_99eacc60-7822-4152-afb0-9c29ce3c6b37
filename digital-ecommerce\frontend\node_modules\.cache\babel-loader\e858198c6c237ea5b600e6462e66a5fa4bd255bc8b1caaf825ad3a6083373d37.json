{"ast": null, "code": "import r, { useState as u } from \"react\";\nfunction f(_ref) {\n  let {\n    children: o,\n    freeze: e\n  } = _ref;\n  let n = l(e, o);\n  return r.createElement(r.Fragment, null, n);\n}\nfunction l(o, e) {\n  let [n, t] = u(e);\n  return !o && n !== e && t(e), o ? n : e;\n}\nexport { f as Frozen, l as useFrozenData };", "map": {"version": 3, "names": ["r", "useState", "u", "f", "_ref", "children", "o", "freeze", "e", "n", "l", "createElement", "Fragment", "t", "Frozen", "useFrozenData"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/frozen.js"], "sourcesContent": ["import r,{useState as u}from\"react\";function f({children:o,freeze:e}){let n=l(e,o);return r.createElement(r.Fragment,null,n)}function l(o,e){let[n,t]=u(e);return!o&&n!==e&&t(e),o?n:e}export{f as Frozen,l as useFrozenData};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAAAC,IAAA,EAAuB;EAAA,IAAtB;IAACC,QAAQ,EAACC,CAAC;IAACC,MAAM,EAACC;EAAC,CAAC,GAAAJ,IAAA;EAAE,IAAIK,CAAC,GAACC,CAAC,CAACF,CAAC,EAACF,CAAC,CAAC;EAAC,OAAON,CAAC,CAACW,aAAa,CAACX,CAAC,CAACY,QAAQ,EAAC,IAAI,EAACH,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAACJ,CAAC,EAACE,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACI,CAAC,CAAC,GAACX,CAAC,CAACM,CAAC,CAAC;EAAC,OAAM,CAACF,CAAC,IAAEG,CAAC,KAAGD,CAAC,IAAEK,CAAC,CAACL,CAAC,CAAC,EAACF,CAAC,GAACG,CAAC,GAACD,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIW,MAAM,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}