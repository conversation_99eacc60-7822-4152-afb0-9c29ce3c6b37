{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport { ShoppingBagIcon, StarIcon, TruckIcon, ShieldCheckIcon, HeartIcon, SparklesIcon, ArrowDownTrayIcon as CloudDownloadIcon, ComputerDesktopIcon } from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport { useTheme } from '../contexts/ThemeContext';\nimport toast, { Toaster } from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [heroRef, heroInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [featuresRef, featuresInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [productsRef, productsInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const {\n    addToCart\n  } = useCart();\n  const {\n    getThemeClasses\n  } = useTheme();\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n  const handleAddToCart = product => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right'\n    });\n  };\n  const testimonials = [{\n    id: 1,\n    name: 'Sarah Johnson',\n    role: 'Verified Customer',\n    content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n  }, {\n    id: 2,\n    name: 'Mike Chen',\n    role: 'Tech Enthusiast',\n    content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n  }, {\n    id: 3,\n    name: 'Emily Davis',\n    role: 'Regular Shopper',\n    content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n  }];\n  const features = [{\n    icon: TruckIcon,\n    title: 'Free Shipping',\n    description: 'Free delivery on orders over $50'\n  }, {\n    icon: ShieldCheckIcon,\n    title: 'Secure Payment',\n    description: '100% secure payment processing'\n  }, {\n    icon: HeartIcon,\n    title: '24/7 Support',\n    description: 'Round-the-clock customer service'\n  }, {\n    icon: SparklesIcon,\n    title: 'Premium Quality',\n    description: 'Only the finest products'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: heroRef,\n      initial: {\n        opacity: 0\n      },\n      animate: heroInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 1\n      },\n      className: \"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: -100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n              children: [\"Discover Amazing\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-light-orange-100 mb-8 leading-relaxed\",\n              children: \"Shop the latest trends with unbeatable prices and premium quality. Your perfect shopping experience starts here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"Shop Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/digital-products\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\",\n                  children: \"Digital Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: 100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.4\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\",\n                alt: \"Shopping Experience\",\n                className: \"rounded-2xl shadow-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: featuresRef,\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: featuresInView ? {\n        opacity: 1,\n        y: 0\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: `py-20 transition-colors duration-300 ${getThemeClasses('bg-white', 'bg-slate-900')}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Us?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"We're committed to providing you with the best shopping experience possible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: featuresInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-10 h-10 text-light-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: productsRef,\n      initial: {\n        opacity: 0\n      },\n      animate: productsInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: `py-20 transition-colors duration-300 ${getThemeClasses('bg-gradient-to-br from-light-orange-50 to-white', 'bg-gradient-to-br from-slate-800 to-slate-900')}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Discover our handpicked selection of premium products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: featuredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: productsInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            whileHover: {\n              y: -10\n            },\n            className: `rounded-2xl shadow-lg overflow-hidden group cursor-pointer transition-colors duration-300 ${getThemeClasses('bg-white', 'bg-slate-800')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.images ? product.images[0] : product.image,\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: product.badge\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1\n                  },\n                  whileTap: {\n                    scale: 0.9\n                  },\n                  className: \"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-5 h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-sm ml-2 transition-colors duration-300 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-light-orange-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 font-semibold\",\n                  children: [\"Save $\", (product.originalPrice - product.price).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                onClick: () => handleAddToCart(product),\n                className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Add to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      initial: {\n        opacity: 0\n      },\n      whileInView: {\n        opacity: 1\n      },\n      transition: {\n        duration: 0.8\n      },\n      viewport: {\n        once: true\n      },\n      className: \"py-20 bg-gradient-to-br from-blue-50 to-purple-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Digital Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Instant access to software, games, and digital content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: digitalProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              y: -10\n            },\n            className: \"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.images[0],\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: \"Digital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CloudDownloadIcon, {\n                    className: \"w-3 h-3 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this), \"Instant\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 ml-2\",\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-blue-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                onClick: () => handleAddToCart(product),\n                className: \"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(CloudDownloadIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Get Instantly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-12\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/digital-products\",\n            children: /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n              children: [/*#__PURE__*/_jsxDEV(ComputerDesktopIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View All Digital Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Customers Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Don't just take our word for it - hear from our satisfied customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: testimonial.avatar,\n                alt: testimonial.name,\n                className: \"w-12 h-12 rounded-full mr-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: testimonial.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: testimonial.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex mb-4\",\n              children: [...Array(testimonial.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                className: \"w-5 h-5 text-yellow-400\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: [\"\\\"\", testimonial.content, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this)]\n          }, testimonial.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"cFS2AmieRYcj2CIJGhe/UEI9ihA=\", false, function () {\n  return [useInView, useInView, useInView, useCart, useTheme];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "motion", "useInView", "Link", "ShoppingBagIcon", "StarIcon", "TruckIcon", "ShieldCheckIcon", "HeartIcon", "SparklesIcon", "ArrowDownTrayIcon", "CloudDownloadIcon", "ComputerDesktopIcon", "StarIconSolid", "getFeaturedProducts", "getDigitalProducts", "useCart", "useTheme", "toast", "Toaster", "jsxDEV", "_jsxDEV", "HomePage", "_s", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "featuresRef", "featuresInView", "productsRef", "productsInView", "addToCart", "getThemeClasses", "featuredProducts", "slice", "digitalProducts", "handleAddToCart", "product", "success", "name", "duration", "position", "testimonials", "id", "role", "content", "rating", "avatar", "features", "icon", "title", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "section", "ref", "initial", "opacity", "animate", "transition", "div", "x", "delay", "to", "button", "whileHover", "scale", "whileTap", "src", "alt", "y", "map", "feature", "index", "images", "image", "badge", "Array", "_", "i", "Math", "floor", "reviews", "price", "originalPrice", "toFixed", "onClick", "whileInView", "viewport", "once", "testimonial", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport {\n  ShoppingBagIcon,\n  StarIcon,\n  TruckIcon,\n  ShieldCheckIcon,\n  HeartIcon,\n  SparklesIcon,\n  ArrowDownTrayIcon as CloudDownloadIcon,\n  ComputerDesktopIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport { useTheme } from '../contexts/ThemeContext';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst HomePage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [featuresRef, featuresInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [productsRef, productsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const { addToCart } = useCart();\n  const { getThemeClasses } = useTheme();\n\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  };\n\n  const testimonials = [\n    {\n      id: 1,\n      name: 'Sarah Johnson',\n      role: 'Verified Customer',\n      content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n    },\n    {\n      id: 2,\n      name: 'Mike Chen',\n      role: 'Tech Enthusiast',\n      content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n    },\n    {\n      id: 3,\n      name: 'Emily Davis',\n      role: 'Regular Shopper',\n      content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n    }\n  ];\n\n  const features = [\n    {\n      icon: TruckIcon,\n      title: 'Free Shipping',\n      description: 'Free delivery on orders over $50'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Secure Payment',\n      description: '100% secure payment processing'\n    },\n    {\n      icon: HeartIcon,\n      title: '24/7 Support',\n      description: 'Round-the-clock customer service'\n    },\n    {\n      icon: SparklesIcon,\n      title: 'Premium Quality',\n      description: 'Only the finest products'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      <Toaster position=\"top-right\" />\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        {/* Animated Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"></div>\n          <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <h1 className=\"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n                Discover Amazing\n                <span className=\"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n                  Products\n                </span>\n              </h1>\n              <p className=\"text-xl text-light-orange-100 mb-8 leading-relaxed\">\n                Shop the latest trends with unbeatable prices and premium quality. \n                Your perfect shopping experience starts here.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link to=\"/products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                  >\n                    Shop Now\n                  </motion.button>\n                </Link>\n                <Link to=\"/digital-products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\"\n                  >\n                    Digital Products\n                  </motion.button>\n                </Link>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\"\n                  alt=\"Shopping Experience\"\n                  className=\"rounded-2xl shadow-2xl\"\n                />\n              </div>\n              <div className=\"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section\n        ref={featuresRef}\n        initial={{ opacity: 0, y: 50 }}\n        animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n        transition={{ duration: 0.8 }}\n        className={`py-20 transition-colors duration-300 ${\n          getThemeClasses('bg-white', 'bg-slate-900')\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              We're committed to providing you with the best shopping experience possible\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <feature.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Featured Products Section */}\n      <motion.section\n        ref={productsRef}\n        initial={{ opacity: 0 }}\n        animate={productsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className={`py-20 transition-colors duration-300 ${\n          getThemeClasses(\n            'bg-gradient-to-br from-light-orange-50 to-white',\n            'bg-gradient-to-br from-slate-800 to-slate-900'\n          )\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover our handpicked selection of premium products\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                animate={productsInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                whileHover={{ y: -10 }}\n                className={`rounded-2xl shadow-lg overflow-hidden group cursor-pointer transition-colors duration-300 ${\n                  getThemeClasses('bg-white', 'bg-slate-800')\n                }`}\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images ? product.images[0] : product.image}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      {product.badge}\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n                    >\n                      <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n                    </motion.button>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                  \n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className={`text-sm ml-2 transition-colors duration-300 ${\n                      getThemeClasses('text-gray-600', 'text-gray-400')\n                    }`}>\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-light-orange-600\">\n                        ${product.price}\n                      </span>\n                      <span className=\"text-lg text-gray-500 line-through\">\n                        ${product.originalPrice}\n                      </span>\n                    </div>\n                    <span className=\"text-sm text-green-600 font-semibold\">\n                      Save ${(product.originalPrice - product.price).toFixed(2)}\n                    </span>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={() => handleAddToCart(product)}\n                    className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <ShoppingBagIcon className=\"w-5 h-5\" />\n                    <span>Add to Cart</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Digital Products Section */}\n      <motion.section\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Digital Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Instant access to software, games, and digital content\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {digitalProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10 }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images[0]}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      Digital\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\">\n                      <CloudDownloadIcon className=\"w-3 h-3 mr-1\" />\n                      Instant\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-blue-600\">\n                        ${product.price}\n                      </span>\n                      {product.originalPrice && product.originalPrice > product.price && (\n                        <span className=\"text-lg text-gray-500 line-through\">\n                          ${product.originalPrice}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={() => handleAddToCart(product)}\n                    className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <CloudDownloadIcon className=\"w-5 h-5\" />\n                    <span>Get Instantly</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link to=\"/digital-products\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\"\n              >\n                <ComputerDesktopIcon className=\"w-6 h-6\" />\n                <span>View All Digital Products</span>\n              </motion.button>\n            </Link>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              What Our Customers Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Don't just take our word for it - hear from our satisfied customers\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <img\n                    src={testimonial.avatar}\n                    alt={testimonial.name}\n                    className=\"w-12 h-12 rounded-full mr-4\"\n                  />\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <StarIconSolid key={i} className=\"w-5 h-5 text-yellow-400\" />\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-700 italic\">\"{testimonial.content}\"</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,eAAe,EACfC,QAAQ,EACRC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,YAAY,EACZC,iBAAiB,IAAIC,iBAAiB,EACtCC,mBAAmB,QACd,6BAA6B;AACpC,SAASP,QAAQ,IAAIQ,aAAa,QAAQ,2BAA2B;AACrE,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC1E,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,SAAS,CAAC;IAAEwB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,SAAS,CAAC;IAAEwB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EACtF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG7B,SAAS,CAAC;IAAEwB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EACtF,MAAM;IAAEK;EAAU,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC/B,MAAM;IAAEiB;EAAgB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;EAEtC,MAAMiB,gBAAgB,GAAGpB,mBAAmB,CAAC,CAAC,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,MAAMC,eAAe,GAAGrB,kBAAkB,CAAC,CAAC,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAExD,MAAME,eAAe,GAAIC,OAAO,IAAK;IACnCN,SAAS,CAACM,OAAO,CAAC;IAClBpB,KAAK,CAACqB,OAAO,CAAC,GAAGD,OAAO,CAACE,IAAI,iBAAiB,EAAE;MAC9CC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,eAAe;IACrBK,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,4FAA4F;IACrGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,0FAA0F;IACnGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,aAAa;IACnBK,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,gGAAgG;IACzGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE5C,SAAS;IACf6C,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE3C,eAAe;IACrB4C,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE1C,SAAS;IACf2C,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEzC,YAAY;IAClB0C,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE/B,OAAA;IAAKgC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BjC,OAAA,CAACF,OAAO;MAACuB,QAAQ,EAAC;IAAW;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhCrC,OAAA,CAACpB,MAAM,CAAC0D,OAAO;MACbC,GAAG,EAAEpC,OAAQ;MACbqC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAEtC,UAAU,GAAG;QAAEqC,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC1CE,UAAU,EAAE;QAAEvB,QAAQ,EAAE;MAAE,CAAE;MAC5BY,SAAS,EAAC,2GAA2G;MAAAC,QAAA,gBAGrHjC,OAAA;QAAKgC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BjC,OAAA;UAAKgC,SAAS,EAAC;QAAqF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3GrC,OAAA;UAAKgC,SAAS,EAAC;QAAuF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7GrC,OAAA;UAAKgC,SAAS,EAAC;QAAwF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7EjC,OAAA;UAAKgC,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClEjC,OAAA,CAACpB,MAAM,CAACgE,GAAG;YACTJ,OAAO,EAAE;cAAEK,CAAC,EAAE,CAAC,GAAG;cAAEJ,OAAO,EAAE;YAAE,CAAE;YACjCC,OAAO,EAAEtC,UAAU,GAAG;cAAEyC,CAAC,EAAE,CAAC;cAAEJ,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEvB,QAAQ,EAAE,GAAG;cAAE0B,KAAK,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAE1CjC,OAAA;cAAIgC,SAAS,EAAC,8DAA8D;cAAAC,QAAA,GAAC,kBAE3E,eAAAjC,OAAA;gBAAMgC,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAErG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLrC,OAAA;cAAGgC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAGlE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrC,OAAA;cAAKgC,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CjC,OAAA,CAAClB,IAAI;gBAACiE,EAAE,EAAC,WAAW;gBAAAd,QAAA,eAClBjC,OAAA,CAACpB,MAAM,CAACoE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BlB,SAAS,EAAC,mIAAmI;kBAAAC,QAAA,EAC9I;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACPrC,OAAA,CAAClB,IAAI;gBAACiE,EAAE,EAAC,mBAAmB;gBAAAd,QAAA,eAC1BjC,OAAA,CAACpB,MAAM,CAACoE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BlB,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EACjK;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrC,OAAA,CAACpB,MAAM,CAACgE,GAAG;YACTJ,OAAO,EAAE;cAAEK,CAAC,EAAE,GAAG;cAAEJ,OAAO,EAAE;YAAE,CAAE;YAChCC,OAAO,EAAEtC,UAAU,GAAG;cAAEyC,CAAC,EAAE,CAAC;cAAEJ,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEvB,QAAQ,EAAE,GAAG;cAAE0B,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpBjC,OAAA;cAAKgC,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BjC,OAAA;gBACEoD,GAAG,EAAC,oEAAoE;gBACxEC,GAAG,EAAC,qBAAqB;gBACzBrB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC;YAA+G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACpB,MAAM,CAAC0D,OAAO;MACbC,GAAG,EAAEhC,WAAY;MACjBiC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEa,CAAC,EAAE;MAAG,CAAE;MAC/BZ,OAAO,EAAElC,cAAc,GAAG;QAAEiC,OAAO,EAAE,CAAC;QAAEa,CAAC,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MACpDX,UAAU,EAAE;QAAEvB,QAAQ,EAAE;MAAI,CAAE;MAC9BY,SAAS,EAAE,wCACTpB,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;MAAAqB,QAAA,eAEHjC,OAAA;QAAKgC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAIgC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEL,QAAQ,CAAC2B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BzD,OAAA,CAACpB,MAAM,CAACgE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BZ,OAAO,EAAElC,cAAc,GAAG;cAAEiC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDX,UAAU,EAAE;cAAEvB,QAAQ,EAAE,GAAG;cAAE0B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDzB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7BjC,OAAA;cAAKgC,SAAS,EAAC,0LAA0L;cAAAC,QAAA,eACvMjC,OAAA,CAACwD,OAAO,CAAC3B,IAAI;gBAACG,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNrC,OAAA;cAAIgC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEuB,OAAO,CAAC1B;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7ErC,OAAA;cAAGgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEuB,OAAO,CAACzB;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAVjDoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACpB,MAAM,CAAC0D,OAAO;MACbC,GAAG,EAAE9B,WAAY;MACjB+B,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAEhC,cAAc,GAAG;QAAE+B,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC9CE,UAAU,EAAE;QAAEvB,QAAQ,EAAE;MAAI,CAAE;MAC9BY,SAAS,EAAE,wCACTpB,eAAe,CACb,iDAAiD,EACjD,+CACF,CAAC,EACA;MAAAqB,QAAA,eAEHjC,OAAA;QAAKgC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAIgC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEpB,gBAAgB,CAAC0C,GAAG,CAAC,CAACtC,OAAO,EAAEwC,KAAK,kBACnCzD,OAAA,CAACpB,MAAM,CAACgE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BZ,OAAO,EAAEhC,cAAc,GAAG;cAAE+B,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDX,UAAU,EAAE;cAAEvB,QAAQ,EAAE,GAAG;cAAE0B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDR,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBtB,SAAS,EAAE,6FACTpB,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAC1C;YAAAqB,QAAA,gBAEHjC,OAAA;cAAKgC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBjC,OAAA;gBACEoD,GAAG,EAAEnC,OAAO,CAACyC,MAAM,GAAGzC,OAAO,CAACyC,MAAM,CAAC,CAAC,CAAC,GAAGzC,OAAO,CAAC0C,KAAM;gBACxDN,GAAG,EAAEpC,OAAO,CAACE,IAAK;gBAClBa,SAAS,EAAC;cAAkF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACFrC,OAAA;gBAAKgC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCjC,OAAA;kBAAMgC,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EAC1FhB,OAAO,CAAC2C;gBAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrC,OAAA;gBAAKgC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCjC,OAAA,CAACpB,MAAM,CAACoE,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAI,CAAE;kBACzBlB,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAE7DjC,OAAA,CAACb,SAAS;oBAAC6C,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKgC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjC,OAAA;gBAAIgC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEhB,OAAO,CAACE;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5ErC,OAAA;gBAAKgC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCjC,OAAA;kBAAKgC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,GAAG,CAAC,CAACO,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAChD,OAAO,CAACS,MAAM,CAAC,gBAC5B1B,OAAA,CAACR,aAAa;oBAASwC,SAAS,EAAC;kBAAyB,GAAtC+B,CAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7DrC,OAAA,CAAChB,QAAQ;oBAASgD,SAAS,EAAC;kBAAuB,GAApC+B,CAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrC,OAAA;kBAAMgC,SAAS,EAAE,+CACfpB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAqB,QAAA,GACAhB,OAAO,CAACS,MAAM,EAAC,IAAE,EAACT,OAAO,CAACiD,OAAO,EAAC,WACrC;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA;gBAAKgC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDjC,OAAA;kBAAKgC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjC,OAAA;oBAAMgC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,GAAC,GACxD,EAAChB,OAAO,CAACkD,KAAK;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACPrC,OAAA;oBAAMgC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAAChB,OAAO,CAACmD,aAAa;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrC,OAAA;kBAAMgC,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GAAC,QAC/C,EAAC,CAAChB,OAAO,CAACmD,aAAa,GAAGnD,OAAO,CAACkD,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA,CAACpB,MAAM,CAACoE,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BoB,OAAO,EAAEA,CAAA,KAAMtD,eAAe,CAACC,OAAO,CAAE;gBACxCe,SAAS,EAAC,yOAAyO;gBAAAC,QAAA,gBAEnPjC,OAAA,CAACjB,eAAe;kBAACiD,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCrC,OAAA;kBAAAiC,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,GA1EDpB,OAAO,CAACM,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2EL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACpB,MAAM,CAAC0D,OAAO;MACbE,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxB8B,WAAW,EAAE;QAAE9B,OAAO,EAAE;MAAE,CAAE;MAC5BE,UAAU,EAAE;QAAEvB,QAAQ,EAAE;MAAI,CAAE;MAC9BoD,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAK,CAAE;MACzBzC,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eAE7DjC,OAAA;QAAKgC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAIgC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDlB,eAAe,CAACwC,GAAG,CAAC,CAACtC,OAAO,EAAEwC,KAAK,kBAClCzD,OAAA,CAACpB,MAAM,CAACgE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BiB,WAAW,EAAE;cAAE9B,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAE;YAClCX,UAAU,EAAE;cAAEvB,QAAQ,EAAE,GAAG;cAAE0B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDe,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBtB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,gBAE/EjC,OAAA;cAAKgC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBjC,OAAA;gBACEoD,GAAG,EAAEnC,OAAO,CAACyC,MAAM,CAAC,CAAC,CAAE;gBACvBL,GAAG,EAAEpC,OAAO,CAACE,IAAK;gBAClBa,SAAS,EAAC;cAAkF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACFrC,OAAA;gBAAKgC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCjC,OAAA;kBAAMgC,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,EAAC;gBAEtF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrC,OAAA;gBAAKgC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCjC,OAAA;kBAAMgC,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,gBACjGjC,OAAA,CAACV,iBAAiB;oBAAC0C,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKgC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjC,OAAA;gBAAIgC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEhB,OAAO,CAACE;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5ErC,OAAA;gBAAKgC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCjC,OAAA;kBAAKgC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,GAAG,CAAC,CAACO,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAChD,OAAO,CAACS,MAAM,CAAC,gBAC5B1B,OAAA,CAACR,aAAa;oBAASwC,SAAS,EAAC;kBAAyB,GAAtC+B,CAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7DrC,OAAA,CAAChB,QAAQ;oBAASgD,SAAS,EAAC;kBAAuB,GAApC+B,CAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrC,OAAA;kBAAMgC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzChB,OAAO,CAACS,MAAM,EAAC,IAAE,EAACT,OAAO,CAACiD,OAAO,EAAC,WACrC;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA;gBAAKgC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrDjC,OAAA;kBAAKgC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjC,OAAA;oBAAMgC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,GAAC,GAChD,EAAChB,OAAO,CAACkD,KAAK;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,EACNpB,OAAO,CAACmD,aAAa,IAAInD,OAAO,CAACmD,aAAa,GAAGnD,OAAO,CAACkD,KAAK,iBAC7DnE,OAAA;oBAAMgC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAAChB,OAAO,CAACmD,aAAa;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrC,OAAA,CAACpB,MAAM,CAACoE,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BoB,OAAO,EAAEA,CAAA,KAAMtD,eAAe,CAACC,OAAO,CAAE;gBACxCe,SAAS,EAAC,yMAAyM;gBAAAC,QAAA,gBAEnNjC,OAAA,CAACV,iBAAiB;kBAAC0C,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzCrC,OAAA;kBAAAiC,QAAA,EAAM;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,GAnEDpB,OAAO,CAACM,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoEL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCjC,OAAA,CAAClB,IAAI;YAACiE,EAAE,EAAC,mBAAmB;YAAAd,QAAA,eAC1BjC,OAAA,CAACpB,MAAM,CAACoE,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BlB,SAAS,EAAC,gMAAgM;cAAAC,QAAA,gBAE1MjC,OAAA,CAACT,mBAAmB;gBAACyC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CrC,OAAA;gBAAAiC,QAAA,EAAM;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA;MAASgC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCjC,OAAA;QAAKgC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjC,OAAA;YAAIgC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDX,YAAY,CAACiC,GAAG,CAAC,CAACmB,WAAW,EAAEjB,KAAK,kBACnCzD,OAAA,CAACpB,MAAM,CAACgE,GAAG;YAETJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAG,CAAE;YAC/BiB,WAAW,EAAE;cAAE9B,OAAO,EAAE,CAAC;cAAEa,CAAC,EAAE;YAAE,CAAE;YAClCX,UAAU,EAAE;cAAEvB,QAAQ,EAAE,GAAG;cAAE0B,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDzB,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBAErFjC,OAAA;cAAKgC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCjC,OAAA;gBACEoD,GAAG,EAAEsB,WAAW,CAAC/C,MAAO;gBACxB0B,GAAG,EAAEqB,WAAW,CAACvD,IAAK;gBACtBa,SAAS,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACFrC,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAIgC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEyC,WAAW,CAACvD;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnErC,OAAA;kBAAGgC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEyC,WAAW,CAAClD;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKgC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,GAAG4B,KAAK,CAACa,WAAW,CAAChD,MAAM,CAAC,CAAC,CAAC6B,GAAG,CAAC,CAACO,CAAC,EAAEC,CAAC,kBACvC/D,OAAA,CAACR,aAAa;gBAASwC,SAAS,EAAC;cAAyB,GAAtC+B,CAAC;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAuC,CAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrC,OAAA;cAAGgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,IAAC,EAACyC,WAAW,CAACjD,OAAO,EAAC,IAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAxB1DqC,WAAW,CAACnD,EAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBT,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnC,EAAA,CA9bID,QAAQ;EAAA,QACkBpB,SAAS,EACDA,SAAS,EACTA,SAAS,EACzBc,OAAO,EACDC,QAAQ;AAAA;AAAA+E,EAAA,GALhC1E,QAAQ;AAgcd,eAAeA,QAAQ;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}