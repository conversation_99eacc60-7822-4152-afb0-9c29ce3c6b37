import React, { useState } from 'react';
import { BellIcon, EnvelopeIcon, CogIcon, CheckIcon } from '@heroicons/react/24/outline';

const EmailNotifications = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState({
    orderUpdates: true,
    promotions: true,
    newProducts: false,
    priceDrops: true,
    backInStock: true,
    newsletter: false,
    reviews: true,
    security: true
  });
  const [email, setEmail] = useState('<EMAIL>');
  const [showSuccess, setShowSuccess] = useState(false);

  const notificationTypes = [
    {
      key: 'orderUpdates',
      title: 'Order Updates',
      description: 'Get notified about your order status, shipping, and delivery',
      icon: '📦'
    },
    {
      key: 'promotions',
      title: 'Promotions & Deals',
      description: 'Receive exclusive offers, discounts, and flash sales',
      icon: '🏷️'
    },
    {
      key: 'newProducts',
      title: 'New Products',
      description: 'Be the first to know about new arrivals and launches',
      icon: '✨'
    },
    {
      key: 'priceDrops',
      title: 'Price Drops',
      description: 'Get alerts when items in your wishlist go on sale',
      icon: '💰'
    },
    {
      key: 'backInStock',
      title: 'Back in Stock',
      description: 'Notifications when out-of-stock items become available',
      icon: '🔄'
    },
    {
      key: 'newsletter',
      title: 'Weekly Newsletter',
      description: 'Weekly roundup of trends, tips, and featured products',
      icon: '📰'
    },
    {
      key: 'reviews',
      title: 'Review Reminders',
      description: 'Reminders to review your purchased products',
      icon: '⭐'
    },
    {
      key: 'security',
      title: 'Security Alerts',
      description: 'Important account security and login notifications',
      icon: '🔒'
    }
  ];

  const handleToggle = (key) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSave = () => {
    // Mock save functionality
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 3000);
  };

  const enabledCount = Object.values(notifications).filter(Boolean).length;

  return (
    <div className="relative">
      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-3 bg-light-orange-100 text-light-orange-700 rounded-full hover:bg-light-orange-200 transition-colors shadow-md"
      >
        <BellIcon className="w-6 h-6" />
        {enabledCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-light-orange-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
            {enabledCount}
          </span>
        )}
      </button>

      {/* Notification Settings Panel */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50 max-h-[80vh] overflow-y-auto">
          {/* Header */}
          <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white flex items-center">
                <EnvelopeIcon className="w-6 h-6 mr-2" />
                Email Preferences
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-light-orange-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Email Address */}
          <div className="p-4 border-b border-light-orange-100">
            <label className="block text-sm font-semibold text-light-orange-800 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors text-sm"
            />
          </div>

          {/* Notification Types */}
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-light-orange-800">Notification Types</h3>
              <span className="text-sm text-light-orange-600">
                {enabledCount} of {notificationTypes.length} enabled
              </span>
            </div>

            {notificationTypes.map((type) => (
              <div key={type.key} className="flex items-start space-x-3 p-3 bg-light-orange-50 rounded-lg border border-light-orange-100">
                <div className="text-2xl">{type.icon}</div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-light-orange-800">{type.title}</h4>
                    <button
                      onClick={() => handleToggle(type.key)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-light-orange-300 ${
                        notifications[type.key]
                          ? 'bg-light-orange-500'
                          : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          notifications[type.key] ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                  <p className="text-sm text-light-orange-600 mt-1">{type.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="p-4 border-t border-light-orange-100">
            <div className="flex space-x-2 mb-4">
              <button
                onClick={() => {
                  const allEnabled = {};
                  notificationTypes.forEach(type => {
                    allEnabled[type.key] = true;
                  });
                  setNotifications(allEnabled);
                }}
                className="flex-1 px-3 py-2 bg-light-orange-100 text-light-orange-700 rounded-lg hover:bg-light-orange-200 transition-colors text-sm font-medium"
              >
                Enable All
              </button>
              <button
                onClick={() => {
                  const allDisabled = {};
                  notificationTypes.forEach(type => {
                    allDisabled[type.key] = type.key === 'security'; // Keep security enabled
                  });
                  setNotifications(allDisabled);
                }}
                className="flex-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
              >
                Disable All
              </button>
            </div>

            <button
              onClick={handleSave}
              className="w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md flex items-center justify-center space-x-2"
            >
              <CogIcon className="w-5 h-5" />
              <span>Save Preferences</span>
            </button>
          </div>

          {/* Success Message */}
          {showSuccess && (
            <div className="absolute top-4 left-4 right-4 bg-green-100 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center space-x-2 shadow-lg">
              <CheckIcon className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium">Preferences saved successfully!</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EmailNotifications;
