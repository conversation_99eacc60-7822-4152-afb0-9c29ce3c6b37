{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ThemeToggle.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = ({\n  className = '',\n  showLabel = false,\n  size = 'md'\n}) => {\n  _s();\n  const {\n    theme,\n    toggleTheme,\n    isLoading\n  } = useTheme();\n  const sizeClasses = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12'\n  };\n  const iconSizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${sizeClasses[size]} bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ${className}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center space-x-2 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(motion.button, {\n      onClick: toggleTheme,\n      className: `\n          ${sizeClasses[size]} \n          relative overflow-hidden rounded-full \n          bg-gradient-to-r from-light-orange-400 to-light-orange-500\n          dark:from-light-orange-500 dark:to-light-orange-600\n          hover:from-light-orange-500 hover:to-light-orange-600\n          dark:hover:from-light-orange-400 dark:hover:to-light-orange-500\n          shadow-lg hover:shadow-xl\n          transition-all duration-300 ease-in-out\n          focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:ring-offset-2\n          dark:focus:ring-light-orange-400 dark:focus:ring-offset-gray-800\n          group\n        `,\n      whileHover: {\n        scale: 1.05\n      },\n      whileTap: {\n        scale: 0.95\n      },\n      \"aria-label\": `Switch to ${theme === 'light' ? 'dark' : 'light'} theme`,\n      title: `Switch to ${theme === 'light' ? 'dark' : 'light'} theme`,\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500\",\n        initial: false,\n        animate: {\n          opacity: theme === 'light' ? 1 : 0\n        },\n        transition: {\n          duration: 0.3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600\",\n        initial: false,\n        animate: {\n          opacity: theme === 'dark' ? 1 : 0\n        },\n        transition: {\n          duration: 0.3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 flex items-center justify-center h-full\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            rotate: -180,\n            opacity: 0\n          },\n          animate: {\n            rotate: 0,\n            opacity: 1\n          },\n          exit: {\n            rotate: 180,\n            opacity: 0\n          },\n          transition: {\n            duration: 0.3,\n            ease: \"easeInOut\"\n          },\n          className: \"flex items-center justify-center\",\n          children: theme === 'light' ? /*#__PURE__*/_jsxDEV(SunIcon, {\n            className: `${iconSizeClasses[size]} text-white drop-shadow-sm`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(MoonIcon, {\n            className: `${iconSizeClasses[size]} text-white drop-shadow-sm`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, theme, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 bg-white rounded-full\",\n        initial: {\n          scale: 0,\n          opacity: 0.3\n        },\n        animate: {\n          scale: 0,\n          opacity: 0\n        },\n        whileTap: {\n          scale: 1.5,\n          opacity: 0.1\n        },\n        transition: {\n          duration: 0.2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), showLabel && /*#__PURE__*/_jsxDEV(motion.span, {\n      initial: {\n        opacity: 0,\n        x: -10\n      },\n      animate: {\n        opacity: 1,\n        x: 0\n      },\n      className: \"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize\",\n      children: [theme, \" mode\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"bhs2nNrwnPXVPJcROq0+hArJHTo=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "motion", "SunIcon", "MoonIcon", "useTheme", "jsxDEV", "_jsxDEV", "ThemeToggle", "className", "showLabel", "size", "_s", "theme", "toggleTheme", "isLoading", "sizeClasses", "sm", "md", "lg", "iconSizeClasses", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "button", "onClick", "whileHover", "scale", "whileTap", "title", "div", "initial", "animate", "opacity", "transition", "duration", "rotate", "exit", "ease", "span", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ThemeToggle.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', showLabel = false, size = 'md' }) => {\n  const { theme, toggleTheme, isLoading } = useTheme();\n\n  const sizeClasses = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12'\n  };\n\n  const iconSizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n\n  if (isLoading) {\n    return (\n      <div className={`${sizeClasses[size]} bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse ${className}`} />\n    );\n  }\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      <motion.button\n        onClick={toggleTheme}\n        className={`\n          ${sizeClasses[size]} \n          relative overflow-hidden rounded-full \n          bg-gradient-to-r from-light-orange-400 to-light-orange-500\n          dark:from-light-orange-500 dark:to-light-orange-600\n          hover:from-light-orange-500 hover:to-light-orange-600\n          dark:hover:from-light-orange-400 dark:hover:to-light-orange-500\n          shadow-lg hover:shadow-xl\n          transition-all duration-300 ease-in-out\n          focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:ring-offset-2\n          dark:focus:ring-light-orange-400 dark:focus:ring-offset-gray-800\n          group\n        `}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}\n        title={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}\n      >\n        {/* Background gradient animation */}\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500\"\n          initial={false}\n          animate={{\n            opacity: theme === 'light' ? 1 : 0,\n          }}\n          transition={{ duration: 0.3 }}\n        />\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600\"\n          initial={false}\n          animate={{\n            opacity: theme === 'dark' ? 1 : 0,\n          }}\n          transition={{ duration: 0.3 }}\n        />\n\n        {/* Icon container */}\n        <div className=\"relative z-10 flex items-center justify-center h-full\">\n          <motion.div\n            key={theme}\n            initial={{ rotate: -180, opacity: 0 }}\n            animate={{ rotate: 0, opacity: 1 }}\n            exit={{ rotate: 180, opacity: 0 }}\n            transition={{ duration: 0.3, ease: \"easeInOut\" }}\n            className=\"flex items-center justify-center\"\n          >\n            {theme === 'light' ? (\n              <SunIcon \n                className={`${iconSizeClasses[size]} text-white drop-shadow-sm`} \n              />\n            ) : (\n              <MoonIcon \n                className={`${iconSizeClasses[size]} text-white drop-shadow-sm`} \n              />\n            )}\n          </motion.div>\n        </div>\n\n        {/* Ripple effect */}\n        <motion.div\n          className=\"absolute inset-0 bg-white rounded-full\"\n          initial={{ scale: 0, opacity: 0.3 }}\n          animate={{ scale: 0, opacity: 0 }}\n          whileTap={{ scale: 1.5, opacity: 0.1 }}\n          transition={{ duration: 0.2 }}\n        />\n      </motion.button>\n\n      {showLabel && (\n        <motion.span\n          initial={{ opacity: 0, x: -10 }}\n          animate={{ opacity: 1, x: 0 }}\n          className=\"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize\"\n        >\n          {theme} mode\n        </motion.span>\n      )}\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,6BAA6B;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,SAAS,GAAG,KAAK;EAAEC,IAAI,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGV,QAAQ,CAAC,CAAC;EAEpD,MAAMW,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBH,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,IAAIJ,SAAS,EAAE;IACb,oBACER,OAAA;MAAKE,SAAS,EAAE,GAAGO,WAAW,CAACL,IAAI,CAAC,4DAA4DF,SAAS;IAAG;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAEnH;EAEA,oBACEjB,OAAA;IAAKE,SAAS,EAAE,+BAA+BA,SAAS,EAAG;IAAAgB,QAAA,gBACzDlB,OAAA,CAACL,MAAM,CAACwB,MAAM;MACZC,OAAO,EAAEb,WAAY;MACrBL,SAAS,EAAE;AACnB,YAAYO,WAAW,CAACL,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAU;MACFiB,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAE;MAC5BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAK,CAAE;MAC1B,cAAY,aAAahB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,QAAS;MACtEkB,KAAK,EAAE,aAAalB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,QAAS;MAAAY,QAAA,gBAGjElB,OAAA,CAACL,MAAM,CAAC8B,GAAG;QACTvB,SAAS,EAAC,iEAAiE;QAC3EwB,OAAO,EAAE,KAAM;QACfC,OAAO,EAAE;UACPC,OAAO,EAAEtB,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG;QACnC,CAAE;QACFuB,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACFjB,OAAA,CAACL,MAAM,CAAC8B,GAAG;QACTvB,SAAS,EAAC,+DAA+D;QACzEwB,OAAO,EAAE,KAAM;QACfC,OAAO,EAAE;UACPC,OAAO,EAAEtB,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG;QAClC,CAAE;QACFuB,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAGFjB,OAAA;QAAKE,SAAS,EAAC,uDAAuD;QAAAgB,QAAA,eACpElB,OAAA,CAACL,MAAM,CAAC8B,GAAG;UAETC,OAAO,EAAE;YAAEK,MAAM,EAAE,CAAC,GAAG;YAAEH,OAAO,EAAE;UAAE,CAAE;UACtCD,OAAO,EAAE;YAAEI,MAAM,EAAE,CAAC;YAAEH,OAAO,EAAE;UAAE,CAAE;UACnCI,IAAI,EAAE;YAAED,MAAM,EAAE,GAAG;YAAEH,OAAO,EAAE;UAAE,CAAE;UAClCC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,IAAI,EAAE;UAAY,CAAE;UACjD/B,SAAS,EAAC,kCAAkC;UAAAgB,QAAA,EAE3CZ,KAAK,KAAK,OAAO,gBAChBN,OAAA,CAACJ,OAAO;YACNM,SAAS,EAAE,GAAGW,eAAe,CAACT,IAAI,CAAC;UAA6B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,gBAEFjB,OAAA,CAACH,QAAQ;YACPK,SAAS,EAAE,GAAGW,eAAe,CAACT,IAAI,CAAC;UAA6B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE;QACF,GAfIX,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNjB,OAAA,CAACL,MAAM,CAAC8B,GAAG;QACTvB,SAAS,EAAC,wCAAwC;QAClDwB,OAAO,EAAE;UAAEJ,KAAK,EAAE,CAAC;UAAEM,OAAO,EAAE;QAAI,CAAE;QACpCD,OAAO,EAAE;UAAEL,KAAK,EAAE,CAAC;UAAEM,OAAO,EAAE;QAAE,CAAE;QAClCL,QAAQ,EAAE;UAAED,KAAK,EAAE,GAAG;UAAEM,OAAO,EAAE;QAAI,CAAE;QACvCC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAEfd,SAAS,iBACRH,OAAA,CAACL,MAAM,CAACuC,IAAI;MACVR,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEO,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCR,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEO,CAAC,EAAE;MAAE,CAAE;MAC9BjC,SAAS,EAAC,iEAAiE;MAAAgB,QAAA,GAE1EZ,KAAK,EAAC,OACT;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CACd;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACZ,EAAA,CAxGIJ,WAAW;EAAA,QAC2BH,QAAQ;AAAA;AAAAsC,EAAA,GAD9CnC,WAAW;AA0GjB,eAAeA,WAAW;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}