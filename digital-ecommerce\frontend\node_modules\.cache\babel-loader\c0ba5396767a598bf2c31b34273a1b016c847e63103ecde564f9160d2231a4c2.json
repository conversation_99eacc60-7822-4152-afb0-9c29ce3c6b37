{"ast": null, "code": "import React,{useState,useEffect,Fragment}from'react';import{Link,useLocation}from'react-router-dom';import{motion,AnimatePresence}from'framer-motion';import{Disclosure,Menu,Transition}from'@headlessui/react';import{Bars3Icon,XMarkIcon,ShoppingBagIcon,MagnifyingGlassIcon,UserIcon,HeartIcon,HomeIcon,TagIcon,PhoneIcon,InformationCircleIcon,ChevronDownIcon,Cog6ToothIcon,ArrowRightOnRectangleIcon}from'@heroicons/react/24/outline';import ShoppingCart from'./ShoppingCart';import{useUser}from'../contexts/UserContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function classNames(){for(var _len=arguments.length,classes=new Array(_len),_key=0;_key<_len;_key++){classes[_key]=arguments[_key];}return classes.filter(Boolean).join(' ');}const ModernNavigation=()=>{const[isScrolled,setIsScrolled]=useState(false);const[searchQuery,setSearchQuery]=useState('');const location=useLocation();const{user,isAuthenticated,logout}=useUser();const handleSearch=e=>{e.preventDefault();if(searchQuery.trim()){window.location.href=\"/products?search=\".concat(encodeURIComponent(searchQuery.trim()));}};useEffect(()=>{const handleScroll=()=>{setIsScrolled(window.scrollY>10);};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);const navigation=[{name:'Home',href:'/',icon:HomeIcon},{name:'Products',href:'/products',icon:TagIcon},{name:'Digital',href:'/digital-products',icon:TagIcon},{name:'About',href:'/about',icon:InformationCircleIcon},{name:'Contact',href:'/contact',icon:PhoneIcon}];const userNavigation=[{name:'Your Profile',href:'/account',icon:UserIcon},{name:'Order History',href:'/orders',icon:ShoppingBagIcon},{name:'Wishlist',href:'/wishlist',icon:HeartIcon},{name:'Settings',href:'/settings',icon:Cog6ToothIcon}];const isActive=path=>location.pathname===path;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Disclosure,{as:\"nav\",className:\"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled?'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100':'bg-white/10 backdrop-blur-sm'),children:_ref=>{let{open}=_ref;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-20 items-center justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center space-x-3 group\",children:[/*#__PURE__*/_jsxs(motion.div,{whileHover:{rotate:360,scale:1.1},transition:{duration:0.6,type:\"spring\",stiffness:200},className:\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\",children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-7 h-7 text-white\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold transition-all duration-300 \".concat(isScrolled?'text-gray-900':'text-white drop-shadow-lg'),children:\"ShopHub\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium transition-all duration-300 \".concat(isScrolled?'text-light-orange-600':'text-white/80'),children:\"Premium Store\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:block\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-baseline space-x-2\",children:navigation.map(item=>/*#__PURE__*/_jsx(motion.div,{whileHover:{y:-2},transition:{duration:0.2},children:/*#__PURE__*/_jsxs(Link,{to:item.href,className:classNames(isActive(item.href)?isScrolled?'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25':'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm':isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50':'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm','relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"relative z-10\",children:item.name}),!isActive(item.href)&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"})]})},item.name))})}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex items-center flex-1 max-w-lg mx-8\",children:/*#__PURE__*/_jsxs(motion.div,{className:\"relative w-full group\",whileHover:{scale:1.02},transition:{duration:0.2},children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 transition-colors duration-300 \".concat(isScrolled?'text-gray-400 group-hover:text-light-orange-500':'text-white/70 group-hover:text-white')})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search for products, brands, and more...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),onKeyDown:e=>e.key==='Enter'&&handleSearch(e),className:\"w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 \".concat(isScrolled?'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100':'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20',\" focus:outline-none shadow-lg hover:shadow-xl\")})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(Link,{to:\"/wishlist\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1,y:-2},whileTap:{scale:0.95},className:\"relative p-3 rounded-xl transition-all duration-300 group \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg':'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'),children:/*#__PURE__*/_jsx(HeartIcon,{className:\"w-6 h-6\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"relative\",children:/*#__PURE__*/_jsx(ShoppingCart,{})}),isAuthenticated?/*#__PURE__*/_jsxs(Menu,{as:\"div\",className:\"relative ml-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Menu.Button,{className:\"relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg':'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Open user menu\"}),user!==null&&user!==void 0&&user.profilePicture?/*#__PURE__*/_jsx(\"img\",{className:\"h-8 w-8 rounded-full ring-2 ring-white/20\",src:user.profilePicture,alt:\"\"}):/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden md:block text-sm font-medium\",children:(user===null||user===void 0?void 0:user.firstName)||'Account'}),/*#__PURE__*/_jsx(ChevronDownIcon,{className:\"w-4 h-4\"})]})}),/*#__PURE__*/_jsx(Transition,{as:Fragment,enter:\"transition ease-out duration-100\",enterFrom:\"transform opacity-0 scale-95\",enterTo:\"transform opacity-100 scale-100\",leave:\"transition ease-in duration-75\",leaveFrom:\"transform opacity-100 scale-100\",leaveTo:\"transform opacity-0 scale-95\",children:/*#__PURE__*/_jsxs(Menu.Items,{className:\"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[user!==null&&user!==void 0&&user.profilePicture?/*#__PURE__*/_jsx(\"img\",{className:\"w-12 h-12 rounded-full ring-2 ring-white/30\",src:user.profilePicture,alt:\"\"}):/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"font-semibold text-white\",children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-white/80\",children:user===null||user===void 0?void 0:user.email})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"py-2\",children:[userNavigation.map(item=>/*#__PURE__*/_jsx(Menu.Item,{children:_ref2=>{let{active}=_ref2;return/*#__PURE__*/_jsxs(Link,{to:item.href,className:classNames(active?'bg-light-orange-50 text-light-orange-600':'text-gray-700','flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'),children:[/*#__PURE__*/_jsx(item.icon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:item.name})]});}},item.name)),/*#__PURE__*/_jsx(\"div\",{className:\"border-t border-gray-100 mt-2 pt-2\",children:/*#__PURE__*/_jsx(Menu.Item,{children:_ref3=>{let{active}=_ref3;return/*#__PURE__*/_jsxs(\"button\",{onClick:logout,className:classNames(active?'bg-red-50 text-red-600':'text-red-600','flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'),children:[/*#__PURE__*/_jsx(ArrowRightOnRectangleIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Sign out\"})]});}})})]})]})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(Link,{to:\"/login\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.05,y:-2},whileTap:{scale:0.95},className:\"px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200':'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm border border-white/20 hover:border-white/40'),children:\"Sign In\"})}),/*#__PURE__*/_jsx(Link,{to:\"/register\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.05,y:-2},whileTap:{scale:0.95},className:\"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\",children:\"Sign Up\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"lg:hidden\",children:/*#__PURE__*/_jsxs(Disclosure.Button,{className:\"relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50':'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm',\" focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Open main menu\"}),/*#__PURE__*/_jsx(motion.div,{animate:{rotate:open?180:0},transition:{duration:0.3},children:open?/*#__PURE__*/_jsx(XMarkIcon,{className:\"block h-6 w-6\",\"aria-hidden\":\"true\"}):/*#__PURE__*/_jsx(Bars3Icon,{className:\"block h-6 w-6\",\"aria-hidden\":\"true\"})})]})})]})]})}),/*#__PURE__*/_jsx(Disclosure.Panel,{className:\"lg:hidden\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,height:0,y:-20},animate:{opacity:1,height:'auto',y:0},exit:{opacity:0,height:0,y:-20},transition:{duration:0.3,ease:\"easeInOut\"},className:\"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-1 px-6 pb-6 pt-6\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:0.1},className:\"relative mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search for products...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),onKeyDown:e=>e.key==='Enter'&&handleSearch(e),className:\"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:navigation.map((item,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:0.1*(index+2)},children:/*#__PURE__*/_jsxs(Disclosure.Button,{as:Link,to:item.href,className:classNames(isActive(item.href)?'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg':'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600','flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'),children:[/*#__PURE__*/_jsx(item.icon,{className:classNames(isActive(item.href)?'text-white':'text-gray-500 group-hover:text-light-orange-500','w-6 h-6')}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-lg\",children:item.name})]})},item.name))}),!isAuthenticated&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.4},className:\"flex space-x-4 pt-6\",children:[/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"flex-1\",children:/*#__PURE__*/_jsx(Disclosure.Button,{as:\"button\",className:\"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\",children:\"Sign In\"})}),/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"flex-1\",children:/*#__PURE__*/_jsx(Disclosure.Button,{as:\"button\",className:\"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\",children:\"Sign Up\"})})]})]})})})]});}}),/*#__PURE__*/_jsx(\"div\",{className:\"h-20\"})]});};export default ModernNavigation;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "Link", "useLocation", "motion", "AnimatePresence", "Disclosure", "<PERSON><PERSON>", "Transition", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ChevronDownIcon", "Cog6ToothIcon", "ArrowRightOnRectangleIcon", "ShoppingCart", "useUser", "jsx", "_jsx", "jsxs", "_jsxs", "_Fragment", "classNames", "_len", "arguments", "length", "classes", "Array", "_key", "filter", "Boolean", "join", "ModernNavigation", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "user", "isAuthenticated", "logout", "handleSearch", "e", "preventDefault", "trim", "window", "href", "concat", "encodeURIComponent", "handleScroll", "scrollY", "addEventListener", "removeEventListener", "navigation", "name", "icon", "userNavigation", "isActive", "path", "pathname", "children", "as", "className", "_ref", "open", "to", "div", "whileHover", "rotate", "scale", "transition", "duration", "type", "stiffness", "map", "item", "y", "placeholder", "value", "onChange", "target", "onKeyDown", "key", "button", "whileTap", "<PERSON><PERSON>", "profilePicture", "src", "alt", "firstName", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Items", "lastName", "email", "<PERSON><PERSON>", "_ref2", "active", "_ref3", "onClick", "animate", "Panel", "initial", "opacity", "height", "exit", "ease", "x", "delay", "index"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ModernNavigation.js"], "sourcesContent": ["import React, { useState, useEffect, Fragment } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Disclosure, Menu, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon,\n  ChevronDownIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport { useUser } from '../contexts/UserContext';\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst ModernNavigation = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const userNavigation = [\n    { name: 'Your Profile', href: '/account', icon: UserIcon },\n    { name: 'Order History', href: '/orders', icon: ShoppingBagIcon },\n    { name: 'Wishlist', href: '/wishlist', icon: HeartIcon },\n    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <Disclosure as=\"nav\" className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n        isScrolled \n          ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100' \n          : 'bg-white/10 backdrop-blur-sm'\n      }`}>\n        {({ open }) => (\n          <>\n            <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex h-20 items-center justify-between\">\n                {/* Logo */}\n                <div className=\"flex items-center\">\n                  <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n                    <motion.div\n                      whileHover={{ rotate: 360, scale: 1.1 }}\n                      transition={{ duration: 0.6, type: \"spring\", stiffness: 200 }}\n                      className=\"relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                    >\n                      <ShoppingBagIcon className=\"w-7 h-7 text-white\" />\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl\"></div>\n                    </motion.div>\n                    <div className=\"flex flex-col\">\n                      <span className={`text-2xl font-bold transition-all duration-300 ${\n                        isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'\n                      }`}>\n                        ShopHub\n                      </span>\n                      <span className={`text-xs font-medium transition-all duration-300 ${\n                        isScrolled ? 'text-light-orange-600' : 'text-white/80'\n                      }`}>\n                        Premium Store\n                      </span>\n                    </div>\n                  </Link>\n                </div>\n\n                {/* Desktop Navigation */}\n                <div className=\"hidden lg:block\">\n                  <div className=\"flex items-baseline space-x-2\">\n                    {navigation.map((item) => (\n                      <motion.div\n                        key={item.name}\n                        whileHover={{ y: -2 }}\n                        transition={{ duration: 0.2 }}\n                      >\n                        <Link\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? isScrolled\n                                ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'\n                                : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm'\n                              : isScrolled\n                                ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                                : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm',\n                            'relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group'\n                          )}\n                        >\n                          <span className=\"relative z-10\">{item.name}</span>\n                          {!isActive(item.href) && (\n                            <div className=\"absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300\"></div>\n                          )}\n                        </Link>\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Search Bar */}\n                <div className=\"hidden md:flex items-center flex-1 max-w-lg mx-8\">\n                  <motion.div \n                    className=\"relative w-full group\"\n                    whileHover={{ scale: 1.02 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${\n                        isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'\n                      }`} />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search for products, brands, and more...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${\n                        isScrolled\n                          ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'\n                          : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'\n                      } focus:outline-none shadow-lg hover:shadow-xl`}\n                    />\n                  </motion.div>\n                </div>\n\n                {/* Right side items */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Wishlist */}\n                  <Link to=\"/wishlist\">\n                    <motion.button\n                      whileHover={{ scale: 1.1, y: -2 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`relative p-3 rounded-xl transition-all duration-300 group ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'\n                      }`}\n                    >\n                      <HeartIcon className=\"w-6 h-6\" />\n                    </motion.button>\n                  </Link>\n\n                  {/* Shopping Cart */}\n                  <div className=\"relative\">\n                    <ShoppingCart />\n                  </div>\n\n                  {/* User menu */}\n                  {isAuthenticated ? (\n                    <Menu as=\"div\" className=\"relative ml-3\">\n                      <div>\n                        <Menu.Button className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${\n                          isScrolled\n                            ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'\n                            : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'\n                        }`}>\n                          <span className=\"sr-only\">Open user menu</span>\n                          {user?.profilePicture ? (\n                            <img\n                              className=\"h-8 w-8 rounded-full ring-2 ring-white/20\"\n                              src={user.profilePicture}\n                              alt=\"\"\n                            />\n                          ) : (\n                            <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center\">\n                              <UserIcon className=\"w-5 h-5 text-white\" />\n                            </div>\n                          )}\n                          <span className=\"hidden md:block text-sm font-medium\">\n                            {user?.firstName || 'Account'}\n                          </span>\n                          <ChevronDownIcon className=\"w-4 h-4\" />\n                        </Menu.Button>\n                      </div>\n                      <Transition\n                        as={Fragment}\n                        enter=\"transition ease-out duration-100\"\n                        enterFrom=\"transform opacity-0 scale-95\"\n                        enterTo=\"transform opacity-100 scale-100\"\n                        leave=\"transition ease-in duration-75\"\n                        leaveFrom=\"transform opacity-100 scale-100\"\n                        leaveTo=\"transform opacity-0 scale-95\"\n                      >\n                        <Menu.Items className=\"absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white py-1 shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden\">\n                          {/* User info header */}\n                          <div className=\"px-4 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white\">\n                            <div className=\"flex items-center space-x-3\">\n                              {user?.profilePicture ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full ring-2 ring-white/30\"\n                                  src={user.profilePicture}\n                                  alt=\"\"\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-white/20 flex items-center justify-center\">\n                                  <UserIcon className=\"w-6 h-6 text-white\" />\n                                </div>\n                              )}\n                              <div>\n                                <p className=\"font-semibold text-white\">\n                                  {user?.firstName} {user?.lastName}\n                                </p>\n                                <p className=\"text-sm text-white/80\">{user?.email}</p>\n                              </div>\n                            </div>\n                          </div>\n                          \n                          {/* Menu items */}\n                          <div className=\"py-2\">\n                            {userNavigation.map((item) => (\n                              <Menu.Item key={item.name}>\n                                {({ active }) => (\n                                  <Link\n                                    to={item.href}\n                                    className={classNames(\n                                      active ? 'bg-light-orange-50 text-light-orange-600' : 'text-gray-700',\n                                      'flex items-center space-x-3 px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <item.icon className=\"w-5 h-5\" />\n                                    <span>{item.name}</span>\n                                  </Link>\n                                )}\n                              </Menu.Item>\n                            ))}\n                            <div className=\"border-t border-gray-100 mt-2 pt-2\">\n                              <Menu.Item>\n                                {({ active }) => (\n                                  <button\n                                    onClick={logout}\n                                    className={classNames(\n                                      active ? 'bg-red-50 text-red-600' : 'text-red-600',\n                                      'flex items-center space-x-3 w-full px-4 py-3 text-sm transition-colors duration-200'\n                                    )}\n                                  >\n                                    <ArrowRightOnRectangleIcon className=\"w-5 h-5\" />\n                                    <span>Sign out</span>\n                                  </button>\n                                )}\n                              </Menu.Item>\n                            </div>\n                          </div>\n                        </Menu.Items>\n                      </Transition>\n                    </Menu>\n                  ) : (\n                    <div className=\"flex items-center space-x-3\">\n                      <Link to=\"/login\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${\n                            isScrolled\n                              ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'\n                              : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm border border-white/20 hover:border-white/40'\n                          }`}\n                        >\n                          Sign In\n                        </motion.button>\n                      </Link>\n                      <Link to=\"/register\">\n                        <motion.button\n                          whileHover={{ scale: 1.05, y: -2 }}\n                          whileTap={{ scale: 0.95 }}\n                          className=\"px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\n                        >\n                          Sign Up\n                        </motion.button>\n                      </Link>\n                    </div>\n                  )}\n\n                  {/* Mobile menu button */}\n                  <div className=\"lg:hidden\">\n                    <Disclosure.Button className={`relative inline-flex items-center justify-center rounded-xl p-3 transition-all duration-300 ${\n                      isScrolled \n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' \n                        : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'\n                    } focus:outline-none focus:ring-2 focus:ring-inset focus:ring-light-orange-500`}>\n                      <span className=\"sr-only\">Open main menu</span>\n                      <motion.div\n                        animate={{ rotate: open ? 180 : 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {open ? (\n                          <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        ) : (\n                          <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                        )}\n                      </motion.div>\n                    </Disclosure.Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Mobile menu */}\n            <Disclosure.Panel className=\"lg:hidden\">\n              <motion.div\n                initial={{ opacity: 0, height: 0, y: -20 }}\n                animate={{ opacity: 1, height: 'auto', y: 0 }}\n                exit={{ opacity: 0, height: 0, y: -20 }}\n                transition={{ duration: 0.3, ease: \"easeInOut\" }}\n                className=\"backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl\"\n              >\n                <div className=\"space-y-1 px-6 pb-6 pt-6\">\n                  {/* Mobile Search */}\n                  <motion.div \n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 }}\n                    className=\"relative mb-6\"\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                      <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search for products...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}\n                      className=\"w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300\"\n                    />\n                  </motion.div>\n\n                  {/* Mobile Navigation */}\n                  <div className=\"space-y-3\">\n                    {navigation.map((item, index) => (\n                      <motion.div\n                        key={item.name}\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: 0.1 * (index + 2) }}\n                      >\n                        <Disclosure.Button\n                          as={Link}\n                          to={item.href}\n                          className={classNames(\n                            isActive(item.href)\n                              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'\n                              : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600',\n                            'flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group'\n                          )}\n                        >\n                          <item.icon className={classNames(\n                            isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500',\n                            'w-6 h-6'\n                          )} />\n                          <span className=\"font-semibold text-lg\">{item.name}</span>\n                        </Disclosure.Button>\n                      </motion.div>\n                    ))}\n                  </div>\n\n                  {/* Mobile Auth Buttons */}\n                  {!isAuthenticated && (\n                    <motion.div \n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4 }}\n                      className=\"flex space-x-4 pt-6\"\n                    >\n                      <Link to=\"/login\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300\"\n                        >\n                          Sign In\n                        </Disclosure.Button>\n                      </Link>\n                      <Link to=\"/register\" className=\"flex-1\">\n                        <Disclosure.Button\n                          as=\"button\"\n                          className=\"w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg\"\n                        >\n                          Sign Up\n                        </Disclosure.Button>\n                      </Link>\n                    </motion.div>\n                  )}\n                </div>\n              </motion.div>\n            </Disclosure.Panel>\n          </>\n        )}\n      </Disclosure>\n\n      {/* Spacer */}\n      <div className=\"h-20\"></div>\n    </>\n  );\n};\n\nexport default ModernNavigation;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,UAAU,CAAEC,IAAI,CAAEC,UAAU,KAAQ,mBAAmB,CAChE,OACEC,SAAS,CACTC,SAAS,CACTC,eAAe,CACfC,mBAAmB,CACnBC,QAAQ,CACRC,SAAS,CACTC,QAAQ,CACRC,OAAO,CACPC,SAAS,CACTC,qBAAqB,CACrBC,eAAe,CACfC,aAAa,CACbC,yBAAyB,KACpB,6BAA6B,CACpC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAA1B,QAAA,IAAA2B,SAAA,yBAElD,QAAS,CAAAC,UAAUA,CAAA,CAAa,SAAAC,IAAA,CAAAC,SAAA,CAAAC,MAAA,CAATC,OAAO,KAAAC,KAAA,CAAAJ,IAAA,EAAAK,IAAA,GAAAA,IAAA,CAAAL,IAAA,CAAAK,IAAA,IAAPF,OAAO,CAAAE,IAAA,EAAAJ,SAAA,CAAAI,IAAA,GAC5B,MAAO,CAAAF,OAAO,CAACG,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAC1C,CAEA,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC2C,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAA6C,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE0C,IAAI,CAAEC,eAAe,CAAEC,MAAO,CAAC,CAAGxB,OAAO,CAAC,CAAC,CAEnD,KAAM,CAAAyB,YAAY,CAAIC,CAAC,EAAK,CAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAIR,WAAW,CAACS,IAAI,CAAC,CAAC,CAAE,CACtBC,MAAM,CAACR,QAAQ,CAACS,IAAI,qBAAAC,MAAA,CAAuBC,kBAAkB,CAACb,WAAW,CAACS,IAAI,CAAC,CAAC,CAAC,CAAE,CACrF,CACF,CAAC,CAEDnD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwD,YAAY,CAAGA,CAAA,GAAM,CACzBf,aAAa,CAACW,MAAM,CAACK,OAAO,CAAG,EAAE,CAAC,CACpC,CAAC,CAEDL,MAAM,CAACM,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAC/C,MAAO,IAAMJ,MAAM,CAACO,mBAAmB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,MAAM,CAAER,IAAI,CAAE,GAAG,CAAES,IAAI,CAAE/C,QAAS,CAAC,CAC3C,CAAE8C,IAAI,CAAE,UAAU,CAAER,IAAI,CAAE,WAAW,CAAES,IAAI,CAAE9C,OAAQ,CAAC,CACtD,CAAE6C,IAAI,CAAE,SAAS,CAAER,IAAI,CAAE,mBAAmB,CAAES,IAAI,CAAE9C,OAAQ,CAAC,CAC7D,CAAE6C,IAAI,CAAE,OAAO,CAAER,IAAI,CAAE,QAAQ,CAAES,IAAI,CAAE5C,qBAAsB,CAAC,CAC9D,CAAE2C,IAAI,CAAE,SAAS,CAAER,IAAI,CAAE,UAAU,CAAES,IAAI,CAAE7C,SAAU,CAAC,CACvD,CAED,KAAM,CAAA8C,cAAc,CAAG,CACrB,CAAEF,IAAI,CAAE,cAAc,CAAER,IAAI,CAAE,UAAU,CAAES,IAAI,CAAEjD,QAAS,CAAC,CAC1D,CAAEgD,IAAI,CAAE,eAAe,CAAER,IAAI,CAAE,SAAS,CAAES,IAAI,CAAEnD,eAAgB,CAAC,CACjE,CAAEkD,IAAI,CAAE,UAAU,CAAER,IAAI,CAAE,WAAW,CAAES,IAAI,CAAEhD,SAAU,CAAC,CACxD,CAAE+C,IAAI,CAAE,UAAU,CAAER,IAAI,CAAE,WAAW,CAAES,IAAI,CAAE1C,aAAc,CAAC,CAC7D,CAED,KAAM,CAAA4C,QAAQ,CAAIC,IAAI,EAAKrB,QAAQ,CAACsB,QAAQ,GAAKD,IAAI,CAErD,mBACEtC,KAAA,CAAAC,SAAA,EAAAuC,QAAA,eACE1C,IAAA,CAACnB,UAAU,EAAC8D,EAAE,CAAC,KAAK,CAACC,SAAS,gEAAAf,MAAA,CAC5Bd,UAAU,CACN,iEAAiE,CACjE,8BAA8B,CACjC,CAAA2B,QAAA,CACAG,IAAA,MAAC,CAAEC,IAAK,CAAC,CAAAD,IAAA,oBACR3C,KAAA,CAAAC,SAAA,EAAAuC,QAAA,eACE1C,IAAA,QAAK4C,SAAS,CAAC,wCAAwC,CAAAF,QAAA,cACrDxC,KAAA,QAAK0C,SAAS,CAAC,wCAAwC,CAAAF,QAAA,eAErD1C,IAAA,QAAK4C,SAAS,CAAC,mBAAmB,CAAAF,QAAA,cAChCxC,KAAA,CAACzB,IAAI,EAACsE,EAAE,CAAC,GAAG,CAACH,SAAS,CAAC,mCAAmC,CAAAF,QAAA,eACxDxC,KAAA,CAACvB,MAAM,CAACqE,GAAG,EACTC,UAAU,CAAE,CAAEC,MAAM,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CACxCC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,QAAQ,CAAEC,SAAS,CAAE,GAAI,CAAE,CAC9DX,SAAS,CAAC,2MAA2M,CAAAF,QAAA,eAErN1C,IAAA,CAACd,eAAe,EAAC0D,SAAS,CAAC,oBAAoB,CAAE,CAAC,cAClD5C,IAAA,QAAK4C,SAAS,CAAC,6EAA6E,CAAM,CAAC,EACzF,CAAC,cACb1C,KAAA,QAAK0C,SAAS,CAAC,eAAe,CAAAF,QAAA,eAC5B1C,IAAA,SAAM4C,SAAS,mDAAAf,MAAA,CACbd,UAAU,CAAG,eAAe,CAAG,2BAA2B,CACzD,CAAA2B,QAAA,CAAC,SAEJ,CAAM,CAAC,cACP1C,IAAA,SAAM4C,SAAS,oDAAAf,MAAA,CACbd,UAAU,CAAG,uBAAuB,CAAG,eAAe,CACrD,CAAA2B,QAAA,CAAC,eAEJ,CAAM,CAAC,EACJ,CAAC,EACF,CAAC,CACJ,CAAC,cAGN1C,IAAA,QAAK4C,SAAS,CAAC,iBAAiB,CAAAF,QAAA,cAC9B1C,IAAA,QAAK4C,SAAS,CAAC,+BAA+B,CAAAF,QAAA,CAC3CP,UAAU,CAACqB,GAAG,CAAEC,IAAI,eACnBzD,IAAA,CAACrB,MAAM,CAACqE,GAAG,EAETC,UAAU,CAAE,CAAES,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBN,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAX,QAAA,cAE9BxC,KAAA,CAACzB,IAAI,EACHsE,EAAE,CAAEU,IAAI,CAAC7B,IAAK,CACdgB,SAAS,CAAExC,UAAU,CACnBmC,QAAQ,CAACkB,IAAI,CAAC7B,IAAI,CAAC,CACfb,UAAU,CACR,qEAAqE,CACrE,sDAAsD,CACxDA,UAAU,CACR,oEAAoE,CACpE,mEAAmE,CACzE,yFACF,CAAE,CAAA2B,QAAA,eAEF1C,IAAA,SAAM4C,SAAS,CAAC,eAAe,CAAAF,QAAA,CAAEe,IAAI,CAACrB,IAAI,CAAO,CAAC,CACjD,CAACG,QAAQ,CAACkB,IAAI,CAAC7B,IAAI,CAAC,eACnB5B,IAAA,QAAK4C,SAAS,CAAC,yJAAyJ,CAAM,CAC/K,EACG,CAAC,EArBFa,IAAI,CAACrB,IAsBA,CACb,CAAC,CACC,CAAC,CACH,CAAC,cAGNpC,IAAA,QAAK4C,SAAS,CAAC,kDAAkD,CAAAF,QAAA,cAC/DxC,KAAA,CAACvB,MAAM,CAACqE,GAAG,EACTJ,SAAS,CAAC,uBAAuB,CACjCK,UAAU,CAAE,CAAEE,KAAK,CAAE,IAAK,CAAE,CAC5BC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAX,QAAA,eAE9B1C,IAAA,QAAK4C,SAAS,CAAC,sEAAsE,CAAAF,QAAA,cACnF1C,IAAA,CAACb,mBAAmB,EAACyD,SAAS,2CAAAf,MAAA,CAC5Bd,UAAU,CAAG,iDAAiD,CAAG,sCAAsC,CACtG,CAAE,CAAC,CACH,CAAC,cACNf,IAAA,UACEsD,IAAI,CAAC,MAAM,CACXK,WAAW,CAAC,0CAA0C,CACtDC,KAAK,CAAE3C,WAAY,CACnB4C,QAAQ,CAAGrC,CAAC,EAAKN,cAAc,CAACM,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE,CAChDG,SAAS,CAAGvC,CAAC,EAAKA,CAAC,CAACwC,GAAG,GAAK,OAAO,EAAIzC,YAAY,CAACC,CAAC,CAAE,CACvDoB,SAAS,4EAAAf,MAAA,CACPd,UAAU,CACN,qJAAqJ,CACrJ,uJAAuJ,iDAC7G,CACjD,CAAC,EACQ,CAAC,CACV,CAAC,cAGNb,KAAA,QAAK0C,SAAS,CAAC,6BAA6B,CAAAF,QAAA,eAE1C1C,IAAA,CAACvB,IAAI,EAACsE,EAAE,CAAC,WAAW,CAAAL,QAAA,cAClB1C,IAAA,CAACrB,MAAM,CAACsF,MAAM,EACZhB,UAAU,CAAE,CAAEE,KAAK,CAAE,GAAG,CAAEO,CAAC,CAAE,CAAC,CAAE,CAAE,CAClCQ,QAAQ,CAAE,CAAEf,KAAK,CAAE,IAAK,CAAE,CAC1BP,SAAS,8DAAAf,MAAA,CACPd,UAAU,CACN,oFAAoF,CACpF,mFAAmF,CACtF,CAAA2B,QAAA,cAEH1C,IAAA,CAACX,SAAS,EAACuD,SAAS,CAAC,SAAS,CAAE,CAAC,CACpB,CAAC,CACZ,CAAC,cAGP5C,IAAA,QAAK4C,SAAS,CAAC,UAAU,CAAAF,QAAA,cACvB1C,IAAA,CAACH,YAAY,GAAE,CAAC,CACb,CAAC,CAGLwB,eAAe,cACdnB,KAAA,CAACpB,IAAI,EAAC6D,EAAE,CAAC,KAAK,CAACC,SAAS,CAAC,eAAe,CAAAF,QAAA,eACtC1C,IAAA,QAAA0C,QAAA,cACExC,KAAA,CAACpB,IAAI,CAACqF,MAAM,EAACvB,SAAS,gGAAAf,MAAA,CACpBd,UAAU,CACN,oFAAoF,CACpF,mFAAmF,CACtF,CAAA2B,QAAA,eACD1C,IAAA,SAAM4C,SAAS,CAAC,SAAS,CAAAF,QAAA,CAAC,gBAAc,CAAM,CAAC,CAC9CtB,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgD,cAAc,cACnBpE,IAAA,QACE4C,SAAS,CAAC,2CAA2C,CACrDyB,GAAG,CAAEjD,IAAI,CAACgD,cAAe,CACzBE,GAAG,CAAC,EAAE,CACP,CAAC,cAEFtE,IAAA,QAAK4C,SAAS,CAAC,mHAAmH,CAAAF,QAAA,cAChI1C,IAAA,CAACZ,QAAQ,EAACwD,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CACN,cACD5C,IAAA,SAAM4C,SAAS,CAAC,qCAAqC,CAAAF,QAAA,CAClD,CAAAtB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEmD,SAAS,GAAI,SAAS,CACzB,CAAC,cACPvE,IAAA,CAACN,eAAe,EAACkD,SAAS,CAAC,SAAS,CAAE,CAAC,EAC5B,CAAC,CACX,CAAC,cACN5C,IAAA,CAACjB,UAAU,EACT4D,EAAE,CAAEnE,QAAS,CACbgG,KAAK,CAAC,kCAAkC,CACxCC,SAAS,CAAC,8BAA8B,CACxCC,OAAO,CAAC,iCAAiC,CACzCC,KAAK,CAAC,gCAAgC,CACtCC,SAAS,CAAC,iCAAiC,CAC3CC,OAAO,CAAC,8BAA8B,CAAAnC,QAAA,cAEtCxC,KAAA,CAACpB,IAAI,CAACgG,KAAK,EAAClC,SAAS,CAAC,2JAA2J,CAAAF,QAAA,eAE/K1C,IAAA,QAAK4C,SAAS,CAAC,iFAAiF,CAAAF,QAAA,cAC9FxC,KAAA,QAAK0C,SAAS,CAAC,6BAA6B,CAAAF,QAAA,EACzCtB,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgD,cAAc,cACnBpE,IAAA,QACE4C,SAAS,CAAC,6CAA6C,CACvDyB,GAAG,CAAEjD,IAAI,CAACgD,cAAe,CACzBE,GAAG,CAAC,EAAE,CACP,CAAC,cAEFtE,IAAA,QAAK4C,SAAS,CAAC,qEAAqE,CAAAF,QAAA,cAClF1C,IAAA,CAACZ,QAAQ,EAACwD,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CACN,cACD1C,KAAA,QAAAwC,QAAA,eACExC,KAAA,MAAG0C,SAAS,CAAC,0BAA0B,CAAAF,QAAA,EACpCtB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEmD,SAAS,CAAC,GAAC,CAACnD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2D,QAAQ,EAChC,CAAC,cACJ/E,IAAA,MAAG4C,SAAS,CAAC,uBAAuB,CAAAF,QAAA,CAAEtB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE4D,KAAK,CAAI,CAAC,EACnD,CAAC,EACH,CAAC,CACH,CAAC,cAGN9E,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAF,QAAA,EAClBJ,cAAc,CAACkB,GAAG,CAAEC,IAAI,eACvBzD,IAAA,CAAClB,IAAI,CAACmG,IAAI,EAAAvC,QAAA,CACPwC,KAAA,MAAC,CAAEC,MAAO,CAAC,CAAAD,KAAA,oBACVhF,KAAA,CAACzB,IAAI,EACHsE,EAAE,CAAEU,IAAI,CAAC7B,IAAK,CACdgB,SAAS,CAAExC,UAAU,CACnB+E,MAAM,CAAG,0CAA0C,CAAG,eAAe,CACrE,8EACF,CAAE,CAAAzC,QAAA,eAEF1C,IAAA,CAACyD,IAAI,CAACpB,IAAI,EAACO,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC5C,IAAA,SAAA0C,QAAA,CAAOe,IAAI,CAACrB,IAAI,CAAO,CAAC,EACpB,CAAC,EACR,EAZaqB,IAAI,CAACrB,IAaV,CACZ,CAAC,cACFpC,IAAA,QAAK4C,SAAS,CAAC,oCAAoC,CAAAF,QAAA,cACjD1C,IAAA,CAAClB,IAAI,CAACmG,IAAI,EAAAvC,QAAA,CACP0C,KAAA,MAAC,CAAED,MAAO,CAAC,CAAAC,KAAA,oBACVlF,KAAA,WACEmF,OAAO,CAAE/D,MAAO,CAChBsB,SAAS,CAAExC,UAAU,CACnB+E,MAAM,CAAG,wBAAwB,CAAG,cAAc,CAClD,qFACF,CAAE,CAAAzC,QAAA,eAEF1C,IAAA,CAACJ,yBAAyB,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,cACjD5C,IAAA,SAAA0C,QAAA,CAAM,UAAQ,CAAM,CAAC,EACf,CAAC,EACV,CACQ,CAAC,CACT,CAAC,EACH,CAAC,EACI,CAAC,CACH,CAAC,EACT,CAAC,cAEPxC,KAAA,QAAK0C,SAAS,CAAC,6BAA6B,CAAAF,QAAA,eAC1C1C,IAAA,CAACvB,IAAI,EAACsE,EAAE,CAAC,QAAQ,CAAAL,QAAA,cACf1C,IAAA,CAACrB,MAAM,CAACsF,MAAM,EACZhB,UAAU,CAAE,CAAEE,KAAK,CAAE,IAAI,CAAEO,CAAC,CAAE,CAAC,CAAE,CAAE,CACnCQ,QAAQ,CAAE,CAAEf,KAAK,CAAE,IAAK,CAAE,CAC1BP,SAAS,6EAAAf,MAAA,CACPd,UAAU,CACN,yHAAyH,CACzH,gHAAgH,CACnH,CAAA2B,QAAA,CACJ,SAED,CAAe,CAAC,CACZ,CAAC,cACP1C,IAAA,CAACvB,IAAI,EAACsE,EAAE,CAAC,WAAW,CAAAL,QAAA,cAClB1C,IAAA,CAACrB,MAAM,CAACsF,MAAM,EACZhB,UAAU,CAAE,CAAEE,KAAK,CAAE,IAAI,CAAEO,CAAC,CAAE,CAAC,CAAE,CAAE,CACnCQ,QAAQ,CAAE,CAAEf,KAAK,CAAE,IAAK,CAAE,CAC1BP,SAAS,CAAC,gOAAgO,CAAAF,QAAA,CAC3O,SAED,CAAe,CAAC,CACZ,CAAC,EACJ,CACN,cAGD1C,IAAA,QAAK4C,SAAS,CAAC,WAAW,CAAAF,QAAA,cACxBxC,KAAA,CAACrB,UAAU,CAACsF,MAAM,EAACvB,SAAS,gGAAAf,MAAA,CAC1Bd,UAAU,CACN,oEAAoE,CACpE,mEAAmE,iFACO,CAAA2B,QAAA,eAC9E1C,IAAA,SAAM4C,SAAS,CAAC,SAAS,CAAAF,QAAA,CAAC,gBAAc,CAAM,CAAC,cAC/C1C,IAAA,CAACrB,MAAM,CAACqE,GAAG,EACTsC,OAAO,CAAE,CAAEpC,MAAM,CAAEJ,IAAI,CAAG,GAAG,CAAG,CAAE,CAAE,CACpCM,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAX,QAAA,CAE7BI,IAAI,cACH9C,IAAA,CAACf,SAAS,EAAC2D,SAAS,CAAC,eAAe,CAAC,cAAY,MAAM,CAAE,CAAC,cAE1D5C,IAAA,CAAChB,SAAS,EAAC4D,SAAS,CAAC,eAAe,CAAC,cAAY,MAAM,CAAE,CAC1D,CACS,CAAC,EACI,CAAC,CACjB,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN5C,IAAA,CAACnB,UAAU,CAAC0G,KAAK,EAAC3C,SAAS,CAAC,WAAW,CAAAF,QAAA,cACrC1C,IAAA,CAACrB,MAAM,CAACqE,GAAG,EACTwC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEhC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC3C4B,OAAO,CAAE,CAAEG,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,MAAM,CAAEhC,CAAC,CAAE,CAAE,CAAE,CAC9CiC,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEhC,CAAC,CAAE,CAAC,EAAG,CAAE,CACxCN,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEuC,IAAI,CAAE,WAAY,CAAE,CACjDhD,SAAS,CAAC,kEAAkE,CAAAF,QAAA,cAE5ExC,KAAA,QAAK0C,SAAS,CAAC,0BAA0B,CAAAF,QAAA,eAEvCxC,KAAA,CAACvB,MAAM,CAACqE,GAAG,EACTwC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEI,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCP,OAAO,CAAE,CAAEG,OAAO,CAAE,CAAC,CAAEI,CAAC,CAAE,CAAE,CAAE,CAC9BzC,UAAU,CAAE,CAAE0C,KAAK,CAAE,GAAI,CAAE,CAC3BlD,SAAS,CAAC,eAAe,CAAAF,QAAA,eAEzB1C,IAAA,QAAK4C,SAAS,CAAC,sEAAsE,CAAAF,QAAA,cACnF1C,IAAA,CAACb,mBAAmB,EAACyD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtD,CAAC,cACN5C,IAAA,UACEsD,IAAI,CAAC,MAAM,CACXK,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAE3C,WAAY,CACnB4C,QAAQ,CAAGrC,CAAC,EAAKN,cAAc,CAACM,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE,CAChDG,SAAS,CAAGvC,CAAC,EAAKA,CAAC,CAACwC,GAAG,GAAK,OAAO,EAAIzC,YAAY,CAACC,CAAC,CAAE,CACvDoB,SAAS,CAAC,gPAAgP,CAC3P,CAAC,EACQ,CAAC,cAGb5C,IAAA,QAAK4C,SAAS,CAAC,WAAW,CAAAF,QAAA,CACvBP,UAAU,CAACqB,GAAG,CAAC,CAACC,IAAI,CAAEsC,KAAK,gBAC1B/F,IAAA,CAACrB,MAAM,CAACqE,GAAG,EAETwC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEI,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCP,OAAO,CAAE,CAAEG,OAAO,CAAE,CAAC,CAAEI,CAAC,CAAE,CAAE,CAAE,CAC9BzC,UAAU,CAAE,CAAE0C,KAAK,CAAE,GAAG,EAAIC,KAAK,CAAG,CAAC,CAAE,CAAE,CAAArD,QAAA,cAEzCxC,KAAA,CAACrB,UAAU,CAACsF,MAAM,EAChBxB,EAAE,CAAElE,IAAK,CACTsE,EAAE,CAAEU,IAAI,CAAC7B,IAAK,CACdgB,SAAS,CAAExC,UAAU,CACnBmC,QAAQ,CAACkB,IAAI,CAAC7B,IAAI,CAAC,CACf,iFAAiF,CACjF,oEAAoE,CACxE,qFACF,CAAE,CAAAc,QAAA,eAEF1C,IAAA,CAACyD,IAAI,CAACpB,IAAI,EAACO,SAAS,CAAExC,UAAU,CAC9BmC,QAAQ,CAACkB,IAAI,CAAC7B,IAAI,CAAC,CAAG,YAAY,CAAG,iDAAiD,CACtF,SACF,CAAE,CAAE,CAAC,cACL5B,IAAA,SAAM4C,SAAS,CAAC,uBAAuB,CAAAF,QAAA,CAAEe,IAAI,CAACrB,IAAI,CAAO,CAAC,EACzC,CAAC,EApBfqB,IAAI,CAACrB,IAqBA,CACb,CAAC,CACC,CAAC,CAGL,CAACf,eAAe,eACfnB,KAAA,CAACvB,MAAM,CAACqE,GAAG,EACTwC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAE/B,CAAC,CAAE,EAAG,CAAE,CAC/B4B,OAAO,CAAE,CAAEG,OAAO,CAAE,CAAC,CAAE/B,CAAC,CAAE,CAAE,CAAE,CAC9BN,UAAU,CAAE,CAAE0C,KAAK,CAAE,GAAI,CAAE,CAC3BlD,SAAS,CAAC,qBAAqB,CAAAF,QAAA,eAE/B1C,IAAA,CAACvB,IAAI,EAACsE,EAAE,CAAC,QAAQ,CAACH,SAAS,CAAC,QAAQ,CAAAF,QAAA,cAClC1C,IAAA,CAACnB,UAAU,CAACsF,MAAM,EAChBxB,EAAE,CAAC,QAAQ,CACXC,SAAS,CAAC,wJAAwJ,CAAAF,QAAA,CACnK,SAED,CAAmB,CAAC,CAChB,CAAC,cACP1C,IAAA,CAACvB,IAAI,EAACsE,EAAE,CAAC,WAAW,CAACH,SAAS,CAAC,QAAQ,CAAAF,QAAA,cACrC1C,IAAA,CAACnB,UAAU,CAACsF,MAAM,EAChBxB,EAAE,CAAC,QAAQ,CACXC,SAAS,CAAC,8MAA8M,CAAAF,QAAA,CACzN,SAED,CAAmB,CAAC,CAChB,CAAC,EACG,CACb,EACE,CAAC,CACI,CAAC,CACG,CAAC,EACnB,CAAC,EACJ,CACS,CAAC,cAGb1C,IAAA,QAAK4C,SAAS,CAAC,MAAM,CAAM,CAAC,EAC5B,CAAC,CAEP,CAAC,CAED,cAAe,CAAA9B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}