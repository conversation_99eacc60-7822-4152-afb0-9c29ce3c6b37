{"ast": null, "code": "import { useSyncExternalStoreWithSelector as a } from \"use-sync-external-store/with-selector\";\nimport { useEvent as t } from './hooks/use-event.js';\nimport { shallowEqual as o } from './machine.js';\nfunction S(e, n) {\n  let r = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : o;\n  return a(t(i => e.subscribe(s, i)), t(() => e.state), t(() => e.state), t(n), r);\n}\nfunction s(e) {\n  return e;\n}\nexport { S as useSlice };", "map": {"version": 3, "names": ["useSyncExternalStoreWithSelector", "a", "useEvent", "t", "shallowEqual", "o", "S", "e", "n", "r", "arguments", "length", "undefined", "i", "subscribe", "s", "state", "useSlice"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/react-glue.js"], "sourcesContent": ["import{useSyncExternalStoreWithSelector as a}from\"use-sync-external-store/with-selector\";import{useEvent as t}from'./hooks/use-event.js';import{shallowEqual as o}from'./machine.js';function S(e,n,r=o){return a(t(i=>e.subscribe(s,i)),t(()=>e.state),t(()=>e.state),t(n),r)}function s(e){return e}export{S as useSlice};\n"], "mappings": "AAAA,SAAOA,gCAAgC,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,cAAc;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAK;EAAA,IAAJC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAACL,CAAC;EAAE,OAAOJ,CAAC,CAACE,CAAC,CAACU,CAAC,IAAEN,CAAC,CAACO,SAAS,CAACC,CAAC,EAACF,CAAC,CAAC,CAAC,EAACV,CAAC,CAAC,MAAII,CAAC,CAACS,KAAK,CAAC,EAACb,CAAC,CAAC,MAAII,CAAC,CAACS,KAAK,CAAC,EAACb,CAAC,CAACK,CAAC,CAAC,EAACC,CAAC,CAAC;AAAA;AAAC,SAASM,CAACA,CAACR,CAAC,EAAC;EAAC,OAAOA,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}