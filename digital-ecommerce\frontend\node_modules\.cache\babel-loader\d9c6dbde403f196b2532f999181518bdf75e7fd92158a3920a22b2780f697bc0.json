{"ast": null, "code": "import React,{useState,useMemo}from'react';import{motion,AnimatePresence}from'framer-motion';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,MagnifyingGlassIcon,FunnelIcon,Squares2X2Icon,ListBulletIcon}from'@heroicons/react/24/outline';import{useAdmin}from'../contexts/AdminContext';import{useProducts}from'../contexts/ProductContext';import AdminLayout from'../components/AdminLayout';import AddProductModal from'../components/AddProductModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminProductsPage=()=>{const{hasPermission}=useAdmin();const{products,categories,addProduct}=useProducts();const[viewMode,setViewMode]=useState('grid');const[showAddProductModal,setShowAddProductModal]=useState(false);const[searchQuery,setSearchQuery]=useState('');const[selectedCategory,setSelectedCategory]=useState('all');const[selectedType,setSelectedType]=useState('all');const[sortBy,setSortBy]=useState('name');const[showFilters,setShowFilters]=useState(false);const[selectedProducts,setSelectedProducts]=useState([]);const filteredProducts=useMemo(()=>{let filtered=products.filter(product=>{var _product$description;const matchesSearch=product.name.toLowerCase().includes(searchQuery.toLowerCase())||((_product$description=product.description)===null||_product$description===void 0?void 0:_product$description.toLowerCase().includes(searchQuery.toLowerCase()));const matchesCategory=selectedCategory==='all'||product.category===selectedCategory;const matchesType=selectedType==='all'||product.type===selectedType;return matchesSearch&&matchesCategory&&matchesType;});// Sort products\nfiltered.sort((a,b)=>{switch(sortBy){case'name':return a.name.localeCompare(b.name);case'price':return a.price-b.price;case'stock':return(b.stockCount||0)-(a.stockCount||0);case'category':return a.category.localeCompare(b.category);default:return 0;}});return filtered;},[products,searchQuery,selectedCategory,selectedType,sortBy]);const handleSelectProduct=productId=>{setSelectedProducts(prev=>prev.includes(productId)?prev.filter(id=>id!==productId):[...prev,productId]);};const handleSelectAll=()=>{if(selectedProducts.length===filteredProducts.length){setSelectedProducts([]);}else{setSelectedProducts(filteredProducts.map(p=>p.id));}};const ProductCard=_ref=>{let{product}=_ref;return/*#__PURE__*/_jsxs(motion.div,{layout:true,initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:0.9},className:\"p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white \".concat(selectedProducts.includes(product.id)?'ring-2 ring-light-orange-500':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.image,alt:product.name,className:\"w-full h-48 object-cover rounded-lg\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-2 left-2\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedProducts.includes(product.id),onChange:()=>handleSelectProduct(product.id),className:\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-2 right-2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"px-2 py-1 text-xs font-medium rounded-full \".concat(product.inStock?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:product.inStock?'In Stock':'Out of Stock'})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold truncate text-gray-900\",children:product.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1 text-gray-600\",children:product.category}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mt-3\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg font-bold text-light-orange-600\",children:[\"$\",product.price]}),product.stockCount&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[\"Stock: \",product.stockCount]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"p-2 rounded-lg transition-colors hover:bg-gray-100\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"w-4 h-4 text-gray-500\"})}),hasPermission('products')&&/*#__PURE__*/_jsx(\"button\",{className:\"p-2 rounded-lg transition-colors hover:bg-gray-100\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4 text-blue-500\"})}),hasPermission('products')&&/*#__PURE__*/_jsx(\"button\",{className:\"p-2 rounded-lg transition-colors hover:bg-gray-100\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"w-4 h-4 text-red-500\"})})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs px-2 py-1 rounded-full \".concat(product.type==='digital'?'bg-blue-100 text-blue-800':'bg-gray-100 text-gray-800'),children:product.type})]})]});};return/*#__PURE__*/_jsxs(AdminLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:\"Products\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Manage your product catalog\"})]}),hasPermission('products')&&/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>setShowAddProductModal(true),className:\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Add Product\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 rounded-xl shadow-lg bg-white\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex-1 max-w-md\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search products...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),className:\"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowFilters(!showFilters),className:\"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100\",children:[/*#__PURE__*/_jsx(FunnelIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Filters\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('grid'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='grid'?'bg-white shadow-sm':'hover:bg-gray-200'),children:/*#__PURE__*/_jsx(Squares2X2Icon,{className:\"w-4 h-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('list'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='list'?'bg-white shadow-sm':'hover:bg-gray-200'),children:/*#__PURE__*/_jsx(ListBulletIcon,{className:\"w-4 h-4\"})})]})]})]}),/*#__PURE__*/_jsx(AnimatePresence,{children:showFilters&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:'auto'},exit:{opacity:0,height:0},className:\"mt-4 pt-4 border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"select\",{value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),className:\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Categories\"}),categories.map(category=>/*#__PURE__*/_jsx(\"option\",{value:category.id,children:category.name},category.id))]}),/*#__PURE__*/_jsxs(\"select\",{value:selectedType,onChange:e=>setSelectedType(e.target.value),className:\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Types\"}),/*#__PURE__*/_jsx(\"option\",{value:\"physical\",children:\"Physical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"digital\",children:\"Digital\"})]}),/*#__PURE__*/_jsxs(\"select\",{value:sortBy,onChange:e=>setSortBy(e.target.value),className:\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"name\",children:\"Sort by Name\"}),/*#__PURE__*/_jsx(\"option\",{value:\"price\",children:\"Sort by Price\"}),/*#__PURE__*/_jsx(\"option\",{value:\"stock\",children:\"Sort by Stock\"}),/*#__PURE__*/_jsx(\"option\",{value:\"category\",children:\"Sort by Category\"})]})]})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[\"Showing \",filteredProducts.length,\" of \",products.length,\" products\"]}),selectedProducts.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600\",children:[selectedProducts.length,\" selected\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors\",children:\"Delete Selected\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",children:/*#__PURE__*/_jsx(AnimatePresence,{children:filteredProducts.map(product=>/*#__PURE__*/_jsx(ProductCard,{product:product},product.id))})})]}),/*#__PURE__*/_jsx(AddProductModal,{isOpen:showAddProductModal,onClose:()=>setShowAddProductModal(false),onSubmit:async productData=>{const result=await addProduct(productData);if(result.success){setShowAddProductModal(false);// Show success notification\nconsole.log('Product added successfully:',result.product);}else{// Show error notification\nconsole.error('Failed to add product:',result.error);}}})]});};export default AdminProductsPage;", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "useAdmin", "useProducts", "AdminLayout", "AddProductModal", "jsx", "_jsx", "jsxs", "_jsxs", "AdminProductsPage", "hasPermission", "products", "categories", "addProduct", "viewMode", "setViewMode", "showAddProductModal", "setShowAddProductModal", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedType", "setSelectedType", "sortBy", "setSortBy", "showFilters", "setShowFilters", "selectedProducts", "setSelectedProducts", "filteredProducts", "filtered", "filter", "product", "_product$description", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesType", "type", "sort", "a", "b", "localeCompare", "price", "stockCount", "handleSelectProduct", "productId", "prev", "id", "handleSelectAll", "length", "map", "p", "ProductCard", "_ref", "div", "layout", "initial", "opacity", "scale", "animate", "exit", "className", "concat", "children", "src", "image", "alt", "checked", "onChange", "inStock", "button", "whileHover", "whileTap", "onClick", "placeholder", "value", "e", "target", "height", "isOpen", "onClose", "onSubmit", "productData", "result", "success", "console", "log", "error"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\n\nconst AdminProductsPage = () => {\n  const { hasPermission } = useAdmin();\n  const { products, categories, addProduct } = useProducts();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           product.description?.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      \n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n\n    return filtered;\n  }, [products, searchQuery, selectedCategory, selectedType, sortBy]);\n\n  const handleSelectProduct = (productId) => {\n    setSelectedProducts(prev => \n      prev.includes(productId) \n        ? prev.filter(id => id !== productId)\n        : [...prev, productId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n\n  const ProductCard = ({ product }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className={`p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white ${\n        selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''\n      }`}\n    >\n      <div className=\"relative\">\n        <img\n          src={product.image}\n          alt={product.name}\n          className=\"w-full h-48 object-cover rounded-lg\"\n        />\n        <div className=\"absolute top-2 left-2\">\n          <input\n            type=\"checkbox\"\n            checked={selectedProducts.includes(product.id)}\n            onChange={() => handleSelectProduct(product.id)}\n            className=\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n          />\n        </div>\n        <div className=\"absolute top-2 right-2\">\n          <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n            product.inStock \n              ? 'bg-green-100 text-green-800'\n              : 'bg-red-100 text-red-800'\n          }`}>\n            {product.inStock ? 'In Stock' : 'Out of Stock'}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"mt-4\">\n        <h3 className=\"font-semibold truncate text-gray-900\">\n          {product.name}\n        </h3>\n        <p className=\"text-sm mt-1 text-gray-600\">\n          {product.category}\n        </p>\n        <div className=\"flex items-center justify-between mt-3\">\n          <span className=\"text-lg font-bold text-light-orange-600\">\n            ${product.price}\n          </span>\n          {product.stockCount && (\n            <span className=\"text-sm text-gray-500\">\n              Stock: {product.stockCount}\n            </span>\n          )}\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <button className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\">\n            <EyeIcon className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          {hasPermission('products') && (\n            <button className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\">\n              <PencilIcon className=\"w-4 h-4 text-blue-500\" />\n            </button>\n          )}\n          {hasPermission('products') && (\n            <button className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\">\n              <TrashIcon className=\"w-4 h-4 text-red-500\" />\n            </button>\n          )}\n        </div>\n        <span className={`text-xs px-2 py-1 rounded-full ${\n          product.type === 'digital' \n            ? 'bg-blue-100 text-blue-800'\n            : 'bg-gray-100 text-gray-800'\n        }`}>\n          {product.type}\n        </span>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Products\n            </h1>\n            <p className=\"mt-2 text-gray-600\">\n              Manage your product catalog\n            </p>\n          </div>\n          {hasPermission('products') && (\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowAddProductModal(true)}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n            >\n              <PlusIcon className=\"w-5 h-5\" />\n              <span>Add Product</span>\n            </motion.button>\n          )}\n        </div>\n\n        {/* Toolbar */}\n        <div className=\"p-6 rounded-xl shadow-lg bg-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n            {/* Search */}\n            <div className=\"relative flex-1 max-w-md\">\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500\"\n              />\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Filters */}\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100\"\n              >\n                <FunnelIcon className=\"w-5 h-5\" />\n                <span>Filters</span>\n              </button>\n\n              {/* View Mode */}\n              <div className=\"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'grid' \n                      ? 'bg-white shadow-sm' \n                      : 'hover:bg-gray-200'\n                  }`}\n                >\n                  <Squares2X2Icon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'list' \n                      ? 'bg-white shadow-sm' \n                      : 'hover:bg-gray-200'\n                  }`}\n                >\n                  <ListBulletIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters Panel */}\n          <AnimatePresence>\n            {showFilters && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-4 pt-4 border-t border-gray-200\"\n              >\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"all\">All Categories</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n\n                  <select\n                    value={selectedType}\n                    onChange={(e) => setSelectedType(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"all\">All Types</option>\n                    <option value=\"physical\">Physical</option>\n                    <option value=\"digital\">Digital</option>\n                  </select>\n\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"name\">Sort by Name</option>\n                    <option value=\"price\">Sort by Price</option>\n                    <option value=\"stock\">Sort by Stock</option>\n                    <option value=\"category\">Sort by Category</option>\n                  </select>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Results Info */}\n        <div className=\"flex items-center justify-between\">\n          <p className=\"text-sm text-gray-600\">\n            Showing {filteredProducts.length} of {products.length} products\n          </p>\n          {selectedProducts.length > 0 && (\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">\n                {selectedProducts.length} selected\n              </span>\n              <button className=\"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors\">\n                Delete Selected\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Products Display */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          <AnimatePresence>\n            {filteredProducts.map(product => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Add Product Modal */}\n      <AddProductModal\n        isOpen={showAddProductModal}\n        onClose={() => setShowAddProductModal(false)}\n        onSubmit={async (productData) => {\n          const result = await addProduct(productData);\n          if (result.success) {\n            setShowAddProductModal(false);\n            // Show success notification\n            console.log('Product added successfully:', result.product);\n          } else {\n            // Show error notification\n            console.error('Failed to add product:', result.error);\n          }\n        }}\n      />\n    </AdminLayout>\n  );\n};\n\nexport default AdminProductsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,OAAO,KAAQ,OAAO,CAChD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,mBAAmB,CACnBC,UAAU,CACVC,cAAc,CACdC,cAAc,KACT,6BAA6B,CACpC,OAASC,QAAQ,KAAQ,0BAA0B,CACnD,OAASC,WAAW,KAAQ,4BAA4B,CACxD,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,aAAc,CAAC,CAAGT,QAAQ,CAAC,CAAC,CACpC,KAAM,CAAEU,QAAQ,CAAEC,UAAU,CAAEC,UAAW,CAAC,CAAGX,WAAW,CAAC,CAAC,CAC1D,KAAM,CAACY,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAC,MAAM,CAAC,CAChD,KAAM,CAAC2B,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC6B,WAAW,CAAEC,cAAc,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC+B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACiC,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACmC,MAAM,CAAEC,SAAS,CAAC,CAAGpC,QAAQ,CAAC,MAAM,CAAC,CAC5C,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACuC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAAAyC,gBAAgB,CAAGxC,OAAO,CAAC,IAAM,CACrC,GAAI,CAAAyC,QAAQ,CAAGpB,QAAQ,CAACqB,MAAM,CAACC,OAAO,EAAI,KAAAC,oBAAA,CACxC,KAAM,CAAAC,aAAa,CAAGF,OAAO,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,WAAW,CAACmB,WAAW,CAAC,CAAC,CAAC,IAAAH,oBAAA,CAC/DD,OAAO,CAACM,WAAW,UAAAL,oBAAA,iBAAnBA,oBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,WAAW,CAACmB,WAAW,CAAC,CAAC,CAAC,EAC3F,KAAM,CAAAG,eAAe,CAAGpB,gBAAgB,GAAK,KAAK,EAAIa,OAAO,CAACQ,QAAQ,GAAKrB,gBAAgB,CAC3F,KAAM,CAAAsB,WAAW,CAAGpB,YAAY,GAAK,KAAK,EAAIW,OAAO,CAACU,IAAI,GAAKrB,YAAY,CAE3E,MAAO,CAAAa,aAAa,EAAIK,eAAe,EAAIE,WAAW,CACxD,CAAC,CAAC,CAEF;AACAX,QAAQ,CAACa,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACtB,OAAQtB,MAAM,EACZ,IAAK,MAAM,CACT,MAAO,CAAAqB,CAAC,CAACT,IAAI,CAACW,aAAa,CAACD,CAAC,CAACV,IAAI,CAAC,CACrC,IAAK,OAAO,CACV,MAAO,CAAAS,CAAC,CAACG,KAAK,CAAGF,CAAC,CAACE,KAAK,CAC1B,IAAK,OAAO,CACV,MAAO,CAACF,CAAC,CAACG,UAAU,EAAI,CAAC,GAAKJ,CAAC,CAACI,UAAU,EAAI,CAAC,CAAC,CAClD,IAAK,UAAU,CACb,MAAO,CAAAJ,CAAC,CAACJ,QAAQ,CAACM,aAAa,CAACD,CAAC,CAACL,QAAQ,CAAC,CAC7C,QACE,MAAO,EAAC,CACZ,CACF,CAAC,CAAC,CAEF,MAAO,CAAAV,QAAQ,CACjB,CAAC,CAAE,CAACpB,QAAQ,CAAEO,WAAW,CAAEE,gBAAgB,CAAEE,YAAY,CAAEE,MAAM,CAAC,CAAC,CAEnE,KAAM,CAAA0B,mBAAmB,CAAIC,SAAS,EAAK,CACzCtB,mBAAmB,CAACuB,IAAI,EACtBA,IAAI,CAACd,QAAQ,CAACa,SAAS,CAAC,CACpBC,IAAI,CAACpB,MAAM,CAACqB,EAAE,EAAIA,EAAE,GAAKF,SAAS,CAAC,CACnC,CAAC,GAAGC,IAAI,CAAED,SAAS,CACzB,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI1B,gBAAgB,CAAC2B,MAAM,GAAKzB,gBAAgB,CAACyB,MAAM,CAAE,CACvD1B,mBAAmB,CAAC,EAAE,CAAC,CACzB,CAAC,IAAM,CACLA,mBAAmB,CAACC,gBAAgB,CAAC0B,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACJ,EAAE,CAAC,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAK,WAAW,CAAGC,IAAA,MAAC,CAAE1B,OAAQ,CAAC,CAAA0B,IAAA,oBAC9BnD,KAAA,CAACjB,MAAM,CAACqE,GAAG,EACTC,MAAM,MACNC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,GAAI,CAAE,CACpCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAE,CAAE,CAClCE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,KAAK,CAAE,GAAI,CAAE,CACjCG,SAAS,kFAAAC,MAAA,CACPxC,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAC,CAAG,8BAA8B,CAAG,EAAE,CAC1E,CAAAgB,QAAA,eAEH7D,KAAA,QAAK2D,SAAS,CAAC,UAAU,CAAAE,QAAA,eACvB/D,IAAA,QACEgE,GAAG,CAAErC,OAAO,CAACsC,KAAM,CACnBC,GAAG,CAAEvC,OAAO,CAACG,IAAK,CAClB+B,SAAS,CAAC,qCAAqC,CAChD,CAAC,cACF7D,IAAA,QAAK6D,SAAS,CAAC,uBAAuB,CAAAE,QAAA,cACpC/D,IAAA,UACEqC,IAAI,CAAC,UAAU,CACf8B,OAAO,CAAE7C,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAE,CAC/CqB,QAAQ,CAAEA,CAAA,GAAMxB,mBAAmB,CAACjB,OAAO,CAACoB,EAAE,CAAE,CAChDc,SAAS,CAAC,4FAA4F,CACvG,CAAC,CACC,CAAC,cACN7D,IAAA,QAAK6D,SAAS,CAAC,wBAAwB,CAAAE,QAAA,cACrC/D,IAAA,SAAM6D,SAAS,+CAAAC,MAAA,CACbnC,OAAO,CAAC0C,OAAO,CACX,6BAA6B,CAC7B,yBAAyB,CAC5B,CAAAN,QAAA,CACApC,OAAO,CAAC0C,OAAO,CAAG,UAAU,CAAG,cAAc,CAC1C,CAAC,CACJ,CAAC,EACH,CAAC,cAENnE,KAAA,QAAK2D,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnB/D,IAAA,OAAI6D,SAAS,CAAC,sCAAsC,CAAAE,QAAA,CACjDpC,OAAO,CAACG,IAAI,CACX,CAAC,cACL9B,IAAA,MAAG6D,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CACtCpC,OAAO,CAACQ,QAAQ,CAChB,CAAC,cACJjC,KAAA,QAAK2D,SAAS,CAAC,wCAAwC,CAAAE,QAAA,eACrD7D,KAAA,SAAM2D,SAAS,CAAC,yCAAyC,CAAAE,QAAA,EAAC,GACvD,CAACpC,OAAO,CAACe,KAAK,EACX,CAAC,CACNf,OAAO,CAACgB,UAAU,eACjBzC,KAAA,SAAM2D,SAAS,CAAC,uBAAuB,CAAAE,QAAA,EAAC,SAC/B,CAACpC,OAAO,CAACgB,UAAU,EACtB,CACP,EACE,CAAC,EACH,CAAC,cAENzC,KAAA,QAAK2D,SAAS,CAAC,sEAAsE,CAAAE,QAAA,eACnF7D,KAAA,QAAK2D,SAAS,CAAC,gBAAgB,CAAAE,QAAA,eAC7B/D,IAAA,WAAQ6D,SAAS,CAAC,oDAAoD,CAAAE,QAAA,cACpE/D,IAAA,CAACV,OAAO,EAACuE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvC,CAAC,CACRzD,aAAa,CAAC,UAAU,CAAC,eACxBJ,IAAA,WAAQ6D,SAAS,CAAC,oDAAoD,CAAAE,QAAA,cACpE/D,IAAA,CAACZ,UAAU,EAACyE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC1C,CACT,CACAzD,aAAa,CAAC,UAAU,CAAC,eACxBJ,IAAA,WAAQ6D,SAAS,CAAC,oDAAoD,CAAAE,QAAA,cACpE/D,IAAA,CAACX,SAAS,EAACwE,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACxC,CACT,EACE,CAAC,cACN7D,IAAA,SAAM6D,SAAS,mCAAAC,MAAA,CACbnC,OAAO,CAACU,IAAI,GAAK,SAAS,CACtB,2BAA2B,CAC3B,2BAA2B,CAC9B,CAAA0B,QAAA,CACApC,OAAO,CAACU,IAAI,CACT,CAAC,EACJ,CAAC,EACI,CAAC,EACd,CAED,mBACEnC,KAAA,CAACL,WAAW,EAAAkE,QAAA,eACV7D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAE,QAAA,eAExB7D,KAAA,QAAK2D,SAAS,CAAC,mCAAmC,CAAAE,QAAA,eAChD7D,KAAA,QAAA6D,QAAA,eACE/D,IAAA,OAAI6D,SAAS,CAAC,kCAAkC,CAAAE,QAAA,CAAC,UAEjD,CAAI,CAAC,cACL/D,IAAA,MAAG6D,SAAS,CAAC,oBAAoB,CAAAE,QAAA,CAAC,6BAElC,CAAG,CAAC,EACD,CAAC,CACL3D,aAAa,CAAC,UAAU,CAAC,eACxBF,KAAA,CAACjB,MAAM,CAACqF,MAAM,EACZC,UAAU,CAAE,CAAEb,KAAK,CAAE,IAAK,CAAE,CAC5Bc,QAAQ,CAAE,CAAEd,KAAK,CAAE,IAAK,CAAE,CAC1Be,OAAO,CAAEA,CAAA,GAAM9D,sBAAsB,CAAC,IAAI,CAAE,CAC5CkD,SAAS,CAAC,6HAA6H,CAAAE,QAAA,eAEvI/D,IAAA,CAACb,QAAQ,EAAC0E,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC7D,IAAA,SAAA+D,QAAA,CAAM,aAAW,CAAM,CAAC,EACX,CAChB,EACE,CAAC,cAGN7D,KAAA,QAAK2D,SAAS,CAAC,mCAAmC,CAAAE,QAAA,eAChD7D,KAAA,QAAK2D,SAAS,CAAC,qFAAqF,CAAAE,QAAA,eAElG7D,KAAA,QAAK2D,SAAS,CAAC,0BAA0B,CAAAE,QAAA,eACvC/D,IAAA,CAACT,mBAAmB,EAACsE,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC5G7D,IAAA,UACEqC,IAAI,CAAC,MAAM,CACXqC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAE/D,WAAY,CACnBwD,QAAQ,CAAGQ,CAAC,EAAK/D,cAAc,CAAC+D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE,CAChDd,SAAS,CAAC,gKAAgK,CAC3K,CAAC,EACC,CAAC,cAGN3D,KAAA,QAAK2D,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAE1C7D,KAAA,WACEuE,OAAO,CAAEA,CAAA,GAAMpD,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CyC,SAAS,CAAC,sFAAsF,CAAAE,QAAA,eAEhG/D,IAAA,CAACR,UAAU,EAACqE,SAAS,CAAC,SAAS,CAAE,CAAC,cAClC7D,IAAA,SAAA+D,QAAA,CAAM,SAAO,CAAM,CAAC,EACd,CAAC,cAGT7D,KAAA,QAAK2D,SAAS,CAAC,wDAAwD,CAAAE,QAAA,eACrE/D,IAAA,WACEyE,OAAO,CAAEA,CAAA,GAAMhE,WAAW,CAAC,MAAM,CAAE,CACnCoD,SAAS,qCAAAC,MAAA,CACPtD,QAAQ,GAAK,MAAM,CACf,oBAAoB,CACpB,mBAAmB,CACtB,CAAAuD,QAAA,cAEH/D,IAAA,CAACP,cAAc,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,cACT7D,IAAA,WACEyE,OAAO,CAAEA,CAAA,GAAMhE,WAAW,CAAC,MAAM,CAAE,CACnCoD,SAAS,qCAAAC,MAAA,CACPtD,QAAQ,GAAK,MAAM,CACf,oBAAoB,CACpB,mBAAmB,CACtB,CAAAuD,QAAA,cAEH/D,IAAA,CAACN,cAAc,EAACmE,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,cAGN7D,IAAA,CAACd,eAAe,EAAA6E,QAAA,CACb3C,WAAW,eACVpB,IAAA,CAACf,MAAM,CAACqE,GAAG,EACTE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEqB,MAAM,CAAE,CAAE,CAAE,CACnCnB,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEqB,MAAM,CAAE,MAAO,CAAE,CACxClB,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEqB,MAAM,CAAE,CAAE,CAAE,CAChCjB,SAAS,CAAC,oCAAoC,CAAAE,QAAA,cAE9C7D,KAAA,QAAK2D,SAAS,CAAC,uCAAuC,CAAAE,QAAA,eACpD7D,KAAA,WACEyE,KAAK,CAAE7D,gBAAiB,CACxBsD,QAAQ,CAAGQ,CAAC,EAAK7D,mBAAmB,CAAC6D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE,CACrDd,SAAS,CAAC,oEAAoE,CAAAE,QAAA,eAE9E/D,IAAA,WAAQ2E,KAAK,CAAC,KAAK,CAAAZ,QAAA,CAAC,gBAAc,CAAQ,CAAC,CAC1CzD,UAAU,CAAC4C,GAAG,CAACf,QAAQ,eACtBnC,IAAA,WAA0B2E,KAAK,CAAExC,QAAQ,CAACY,EAAG,CAAAgB,QAAA,CAC1C5B,QAAQ,CAACL,IAAI,EADHK,QAAQ,CAACY,EAEd,CACT,CAAC,EACI,CAAC,cAET7C,KAAA,WACEyE,KAAK,CAAE3D,YAAa,CACpBoD,QAAQ,CAAGQ,CAAC,EAAK3D,eAAe,CAAC2D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE,CACjDd,SAAS,CAAC,oEAAoE,CAAAE,QAAA,eAE9E/D,IAAA,WAAQ2E,KAAK,CAAC,KAAK,CAAAZ,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtC/D,IAAA,WAAQ2E,KAAK,CAAC,UAAU,CAAAZ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1C/D,IAAA,WAAQ2E,KAAK,CAAC,SAAS,CAAAZ,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,cAET7D,KAAA,WACEyE,KAAK,CAAEzD,MAAO,CACdkD,QAAQ,CAAGQ,CAAC,EAAKzD,SAAS,CAACyD,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE,CAC3Cd,SAAS,CAAC,oEAAoE,CAAAE,QAAA,eAE9E/D,IAAA,WAAQ2E,KAAK,CAAC,MAAM,CAAAZ,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC1C/D,IAAA,WAAQ2E,KAAK,CAAC,OAAO,CAAAZ,QAAA,CAAC,eAAa,CAAQ,CAAC,cAC5C/D,IAAA,WAAQ2E,KAAK,CAAC,OAAO,CAAAZ,QAAA,CAAC,eAAa,CAAQ,CAAC,cAC5C/D,IAAA,WAAQ2E,KAAK,CAAC,UAAU,CAAAZ,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EAC5C,CAAC,EACN,CAAC,CACI,CACb,CACc,CAAC,EACf,CAAC,cAGN7D,KAAA,QAAK2D,SAAS,CAAC,mCAAmC,CAAAE,QAAA,eAChD7D,KAAA,MAAG2D,SAAS,CAAC,uBAAuB,CAAAE,QAAA,EAAC,UAC3B,CAACvC,gBAAgB,CAACyB,MAAM,CAAC,MAAI,CAAC5C,QAAQ,CAAC4C,MAAM,CAAC,WACxD,EAAG,CAAC,CACH3B,gBAAgB,CAAC2B,MAAM,CAAG,CAAC,eAC1B/C,KAAA,QAAK2D,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAC1C7D,KAAA,SAAM2D,SAAS,CAAC,uBAAuB,CAAAE,QAAA,EACpCzC,gBAAgB,CAAC2B,MAAM,CAAC,WAC3B,EAAM,CAAC,cACPjD,IAAA,WAAQ6D,SAAS,CAAC,uFAAuF,CAAAE,QAAA,CAAC,iBAE1G,CAAQ,CAAC,EACN,CACN,EACE,CAAC,cAGN/D,IAAA,QAAK6D,SAAS,CAAC,qEAAqE,CAAAE,QAAA,cAClF/D,IAAA,CAACd,eAAe,EAAA6E,QAAA,CACbvC,gBAAgB,CAAC0B,GAAG,CAACvB,OAAO,eAC3B3B,IAAA,CAACoD,WAAW,EAAkBzB,OAAO,CAAEA,OAAQ,EAA7BA,OAAO,CAACoB,EAAuB,CAClD,CAAC,CACa,CAAC,CACf,CAAC,EACH,CAAC,cAGN/C,IAAA,CAACF,eAAe,EACdiF,MAAM,CAAErE,mBAAoB,CAC5BsE,OAAO,CAAEA,CAAA,GAAMrE,sBAAsB,CAAC,KAAK,CAAE,CAC7CsE,QAAQ,CAAE,KAAO,CAAAC,WAAW,EAAK,CAC/B,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA5E,UAAU,CAAC2E,WAAW,CAAC,CAC5C,GAAIC,MAAM,CAACC,OAAO,CAAE,CAClBzE,sBAAsB,CAAC,KAAK,CAAC,CAC7B;AACA0E,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAEH,MAAM,CAACxD,OAAO,CAAC,CAC5D,CAAC,IAAM,CACL;AACA0D,OAAO,CAACE,KAAK,CAAC,wBAAwB,CAAEJ,MAAM,CAACI,KAAK,CAAC,CACvD,CACF,CAAE,CACH,CAAC,EACS,CAAC,CAElB,CAAC,CAED,cAAe,CAAApF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}