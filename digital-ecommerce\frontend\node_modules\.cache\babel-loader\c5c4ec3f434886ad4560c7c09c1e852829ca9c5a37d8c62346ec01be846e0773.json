{"ast": null, "code": "import { useRef as $lPAwt$useRef, useCallback as $lPAwt$useCallback, useEffect as $lPAwt$useEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $03deb23ff14920c4$export$4eaf04e54aa8eed6() {\n  let globalListeners = (0, $lPAwt$useRef)(new Map());\n  let addGlobalListener = (0, $lPAwt$useCallback)((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = (options === null || options === void 0 ? void 0 : options.once) ? function () {\n      globalListeners.current.delete(listener);\n      listener(...arguments);\n    } : listener;\n    globalListeners.current.set(listener, {\n      type: type,\n      eventTarget: eventTarget,\n      fn: fn,\n      options: options\n    });\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = (0, $lPAwt$useCallback)((eventTarget, type, listener, options) => {\n    var _globalListeners_current_get;\n    let fn = ((_globalListeners_current_get = globalListeners.current.get(listener)) === null || _globalListeners_current_get === void 0 ? void 0 : _globalListeners_current_get.fn) || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = (0, $lPAwt$useCallback)(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n  (0, $lPAwt$useEffect)(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n  return {\n    addGlobalListener: addGlobalListener,\n    removeGlobalListener: removeGlobalListener,\n    removeAllGlobalListeners: removeAllGlobalListeners\n  };\n}\nexport { $03deb23ff14920c4$export$4eaf04e54aa8eed6 as useGlobalListeners };", "map": {"version": 3, "names": ["$03deb23ff14920c4$export$4eaf04e54aa8eed6", "globalListeners", "$lPAwt$useRef", "Map", "addGlobalListener", "$lPAwt$useCallback", "eventTarget", "type", "listener", "options", "fn", "once", "current", "delete", "arguments", "set", "addEventListener", "removeGlobalListener", "_globalListeners_current_get", "get", "removeEventListener", "removeAllGlobalListeners", "for<PERSON>ach", "value", "key", "$lPAwt$useEffect"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useGlobalListeners.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAuBO,SAASA,0CAAA;EACd,IAAIC,eAAA,GAAkB,IAAAC,aAAK,EAAE,IAAIC,GAAA;EACjC,IAAIC,iBAAA,GAAoB,IAAAC,kBAAU,EAAE,CAACC,WAAA,EAAaC,IAAA,EAAMC,QAAA,EAAUC,OAAA;IAChE;IACA,IAAIC,EAAA,GAAK,CAAAD,OAAA,aAAAA,OAAA,uBAAAA,OAAA,CAASE,IAAI,IAAG,YAAI;MAC3BV,eAAA,CAAgBW,OAAO,CAACC,MAAM,CAACL,QAAA;MAC/BA,QAAA,IAAAM,SAAY;IACd,IAAIN,QAAA;IACJP,eAAA,CAAgBW,OAAO,CAACG,GAAG,CAACP,QAAA,EAAU;YAACD,IAAA;mBAAMD,WAAA;UAAaI,EAAA;eAAID;IAAO;IACrEH,WAAA,CAAYU,gBAAgB,CAACT,IAAA,EAAMG,EAAA,EAAID,OAAA;EACzC,GAAG,EAAE;EACL,IAAIQ,oBAAA,GAAuB,IAAAZ,kBAAU,EAAE,CAACC,WAAA,EAAaC,IAAA,EAAMC,QAAA,EAAUC,OAAA;QAC1DS,4BAAA;IAAT,IAAIR,EAAA,GAAK,EAAAQ,4BAAA,GAAAjB,eAAA,CAAgBW,OAAO,CAACO,GAAG,CAACX,QAAA,eAA5BU,4BAAA,uBAAAA,4BAAA,CAAuCR,EAAE,KAAIF,QAAA;IACtDF,WAAA,CAAYc,mBAAmB,CAACb,IAAA,EAAMG,EAAA,EAAID,OAAA;IAC1CR,eAAA,CAAgBW,OAAO,CAACC,MAAM,CAACL,QAAA;EACjC,GAAG,EAAE;EACL,IAAIa,wBAAA,GAA2B,IAAAhB,kBAAU,EAAE;IACzCJ,eAAA,CAAgBW,OAAO,CAACU,OAAO,CAAC,CAACC,KAAA,EAAOC,GAAA;MACtCP,oBAAA,CAAqBM,KAAA,CAAMjB,WAAW,EAAEiB,KAAA,CAAMhB,IAAI,EAAEiB,GAAA,EAAKD,KAAA,CAAMd,OAAO;IACxE;EACF,GAAG,CAACQ,oBAAA,CAAqB;EAGzB,IAAAQ,gBAAQ,EAAE;IACR,OAAOJ,wBAAA;EACT,GAAG,CAACA,wBAAA,CAAyB;EAE7B,OAAO;uBAACjB,iBAAA;0BAAmBa,oBAAA;8BAAsBI;EAAwB;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}