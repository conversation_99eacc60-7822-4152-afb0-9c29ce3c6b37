{"ast": null, "code": "import { useCallback as $nrdL2$useCallback } from \"react\";\nimport { useEvent as $nrdL2$useEvent } from \"@react-aria/utils\";\n\n/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $7d0a636d7a4dcefd$export$2123ff2b87c81ca(props, ref) {\n  let {\n    onScroll: onScroll,\n    isDisabled: isDisabled\n  } = props;\n  let onScrollHandler = (0, $nrdL2$useCallback)(e => {\n    // If the ctrlKey is pressed, this is a zoom event, do nothing.\n    if (e.ctrlKey) return;\n    // stop scrolling the page\n    e.preventDefault();\n    e.stopPropagation();\n    if (onScroll) onScroll({\n      deltaX: e.deltaX,\n      deltaY: e.deltaY\n    });\n  }, [onScroll]);\n  (0, $nrdL2$useEvent)(ref, 'wheel', isDisabled ? undefined : onScrollHandler);\n}\nexport { $7d0a636d7a4dcefd$export$2123ff2b87c81ca as useScrollWheel };", "map": {"version": 3, "names": ["$7d0a636d7a4dcefd$export$2123ff2b87c81ca", "props", "ref", "onScroll", "isDisabled", "onScrollHandler", "$nrdL2$useCallback", "e", "ctrl<PERSON>ey", "preventDefault", "stopPropagation", "deltaX", "deltaY", "$nrdL2$useEvent", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useScrollWheel.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject, ScrollEvents} from '@react-types/shared';\nimport {useCallback} from 'react';\nimport {useEvent} from '@react-aria/utils';\n\nexport interface ScrollWheelProps extends ScrollEvents {\n  /** Whether the scroll listener should be disabled. */\n  isDisabled?: boolean\n}\n\n// scroll wheel needs to be added not passively so it's cancelable, small helper hook to remember that\nexport function useScrollWheel(props: ScrollWheelProps, ref: RefObject<HTMLElement | null>): void {\n  let {onScroll, isDisabled} = props;\n  let onScrollHandler = useCallback((e) => {\n    // If the ctrlKey is pressed, this is a zoom event, do nothing.\n    if (e.ctrlKey) {\n      return;\n    }\n\n    // stop scrolling the page\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (onScroll) {\n      onScroll({deltaX: e.deltaX, deltaY: e.deltaY});\n    }\n  }, [onScroll]);\n\n  useEvent(ref, 'wheel', isDisabled ? undefined : onScrollHandler);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAsBO,SAASA,yCAAeC,KAAuB,EAAEC,GAAkC;EACxF,IAAI;IAAAC,QAAA,EAACA,QAAQ;IAAAC,UAAA,EAAEA;EAAU,CAAC,GAAGH,KAAA;EAC7B,IAAII,eAAA,GAAkB,IAAAC,kBAAU,EAAGC,CAAA;IACjC;IACA,IAAIA,CAAA,CAAEC,OAAO,EACX;IAGF;IACAD,CAAA,CAAEE,cAAc;IAChBF,CAAA,CAAEG,eAAe;IAEjB,IAAIP,QAAA,EACFA,QAAA,CAAS;MAACQ,MAAA,EAAQJ,CAAA,CAAEI,MAAM;MAAEC,MAAA,EAAQL,CAAA,CAAEK;IAAM;EAEhD,GAAG,CAACT,QAAA,CAAS;EAEb,IAAAU,eAAO,EAAEX,GAAA,EAAK,SAASE,UAAA,GAAaU,SAAA,GAAYT,eAAA;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}