{"ast": null, "code": "import { useLayoutEffect as $6dfIe$useLayoutEffect, useEffectEvent as $6dfIe$useEffectEvent, isFocusable as $6dfIe$isFocusable, getOwnerWindow as $6dfIe$getOwnerWindow, focusWithoutScrolling as $6dfIe$focusWithoutScrolling } from \"@react-aria/utils\";\nimport { useRef as $6dfIe$useRef, useCallback as $6dfIe$useCallback } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $8a9cb279dc87e130$export$525bc4921d56d4a(nativeEvent) {\n  let event = nativeEvent;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => event.cancelBubble;\n  event.persist = () => {};\n  return event;\n}\nfunction $8a9cb279dc87e130$export$c2b7abe5d61ec696(event, target) {\n  Object.defineProperty(event, 'target', {\n    value: target\n  });\n  Object.defineProperty(event, 'currentTarget', {\n    value: target\n  });\n}\nfunction $8a9cb279dc87e130$export$715c682d09d639cc(onBlur) {\n  let stateRef = (0, $6dfIe$useRef)({\n    isFocused: false,\n    observer: null\n  });\n  // Clean up MutationObserver on unmount. See below.\n  (0, $6dfIe$useLayoutEffect)(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n  let dispatchBlur = (0, $6dfIe$useEffectEvent)(e => {\n    onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n  });\n  // This function is called during a React onFocus event.\n  return (0, $6dfIe$useCallback)(e => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (e.target instanceof HTMLButtonElement || e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || e.target instanceof HTMLSelectElement) {\n      stateRef.current.isFocused = true;\n      let target = e.target;\n      let onBlurHandler = e => {\n        stateRef.current.isFocused = false;\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = $8a9cb279dc87e130$export$525bc4921d56d4a(e);\n          dispatchBlur(event);\n        }\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n      target.addEventListener('focusout', onBlurHandler, {\n        once: true\n      });\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          var _stateRef_current_observer;\n          (_stateRef_current_observer = stateRef.current.observer) === null || _stateRef_current_observer === void 0 ? void 0 : _stateRef_current_observer.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {\n            relatedTarget: relatedTargetEl\n          }));\n          target.dispatchEvent(new FocusEvent('focusout', {\n            bubbles: true,\n            relatedTarget: relatedTargetEl\n          }));\n        }\n      });\n      stateRef.current.observer.observe(target, {\n        attributes: true,\n        attributeFilter: ['disabled']\n      });\n    }\n  }, [dispatchBlur]);\n}\nlet $8a9cb279dc87e130$export$fda7da73ab5d4c48 = false;\nfunction $8a9cb279dc87e130$export$cabe61c495ee3649(target) {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !(0, $6dfIe$isFocusable)(target)) target = target.parentElement;\n  let window = (0, $6dfIe$getOwnerWindow)(target);\n  let activeElement = window.document.activeElement;\n  if (!activeElement || activeElement === target) return;\n  $8a9cb279dc87e130$export$fda7da73ab5d4c48 = true;\n  let isRefocusing = false;\n  let onBlur = e => {\n    if (e.target === activeElement || isRefocusing) e.stopImmediatePropagation();\n  };\n  let onFocusOut = e => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        (0, $6dfIe$focusWithoutScrolling)(activeElement);\n        cleanup();\n      }\n    }\n  };\n  let onFocus = e => {\n    if (e.target === target || isRefocusing) e.stopImmediatePropagation();\n  };\n  let onFocusIn = e => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n      if (!isRefocusing) {\n        isRefocusing = true;\n        (0, $6dfIe$focusWithoutScrolling)(activeElement);\n        cleanup();\n      }\n    }\n  };\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    $8a9cb279dc87e130$export$fda7da73ab5d4c48 = false;\n    isRefocusing = false;\n  };\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\nexport { $8a9cb279dc87e130$export$525bc4921d56d4a as createSyntheticEvent, $8a9cb279dc87e130$export$c2b7abe5d61ec696 as setEventTarget, $8a9cb279dc87e130$export$715c682d09d639cc as useSyntheticBlurEvent, $8a9cb279dc87e130$export$fda7da73ab5d4c48 as ignoreFocusEvent, $8a9cb279dc87e130$export$cabe61c495ee3649 as preventFocus };", "map": {"version": 3, "names": ["$8a9cb279dc87e130$export$525bc4921d56d4a", "nativeEvent", "event", "isDefaultPrevented", "defaultPrevented", "isPropagationStopped", "cancelBubble", "persist", "$8a9cb279dc87e130$export$c2b7abe5d61ec696", "target", "Object", "defineProperty", "value", "$8a9cb279dc87e130$export$715c682d09d639cc", "onBlur", "stateRef", "$6dfIe$useRef", "isFocused", "observer", "$6dfIe$useLayoutEffect", "state", "current", "disconnect", "dispatchBlur", "$6dfIe$useEffectEvent", "e", "$6dfIe$useCallback", "HTMLButtonElement", "HTMLInputElement", "HTMLTextAreaElement", "HTMLSelectElement", "onBlurHandler", "disabled", "addEventListener", "once", "MutationObserver", "_stateRef_current_observer", "relatedTargetEl", "document", "activeElement", "dispatchEvent", "FocusEvent", "relatedTarget", "bubbles", "observe", "attributes", "attributeFilter", "$8a9cb279dc87e130$export$fda7da73ab5d4c48", "$8a9cb279dc87e130$export$cabe61c495ee3649", "$6dfIe$isFocusable", "parentElement", "window", "$6dfIe$getOwnerWindow", "isRefocusing", "stopImmediatePropagation", "onFocusOut", "$6dfIe$focusWithoutScrolling", "cleanup", "onFocus", "onFocusIn", "cancelAnimationFrame", "raf", "removeEventListener", "requestAnimationFrame"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAiBO,SAASA,yCAA+CC,WAAkB;EAC/E,IAAIC,KAAA,GAAQD,WAAA;EACZC,KAAA,CAAMD,WAAW,GAAGA,WAAA;EACpBC,KAAA,CAAMC,kBAAkB,GAAG,MAAMD,KAAA,CAAME,gBAAgB;EACvD;EACAF,KAAA,CAAMG,oBAAoB,GAAG,MAAMH,KAAC,CAAcI,YAAY;EAC9DJ,KAAA,CAAMK,OAAO,GAAG,OAAO;EACvB,OAAOL,KAAA;AACT;AAEO,SAASM,0CAAeN,KAAY,EAAEO,MAAe;EAC1DC,MAAA,CAAOC,cAAc,CAACT,KAAA,EAAO,UAAU;IAACU,KAAA,EAAOH;EAAM;EACrDC,MAAA,CAAOC,cAAc,CAACT,KAAA,EAAO,iBAAiB;IAACU,KAAA,EAAOH;EAAM;AAC9D;AAEO,SAASI,0CAAwDC,MAA4C;EAClH,IAAIC,QAAA,GAAW,IAAAC,aAAK,EAAE;IACpBC,SAAA,EAAW;IACXC,QAAA,EAAU;EACZ;EAEA;EAEA,IAAAC,sBAAc,EAAE;IACd,MAAMC,KAAA,GAAQL,QAAA,CAASM,OAAO;IAC9B,OAAO;MACL,IAAID,KAAA,CAAMF,QAAQ,EAAE;QAClBE,KAAA,CAAMF,QAAQ,CAACI,UAAU;QACzBF,KAAA,CAAMF,QAAQ,GAAG;MACnB;IACF;EACF,GAAG,EAAE;EAEL,IAAIK,YAAA,GAAe,IAAAC,qBAAa,EAAGC,CAAA;IACjCX,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAASW,CAAA;EACX;EAEA;EACA,OAAO,IAAAC,kBAAU,EAAGD,CAAA;IAClB;IACA;IACA;IACA;IACA,IACEA,CAAA,CAAEhB,MAAM,YAAYkB,iBAAA,IACpBF,CAAA,CAAEhB,MAAM,YAAYmB,gBAAA,IACpBH,CAAA,CAAEhB,MAAM,YAAYoB,mBAAA,IACpBJ,CAAA,CAAEhB,MAAM,YAAYqB,iBAAA,EACpB;MACAf,QAAA,CAASM,OAAO,CAACJ,SAAS,GAAG;MAE7B,IAAIR,MAAA,GAASgB,CAAA,CAAEhB,MAAM;MACrB,IAAIsB,aAAA,GAA4DN,CAAA;QAC9DV,QAAA,CAASM,OAAO,CAACJ,SAAS,GAAG;QAE7B,IAAIR,MAAA,CAAOuB,QAAQ,EAAE;UACnB;UACA,IAAI9B,KAAA,GAAQF,wCAAA,CAA8CyB,CAAA;UAC1DF,YAAA,CAAarB,KAAA;QACf;QAEA;QACA,IAAIa,QAAA,CAASM,OAAO,CAACH,QAAQ,EAAE;UAC7BH,QAAA,CAASM,OAAO,CAACH,QAAQ,CAACI,UAAU;UACpCP,QAAA,CAASM,OAAO,CAACH,QAAQ,GAAG;QAC9B;MACF;MAEAT,MAAA,CAAOwB,gBAAgB,CAAC,YAAYF,aAAA,EAAe;QAACG,IAAA,EAAM;MAAI;MAE9DnB,QAAA,CAASM,OAAO,CAACH,QAAQ,GAAG,IAAIiB,gBAAA,CAAiB;QAC/C,IAAIpB,QAAA,CAASM,OAAO,CAACJ,SAAS,IAAIR,MAAA,CAAOuB,QAAQ,EAAE;cACjDI,0BAAA;WAAAA,0BAAA,GAAArB,QAAA,CAASM,OAAO,CAACH,QAAQ,cAAzBkB,0BAAA,uBAAAA,0BAAA,CAA2Bd,UAAU;UACrC,IAAIe,eAAA,GAAkB5B,MAAA,KAAW6B,QAAA,CAASC,aAAa,GAAG,OAAOD,QAAA,CAASC,aAAa;UACvF9B,MAAA,CAAO+B,aAAa,CAAC,IAAIC,UAAA,CAAW,QAAQ;YAACC,aAAA,EAAeL;UAAe;UAC3E5B,MAAA,CAAO+B,aAAa,CAAC,IAAIC,UAAA,CAAW,YAAY;YAACE,OAAA,EAAS;YAAMD,aAAA,EAAeL;UAAe;QAChG;MACF;MAEAtB,QAAA,CAASM,OAAO,CAACH,QAAQ,CAAC0B,OAAO,CAACnC,MAAA,EAAQ;QAACoC,UAAA,EAAY;QAAMC,eAAA,EAAiB,CAAC;MAAW;IAC5F;EACF,GAAG,CAACvB,YAAA,CAAa;AACnB;AAEO,IAAIwB,yCAAA,GAAmB;AAOvB,SAASC,0CAAavC,MAA+B;EAC1D;EACA,OAAOA,MAAA,IAAU,CAAC,IAAAwC,kBAAU,EAAExC,MAAA,GAC5BA,MAAA,GAASA,MAAA,CAAOyC,aAAa;EAG/B,IAAIC,MAAA,GAAS,IAAAC,qBAAa,EAAE3C,MAAA;EAC5B,IAAI8B,aAAA,GAAgBY,MAAA,CAAOb,QAAQ,CAACC,aAAa;EACjD,IAAI,CAACA,aAAA,IAAiBA,aAAA,KAAkB9B,MAAA,EACtC;EAGFsC,yCAAA,GAAmB;EACnB,IAAIM,YAAA,GAAe;EACnB,IAAIvC,MAAA,GAAUW,CAAA;IACZ,IAAIA,CAAA,CAAEhB,MAAM,KAAK8B,aAAA,IAAiBc,YAAA,EAChC5B,CAAA,CAAE6B,wBAAwB;EAE9B;EAEA,IAAIC,UAAA,GAAc9B,CAAA;IAChB,IAAIA,CAAA,CAAEhB,MAAM,KAAK8B,aAAA,IAAiBc,YAAA,EAAc;MAC9C5B,CAAA,CAAE6B,wBAAwB;MAE1B;MACA;MACA,IAAI,CAAC7C,MAAA,IAAU,CAAC4C,YAAA,EAAc;QAC5BA,YAAA,GAAe;QACf,IAAAG,4BAAoB,EAAEjB,aAAA;QACtBkB,OAAA;MACF;IACF;EACF;EAEA,IAAIC,OAAA,GAAWjC,CAAA;IACb,IAAIA,CAAA,CAAEhB,MAAM,KAAKA,MAAA,IAAU4C,YAAA,EACzB5B,CAAA,CAAE6B,wBAAwB;EAE9B;EAEA,IAAIK,SAAA,GAAalC,CAAA;IACf,IAAIA,CAAA,CAAEhB,MAAM,KAAKA,MAAA,IAAU4C,YAAA,EAAc;MACvC5B,CAAA,CAAE6B,wBAAwB;MAE1B,IAAI,CAACD,YAAA,EAAc;QACjBA,YAAA,GAAe;QACf,IAAAG,4BAAoB,EAAEjB,aAAA;QACtBkB,OAAA;MACF;IACF;EACF;EAEAN,MAAA,CAAOlB,gBAAgB,CAAC,QAAQnB,MAAA,EAAQ;EACxCqC,MAAA,CAAOlB,gBAAgB,CAAC,YAAYsB,UAAA,EAAY;EAChDJ,MAAA,CAAOlB,gBAAgB,CAAC,WAAW0B,SAAA,EAAW;EAC9CR,MAAA,CAAOlB,gBAAgB,CAAC,SAASyB,OAAA,EAAS;EAE1C,IAAID,OAAA,GAAUA,CAAA;IACZG,oBAAA,CAAqBC,GAAA;IACrBV,MAAA,CAAOW,mBAAmB,CAAC,QAAQhD,MAAA,EAAQ;IAC3CqC,MAAA,CAAOW,mBAAmB,CAAC,YAAYP,UAAA,EAAY;IACnDJ,MAAA,CAAOW,mBAAmB,CAAC,WAAWH,SAAA,EAAW;IACjDR,MAAA,CAAOW,mBAAmB,CAAC,SAASJ,OAAA,EAAS;IAC7CX,yCAAA,GAAmB;IACnBM,YAAA,GAAe;EACjB;EAEA,IAAIQ,GAAA,GAAME,qBAAA,CAAsBN,OAAA;EAChC,OAAOA,OAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}