import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  TagIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAdmin } from '../contexts/AdminContext';
import { useProducts } from '../contexts/ProductContext';
import AdminLayout from '../components/AdminLayout';

const AdminCategoriesPage = () => {
  const { hasPermission } = useAdmin();
  const { categories, addCategory } = useProducts();
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    subcategories: []
  });

  const handleAddCategory = () => {
    setFormData({ name: '', description: '', icon: '', subcategories: [] });
    setEditingCategory(null);
    setShowAddModal(true);
  };

  const handleEditCategory = (category) => {
    setFormData({
      name: category.name,
      description: category.description,
      icon: category.icon,
      subcategories: category.subcategories || []
    });
    setEditingCategory(category);
    setShowAddModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const result = await addCategory(formData);
      if (result.success) {
        setShowAddModal(false);
        setEditingCategory(null);
        setFormData({ name: '', description: '', icon: '', subcategories: [] });
        console.log('Category added successfully:', result.category);
      } else {
        console.error('Failed to add category:', result.error);
      }
    } catch (error) {
      console.error('Error adding category:', error);
    }
  };

  const handleDeleteCategory = (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      // Here you would typically delete from your backend
      console.log('Deleting category:', categoryId);
    }
  };

  const CategoryCard = ({ category }) => (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`p-6 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl ${
        getThemeClasses('bg-white', 'bg-slate-800')
      }`}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-3xl">{category.icon}</div>
          <div>
            <h3 className={`text-lg font-semibold ${
              getThemeClasses('text-gray-900', 'text-white')
            }`}>
              {category.name}
            </h3>
            <p className={`text-sm ${
              getThemeClasses('text-gray-600', 'text-gray-400')
            }`}>
              {category.description}
            </p>
          </div>
        </div>
        {hasPermission('categories') && (
          <div className="flex space-x-2">
            <button
              onClick={() => handleEditCategory(category)}
              className={`p-2 rounded-lg transition-colors ${
                getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
              }`}
            >
              <PencilIcon className="w-4 h-4 text-blue-500" />
            </button>
            <button
              onClick={() => handleDeleteCategory(category.id)}
              className={`p-2 rounded-lg transition-colors ${
                getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
              }`}
            >
              <TrashIcon className="w-4 h-4 text-red-500" />
            </button>
          </div>
        )}
      </div>

      {category.subcategories && category.subcategories.length > 0 && (
        <div>
          <h4 className={`text-sm font-medium mb-2 ${
            getThemeClasses('text-gray-700', 'text-gray-300')
          }`}>
            Subcategories:
          </h4>
          <div className="flex flex-wrap gap-2">
            {category.subcategories.map((sub, index) => (
              <span
                key={index}
                className={`px-2 py-1 text-xs rounded-full ${
                  getThemeClasses(
                    'bg-light-orange-100 text-light-orange-800',
                    'bg-light-orange-900/20 text-light-orange-400'
                  )
                }`}
              >
                {sub.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  );

  const Modal = () => (
    <AnimatePresence>
      {showAddModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          onClick={() => setShowAddModal(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
            className={`w-full max-w-md p-6 rounded-xl shadow-xl ${
              getThemeClasses('bg-white', 'bg-slate-800')
            }`}
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                getThemeClasses('text-gray-900', 'text-white')
              }`}>
                {editingCategory ? 'Edit Category' : 'Add New Category'}
              </h3>
              <button
                onClick={() => setShowAddModal(false)}
                className={`p-2 rounded-lg transition-colors ${
                  getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
                }`}
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  getThemeClasses('text-gray-700', 'text-gray-300')
                }`}>
                  Category Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                    getThemeClasses(
                      'border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500',
                      'border-slate-600 bg-slate-700 text-white focus:border-light-orange-400 focus:ring-light-orange-400'
                    )
                  }`}
                  required
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  getThemeClasses('text-gray-700', 'text-gray-300')
                }`}>
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                    getThemeClasses(
                      'border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500',
                      'border-slate-600 bg-slate-700 text-white focus:border-light-orange-400 focus:ring-light-orange-400'
                    )
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  getThemeClasses('text-gray-700', 'text-gray-300')
                }`}>
                  Icon (Emoji)
                </label>
                <input
                  type="text"
                  value={formData.icon}
                  onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  placeholder="📱"
                  className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                    getThemeClasses(
                      'border-gray-300 bg-white text-gray-900 focus:border-light-orange-500 focus:ring-light-orange-500',
                      'border-slate-600 bg-slate-700 text-white focus:border-light-orange-400 focus:ring-light-orange-400'
                    )
                  }`}
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className={`flex-1 px-4 py-2 rounded-lg font-medium transition-colors ${
                    getThemeClasses(
                      'bg-gray-200 text-gray-800 hover:bg-gray-300',
                      'bg-slate-600 text-white hover:bg-slate-500'
                    )
                  }`}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-light-orange-500 text-white rounded-lg font-medium hover:bg-light-orange-600 transition-colors"
                >
                  {editingCategory ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-3xl font-bold ${
              getThemeClasses('text-gray-900', 'text-white')
            }`}>
              Categories
            </h1>
            <p className={`mt-2 ${
              getThemeClasses('text-gray-600', 'text-gray-400')
            }`}>
              Manage product categories and subcategories
            </p>
          </div>
          {hasPermission('categories') && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleAddCategory}
              className="flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
              <span>Add Category</span>
            </motion.button>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className={`p-6 rounded-xl shadow-lg ${
            getThemeClasses('bg-white', 'bg-slate-800')
          }`}>
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                <TagIcon className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className={`text-sm font-medium ${
                  getThemeClasses('text-gray-600', 'text-gray-400')
                }`}>
                  Total Categories
                </p>
                <p className={`text-2xl font-bold ${
                  getThemeClasses('text-gray-900', 'text-white')
                }`}>
                  {categories.length}
                </p>
              </div>
            </div>
          </div>

          <div className={`p-6 rounded-xl shadow-lg ${
            getThemeClasses('bg-white', 'bg-slate-800')
          }`}>
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full">
                <TagIcon className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className={`text-sm font-medium ${
                  getThemeClasses('text-gray-600', 'text-gray-400')
                }`}>
                  Active Categories
                </p>
                <p className={`text-2xl font-bold ${
                  getThemeClasses('text-gray-900', 'text-white')
                }`}>
                  {categories.length}
                </p>
              </div>
            </div>
          </div>

          <div className={`p-6 rounded-xl shadow-lg ${
            getThemeClasses('bg-white', 'bg-slate-800')
          }`}>
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full">
                <TagIcon className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className={`text-sm font-medium ${
                  getThemeClasses('text-gray-600', 'text-gray-400')
                }`}>
                  Subcategories
                </p>
                <p className={`text-2xl font-bold ${
                  getThemeClasses('text-gray-900', 'text-white')
                }`}>
                  {categories.reduce((total, cat) => total + (cat.subcategories?.length || 0), 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {categories.map(category => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </AnimatePresence>
        </div>

        {/* Modal */}
        <Modal />
      </div>
    </AdminLayout>
  );
};

export default AdminCategoriesPage;
