{"ast": null, "code": "import * as g from './dom.js';\nlet a = /([\\u2700-\\u27BF]|[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2011-\\u26FF]|\\uD83E[\\uDD10-\\uDDFF])/g;\nfunction o(e) {\n  var l, n;\n  let i = (l = e.innerText) != null ? l : \"\",\n    t = e.cloneNode(!0);\n  if (!g.isHTMLElement(t)) return i;\n  let u = !1;\n  for (let f of t.querySelectorAll('[hidden],[aria-hidden],[role=\"img\"]')) f.remove(), u = !0;\n  let r = u ? (n = t.innerText) != null ? n : \"\" : i;\n  return a.test(r) && (r = r.replace(a, \"\")), r;\n}\nfunction F(e) {\n  let i = e.getAttribute(\"aria-label\");\n  if (typeof i == \"string\") return i.trim();\n  let t = e.getAttribute(\"aria-labelledby\");\n  if (t) {\n    let u = t.split(\" \").map(r => {\n      let l = document.getElementById(r);\n      if (l) {\n        let n = l.getAttribute(\"aria-label\");\n        return typeof n == \"string\" ? n.trim() : o(l).trim();\n      }\n      return null;\n    }).filter(Boolean);\n    if (u.length > 0) return u.join(\", \");\n  }\n  return o(e).trim();\n}\nexport { F as getTextValue };", "map": {"version": 3, "names": ["g", "a", "o", "e", "l", "n", "i", "innerText", "t", "cloneNode", "isHTMLElement", "u", "f", "querySelectorAll", "remove", "r", "test", "replace", "F", "getAttribute", "trim", "split", "map", "document", "getElementById", "filter", "Boolean", "length", "join", "getTextValue"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/get-text-value.js"], "sourcesContent": ["import*as g from'./dom.js';let a=/([\\u2700-\\u27BF]|[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2011-\\u26FF]|\\uD83E[\\uDD10-\\uDDFF])/g;function o(e){var l,n;let i=(l=e.innerText)!=null?l:\"\",t=e.cloneNode(!0);if(!g.isHTMLElement(t))return i;let u=!1;for(let f of t.querySelectorAll('[hidden],[aria-hidden],[role=\"img\"]'))f.remove(),u=!0;let r=u?(n=t.innerText)!=null?n:\"\":i;return a.test(r)&&(r=r.replace(a,\"\")),r}function F(e){let i=e.getAttribute(\"aria-label\");if(typeof i==\"string\")return i.trim();let t=e.getAttribute(\"aria-labelledby\");if(t){let u=t.split(\" \").map(r=>{let l=document.getElementById(r);if(l){let n=l.getAttribute(\"aria-label\");return typeof n==\"string\"?n.trim():o(l).trim()}return null}).filter(Boolean);if(u.length>0)return u.join(\", \")}return o(e).trim()}export{F as getTextValue};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,UAAU;AAAC,IAAIC,CAAC,GAAC,sHAAsH;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,EAACC,CAAC;EAAC,IAAIC,CAAC,GAAC,CAACF,CAAC,GAACD,CAAC,CAACI,SAAS,KAAG,IAAI,GAACH,CAAC,GAAC,EAAE;IAACI,CAAC,GAACL,CAAC,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC;EAAC,IAAG,CAACT,CAAC,CAACU,aAAa,CAACF,CAAC,CAAC,EAAC,OAAOF,CAAC;EAAC,IAAIK,CAAC,GAAC,CAAC,CAAC;EAAC,KAAI,IAAIC,CAAC,IAAIJ,CAAC,CAACK,gBAAgB,CAAC,qCAAqC,CAAC,EAACD,CAAC,CAACE,MAAM,CAAC,CAAC,EAACH,CAAC,GAAC,CAAC,CAAC;EAAC,IAAII,CAAC,GAACJ,CAAC,GAAC,CAACN,CAAC,GAACG,CAAC,CAACD,SAAS,KAAG,IAAI,GAACF,CAAC,GAAC,EAAE,GAACC,CAAC;EAAC,OAAOL,CAAC,CAACe,IAAI,CAACD,CAAC,CAAC,KAAGA,CAAC,GAACA,CAAC,CAACE,OAAO,CAAChB,CAAC,EAAC,EAAE,CAAC,CAAC,EAACc,CAAC;AAAA;AAAC,SAASG,CAACA,CAACf,CAAC,EAAC;EAAC,IAAIG,CAAC,GAACH,CAAC,CAACgB,YAAY,CAAC,YAAY,CAAC;EAAC,IAAG,OAAOb,CAAC,IAAE,QAAQ,EAAC,OAAOA,CAAC,CAACc,IAAI,CAAC,CAAC;EAAC,IAAIZ,CAAC,GAACL,CAAC,CAACgB,YAAY,CAAC,iBAAiB,CAAC;EAAC,IAAGX,CAAC,EAAC;IAAC,IAAIG,CAAC,GAACH,CAAC,CAACa,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACP,CAAC,IAAE;MAAC,IAAIX,CAAC,GAACmB,QAAQ,CAACC,cAAc,CAACT,CAAC,CAAC;MAAC,IAAGX,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACD,CAAC,CAACe,YAAY,CAAC,YAAY,CAAC;QAAC,OAAO,OAAOd,CAAC,IAAE,QAAQ,GAACA,CAAC,CAACe,IAAI,CAAC,CAAC,GAAClB,CAAC,CAACE,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC;MAAA;MAAC,OAAO,IAAI;IAAA,CAAC,CAAC,CAACK,MAAM,CAACC,OAAO,CAAC;IAAC,IAAGf,CAAC,CAACgB,MAAM,GAAC,CAAC,EAAC,OAAOhB,CAAC,CAACiB,IAAI,CAAC,IAAI,CAAC;EAAA;EAAC,OAAO1B,CAAC,CAACC,CAAC,CAAC,CAACiB,IAAI,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIW,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}