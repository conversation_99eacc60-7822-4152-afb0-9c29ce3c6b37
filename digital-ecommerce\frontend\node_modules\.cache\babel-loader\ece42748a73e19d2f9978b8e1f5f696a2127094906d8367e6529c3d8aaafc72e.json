{"ast": null, "code": "import { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useRef as $lmaYr$useRef, useCallback as $lmaYr$useCallback } from \"react\";\n\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $8ae05eaa5c114e9c$export$7f54fc3180508a52(fn) {\n  const ref = (0, $lmaYr$useRef)(null);\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return (0, $lmaYr$useCallback)(function () {\n    const f = ref.current;\n    return f === null || f === void 0 ? void 0 : f(...arguments);\n  }, []);\n}\nexport { $8ae05eaa5c114e9c$export$7f54fc3180508a52 as useEffectEvent };", "map": {"version": 3, "names": ["$8ae05eaa5c114e9c$export$7f54fc3180508a52", "fn", "ref", "$lmaYr$useRef", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "current", "$lmaYr$useCallback", "f", "arguments"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useEffectEvent.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAeO,SAASA,0CAAmCC,EAAM;EACvD,MAAMC,GAAA,GAAM,IAAAC,aAAK,EAAwB;EACzC,IAAAC,yCAAc,EAAE;IACdF,GAAA,CAAIG,OAAO,GAAGJ,EAAA;EAChB,GAAG,CAACA,EAAA,CAAG;EACP;EACA,OAAO,IAAAK,kBAAU,EAAK,YAAI;IACxB,MAAMC,CAAA,GAAIL,GAAA,CAAIG,OAAO;IACrB,OAAOE,CAAA,aAAAA,CAAA,uBAAAA,CAAA,IAAAC,SAAO;EAChB,GAAG,EAAE;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}