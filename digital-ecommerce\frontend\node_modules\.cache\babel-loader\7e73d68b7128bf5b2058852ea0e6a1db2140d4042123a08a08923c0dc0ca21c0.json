{"ast": null, "code": "import { Keys as u } from '../components/keyboard.js';\nimport { useEventListener as i } from './use-event-listener.js';\nimport { useIsTopLayer as f } from './use-is-top-layer.js';\nfunction a(o) {\n  let r = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : typeof document != \"undefined\" ? document.defaultView : null;\n  let t = arguments.length > 2 ? arguments[2] : undefined;\n  let n = f(o, \"escape\");\n  i(r, \"keydown\", e => {\n    n && (e.defaultPrevented || e.key === u.Escape && t(e));\n  });\n}\nexport { a as useEscape };", "map": {"version": 3, "names": ["Keys", "u", "useEventListener", "i", "useIsTopLayer", "f", "a", "o", "r", "arguments", "length", "undefined", "document", "defaultView", "t", "n", "e", "defaultPrevented", "key", "Escape", "useEscape"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-escape.js"], "sourcesContent": ["import{Keys as u}from'../components/keyboard.js';import{useEventListener as i}from'./use-event-listener.js';import{useIsTopLayer as f}from'./use-is-top-layer.js';function a(o,r=typeof document!=\"undefined\"?document.defaultView:null,t){let n=f(o,\"escape\");i(r,\"keydown\",e=>{n&&(e.defaultPrevented||e.key===u.Escape&&t(e))})}export{a as useEscape};\n"], "mappings": "AAAA,SAAOA,IAAI,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAA4D;EAAA,IAA3DC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,OAAOG,QAAQ,IAAE,WAAW,GAACA,QAAQ,CAACC,WAAW,GAAC,IAAI;EAAA,IAACC,CAAC,GAAAL,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAE,IAAII,CAAC,GAACV,CAAC,CAACE,CAAC,EAAC,QAAQ,CAAC;EAACJ,CAAC,CAACK,CAAC,EAAC,SAAS,EAACQ,CAAC,IAAE;IAACD,CAAC,KAAGC,CAAC,CAACC,gBAAgB,IAAED,CAAC,CAACE,GAAG,KAAGjB,CAAC,CAACkB,MAAM,IAAEL,CAAC,CAACE,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC;AAAA;AAAC,SAAOV,CAAC,IAAIc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}