{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useState,useEffect}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const UserContext=/*#__PURE__*/createContext();export const useUser=()=>{const context=useContext(UserContext);if(!context){throw new Error('useUser must be used within a UserProvider');}return context;};// Mock user data for demonstration\nconst mockUsers=[{id:'user-001',email:'<EMAIL>',password:'password123',// In real app, this would be hashed\nfirstName:'John',lastName:'Doe',phone:'+****************',profilePicture:'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',addresses:[{id:'addr-001',type:'shipping',isDefault:true,firstName:'John',lastName:'Doe',address:'123 Main Street',city:'New York',state:'NY',zipCode:'10001',country:'United States'}],paymentMethods:[{id:'payment-001',type:'credit_card',isDefault:true,last4:'4242',brand:'Visa',expiryMonth:12,expiryYear:2025}],preferences:{emailNotifications:true,smsNotifications:false,marketingEmails:true,currency:'USD',language:'en'},orderHistory:[],wishlist:[],createdAt:'2024-01-15T10:30:00Z',lastLogin:'2024-01-20T14:22:00Z',isVerified:true}];export const UserProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[isLoading,setIsLoading]=useState(true);const[isAuthenticated,setIsAuthenticated]=useState(false);// Check for existing session on mount\nuseEffect(()=>{const checkAuthStatus=()=>{const token=localStorage.getItem('authToken');const userData=localStorage.getItem('userData');if(token&&userData){try{const parsedUser=JSON.parse(userData);setUser(parsedUser);setIsAuthenticated(true);}catch(error){console.error('Error parsing user data:',error);localStorage.removeItem('authToken');localStorage.removeItem('userData');}}setIsLoading(false);};checkAuthStatus();},[]);const login=async function(email,password){let rememberMe=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,1000));// Find user in mock data\nconst foundUser=mockUsers.find(u=>u.email===email&&u.password===password);if(!foundUser){throw new Error('Invalid email or password');}// Generate mock token\nconst token=\"mock_token_\".concat(Date.now());// Store auth data\nif(rememberMe){localStorage.setItem('authToken',token);localStorage.setItem('userData',JSON.stringify(foundUser));}else{sessionStorage.setItem('authToken',token);sessionStorage.setItem('userData',JSON.stringify(foundUser));}setUser(foundUser);setIsAuthenticated(true);setIsLoading(false);return{success:true,user:foundUser};}catch(error){setIsLoading(false);return{success:false,error:error.message};}};const register=async userData=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,1500));// Check if user already exists\nconst existingUser=mockUsers.find(u=>u.email===userData.email);if(existingUser){throw new Error('User with this email already exists');}// Create new user\nconst newUser={id:\"user-\".concat(Date.now()),email:userData.email,password:userData.password,firstName:userData.firstName,lastName:userData.lastName,phone:userData.phone||'',profilePicture:'',addresses:[],paymentMethods:[],preferences:{emailNotifications:true,smsNotifications:false,marketingEmails:userData.marketingEmails||false,currency:'USD',language:'en'},orderHistory:[],wishlist:[],createdAt:new Date().toISOString(),lastLogin:new Date().toISOString(),isVerified:false};// Add to mock users (in real app, this would be sent to server)\nmockUsers.push(newUser);setIsLoading(false);return{success:true,message:'Account created successfully. Please check your email for verification.'};}catch(error){setIsLoading(false);return{success:false,error:error.message};}};const logout=()=>{localStorage.removeItem('authToken');localStorage.removeItem('userData');sessionStorage.removeItem('authToken');sessionStorage.removeItem('userData');setUser(null);setIsAuthenticated(false);};const updateProfile=async updatedData=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,800));const updatedUser=_objectSpread(_objectSpread({},user),updatedData);// Update stored data\nconst token=localStorage.getItem('authToken')||sessionStorage.getItem('authToken');if(token){if(localStorage.getItem('authToken')){localStorage.setItem('userData',JSON.stringify(updatedUser));}else{sessionStorage.setItem('userData',JSON.stringify(updatedUser));}}setUser(updatedUser);setIsLoading(false);return{success:true,user:updatedUser};}catch(error){setIsLoading(false);return{success:false,error:error.message};}};const resetPassword=async email=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,1000));// Check if user exists\nconst foundUser=mockUsers.find(u=>u.email===email);if(!foundUser){throw new Error('No account found with this email address');}setIsLoading(false);return{success:true,message:'Password reset link sent to your email'};}catch(error){setIsLoading(false);return{success:false,error:error.message};}};const addToWishlist=productId=>{if(!user)return;const updatedWishlist=[...user.wishlist];if(!updatedWishlist.includes(productId)){updatedWishlist.push(productId);updateProfile({wishlist:updatedWishlist});}};const removeFromWishlist=productId=>{if(!user)return;const updatedWishlist=user.wishlist.filter(id=>id!==productId);updateProfile({wishlist:updatedWishlist});};const isInWishlist=productId=>{var _user$wishlist;return(user===null||user===void 0?void 0:(_user$wishlist=user.wishlist)===null||_user$wishlist===void 0?void 0:_user$wishlist.includes(productId))||false;};const value={user,isLoading,isAuthenticated,login,register,logout,updateProfile,resetPassword,addToWishlist,removeFromWishlist,isInWishlist};return/*#__PURE__*/_jsx(UserContext.Provider,{value:value,children:children});};export default UserContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsx", "_jsx", "UserContext", "useUser", "context", "Error", "mockUsers", "id", "email", "password", "firstName", "lastName", "phone", "profilePicture", "addresses", "type", "isDefault", "address", "city", "state", "zipCode", "country", "paymentMethods", "last4", "brand", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "preferences", "emailNotifications", "smsNotifications", "marketingEmails", "currency", "language", "orderHistory", "wishlist", "createdAt", "lastLogin", "isVerified", "UserProvider", "_ref", "children", "user", "setUser", "isLoading", "setIsLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "rememberMe", "arguments", "length", "undefined", "Promise", "resolve", "setTimeout", "foundUser", "find", "u", "concat", "Date", "now", "setItem", "stringify", "sessionStorage", "success", "message", "register", "existingUser", "newUser", "toISOString", "push", "logout", "updateProfile", "updatedData", "updatedUser", "_objectSpread", "resetPassword", "addToWishlist", "productId", "updatedWishlist", "includes", "removeFromWishlist", "filter", "isInWishlist", "_user$wishlist", "value", "Provider"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/UserContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst UserContext = createContext();\n\nexport const useUser = () => {\n  const context = useContext(UserContext);\n  if (!context) {\n    throw new Error('useUser must be used within a UserProvider');\n  }\n  return context;\n};\n\n// Mock user data for demonstration\nconst mockUsers = [\n  {\n    id: 'user-001',\n    email: '<EMAIL>',\n    password: 'password123', // In real app, this would be hashed\n    firstName: '<PERSON>',\n    lastName: 'Doe',\n    phone: '+****************',\n    profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',\n    addresses: [\n      {\n        id: 'addr-001',\n        type: 'shipping',\n        isDefault: true,\n        firstName: 'John',\n        lastName: 'Doe',\n        address: '123 Main Street',\n        city: 'New York',\n        state: 'NY',\n        zipCode: '10001',\n        country: 'United States'\n      }\n    ],\n    paymentMethods: [\n      {\n        id: 'payment-001',\n        type: 'credit_card',\n        isDefault: true,\n        last4: '4242',\n        brand: 'Visa',\n        expiryMonth: 12,\n        expiryYear: 2025\n      }\n    ],\n    preferences: {\n      emailNotifications: true,\n      smsNotifications: false,\n      marketingEmails: true,\n      currency: 'USD',\n      language: 'en'\n    },\n    orderHistory: [],\n    wishlist: [],\n    createdAt: '2024-01-15T10:30:00Z',\n    lastLogin: '2024-01-20T14:22:00Z',\n    isVerified: true\n  }\n];\n\nexport const UserProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('userData');\n      \n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n        }\n      }\n      setIsLoading(false);\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  const login = async (email, password, rememberMe = false) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Find user in mock data\n      const foundUser = mockUsers.find(u => u.email === email && u.password === password);\n      \n      if (!foundUser) {\n        throw new Error('Invalid email or password');\n      }\n\n      // Generate mock token\n      const token = `mock_token_${Date.now()}`;\n      \n      // Store auth data\n      if (rememberMe) {\n        localStorage.setItem('authToken', token);\n        localStorage.setItem('userData', JSON.stringify(foundUser));\n      } else {\n        sessionStorage.setItem('authToken', token);\n        sessionStorage.setItem('userData', JSON.stringify(foundUser));\n      }\n\n      setUser(foundUser);\n      setIsAuthenticated(true);\n      setIsLoading(false);\n      \n      return { success: true, user: foundUser };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const register = async (userData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Check if user already exists\n      const existingUser = mockUsers.find(u => u.email === userData.email);\n      if (existingUser) {\n        throw new Error('User with this email already exists');\n      }\n\n      // Create new user\n      const newUser = {\n        id: `user-${Date.now()}`,\n        email: userData.email,\n        password: userData.password,\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n        phone: userData.phone || '',\n        profilePicture: '',\n        addresses: [],\n        paymentMethods: [],\n        preferences: {\n          emailNotifications: true,\n          smsNotifications: false,\n          marketingEmails: userData.marketingEmails || false,\n          currency: 'USD',\n          language: 'en'\n        },\n        orderHistory: [],\n        wishlist: [],\n        createdAt: new Date().toISOString(),\n        lastLogin: new Date().toISOString(),\n        isVerified: false\n      };\n\n      // Add to mock users (in real app, this would be sent to server)\n      mockUsers.push(newUser);\n\n      setIsLoading(false);\n      return { success: true, message: 'Account created successfully. Please check your email for verification.' };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    \n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  const updateProfile = async (updatedData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 800));\n      \n      const updatedUser = { ...user, ...updatedData };\n      \n      // Update stored data\n      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');\n      if (token) {\n        if (localStorage.getItem('authToken')) {\n          localStorage.setItem('userData', JSON.stringify(updatedUser));\n        } else {\n          sessionStorage.setItem('userData', JSON.stringify(updatedUser));\n        }\n      }\n\n      setUser(updatedUser);\n      setIsLoading(false);\n      \n      return { success: true, user: updatedUser };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const resetPassword = async (email) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Check if user exists\n      const foundUser = mockUsers.find(u => u.email === email);\n      if (!foundUser) {\n        throw new Error('No account found with this email address');\n      }\n\n      setIsLoading(false);\n      return { success: true, message: 'Password reset link sent to your email' };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const addToWishlist = (productId) => {\n    if (!user) return;\n    \n    const updatedWishlist = [...user.wishlist];\n    if (!updatedWishlist.includes(productId)) {\n      updatedWishlist.push(productId);\n      updateProfile({ wishlist: updatedWishlist });\n    }\n  };\n\n  const removeFromWishlist = (productId) => {\n    if (!user) return;\n    \n    const updatedWishlist = user.wishlist.filter(id => id !== productId);\n    updateProfile({ wishlist: updatedWishlist });\n  };\n\n  const isInWishlist = (productId) => {\n    return user?.wishlist?.includes(productId) || false;\n  };\n\n  const value = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    updateProfile,\n    resetPassword,\n    addToWishlist,\n    removeFromWishlist,\n    isInWishlist\n  };\n\n  return (\n    <UserContext.Provider value={value}>\n      {children}\n    </UserContext.Provider>\n  );\n};\n\nexport default UserContext;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9E,KAAM,CAAAC,WAAW,cAAGN,aAAa,CAAC,CAAC,CAEnC,MAAO,MAAM,CAAAO,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGP,UAAU,CAACK,WAAW,CAAC,CACvC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,4CAA4C,CAAC,CAC/D,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAE,SAAS,CAAG,CAChB,CACEC,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,kBAAkB,CACzBC,QAAQ,CAAE,aAAa,CAAE;AACzBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,mBAAmB,CAC1BC,cAAc,CAAE,oEAAoE,CACpFC,SAAS,CAAE,CACT,CACEP,EAAE,CAAE,UAAU,CACdQ,IAAI,CAAE,UAAU,CAChBC,SAAS,CAAE,IAAI,CACfN,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,KAAK,CACfM,OAAO,CAAE,iBAAiB,CAC1BC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,OAAO,CAChBC,OAAO,CAAE,eACX,CAAC,CACF,CACDC,cAAc,CAAE,CACd,CACEf,EAAE,CAAE,aAAa,CACjBQ,IAAI,CAAE,aAAa,CACnBC,SAAS,CAAE,IAAI,CACfO,KAAK,CAAE,MAAM,CACbC,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,EAAE,CACfC,UAAU,CAAE,IACd,CAAC,CACF,CACDC,WAAW,CAAE,CACXC,kBAAkB,CAAE,IAAI,CACxBC,gBAAgB,CAAE,KAAK,CACvBC,eAAe,CAAE,IAAI,CACrBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,IACZ,CAAC,CACDC,YAAY,CAAE,EAAE,CAChBC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,sBAAsB,CACjCC,SAAS,CAAE,sBAAsB,CACjCC,UAAU,CAAE,IACd,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC+C,eAAe,CAAEC,kBAAkB,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgD,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAEjD,GAAIF,KAAK,EAAIG,QAAQ,CAAE,CACrB,GAAI,CACF,KAAM,CAAAC,UAAU,CAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC,CACvCT,OAAO,CAACU,UAAU,CAAC,CACnBN,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC,CACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC,CACrC,CACF,CACAb,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDG,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,KAAK,CAAG,cAAAA,CAAOlD,KAAK,CAAEC,QAAQ,CAAyB,IAAvB,CAAAkD,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACtDhB,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAmB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,SAAS,CAAG5D,SAAS,CAAC6D,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC5D,KAAK,GAAKA,KAAK,EAAI4D,CAAC,CAAC3D,QAAQ,GAAKA,QAAQ,CAAC,CAEnF,GAAI,CAACyD,SAAS,CAAE,CACd,KAAM,IAAI,CAAA7D,KAAK,CAAC,2BAA2B,CAAC,CAC9C,CAEA;AACA,KAAM,CAAA2C,KAAK,eAAAqB,MAAA,CAAiBC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CAExC;AACA,GAAIZ,UAAU,CAAE,CACdV,YAAY,CAACuB,OAAO,CAAC,WAAW,CAAExB,KAAK,CAAC,CACxCC,YAAY,CAACuB,OAAO,CAAC,UAAU,CAAEnB,IAAI,CAACoB,SAAS,CAACP,SAAS,CAAC,CAAC,CAC7D,CAAC,IAAM,CACLQ,cAAc,CAACF,OAAO,CAAC,WAAW,CAAExB,KAAK,CAAC,CAC1C0B,cAAc,CAACF,OAAO,CAAC,UAAU,CAAEnB,IAAI,CAACoB,SAAS,CAACP,SAAS,CAAC,CAAC,CAC/D,CAEAxB,OAAO,CAACwB,SAAS,CAAC,CAClBpB,kBAAkB,CAAC,IAAI,CAAC,CACxBF,YAAY,CAAC,KAAK,CAAC,CAEnB,MAAO,CAAE+B,OAAO,CAAE,IAAI,CAAElC,IAAI,CAAEyB,SAAU,CAAC,CAC3C,CAAE,MAAOX,KAAK,CAAE,CACdX,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE+B,OAAO,CAAE,KAAK,CAAEpB,KAAK,CAAEA,KAAK,CAACqB,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG,KAAO,CAAA1B,QAAQ,EAAK,CACnCP,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAmB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAc,YAAY,CAAGxE,SAAS,CAAC6D,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC5D,KAAK,GAAK2C,QAAQ,CAAC3C,KAAK,CAAC,CACpE,GAAIsE,YAAY,CAAE,CAChB,KAAM,IAAI,CAAAzE,KAAK,CAAC,qCAAqC,CAAC,CACxD,CAEA;AACA,KAAM,CAAA0E,OAAO,CAAG,CACdxE,EAAE,SAAA8D,MAAA,CAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CACxB/D,KAAK,CAAE2C,QAAQ,CAAC3C,KAAK,CACrBC,QAAQ,CAAE0C,QAAQ,CAAC1C,QAAQ,CAC3BC,SAAS,CAAEyC,QAAQ,CAACzC,SAAS,CAC7BC,QAAQ,CAAEwC,QAAQ,CAACxC,QAAQ,CAC3BC,KAAK,CAAEuC,QAAQ,CAACvC,KAAK,EAAI,EAAE,CAC3BC,cAAc,CAAE,EAAE,CAClBC,SAAS,CAAE,EAAE,CACbQ,cAAc,CAAE,EAAE,CAClBK,WAAW,CAAE,CACXC,kBAAkB,CAAE,IAAI,CACxBC,gBAAgB,CAAE,KAAK,CACvBC,eAAe,CAAEqB,QAAQ,CAACrB,eAAe,EAAI,KAAK,CAClDC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,IACZ,CAAC,CACDC,YAAY,CAAE,EAAE,CAChBC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,GAAI,CAAAmC,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,CACnC5C,SAAS,CAAE,GAAI,CAAAkC,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,CACnC3C,UAAU,CAAE,KACd,CAAC,CAED;AACA/B,SAAS,CAAC2E,IAAI,CAACF,OAAO,CAAC,CAEvBnC,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE+B,OAAO,CAAE,IAAI,CAAEC,OAAO,CAAE,yEAA0E,CAAC,CAC9G,CAAE,MAAOrB,KAAK,CAAE,CACdX,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE+B,OAAO,CAAE,KAAK,CAAEpB,KAAK,CAAEA,KAAK,CAACqB,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAM,MAAM,CAAGA,CAAA,GAAM,CACnBjC,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC,CACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC,CACnCiB,cAAc,CAACjB,UAAU,CAAC,WAAW,CAAC,CACtCiB,cAAc,CAACjB,UAAU,CAAC,UAAU,CAAC,CAErCf,OAAO,CAAC,IAAI,CAAC,CACbI,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAqC,aAAa,CAAG,KAAO,CAAAC,WAAW,EAAK,CAC3CxC,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAmB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD,KAAM,CAAAqB,WAAW,CAAAC,aAAA,CAAAA,aAAA,IAAQ7C,IAAI,EAAK2C,WAAW,CAAE,CAE/C;AACA,KAAM,CAAApC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,EAAIwB,cAAc,CAACxB,OAAO,CAAC,WAAW,CAAC,CACtF,GAAIF,KAAK,CAAE,CACT,GAAIC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAE,CACrCD,YAAY,CAACuB,OAAO,CAAC,UAAU,CAAEnB,IAAI,CAACoB,SAAS,CAACY,WAAW,CAAC,CAAC,CAC/D,CAAC,IAAM,CACLX,cAAc,CAACF,OAAO,CAAC,UAAU,CAAEnB,IAAI,CAACoB,SAAS,CAACY,WAAW,CAAC,CAAC,CACjE,CACF,CAEA3C,OAAO,CAAC2C,WAAW,CAAC,CACpBzC,YAAY,CAAC,KAAK,CAAC,CAEnB,MAAO,CAAE+B,OAAO,CAAE,IAAI,CAAElC,IAAI,CAAE4C,WAAY,CAAC,CAC7C,CAAE,MAAO9B,KAAK,CAAE,CACdX,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE+B,OAAO,CAAE,KAAK,CAAEpB,KAAK,CAAEA,KAAK,CAACqB,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAW,aAAa,CAAG,KAAO,CAAA/E,KAAK,EAAK,CACrCoC,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAmB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,SAAS,CAAG5D,SAAS,CAAC6D,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC5D,KAAK,GAAKA,KAAK,CAAC,CACxD,GAAI,CAAC0D,SAAS,CAAE,CACd,KAAM,IAAI,CAAA7D,KAAK,CAAC,0CAA0C,CAAC,CAC7D,CAEAuC,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE+B,OAAO,CAAE,IAAI,CAAEC,OAAO,CAAE,wCAAyC,CAAC,CAC7E,CAAE,MAAOrB,KAAK,CAAE,CACdX,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE+B,OAAO,CAAE,KAAK,CAAEpB,KAAK,CAAEA,KAAK,CAACqB,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAY,aAAa,CAAIC,SAAS,EAAK,CACnC,GAAI,CAAChD,IAAI,CAAE,OAEX,KAAM,CAAAiD,eAAe,CAAG,CAAC,GAAGjD,IAAI,CAACP,QAAQ,CAAC,CAC1C,GAAI,CAACwD,eAAe,CAACC,QAAQ,CAACF,SAAS,CAAC,CAAE,CACxCC,eAAe,CAACT,IAAI,CAACQ,SAAS,CAAC,CAC/BN,aAAa,CAAC,CAAEjD,QAAQ,CAAEwD,eAAgB,CAAC,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAIH,SAAS,EAAK,CACxC,GAAI,CAAChD,IAAI,CAAE,OAEX,KAAM,CAAAiD,eAAe,CAAGjD,IAAI,CAACP,QAAQ,CAAC2D,MAAM,CAACtF,EAAE,EAAIA,EAAE,GAAKkF,SAAS,CAAC,CACpEN,aAAa,CAAC,CAAEjD,QAAQ,CAAEwD,eAAgB,CAAC,CAAC,CAC9C,CAAC,CAED,KAAM,CAAAI,YAAY,CAAIL,SAAS,EAAK,KAAAM,cAAA,CAClC,MAAO,CAAAtD,IAAI,SAAJA,IAAI,kBAAAsD,cAAA,CAAJtD,IAAI,CAAEP,QAAQ,UAAA6D,cAAA,iBAAdA,cAAA,CAAgBJ,QAAQ,CAACF,SAAS,CAAC,GAAI,KAAK,CACrD,CAAC,CAED,KAAM,CAAAO,KAAK,CAAG,CACZvD,IAAI,CACJE,SAAS,CACTE,eAAe,CACfa,KAAK,CACLmB,QAAQ,CACRK,MAAM,CACNC,aAAa,CACbI,aAAa,CACbC,aAAa,CACbI,kBAAkB,CAClBE,YACF,CAAC,CAED,mBACE7F,IAAA,CAACC,WAAW,CAAC+F,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAxD,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,cAAe,CAAAtC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}