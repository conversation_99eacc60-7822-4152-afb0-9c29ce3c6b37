{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './index.css';\nimport './App.css';\nimport App from './App';\nimport * as serviceWorker from './serviceWorker';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst container = document.getElementById('root');\nconst root = createRoot(container);\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 11,\n  columnNumber: 3\n}, this));\n\n// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://bit.ly/CRA-PWA\nserviceWorker.unregister();", "map": {"version": 3, "names": ["React", "createRoot", "App", "serviceWorker", "jsxDEV", "_jsxDEV", "container", "document", "getElementById", "root", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "unregister"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './index.css';\nimport './App.css';\nimport App from './App';\nimport * as serviceWorker from './serviceWorker';\n\nconst container = document.getElementById('root');\nconst root = createRoot(container);\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://bit.ly/CRA-PWA\nserviceWorker.unregister();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAO,aAAa;AACpB,OAAO,WAAW;AAClB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,KAAKC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;AACjD,MAAMC,IAAI,GAAGR,UAAU,CAACK,SAAS,CAAC;AAClCG,IAAI,CAACC,MAAM,cACTL,OAAA,CAACL,KAAK,CAACW,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACH,GAAG;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC;;AAED;AACA;AACA;AACAb,aAAa,CAACc,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}