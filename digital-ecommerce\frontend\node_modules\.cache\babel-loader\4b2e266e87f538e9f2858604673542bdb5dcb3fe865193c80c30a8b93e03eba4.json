{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminProductsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MagnifyingGlassIcon, FunnelIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminProductsPage = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAdmin();\n  const {\n    products,\n    categories,\n    addProduct\n  } = useProducts();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      var _product$description;\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || ((_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.toLowerCase().includes(searchQuery.toLowerCase()));\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n    return filtered;\n  }, [products, searchQuery, selectedCategory, selectedType, sortBy]);\n  const handleSelectProduct = productId => {\n    setSelectedProducts(prev => prev.includes(productId) ? prev.filter(id => id !== productId) : [...prev, productId]);\n  };\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n  const ProductCard = ({\n    product\n  }) => /*#__PURE__*/_jsxDEV(motion.div, {\n    layout: true,\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.9\n    },\n    className: `p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white ${selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: product.image,\n        alt: product.name,\n        className: \"w-full h-48 object-cover rounded-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 left-2\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: selectedProducts.includes(product.id),\n          onChange: () => handleSelectProduct(product.id),\n          className: \"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 text-xs font-medium rounded-full ${product.inStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n          children: product.inStock ? 'In Stock' : 'Out of Stock'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-semibold truncate text-gray-900\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm mt-1 text-gray-600\",\n        children: product.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-bold text-light-orange-600\",\n          children: [\"$\", product.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), product.stockCount && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Stock: \", product.stockCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n            className: \"w-4 h-4 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-lg transition-colors hover:bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full ${product.type === 'digital' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n        children: product.type\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Manage your product catalog\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), hasPermission('products') && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setShowAddProductModal(true),\n          className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 rounded-xl shadow-lg bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search products...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('grid'),\n                className: `p-2 rounded-md transition-colors ${viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(Squares2X2Icon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('list'),\n                className: `p-2 rounded-md transition-colors ${viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(ListBulletIcon, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showFilters && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: 'auto'\n            },\n            exit: {\n              opacity: 0,\n              height: 0\n            },\n            className: \"mt-4 pt-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedCategory,\n                onChange: e => setSelectedCategory(e.target.value),\n                className: \"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedType,\n                onChange: e => setSelectedType(e.target.value),\n                className: \"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"physical\",\n                  children: \"Physical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"digital\",\n                  children: \"Digital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: \"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"name\",\n                  children: \"Sort by Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"price\",\n                  children: \"Sort by Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"stock\",\n                  children: \"Sort by Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"category\",\n                  children: \"Sort by Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: [\"Showing \", filteredProducts.length, \" of \", products.length, \" products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), selectedProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: [selectedProducts.length, \" selected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors\",\n            children: \"Delete Selected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddProductModal, {\n      isOpen: showAddProductModal,\n      onClose: () => setShowAddProductModal(false),\n      onSubmit: async productData => {\n        const result = await addProduct(productData);\n        if (result.success) {\n          setShowAddProductModal(false);\n          // Show success notification\n          console.log('Product added successfully:', result.product);\n        } else {\n          // Show error notification\n          console.error('Failed to add product:', result.error);\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminProductsPage, \"yTQhRIQmoAJUo9pdYW4Uw+PXIrU=\", false, function () {\n  return [useAdmin, useProducts];\n});\n_c = AdminProductsPage;\nexport default AdminProductsPage;\nvar _c;\n$RefreshReg$(_c, \"AdminProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "useAdmin", "useProducts", "AdminLayout", "AddProductModal", "jsxDEV", "_jsxDEV", "AdminProductsPage", "_s", "hasPermission", "products", "categories", "addProduct", "viewMode", "setViewMode", "showAddProductModal", "setShowAddProductModal", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedType", "setSelectedType", "sortBy", "setSortBy", "showFilters", "setShowFilters", "selectedProducts", "setSelectedProducts", "filteredProducts", "filtered", "filter", "product", "_product$description", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesCategory", "category", "matchesType", "type", "sort", "a", "b", "localeCompare", "price", "stockCount", "handleSelectProduct", "productId", "prev", "id", "handleSelectAll", "length", "map", "p", "ProductCard", "div", "layout", "initial", "opacity", "scale", "animate", "exit", "className", "children", "src", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "checked", "onChange", "inStock", "button", "whileHover", "whileTap", "onClick", "placeholder", "value", "e", "target", "height", "isOpen", "onClose", "onSubmit", "productData", "result", "success", "console", "log", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon\n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { useProducts } from '../contexts/ProductContext';\nimport AdminLayout from '../components/AdminLayout';\nimport AddProductModal from '../components/AddProductModal';\n\nconst AdminProductsPage = () => {\n  const { hasPermission } = useAdmin();\n  const { products, categories, addProduct } = useProducts();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showAddProductModal, setShowAddProductModal] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedProducts, setSelectedProducts] = useState([]);\n\n  const filteredProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           product.description?.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n      const matchesType = selectedType === 'all' || product.type === selectedType;\n      \n      return matchesSearch && matchesCategory && matchesType;\n    });\n\n    // Sort products\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'price':\n          return a.price - b.price;\n        case 'stock':\n          return (b.stockCount || 0) - (a.stockCount || 0);\n        case 'category':\n          return a.category.localeCompare(b.category);\n        default:\n          return 0;\n      }\n    });\n\n    return filtered;\n  }, [products, searchQuery, selectedCategory, selectedType, sortBy]);\n\n  const handleSelectProduct = (productId) => {\n    setSelectedProducts(prev => \n      prev.includes(productId) \n        ? prev.filter(id => id !== productId)\n        : [...prev, productId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    if (selectedProducts.length === filteredProducts.length) {\n      setSelectedProducts([]);\n    } else {\n      setSelectedProducts(filteredProducts.map(p => p.id));\n    }\n  };\n\n  const ProductCard = ({ product }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className={`p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl bg-white ${\n        selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''\n      }`}\n    >\n      <div className=\"relative\">\n        <img\n          src={product.image}\n          alt={product.name}\n          className=\"w-full h-48 object-cover rounded-lg\"\n        />\n        <div className=\"absolute top-2 left-2\">\n          <input\n            type=\"checkbox\"\n            checked={selectedProducts.includes(product.id)}\n            onChange={() => handleSelectProduct(product.id)}\n            className=\"w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500\"\n          />\n        </div>\n        <div className=\"absolute top-2 right-2\">\n          <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n            product.inStock \n              ? 'bg-green-100 text-green-800'\n              : 'bg-red-100 text-red-800'\n          }`}>\n            {product.inStock ? 'In Stock' : 'Out of Stock'}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"mt-4\">\n        <h3 className=\"font-semibold truncate text-gray-900\">\n          {product.name}\n        </h3>\n        <p className=\"text-sm mt-1 text-gray-600\">\n          {product.category}\n        </p>\n        <div className=\"flex items-center justify-between mt-3\">\n          <span className=\"text-lg font-bold text-light-orange-600\">\n            ${product.price}\n          </span>\n          {product.stockCount && (\n            <span className=\"text-sm text-gray-500\">\n              Stock: {product.stockCount}\n            </span>\n          )}\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"flex space-x-2\">\n          <button className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\">\n            <EyeIcon className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          {hasPermission('products') && (\n            <button className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\">\n              <PencilIcon className=\"w-4 h-4 text-blue-500\" />\n            </button>\n          )}\n          {hasPermission('products') && (\n            <button className=\"p-2 rounded-lg transition-colors hover:bg-gray-100\">\n              <TrashIcon className=\"w-4 h-4 text-red-500\" />\n            </button>\n          )}\n        </div>\n        <span className={`text-xs px-2 py-1 rounded-full ${\n          product.type === 'digital' \n            ? 'bg-blue-100 text-blue-800'\n            : 'bg-gray-100 text-gray-800'\n        }`}>\n          {product.type}\n        </span>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Products\n            </h1>\n            <p className=\"mt-2 text-gray-600\">\n              Manage your product catalog\n            </p>\n          </div>\n          {hasPermission('products') && (\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowAddProductModal(true)}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors\"\n            >\n              <PlusIcon className=\"w-5 h-5\" />\n              <span>Add Product</span>\n            </motion.button>\n          )}\n        </div>\n\n        {/* Toolbar */}\n        <div className=\"p-6 rounded-xl shadow-lg bg-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n            {/* Search */}\n            <div className=\"relative flex-1 max-w-md\">\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500\"\n              />\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Filters */}\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors hover:bg-gray-100\"\n              >\n                <FunnelIcon className=\"w-5 h-5\" />\n                <span>Filters</span>\n              </button>\n\n              {/* View Mode */}\n              <div className=\"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'grid' \n                      ? 'bg-white shadow-sm' \n                      : 'hover:bg-gray-200'\n                  }`}\n                >\n                  <Squares2X2Icon className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded-md transition-colors ${\n                    viewMode === 'list' \n                      ? 'bg-white shadow-sm' \n                      : 'hover:bg-gray-200'\n                  }`}\n                >\n                  <ListBulletIcon className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters Panel */}\n          <AnimatePresence>\n            {showFilters && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-4 pt-4 border-t border-gray-200\"\n              >\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"all\">All Categories</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n\n                  <select\n                    value={selectedType}\n                    onChange={(e) => setSelectedType(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"all\">All Types</option>\n                    <option value=\"physical\">Physical</option>\n                    <option value=\"digital\">Digital</option>\n                  </select>\n\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900\"\n                  >\n                    <option value=\"name\">Sort by Name</option>\n                    <option value=\"price\">Sort by Price</option>\n                    <option value=\"stock\">Sort by Stock</option>\n                    <option value=\"category\">Sort by Category</option>\n                  </select>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Results Info */}\n        <div className=\"flex items-center justify-between\">\n          <p className=\"text-sm text-gray-600\">\n            Showing {filteredProducts.length} of {products.length} products\n          </p>\n          {selectedProducts.length > 0 && (\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">\n                {selectedProducts.length} selected\n              </span>\n              <button className=\"px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors\">\n                Delete Selected\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Products Display */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          <AnimatePresence>\n            {filteredProducts.map(product => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Add Product Modal */}\n      <AddProductModal\n        isOpen={showAddProductModal}\n        onClose={() => setShowAddProductModal(false)}\n        onSubmit={async (productData) => {\n          const result = await addProduct(productData);\n          if (result.success) {\n            setShowAddProductModal(false);\n            // Show success notification\n            console.log('Product added successfully:', result.product);\n          } else {\n            // Show error notification\n            console.error('Failed to add product:', result.error);\n          }\n        }}\n      />\n    </AdminLayout>\n  );\n};\n\nexport default AdminProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,UAAU,EACVC,cAAc,EACdC,cAAc,QACT,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAc,CAAC,GAAGR,QAAQ,CAAC,CAAC;EACpC,MAAM;IAAES,QAAQ;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGV,WAAW,CAAC,CAAC;EAC1D,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAAC0B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAMwC,gBAAgB,GAAGvC,OAAO,CAAC,MAAM;IACrC,IAAIwC,QAAQ,GAAGpB,QAAQ,CAACqB,MAAM,CAACC,OAAO,IAAI;MAAA,IAAAC,oBAAA;MACxC,MAAMC,aAAa,GAAGF,OAAO,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,WAAW,CAACmB,WAAW,CAAC,CAAC,CAAC,MAAAH,oBAAA,GAC/DD,OAAO,CAACM,WAAW,cAAAL,oBAAA,uBAAnBA,oBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,WAAW,CAACmB,WAAW,CAAC,CAAC,CAAC;MAC3F,MAAMG,eAAe,GAAGpB,gBAAgB,KAAK,KAAK,IAAIa,OAAO,CAACQ,QAAQ,KAAKrB,gBAAgB;MAC3F,MAAMsB,WAAW,GAAGpB,YAAY,KAAK,KAAK,IAAIW,OAAO,CAACU,IAAI,KAAKrB,YAAY;MAE3E,OAAOa,aAAa,IAAIK,eAAe,IAAIE,WAAW;IACxD,CAAC,CAAC;;IAEF;IACAX,QAAQ,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,QAAQtB,MAAM;QACZ,KAAK,MAAM;UACT,OAAOqB,CAAC,CAACT,IAAI,CAACW,aAAa,CAACD,CAAC,CAACV,IAAI,CAAC;QACrC,KAAK,OAAO;UACV,OAAOS,CAAC,CAACG,KAAK,GAAGF,CAAC,CAACE,KAAK;QAC1B,KAAK,OAAO;UACV,OAAO,CAACF,CAAC,CAACG,UAAU,IAAI,CAAC,KAAKJ,CAAC,CAACI,UAAU,IAAI,CAAC,CAAC;QAClD,KAAK,UAAU;UACb,OAAOJ,CAAC,CAACJ,QAAQ,CAACM,aAAa,CAACD,CAAC,CAACL,QAAQ,CAAC;QAC7C;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEF,OAAOV,QAAQ;EACjB,CAAC,EAAE,CAACpB,QAAQ,EAAEO,WAAW,EAAEE,gBAAgB,EAAEE,YAAY,EAAEE,MAAM,CAAC,CAAC;EAEnE,MAAM0B,mBAAmB,GAAIC,SAAS,IAAK;IACzCtB,mBAAmB,CAACuB,IAAI,IACtBA,IAAI,CAACd,QAAQ,CAACa,SAAS,CAAC,GACpBC,IAAI,CAACpB,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAKF,SAAS,CAAC,GACnC,CAAC,GAAGC,IAAI,EAAED,SAAS,CACzB,CAAC;EACH,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI1B,gBAAgB,CAAC2B,MAAM,KAAKzB,gBAAgB,CAACyB,MAAM,EAAE;MACvD1B,mBAAmB,CAAC,EAAE,CAAC;IACzB,CAAC,MAAM;MACLA,mBAAmB,CAACC,gBAAgB,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAC;IAAEzB;EAAQ,CAAC,kBAC9B1B,OAAA,CAACf,MAAM,CAACmE,GAAG;IACTC,MAAM;IACNC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACpCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IAClCE,IAAI,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE;IACjCG,SAAS,EAAE,iFACTtC,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAC,GAAG,8BAA8B,GAAG,EAAE,EAC1E;IAAAc,QAAA,gBAEH5D,OAAA;MAAK2D,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB5D,OAAA;QACE6D,GAAG,EAAEnC,OAAO,CAACoC,KAAM;QACnBC,GAAG,EAAErC,OAAO,CAACG,IAAK;QAClB8B,SAAS,EAAC;MAAqC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACFnE,OAAA;QAAK2D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC5D,OAAA;UACEoC,IAAI,EAAC,UAAU;UACfgC,OAAO,EAAE/C,gBAAgB,CAACU,QAAQ,CAACL,OAAO,CAACoB,EAAE,CAAE;UAC/CuB,QAAQ,EAAEA,CAAA,KAAM1B,mBAAmB,CAACjB,OAAO,CAACoB,EAAE,CAAE;UAChDa,SAAS,EAAC;QAA4F;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnE,OAAA;QAAK2D,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC5D,OAAA;UAAM2D,SAAS,EAAE,8CACfjC,OAAO,CAAC4C,OAAO,GACX,6BAA6B,GAC7B,yBAAyB,EAC5B;UAAAV,QAAA,EACAlC,OAAO,CAAC4C,OAAO,GAAG,UAAU,GAAG;QAAc;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnE,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA;QAAI2D,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EACjDlC,OAAO,CAACG;MAAI;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACLnE,OAAA;QAAG2D,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACtClC,OAAO,CAACQ;MAAQ;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACJnE,OAAA;QAAK2D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5D,OAAA;UAAM2D,SAAS,EAAC,yCAAyC;UAAAC,QAAA,GAAC,GACvD,EAAClC,OAAO,CAACe,KAAK;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EACNzC,OAAO,CAACgB,UAAU,iBACjB1C,OAAA;UAAM2D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,SAC/B,EAAClC,OAAO,CAACgB,UAAU;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnE,OAAA;MAAK2D,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBACnF5D,OAAA;QAAK2D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B5D,OAAA;UAAQ2D,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACpE5D,OAAA,CAACV,OAAO;YAACqE,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRhE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UAAQ2D,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACpE5D,OAAA,CAACZ,UAAU;YAACuE,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACT,EACAhE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA;UAAQ2D,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACpE5D,OAAA,CAACX,SAAS;YAACsE,SAAS,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNnE,OAAA;QAAM2D,SAAS,EAAE,kCACfjC,OAAO,CAACU,IAAI,KAAK,SAAS,GACtB,2BAA2B,GAC3B,2BAA2B,EAC9B;QAAAwB,QAAA,EACAlC,OAAO,CAACU;MAAI;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,oBACEnE,OAAA,CAACH,WAAW;IAAA+D,QAAA,gBACV5D,OAAA;MAAK2D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExB5D,OAAA;QAAK2D,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5D,OAAA;UAAA4D,QAAA,gBACE5D,OAAA;YAAI2D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnE,OAAA;YAAG2D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLhE,aAAa,CAAC,UAAU,CAAC,iBACxBH,OAAA,CAACf,MAAM,CAACsF,MAAM;UACZC,UAAU,EAAE;YAAEhB,KAAK,EAAE;UAAK,CAAE;UAC5BiB,QAAQ,EAAE;YAAEjB,KAAK,EAAE;UAAK,CAAE;UAC1BkB,OAAO,EAAEA,CAAA,KAAMhE,sBAAsB,CAAC,IAAI,CAAE;UAC5CiD,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAEvI5D,OAAA,CAACb,QAAQ;YAACwE,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCnE,OAAA;YAAA4D,QAAA,EAAM;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnE,OAAA;QAAK2D,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5D,OAAA;UAAK2D,SAAS,EAAC,qFAAqF;UAAAC,QAAA,gBAElG5D,OAAA;YAAK2D,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC5D,OAAA,CAACT,mBAAmB;cAACoE,SAAS,EAAC;YAA0E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GnE,OAAA;cACEoC,IAAI,EAAC,MAAM;cACXuC,WAAW,EAAC,oBAAoB;cAChCC,KAAK,EAAEjE,WAAY;cACnB0D,QAAQ,EAAGQ,CAAC,IAAKjE,cAAc,CAACiE,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;cAChDjB,SAAS,EAAC;YAAgK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnE,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1C5D,OAAA;cACE0E,OAAO,EAAEA,CAAA,KAAMtD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CwC,SAAS,EAAC,sFAAsF;cAAAC,QAAA,gBAEhG5D,OAAA,CAACR,UAAU;gBAACmE,SAAS,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCnE,OAAA;gBAAA4D,QAAA,EAAM;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGTnE,OAAA;cAAK2D,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrE5D,OAAA;gBACE0E,OAAO,EAAEA,CAAA,KAAMlE,WAAW,CAAC,MAAM,CAAE;gBACnCmD,SAAS,EAAE,oCACTpD,QAAQ,KAAK,MAAM,GACf,oBAAoB,GACpB,mBAAmB,EACtB;gBAAAqD,QAAA,eAEH5D,OAAA,CAACP,cAAc;kBAACkE,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACTnE,OAAA;gBACE0E,OAAO,EAAEA,CAAA,KAAMlE,WAAW,CAAC,MAAM,CAAE;gBACnCmD,SAAS,EAAE,oCACTpD,QAAQ,KAAK,MAAM,GACf,oBAAoB,GACpB,mBAAmB,EACtB;gBAAAqD,QAAA,eAEH5D,OAAA,CAACN,cAAc;kBAACiE,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA,CAACd,eAAe;UAAA0E,QAAA,EACbzC,WAAW,iBACVnB,OAAA,CAACf,MAAM,CAACmE,GAAG;YACTE,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAE,CAAE;YACnCtB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAO,CAAE;YACxCrB,IAAI,EAAE;cAAEH,OAAO,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAE,CAAE;YAChCpB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eAE9C5D,OAAA;cAAK2D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5D,OAAA;gBACE4E,KAAK,EAAE/D,gBAAiB;gBACxBwD,QAAQ,EAAGQ,CAAC,IAAK/D,mBAAmB,CAAC+D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBACrDjB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBAE9E5D,OAAA;kBAAQ4E,KAAK,EAAC,KAAK;kBAAAhB,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC1C9D,UAAU,CAAC4C,GAAG,CAACf,QAAQ,iBACtBlC,OAAA;kBAA0B4E,KAAK,EAAE1C,QAAQ,CAACY,EAAG;kBAAAc,QAAA,EAC1C1B,QAAQ,CAACL;gBAAI,GADHK,QAAQ,CAACY,EAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAETnE,OAAA;gBACE4E,KAAK,EAAE7D,YAAa;gBACpBsD,QAAQ,EAAGQ,CAAC,IAAK7D,eAAe,CAAC6D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBACjDjB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBAE9E5D,OAAA;kBAAQ4E,KAAK,EAAC,KAAK;kBAAAhB,QAAA,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCnE,OAAA;kBAAQ4E,KAAK,EAAC,UAAU;kBAAAhB,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnE,OAAA;kBAAQ4E,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAETnE,OAAA;gBACE4E,KAAK,EAAE3D,MAAO;gBACdoD,QAAQ,EAAGQ,CAAC,IAAK3D,SAAS,CAAC2D,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;gBAC3CjB,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBAE9E5D,OAAA;kBAAQ4E,KAAK,EAAC,MAAM;kBAAAhB,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnE,OAAA;kBAAQ4E,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CnE,OAAA;kBAAQ4E,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CnE,OAAA;kBAAQ4E,KAAK,EAAC,UAAU;kBAAAhB,QAAA,EAAC;gBAAgB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGNnE,OAAA;QAAK2D,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5D,OAAA;UAAG2D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,UAC3B,EAACrC,gBAAgB,CAACyB,MAAM,EAAC,MAAI,EAAC5C,QAAQ,CAAC4C,MAAM,EAAC,WACxD;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACH9C,gBAAgB,CAAC2B,MAAM,GAAG,CAAC,iBAC1BhD,OAAA;UAAK2D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5D,OAAA;YAAM2D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCvC,gBAAgB,CAAC2B,MAAM,EAAC,WAC3B;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnE,OAAA;YAAQ2D,SAAS,EAAC,uFAAuF;YAAAC,QAAA,EAAC;UAE1G;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnE,OAAA;QAAK2D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAClF5D,OAAA,CAACd,eAAe;UAAA0E,QAAA,EACbrC,gBAAgB,CAAC0B,GAAG,CAACvB,OAAO,iBAC3B1B,OAAA,CAACmD,WAAW;YAAkBzB,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAACoB,EAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA,CAACF,eAAe;MACdkF,MAAM,EAAEvE,mBAAoB;MAC5BwE,OAAO,EAAEA,CAAA,KAAMvE,sBAAsB,CAAC,KAAK,CAAE;MAC7CwE,QAAQ,EAAE,MAAOC,WAAW,IAAK;QAC/B,MAAMC,MAAM,GAAG,MAAM9E,UAAU,CAAC6E,WAAW,CAAC;QAC5C,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClB3E,sBAAsB,CAAC,KAAK,CAAC;UAC7B;UACA4E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,MAAM,CAAC1D,OAAO,CAAC;QAC5D,CAAC,MAAM;UACL;UACA4D,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEJ,MAAM,CAACI,KAAK,CAAC;QACvD;MACF;IAAE;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAACjE,EAAA,CAtTID,iBAAiB;EAAA,QACKN,QAAQ,EACWC,WAAW;AAAA;AAAA6F,EAAA,GAFpDxF,iBAAiB;AAwTvB,eAAeA,iBAAiB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}