{"ast": null, "code": "import n, { createContext as d, useContext as i } from \"react\";\nlet e = d(void 0);\nfunction u() {\n  return i(e);\n}\nfunction f({\n  id: t,\n  children: r\n}) {\n  return n.createElement(e.Provider, {\n    value: t\n  }, r);\n}\nexport { f as IdProvider, u as useProvidedId };", "map": {"version": 3, "names": ["n", "createContext", "d", "useContext", "i", "e", "u", "f", "id", "t", "children", "r", "createElement", "Provider", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useProvidedId"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/id.js"], "sourcesContent": ["import n,{createContext as d,useContext as i}from\"react\";let e=d(void 0);function u(){return i(e)}function f({id:t,children:r}){return n.createElement(e.Provider,{value:t},r)}export{f as IdProvider,u as useProvidedId};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,KAAK,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAAC;EAACC,EAAE,EAACC,CAAC;EAACC,QAAQ,EAACC;AAAC,CAAC,EAAC;EAAC,OAAOX,CAAC,CAACY,aAAa,CAACP,CAAC,CAACQ,QAAQ,EAAC;IAACC,KAAK,EAACL;EAAC,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIQ,UAAU,EAACT,CAAC,IAAIU,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}