import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useAdmin } from '../contexts/AdminContext';
import AdminLayout from '../components/AdminLayout';
import { products, categories } from '../data/products';

const AdminProductsPage = () => {
  const { getThemeClasses } = useTheme();
  const { hasPermission } = useAdmin();
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState([]);

  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesType = selectedType === 'all' || product.type === selectedType;
      
      return matchesSearch && matchesCategory && matchesType;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return a.price - b.price;
        case 'stock':
          return (b.stockCount || 0) - (a.stockCount || 0);
        case 'category':
          return a.category.localeCompare(b.category);
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchQuery, selectedCategory, selectedType, sortBy]);

  const handleSelectProduct = (productId) => {
    setSelectedProducts(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id));
    }
  };

  const ProductCard = ({ product }) => (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`p-4 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl ${
        getThemeClasses('bg-white', 'bg-slate-800')
      } ${selectedProducts.includes(product.id) ? 'ring-2 ring-light-orange-500' : ''}`}
    >
      <div className="relative">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-48 object-cover rounded-lg"
        />
        <div className="absolute top-2 left-2">
          <input
            type="checkbox"
            checked={selectedProducts.includes(product.id)}
            onChange={() => handleSelectProduct(product.id)}
            className="w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500"
          />
        </div>
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            product.inStock 
              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
              : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
          }`}>
            {product.inStock ? 'In Stock' : 'Out of Stock'}
          </span>
        </div>
      </div>

      <div className="mt-4">
        <h3 className={`font-semibold truncate ${
          getThemeClasses('text-gray-900', 'text-white')
        }`}>
          {product.name}
        </h3>
        <p className={`text-sm mt-1 ${
          getThemeClasses('text-gray-600', 'text-gray-400')
        }`}>
          {product.category}
        </p>
        <div className="flex items-center justify-between mt-3">
          <span className="text-lg font-bold text-light-orange-600">
            ${product.price}
          </span>
          {product.stockCount && (
            <span className={`text-sm ${
              getThemeClasses('text-gray-500', 'text-gray-400')
            }`}>
              Stock: {product.stockCount}
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-slate-700">
        <div className="flex space-x-2">
          <button className={`p-2 rounded-lg transition-colors ${
            getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
          }`}>
            <EyeIcon className="w-4 h-4 text-gray-500" />
          </button>
          {hasPermission('products') && (
            <button className={`p-2 rounded-lg transition-colors ${
              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
            }`}>
              <PencilIcon className="w-4 h-4 text-blue-500" />
            </button>
          )}
          {hasPermission('products') && (
            <button className={`p-2 rounded-lg transition-colors ${
              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
            }`}>
              <TrashIcon className="w-4 h-4 text-red-500" />
            </button>
          )}
        </div>
        <span className={`text-xs px-2 py-1 rounded-full ${
          product.type === 'digital' 
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
        }`}>
          {product.type}
        </span>
      </div>
    </motion.div>
  );

  const ProductRow = ({ product }) => (
    <motion.tr
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`transition-colors ${
        getThemeClasses('hover:bg-gray-50', 'hover:bg-slate-700')
      } ${selectedProducts.includes(product.id) ? 'bg-light-orange-50 dark:bg-light-orange-900/10' : ''}`}
    >
      <td className="px-6 py-4">
        <input
          type="checkbox"
          checked={selectedProducts.includes(product.id)}
          onChange={() => handleSelectProduct(product.id)}
          className="w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500"
        />
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center space-x-3">
          <img
            src={product.image}
            alt={product.name}
            className="w-12 h-12 rounded-lg object-cover"
          />
          <div>
            <p className={`font-medium ${
              getThemeClasses('text-gray-900', 'text-white')
            }`}>
              {product.name}
            </p>
            <p className={`text-sm ${
              getThemeClasses('text-gray-500', 'text-gray-400')
            }`}>
              {product.category}
            </p>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <span className="text-lg font-semibold text-light-orange-600">
          ${product.price}
        </span>
      </td>
      <td className="px-6 py-4">
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
          product.inStock 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {product.inStock ? `${product.stockCount || 'In Stock'}` : 'Out of Stock'}
        </span>
      </td>
      <td className="px-6 py-4">
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
          product.type === 'digital' 
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
        }`}>
          {product.type}
        </span>
      </td>
      <td className="px-6 py-4">
        <div className="flex space-x-2">
          <button className={`p-2 rounded-lg transition-colors ${
            getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')
          }`}>
            <EyeIcon className="w-4 h-4 text-gray-500" />
          </button>
          {hasPermission('products') && (
            <button className={`p-2 rounded-lg transition-colors ${
              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')
            }`}>
              <PencilIcon className="w-4 h-4 text-blue-500" />
            </button>
          )}
          {hasPermission('products') && (
            <button className={`p-2 rounded-lg transition-colors ${
              getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-600')
            }`}>
              <TrashIcon className="w-4 h-4 text-red-500" />
            </button>
          )}
        </div>
      </td>
    </motion.tr>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-3xl font-bold ${
              getThemeClasses('text-gray-900', 'text-white')
            }`}>
              Products
            </h1>
            <p className={`mt-2 ${
              getThemeClasses('text-gray-600', 'text-gray-400')
            }`}>
              Manage your product catalog
            </p>
          </div>
          {hasPermission('products') && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
              <span>Add Product</span>
            </motion.button>
          )}
        </div>

        {/* Toolbar */}
        <div className={`p-6 rounded-xl shadow-lg ${
          getThemeClasses('bg-white', 'bg-slate-800')
        }`}>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${
                  getThemeClasses(
                    'border-gray-300 bg-white text-gray-900 placeholder-gray-500 focus:border-light-orange-500 focus:ring-light-orange-500',
                    'border-slate-600 bg-slate-700 text-white placeholder-gray-400 focus:border-light-orange-400 focus:ring-light-orange-400'
                  )
                }`}
              />
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              {/* Filters */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                  getThemeClasses('hover:bg-gray-100', 'hover:bg-slate-700')
                }`}
              >
                <FunnelIcon className="w-5 h-5" />
                <span>Filters</span>
              </button>

              {/* View Mode */}
              <div className="flex items-center space-x-1 bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-white dark:bg-slate-600 shadow-sm' 
                      : 'hover:bg-gray-200 dark:hover:bg-slate-600'
                  }`}
                >
                  <Squares2X2Icon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white dark:bg-slate-600 shadow-sm' 
                      : 'hover:bg-gray-200 dark:hover:bg-slate-600'
                  }`}
                >
                  <ListBulletIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-gray-200 dark:border-slate-700"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className={`px-3 py-2 rounded-lg border transition-colors ${
                      getThemeClasses(
                        'border-gray-300 bg-white text-gray-900',
                        'border-slate-600 bg-slate-700 text-white'
                      )
                    }`}
                  >
                    <option value="all">All Categories</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>

                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className={`px-3 py-2 rounded-lg border transition-colors ${
                      getThemeClasses(
                        'border-gray-300 bg-white text-gray-900',
                        'border-slate-600 bg-slate-700 text-white'
                      )
                    }`}
                  >
                    <option value="all">All Types</option>
                    <option value="physical">Physical</option>
                    <option value="digital">Digital</option>
                  </select>

                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className={`px-3 py-2 rounded-lg border transition-colors ${
                      getThemeClasses(
                        'border-gray-300 bg-white text-gray-900',
                        'border-slate-600 bg-slate-700 text-white'
                      )
                    }`}
                  >
                    <option value="name">Sort by Name</option>
                    <option value="price">Sort by Price</option>
                    <option value="stock">Sort by Stock</option>
                    <option value="category">Sort by Category</option>
                  </select>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Results Info */}
        <div className="flex items-center justify-between">
          <p className={`text-sm ${
            getThemeClasses('text-gray-600', 'text-gray-400')
          }`}>
            Showing {filteredProducts.length} of {products.length} products
          </p>
          {selectedProducts.length > 0 && (
            <div className="flex items-center space-x-4">
              <span className={`text-sm ${
                getThemeClasses('text-gray-600', 'text-gray-400')
              }`}>
                {selectedProducts.length} selected
              </span>
              <button className="px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors">
                Delete Selected
              </button>
            </div>
          )}
        </div>

        {/* Products Display */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <AnimatePresence>
              {filteredProducts.map(product => (
                <ProductCard key={product.id} product={product} />
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <div className={`rounded-xl shadow-lg overflow-hidden ${
            getThemeClasses('bg-white', 'bg-slate-800')
          }`}>
            <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
              <thead className={getThemeClasses('bg-gray-50', 'bg-slate-700')}>
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                      onChange={handleSelectAll}
                      className="w-4 h-4 text-light-orange-600 bg-white rounded border-gray-300 focus:ring-light-orange-500"
                    />
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    getThemeClasses('text-gray-500', 'text-gray-400')
                  }`}>
                    Product
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    getThemeClasses('text-gray-500', 'text-gray-400')
                  }`}>
                    Price
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    getThemeClasses('text-gray-500', 'text-gray-400')
                  }`}>
                    Stock
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    getThemeClasses('text-gray-500', 'text-gray-400')
                  }`}>
                    Type
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    getThemeClasses('text-gray-500', 'text-gray-400')
                  }`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className={`divide-y ${
                getThemeClasses('divide-gray-200', 'divide-slate-700')
              }`}>
                <AnimatePresence>
                  {filteredProducts.map(product => (
                    <ProductRow key={product.id} product={product} />
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminProductsPage;
