import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bars3Icon,
  XMarkIcon,
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  UserIcon,
  HeartIcon,
  HomeIcon,
  TagIcon,
  PhoneIcon,
  InformationCircleIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import ShoppingCart from './ShoppingCart';
import { useUser } from '../contexts/UserContext';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const location = useLocation();
  const { user, isAuthenticated, logout } = useUser();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to products page with search query
      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-dropdown')) {
        setShowUserDropdown(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const navigationItems = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Products', href: '/products', icon: TagIcon },
    { name: 'Digital', href: '/digital-products', icon: TagIcon },
    { name: 'About', href: '/about', icon: InformationCircleIcon },
    { name: 'Contact', href: '/contact', icon: PhoneIcon }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled
            ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100'
            : 'bg-white/10 backdrop-blur-sm'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-18 lg:h-22">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 group">
              <motion.div
                whileHover={{ rotate: 360, scale: 1.1 }}
                transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
                className="relative w-12 h-12 bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
              >
                <ShoppingBagIcon className="w-7 h-7 text-white" />
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
              </motion.div>
              <div className="flex flex-col">
                <span className={`text-2xl font-bold transition-all duration-300 ${
                  isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'
                }`}>
                  ShopHub
                </span>
                <span className={`text-xs font-medium transition-all duration-300 ${
                  isScrolled ? 'text-light-orange-600' : 'text-white/80'
                }`}>
                  Premium Store
                </span>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-2">
              {navigationItems.map((item) => (
                <motion.div
                  key={item.name}
                  whileHover={{ y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link
                    to={item.href}
                    className={`relative px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 group ${
                      isActive(item.href)
                        ? isScrolled
                          ? 'text-white bg-light-orange-500 shadow-lg shadow-light-orange-500/25'
                          : 'text-gray-900 bg-white/90 shadow-lg backdrop-blur-sm'
                        : isScrolled
                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'
                    }`}
                  >
                    <span className="relative z-10">{item.name}</span>
                    {isActive(item.href) && (
                      <motion.div
                        layoutId="activeNavBg"
                        className="absolute inset-0 rounded-xl"
                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      />
                    )}
                    {!isActive(item.href) && (
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    )}
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Search Bar */}
            <div className="hidden md:flex items-center flex-1 max-w-lg mx-8">
              <motion.div
                className="relative w-full group"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className={`h-5 w-5 transition-colors duration-300 ${
                    isScrolled ? 'text-gray-400 group-hover:text-light-orange-500' : 'text-white/70 group-hover:text-white'
                  }`} />
                </div>
                <input
                  type="text"
                  placeholder="Search for products, brands, and more..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                  className={`w-full pl-12 pr-6 py-3 rounded-2xl transition-all duration-300 border-2 ${
                    isScrolled
                      ? 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100'
                      : 'bg-white/15 border-white/20 text-white placeholder-white/60 backdrop-blur-md focus:bg-white/25 focus:border-white/40 focus:ring-4 focus:ring-white/20'
                  } focus:outline-none shadow-lg hover:shadow-xl`}
                />
                <div className={`absolute inset-0 rounded-2xl transition-opacity duration-300 pointer-events-none ${
                  isScrolled
                    ? 'bg-gradient-to-r from-light-orange-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100'
                    : 'bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100'
                }`}></div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <Link to="/wishlist">
                <motion.button
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative p-3 rounded-xl transition-all duration-300 group ${
                    isScrolled
                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                      : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'
                  }`}
                >
                  <HeartIcon className="w-6 h-6" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </motion.button>
              </Link>

              <div className="relative">
                <ShoppingCart />
              </div>

              {/* User Account */}
              {isAuthenticated ? (
                <div className="relative user-dropdown">
                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowUserDropdown(!showUserDropdown)}
                    className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl transition-all duration-300 group ${
                      isScrolled
                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 hover:shadow-lg'
                        : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm hover:shadow-lg'
                    }`}
                  >
                    {user?.profilePicture ? (
                      <img
                        src={user.profilePicture}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover ring-2 ring-white/20"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-light-orange-400 to-light-orange-600 flex items-center justify-center">
                        <UserIcon className="w-5 h-5 text-white" />
                      </div>
                    )}
                    <span className="hidden md:block text-sm font-medium">
                      {user?.firstName || 'Account'}
                    </span>
                    <ChevronDownIcon className={`w-4 h-4 transition-transform duration-300 ${showUserDropdown ? 'rotate-180' : ''}`} />
                  </motion.button>

                  {/* User Dropdown */}
                  <AnimatePresence>
                    {showUserDropdown && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden z-50"
                      >
                        <div className="p-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white">
                          <div className="flex items-center space-x-3">
                            {user?.profilePicture ? (
                              <img
                                src={user.profilePicture}
                                alt="Profile"
                                className="w-12 h-12 rounded-full object-cover ring-2 ring-white/30"
                              />
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                                <UserIcon className="w-6 h-6 text-white" />
                              </div>
                            )}
                            <div>
                              <p className="font-semibold text-white">
                                {user?.firstName} {user?.lastName}
                              </p>
                              <p className="text-sm text-white/80">{user?.email}</p>
                            </div>
                          </div>
                        </div>
                        <div className="py-2">
                          <Link
                            to="/account"
                            onClick={() => setShowUserDropdown(false)}
                            className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200"
                          >
                            <UserIcon className="w-5 h-5" />
                            <span>My Account</span>
                          </Link>
                          <Link
                            to="/orders"
                            onClick={() => setShowUserDropdown(false)}
                            className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200"
                          >
                            <ShoppingBagIcon className="w-5 h-5" />
                            <span>Order History</span>
                          </Link>
                          <Link
                            to="/wishlist"
                            onClick={() => setShowUserDropdown(false)}
                            className="flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600 transition-colors duration-200"
                          >
                            <HeartIcon className="w-5 h-5" />
                            <span>Wishlist</span>
                          </Link>
                          <div className="border-t border-gray-100 mt-2 pt-2">
                            <button
                              onClick={() => {
                                logout();
                                setShowUserDropdown(false);
                              }}
                              className="flex items-center space-x-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
                            >
                              <XMarkIcon className="w-5 h-5" />
                              <span>Sign Out</span>
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <div className="flex items-center space-x-3">
                  <Link to="/login">
                    <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className={`px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${
                        isScrolled
                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 border border-gray-200 hover:border-light-orange-200'
                          : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm border border-white/20 hover:border-white/40'
                      }`}
                    >
                      Sign In
                    </motion.button>
                  </Link>
                  <Link to="/register">
                    <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-4 py-2.5 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white rounded-xl text-sm font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      Sign Up
                    </motion.button>
                  </Link>
                </div>
              )}

              {/* Mobile Menu Button */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setIsOpen(!isOpen)}
                className={`lg:hidden p-3 rounded-xl transition-all duration-300 ${
                  isScrolled
                    ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                    : 'text-white hover:text-gray-900 hover:bg-white/20 backdrop-blur-sm'
                }`}
              >
                <motion.div
                  animate={{ rotate: isOpen ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {isOpen ? (
                    <XMarkIcon className="w-6 h-6" />
                  ) : (
                    <Bars3Icon className="w-6 h-6" />
                  )}
                </motion.div>
              </motion.button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: 'auto', y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="lg:hidden backdrop-blur-xl border-t bg-white/98 border-gray-100 shadow-2xl"
            >
              <div className="px-6 py-8 space-y-6">
                {/* Mobile Search */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className="relative"
                >
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search for products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                    className="w-full pl-12 pr-6 py-4 rounded-2xl bg-gray-50 border-2 border-gray-200 text-gray-900 placeholder-gray-500 focus:bg-white focus:border-light-orange-300 focus:ring-4 focus:ring-light-orange-100 focus:outline-none transition-all duration-300"
                  />
                </motion.div>

                {/* Mobile Navigation Links */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="space-y-3"
                >
                  {navigationItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * (index + 3) }}
                    >
                      <Link
                        to={item.href}
                        onClick={() => setIsOpen(false)}
                        className={`flex items-center space-x-4 px-5 py-4 rounded-2xl transition-all duration-300 group ${
                          isActive(item.href)
                            ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white shadow-lg'
                            : 'text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600'
                        }`}
                      >
                        <item.icon className={`w-6 h-6 ${isActive(item.href) ? 'text-white' : 'text-gray-500 group-hover:text-light-orange-500'}`} />
                        <span className="font-semibold text-lg">{item.name}</span>
                        {isActive(item.href) && (
                          <motion.div
                            layoutId="activeMobileTab"
                            className="ml-auto w-2 h-2 bg-white rounded-full"
                          />
                        )}
                      </Link>
                    </motion.div>
                  ))}
                </motion.div>

                {/* Mobile Auth Buttons */}
                {!isAuthenticated && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="flex space-x-4 pt-4"
                  >
                    <Link to="/login" className="flex-1" onClick={() => setIsOpen(false)}>
                      <button className="w-full py-3 px-6 rounded-2xl border-2 border-light-orange-200 text-light-orange-600 font-semibold hover:bg-light-orange-50 transition-all duration-300">
                        Sign In
                      </button>
                    </Link>
                    <Link to="/register" className="flex-1" onClick={() => setIsOpen(false)}>
                      <button className="w-full py-3 px-6 rounded-2xl bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 shadow-lg">
                        Sign Up
                      </button>
                    </Link>
                  </motion.div>
                )}

                {/* Mobile Action Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center justify-around pt-6 border-t border-gray-200"
                >
                  <Link to="/wishlist" onClick={() => setIsOpen(false)} className="flex flex-col items-center space-y-2 text-gray-600 hover:text-light-orange-600 transition-colors duration-300">
                    <div className="p-3 rounded-2xl bg-gray-100 hover:bg-light-orange-50 transition-colors duration-300">
                      <HeartIcon className="w-6 h-6" />
                    </div>
                    <span className="text-sm font-medium">Wishlist</span>
                  </Link>
                  <Link to="/account" onClick={() => setIsOpen(false)} className="flex flex-col items-center space-y-2 text-gray-600 hover:text-light-orange-600 transition-colors duration-300">
                    <div className="p-3 rounded-2xl bg-gray-100 hover:bg-light-orange-50 transition-colors duration-300">
                      <UserIcon className="w-6 h-6" />
                    </div>
                    <span className="text-sm font-medium">Account</span>
                  </Link>
                  <div className="flex flex-col items-center space-y-2">
                    <div className="p-3 rounded-2xl bg-gray-100">
                      <ShoppingCart />
                    </div>
                    <span className="text-sm font-medium text-gray-600">Cart</span>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Spacer to prevent content from hiding behind fixed nav */}
      <div className="h-18 lg:h-22"></div>
    </>
  );
};

export default Navigation;
