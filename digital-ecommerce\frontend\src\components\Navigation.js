import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bars3Icon,
  XMarkIcon,
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  UserIcon,
  HeartIcon,
  HomeIcon,
  TagIcon,
  PhoneIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import ShoppingCart from './ShoppingCart';
import ThemeToggle from './ThemeToggle';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const { user, isAuthenticated, logout } = useUser();
  const { theme, getThemeClasses } = useTheme();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Implement your search logic here
      console.log('Searching for:', searchQuery);
    }
  };
  // const { totalItems } = useCart(); // Available for future use

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Products', href: '/products', icon: TagIcon },
    { name: 'Digital', href: '/digital-products', icon: TagIcon },
    { name: 'About', href: '/about', icon: InformationCircleIcon },
    { name: 'Contact', href: '/contact', icon: PhoneIcon }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? getThemeClasses(
                'bg-white/95 backdrop-blur-md shadow-lg',
                'bg-slate-900/95 backdrop-blur-md shadow-xl shadow-black/20'
              )
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg"
              >
                <ShoppingBagIcon className="w-6 h-6 text-white" />
              </motion.div>
              <span className={`text-2xl font-bold transition-colors duration-300 ${
                isScrolled
                  ? getThemeClasses('text-gray-900', 'text-white')
                  : 'text-white'
              }`}>
                ShopHub
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${
                    isActive(item.href)
                      ? isScrolled
                        ? 'text-light-orange-600'
                        : 'text-yellow-300'
                      : isScrolled
                        ? getThemeClasses(
                            'text-gray-700 hover:text-light-orange-600',
                            'text-gray-300 hover:text-light-orange-400'
                          )
                        : 'text-white hover:text-yellow-300'
                  }`}
                >
                  {item.name}
                  {isActive(item.href) && (
                    <motion.div
                      layoutId="activeTab"
                      className={`absolute bottom-0 left-0 right-0 h-0.5 ${
                        isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'
                      }`}
                    />
                  )}
                </Link>
              ))}
            </div>

            {/* Search Bar */}
            <div className="hidden md:flex items-center flex-1 max-w-md mx-8">
              <div className="relative w-full">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className={`h-5 w-5 ${
                    isScrolled
                      ? getThemeClasses('text-gray-400', 'text-gray-400')
                      : 'text-white/70'
                  }`} />
                </div>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${
                    isScrolled
                      ? getThemeClasses(
                          'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300',
                          'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-2 focus:ring-light-orange-400'
                        )
                      : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'
                  }`}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className={`p-2 rounded-full transition-colors duration-300 ${
                  isScrolled
                    ? getThemeClasses(
                        'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50',
                        'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700'
                      )
                    : 'text-white hover:text-yellow-300 hover:bg-white/10'
                }`}
              >
                <HeartIcon className="w-6 h-6" />
              </motion.button>

              <Link to="/account">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className={`p-2 rounded-full transition-colors duration-300 ${
                    isScrolled
                      ? getThemeClasses(
                          'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50',
                          'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700'
                        )
                      : 'text-white hover:text-yellow-300 hover:bg-white/10'
                  }`}
                >
                  <UserIcon className="w-6 h-6" />
                </motion.button>
              </Link>

              <ShoppingCart />

              {/* Theme Toggle */}
              <ThemeToggle size="md" />

              {/* User Account */}
              {isAuthenticated ? (
                <div className="relative group">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`relative p-2 rounded-full transition-colors duration-300 ${
                      isScrolled
                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                        : 'text-white hover:text-yellow-300 hover:bg-white/10'
                    }`}
                  >
                    {user?.profilePicture ? (
                      <img
                        src={user.profilePicture}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <UserIcon className="w-6 h-6" />
                    )}
                  </motion.button>

                  {/* User Dropdown */}
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="p-3 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">
                        {user?.firstName} {user?.lastName}
                      </p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                    </div>
                    <div className="py-1">
                      <Link
                        to="/account"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600"
                      >
                        My Account
                      </Link>
                      <Link
                        to="/orders"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600"
                      >
                        Order History
                      </Link>
                      <Link
                        to="/wishlist"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600"
                      >
                        Wishlist
                      </Link>
                      <button
                        onClick={logout}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link to="/login">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${
                        isScrolled
                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'
                          : 'text-white hover:text-yellow-300 hover:bg-white/10'
                      }`}
                    >
                      Sign In
                    </motion.button>
                  </Link>
                  <Link to="/register">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300"
                    >
                      Sign Up
                    </motion.button>
                  </Link>
                </div>
              )}

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsOpen(!isOpen)}
                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${
                  isScrolled 
                    ? 'text-gray-700 hover:text-light-orange-600' 
                    : 'text-white hover:text-yellow-300'
                }`}
              >
                {isOpen ? (
                  <XMarkIcon className="w-6 h-6" />
                ) : (
                  <Bars3Icon className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className={`lg:hidden backdrop-blur-md border-t transition-colors duration-300 ${
                getThemeClasses(
                  'bg-white/95 border-gray-200',
                  'bg-slate-900/95 border-slate-700'
                )
              }`}
            >
              <div className="px-4 py-6 space-y-4">
                {/* Mobile Search */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className={`h-5 w-5 ${
                      getThemeClasses('text-gray-400', 'text-gray-400')
                    }`} />
                  </div>
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg focus:ring-2 transition-colors duration-300 ${
                      getThemeClasses(
                        'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-light-orange-300',
                        'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-light-orange-400'
                      )
                    }`}
                  />
                </div>

                {/* Mobile Navigation Links */}
                <div className="space-y-2">
                  {navigationItems.map((item) => (
                    <Link
                      key={item.name}
                      to={item.href}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${
                        isActive(item.href)
                          ? getThemeClasses(
                              'bg-light-orange-100 text-light-orange-700',
                              'bg-light-orange-900/20 text-light-orange-400'
                            )
                          : getThemeClasses(
                              'text-gray-700 hover:bg-gray-100',
                              'text-gray-300 hover:bg-slate-700'
                            )
                      }`}
                    >
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.name}</span>
                    </Link>
                  ))}
                </div>

                {/* Mobile Action Buttons */}
                <div className={`flex items-center justify-around pt-4 border-t transition-colors duration-300 ${
                  getThemeClasses('border-gray-200', 'border-slate-700')
                }`}>
                  <button className={`flex flex-col items-center space-y-1 transition-colors duration-300 ${
                    getThemeClasses(
                      'text-gray-600 hover:text-light-orange-600',
                      'text-gray-400 hover:text-light-orange-400'
                    )
                  }`}>
                    <HeartIcon className="w-6 h-6" />
                    <span className="text-xs">Wishlist</span>
                  </button>
                  <Link to="/account" className={`flex flex-col items-center space-y-1 transition-colors duration-300 ${
                    getThemeClasses(
                      'text-gray-600 hover:text-light-orange-600',
                      'text-gray-400 hover:text-light-orange-400'
                    )
                  }`}>
                    <UserIcon className="w-6 h-6" />
                    <span className="text-xs">Account</span>
                  </Link>
                  <div className="flex flex-col items-center space-y-1">
                    <ThemeToggle size="sm" />
                    <span className={`text-xs transition-colors duration-300 ${
                      getThemeClasses('text-gray-600', 'text-gray-400')
                    }`}>Theme</span>
                  </div>
                  <div className="flex flex-col items-center space-y-1">
                    <ShoppingCart />
                    <span className={`text-xs transition-colors duration-300 ${
                      getThemeClasses('text-gray-600', 'text-gray-400')
                    }`}>Cart</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Spacer to prevent content from hiding behind fixed nav */}
      <div className="h-16 lg:h-20"></div>
    </>
  );
};

export default Navigation;
