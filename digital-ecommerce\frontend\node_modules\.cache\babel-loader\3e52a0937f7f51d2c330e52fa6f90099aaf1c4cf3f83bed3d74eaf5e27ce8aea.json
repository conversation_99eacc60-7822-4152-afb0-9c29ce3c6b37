{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { debounce, memo, notUndefined, approxEqual } from \"./utils.js\";\nconst getRect = element => {\n  const {\n    offsetWidth,\n    offsetHeight\n  } = element;\n  return {\n    width: offsetWidth,\n    height: offsetHeight\n  };\n};\nconst defaultKeyExtractor = index => index;\nconst defaultRangeExtractor = range => {\n  const start = Math.max(range.startIndex - range.overscan, 0);\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1);\n  const arr = [];\n  for (let i = start; i <= end; i++) {\n    arr.push(i);\n  }\n  return arr;\n};\nconst observeElementRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  const handler = rect => {\n    const {\n      width,\n      height\n    } = rect;\n    cb({\n      width: Math.round(width),\n      height: Math.round(height)\n    });\n  };\n  handler(getRect(element));\n  if (!targetWindow.ResizeObserver) {\n    return () => {};\n  }\n  const observer = new targetWindow.ResizeObserver(entries => {\n    const run = () => {\n      const entry = entries[0];\n      if (entry == null ? void 0 : entry.borderBoxSize) {\n        const box = entry.borderBoxSize[0];\n        if (box) {\n          handler({\n            width: box.inlineSize,\n            height: box.blockSize\n          });\n          return;\n        }\n      }\n      handler(getRect(element));\n    };\n    instance.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();\n  });\n  observer.observe(element, {\n    box: \"border-box\"\n  });\n  return () => {\n    observer.unobserve(element);\n  };\n};\nconst addEventListenerOptions = {\n  passive: true\n};\nconst observeWindowRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const handler = () => {\n    cb({\n      width: element.innerWidth,\n      height: element.innerHeight\n    });\n  };\n  handler();\n  element.addEventListener(\"resize\", handler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"resize\", handler);\n  };\n};\nconst supportsScrollend = typeof window == \"undefined\" ? true : \"onscrollend\" in window;\nconst observeElementOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : debounce(targetWindow, () => {\n    cb(offset, false);\n  }, instance.options.isScrollingResetDelay);\n  const createHandler = isScrolling => () => {\n    const {\n      horizontal,\n      isRtl\n    } = instance.options;\n    offset = horizontal ? element[\"scrollLeft\"] * (isRtl && -1 || 1) : element[\"scrollTop\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  const registerScrollendEvent = instance.options.useScrollendEvent && supportsScrollend;\n  if (registerScrollendEvent) {\n    element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  }\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    if (registerScrollendEvent) {\n      element.removeEventListener(\"scrollend\", endHandler);\n    }\n  };\n};\nconst observeWindowOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : debounce(targetWindow, () => {\n    cb(offset, false);\n  }, instance.options.isScrollingResetDelay);\n  const createHandler = isScrolling => () => {\n    offset = element[instance.options.horizontal ? \"scrollX\" : \"scrollY\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  const registerScrollendEvent = instance.options.useScrollendEvent && supportsScrollend;\n  if (registerScrollendEvent) {\n    element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  }\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    if (registerScrollendEvent) {\n      element.removeEventListener(\"scrollend\", endHandler);\n    }\n  };\n};\nconst measureElement = (element, entry, instance) => {\n  if (entry == null ? void 0 : entry.borderBoxSize) {\n    const box = entry.borderBoxSize[0];\n    if (box) {\n      const size = Math.round(box[instance.options.horizontal ? \"inlineSize\" : \"blockSize\"]);\n      return size;\n    }\n  }\n  return element[instance.options.horizontal ? \"offsetWidth\" : \"offsetHeight\"];\n};\nconst windowScroll = (offset, _ref, instance) => {\n  let {\n    adjustments = 0,\n    behavior\n  } = _ref;\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nconst elementScroll = (offset, _ref2, instance) => {\n  let {\n    adjustments = 0,\n    behavior\n  } = _ref2;\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nclass Virtualizer {\n  constructor(opts) {\n    var _this = this;\n    this.unsubs = [];\n    this.scrollElement = null;\n    this.targetWindow = null;\n    this.isScrolling = false;\n    this.scrollToIndexTimeoutId = null;\n    this.measurementsCache = [];\n    this.itemSizeCache = /* @__PURE__ */new Map();\n    this.pendingMeasuredCacheIndexes = [];\n    this.scrollRect = null;\n    this.scrollOffset = null;\n    this.scrollDirection = null;\n    this.scrollAdjustments = 0;\n    this.elementsCache = /* @__PURE__ */new Map();\n    this.observer = /* @__PURE__ */(() => {\n      let _ro = null;\n      const get = () => {\n        if (_ro) {\n          return _ro;\n        }\n        if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n          return null;\n        }\n        return _ro = new this.targetWindow.ResizeObserver(entries => {\n          entries.forEach(entry => {\n            const run = () => {\n              this._measureElement(entry.target, entry);\n            };\n            this.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();\n          });\n        });\n      };\n      return {\n        disconnect: () => {\n          var _a;\n          (_a = get()) == null ? void 0 : _a.disconnect();\n          _ro = null;\n        },\n        observe: target => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.observe(target, {\n            box: \"border-box\"\n          });\n        },\n        unobserve: target => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.unobserve(target);\n        }\n      };\n    })();\n    this.range = null;\n    this.setOptions = opts2 => {\n      Object.entries(opts2).forEach(_ref3 => {\n        let [key, value] = _ref3;\n        if (typeof value === \"undefined\") delete opts2[key];\n      });\n      this.options = _objectSpread({\n        debug: false,\n        initialOffset: 0,\n        overscan: 1,\n        paddingStart: 0,\n        paddingEnd: 0,\n        scrollPaddingStart: 0,\n        scrollPaddingEnd: 0,\n        horizontal: false,\n        getItemKey: defaultKeyExtractor,\n        rangeExtractor: defaultRangeExtractor,\n        onChange: () => {},\n        measureElement,\n        initialRect: {\n          width: 0,\n          height: 0\n        },\n        scrollMargin: 0,\n        gap: 0,\n        indexAttribute: \"data-index\",\n        initialMeasurementsCache: [],\n        lanes: 1,\n        isScrollingResetDelay: 150,\n        enabled: true,\n        isRtl: false,\n        useScrollendEvent: false,\n        useAnimationFrameWithResizeObserver: false\n      }, opts2);\n    };\n    this.notify = sync => {\n      var _a, _b;\n      (_b = (_a = this.options).onChange) == null ? void 0 : _b.call(_a, this, sync);\n    };\n    this.maybeNotify = memo(() => {\n      this.calculateRange();\n      return [this.isScrolling, this.range ? this.range.startIndex : null, this.range ? this.range.endIndex : null];\n    }, isScrolling => {\n      this.notify(isScrolling);\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"maybeNotify\",\n      debug: () => this.options.debug,\n      initialDeps: [this.isScrolling, this.range ? this.range.startIndex : null, this.range ? this.range.endIndex : null]\n    });\n    this.cleanup = () => {\n      this.unsubs.filter(Boolean).forEach(d => d());\n      this.unsubs = [];\n      this.observer.disconnect();\n      this.scrollElement = null;\n      this.targetWindow = null;\n    };\n    this._didMount = () => {\n      return () => {\n        this.cleanup();\n      };\n    };\n    this._willUpdate = () => {\n      var _a;\n      const scrollElement = this.options.enabled ? this.options.getScrollElement() : null;\n      if (this.scrollElement !== scrollElement) {\n        this.cleanup();\n        if (!scrollElement) {\n          this.maybeNotify();\n          return;\n        }\n        this.scrollElement = scrollElement;\n        if (this.scrollElement && \"ownerDocument\" in this.scrollElement) {\n          this.targetWindow = this.scrollElement.ownerDocument.defaultView;\n        } else {\n          var _ref4;\n          this.targetWindow = (_ref4 = (_a = this.scrollElement) == null ? void 0 : _a.window) !== null && _ref4 !== void 0 ? _ref4 : null;\n        }\n        this.elementsCache.forEach(cached => {\n          this.observer.observe(cached);\n        });\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: void 0,\n          behavior: void 0\n        });\n        this.unsubs.push(this.options.observeElementRect(this, rect => {\n          this.scrollRect = rect;\n          this.maybeNotify();\n        }));\n        this.unsubs.push(this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0;\n          this.scrollDirection = isScrolling ? this.getScrollOffset() < offset ? \"forward\" : \"backward\" : null;\n          this.scrollOffset = offset;\n          this.isScrolling = isScrolling;\n          this.maybeNotify();\n        }));\n      }\n    };\n    this.getSize = () => {\n      var _this$scrollRect;\n      if (!this.options.enabled) {\n        this.scrollRect = null;\n        return 0;\n      }\n      this.scrollRect = (_this$scrollRect = this.scrollRect) !== null && _this$scrollRect !== void 0 ? _this$scrollRect : this.options.initialRect;\n      return this.scrollRect[this.options.horizontal ? \"width\" : \"height\"];\n    };\n    this.getScrollOffset = () => {\n      var _this$scrollOffset;\n      if (!this.options.enabled) {\n        this.scrollOffset = null;\n        return 0;\n      }\n      this.scrollOffset = (_this$scrollOffset = this.scrollOffset) !== null && _this$scrollOffset !== void 0 ? _this$scrollOffset : typeof this.options.initialOffset === \"function\" ? this.options.initialOffset() : this.options.initialOffset;\n      return this.scrollOffset;\n    };\n    this.getFurthestMeasurement = (measurements, index) => {\n      const furthestMeasurementsFound = /* @__PURE__ */new Map();\n      const furthestMeasurements = /* @__PURE__ */new Map();\n      for (let m = index - 1; m >= 0; m--) {\n        const measurement = measurements[m];\n        if (furthestMeasurementsFound.has(measurement.lane)) {\n          continue;\n        }\n        const previousFurthestMeasurement = furthestMeasurements.get(measurement.lane);\n        if (previousFurthestMeasurement == null || measurement.end > previousFurthestMeasurement.end) {\n          furthestMeasurements.set(measurement.lane, measurement);\n        } else if (measurement.end < previousFurthestMeasurement.end) {\n          furthestMeasurementsFound.set(measurement.lane, true);\n        }\n        if (furthestMeasurementsFound.size === this.options.lanes) {\n          break;\n        }\n      }\n      return furthestMeasurements.size === this.options.lanes ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n        if (a.end === b.end) {\n          return a.index - b.index;\n        }\n        return a.end - b.end;\n      })[0] : void 0;\n    };\n    this.getMeasurementOptions = memo(() => [this.options.count, this.options.paddingStart, this.options.scrollMargin, this.options.getItemKey, this.options.enabled], (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = [];\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled\n      };\n    }, {\n      key: false\n    });\n    this.getMeasurements = memo(() => [this.getMeasurementOptions(), this.itemSizeCache], (_ref5, itemSizeCache) => {\n      let {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled\n      } = _ref5;\n      if (!enabled) {\n        this.measurementsCache = [];\n        this.itemSizeCache.clear();\n        return [];\n      }\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache;\n        this.measurementsCache.forEach(item => {\n          this.itemSizeCache.set(item.key, item.size);\n        });\n      }\n      const min = this.pendingMeasuredCacheIndexes.length > 0 ? Math.min(...this.pendingMeasuredCacheIndexes) : 0;\n      this.pendingMeasuredCacheIndexes = [];\n      const measurements = this.measurementsCache.slice(0, min);\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i);\n        const furthestMeasurement = this.options.lanes === 1 ? measurements[i - 1] : this.getFurthestMeasurement(measurements, i);\n        const start = furthestMeasurement ? furthestMeasurement.end + this.options.gap : paddingStart + scrollMargin;\n        const measuredSize = itemSizeCache.get(key);\n        const size = typeof measuredSize === \"number\" ? measuredSize : this.options.estimateSize(i);\n        const end = start + size;\n        const lane = furthestMeasurement ? furthestMeasurement.lane : i % this.options.lanes;\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane\n        };\n      }\n      this.measurementsCache = measurements;\n      return measurements;\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"getMeasurements\",\n      debug: () => this.options.debug\n    });\n    this.calculateRange = memo(() => [this.getMeasurements(), this.getSize(), this.getScrollOffset(), this.options.lanes], (measurements, outerSize, scrollOffset, lanes) => {\n      return this.range = measurements.length > 0 && outerSize > 0 ? calculateRange({\n        measurements,\n        outerSize,\n        scrollOffset,\n        lanes\n      }) : null;\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"calculateRange\",\n      debug: () => this.options.debug\n    });\n    this.getVirtualIndexes = memo(() => {\n      let startIndex = null;\n      let endIndex = null;\n      const range = this.calculateRange();\n      if (range) {\n        startIndex = range.startIndex;\n        endIndex = range.endIndex;\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex]);\n      return [this.options.rangeExtractor, this.options.overscan, this.options.count, startIndex, endIndex];\n    }, (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null ? [] : rangeExtractor({\n        startIndex,\n        endIndex,\n        overscan,\n        count\n      });\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"getVirtualIndexes\",\n      debug: () => this.options.debug\n    });\n    this.indexFromElement = node => {\n      const attributeName = this.options.indexAttribute;\n      const indexStr = node.getAttribute(attributeName);\n      if (!indexStr) {\n        console.warn(\"Missing attribute name '\".concat(attributeName, \"={index}' on measured element.\"));\n        return -1;\n      }\n      return parseInt(indexStr, 10);\n    };\n    this._measureElement = (node, entry) => {\n      const index = this.indexFromElement(node);\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const key = item.key;\n      const prevNode = this.elementsCache.get(key);\n      if (prevNode !== node) {\n        if (prevNode) {\n          this.observer.unobserve(prevNode);\n        }\n        this.observer.observe(node);\n        this.elementsCache.set(key, node);\n      }\n      if (node.isConnected) {\n        this.resizeItem(index, this.options.measureElement(node, entry, this));\n      }\n    };\n    this.resizeItem = (index, size) => {\n      var _this$itemSizeCache$g;\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const itemSize = (_this$itemSizeCache$g = this.itemSizeCache.get(item.key)) !== null && _this$itemSizeCache$g !== void 0 ? _this$itemSizeCache$g : item.size;\n      const delta = size - itemSize;\n      if (delta !== 0) {\n        if (this.shouldAdjustScrollPositionOnItemSizeChange !== void 0 ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this) : this.scrollDirection === \"backward\" && item.start < this.getScrollOffset() + this.scrollAdjustments) {\n          if (process.env.NODE_ENV !== \"production\" && this.options.debug) {\n            console.info(\"correction\", delta);\n          }\n          this._scrollToOffset(this.getScrollOffset(), {\n            adjustments: this.scrollAdjustments += delta,\n            behavior: void 0\n          });\n        }\n        this.pendingMeasuredCacheIndexes.push(item.index);\n        this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size));\n        this.notify(false);\n      }\n    };\n    this.measureElement = node => {\n      if (!node) {\n        this.elementsCache.forEach((cached, key) => {\n          if (!cached.isConnected) {\n            this.observer.unobserve(cached);\n            this.elementsCache.delete(key);\n          }\n        });\n        return;\n      }\n      this._measureElement(node, void 0);\n    };\n    this.getVirtualItems = memo(() => [this.getVirtualIndexes(), this.getMeasurements()], (indexes, measurements) => {\n      const virtualItems = [];\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k];\n        const measurement = measurements[i];\n        virtualItems.push(measurement);\n      }\n      return virtualItems;\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"getVirtualItems\",\n      debug: () => this.options.debug\n    });\n    this.getVirtualItemForOffset = offset => {\n      const measurements = this.getMeasurements();\n      if (measurements.length === 0) {\n        return void 0;\n      }\n      return notUndefined(measurements[findNearestBinarySearch(0, measurements.length - 1, index => notUndefined(measurements[index]).start, offset)]);\n    };\n    this.getOffsetForAlignment = function (toOffset, align) {\n      let itemSize = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n      const size = _this.getSize();\n      const scrollOffset = _this.getScrollOffset();\n      if (align === \"auto\") {\n        align = toOffset >= scrollOffset + size ? \"end\" : \"start\";\n      }\n      if (align === \"center\") {\n        toOffset += (itemSize - size) / 2;\n      } else if (align === \"end\") {\n        toOffset -= size;\n      }\n      const maxOffset = _this.getTotalSize() - size;\n      return Math.max(Math.min(maxOffset, toOffset), 0);\n    };\n    this.getOffsetForIndex = function (index) {\n      let align = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"auto\";\n      index = Math.max(0, Math.min(index, _this.options.count - 1));\n      const item = _this.measurementsCache[index];\n      if (!item) {\n        return void 0;\n      }\n      const size = _this.getSize();\n      const scrollOffset = _this.getScrollOffset();\n      if (align === \"auto\") {\n        if (item.end >= scrollOffset + size - _this.options.scrollPaddingEnd) {\n          align = \"end\";\n        } else if (item.start <= scrollOffset + _this.options.scrollPaddingStart) {\n          align = \"start\";\n        } else {\n          return [scrollOffset, align];\n        }\n      }\n      const toOffset = align === \"end\" ? item.end + _this.options.scrollPaddingEnd : item.start - _this.options.scrollPaddingStart;\n      return [_this.getOffsetForAlignment(toOffset, align, item.size), align];\n    };\n    this.isDynamicMode = () => this.elementsCache.size > 0;\n    this.cancelScrollToIndex = () => {\n      if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n        this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId);\n        this.scrollToIndexTimeoutId = null;\n      }\n    };\n    this.scrollToOffset = function (toOffset) {\n      let {\n        align = \"start\",\n        behavior\n      } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      _this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && _this.isDynamicMode()) {\n        console.warn(\"The `smooth` scroll behavior is not fully supported with dynamic size.\");\n      }\n      _this._scrollToOffset(_this.getOffsetForAlignment(toOffset, align), {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.scrollToIndex = function (index) {\n      let {\n        align: initialAlign = \"auto\",\n        behavior\n      } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      index = Math.max(0, Math.min(index, _this.options.count - 1));\n      _this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && _this.isDynamicMode()) {\n        console.warn(\"The `smooth` scroll behavior is not fully supported with dynamic size.\");\n      }\n      const offsetAndAlign = _this.getOffsetForIndex(index, initialAlign);\n      if (!offsetAndAlign) return;\n      const [offset, align] = offsetAndAlign;\n      _this._scrollToOffset(offset, {\n        adjustments: void 0,\n        behavior\n      });\n      if (behavior !== \"smooth\" && _this.isDynamicMode() && _this.targetWindow) {\n        _this.scrollToIndexTimeoutId = _this.targetWindow.setTimeout(() => {\n          _this.scrollToIndexTimeoutId = null;\n          const elementInDOM = _this.elementsCache.has(_this.options.getItemKey(index));\n          if (elementInDOM) {\n            const result = _this.getOffsetForIndex(index, align);\n            if (!result) return;\n            const [latestOffset] = result;\n            const currentScrollOffset = _this.getScrollOffset();\n            if (!approxEqual(latestOffset, currentScrollOffset)) {\n              _this.scrollToIndex(index, {\n                align,\n                behavior\n              });\n            }\n          } else {\n            _this.scrollToIndex(index, {\n              align,\n              behavior\n            });\n          }\n        });\n      }\n    };\n    this.scrollBy = function (delta) {\n      let {\n        behavior\n      } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      _this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && _this.isDynamicMode()) {\n        console.warn(\"The `smooth` scroll behavior is not fully supported with dynamic size.\");\n      }\n      _this._scrollToOffset(_this.getScrollOffset() + delta, {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.getTotalSize = () => {\n      var _a;\n      const measurements = this.getMeasurements();\n      let end;\n      if (measurements.length === 0) {\n        end = this.options.paddingStart;\n      } else if (this.options.lanes === 1) {\n        var _ref6;\n        end = (_ref6 = (_a = measurements[measurements.length - 1]) == null ? void 0 : _a.end) !== null && _ref6 !== void 0 ? _ref6 : 0;\n      } else {\n        const endByLane = Array(this.options.lanes).fill(null);\n        let endIndex = measurements.length - 1;\n        while (endIndex >= 0 && endByLane.some(val => val === null)) {\n          const item = measurements[endIndex];\n          if (endByLane[item.lane] === null) {\n            endByLane[item.lane] = item.end;\n          }\n          endIndex--;\n        }\n        end = Math.max(...endByLane.filter(val => val !== null));\n      }\n      return Math.max(end - this.options.scrollMargin + this.options.paddingEnd, 0);\n    };\n    this._scrollToOffset = (offset, _ref7) => {\n      let {\n        adjustments,\n        behavior\n      } = _ref7;\n      this.options.scrollToFn(offset, {\n        behavior,\n        adjustments\n      }, this);\n    };\n    this.measure = () => {\n      this.itemSizeCache = /* @__PURE__ */new Map();\n      this.notify(false);\n    };\n    this.setOptions(opts);\n  }\n}\nconst findNearestBinarySearch = (low, high, getCurrentValue, value) => {\n  while (low <= high) {\n    const middle = (low + high) / 2 | 0;\n    const currentValue = getCurrentValue(middle);\n    if (currentValue < value) {\n      low = middle + 1;\n    } else if (currentValue > value) {\n      high = middle - 1;\n    } else {\n      return middle;\n    }\n  }\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\nfunction calculateRange(_ref8) {\n  let {\n    measurements,\n    outerSize,\n    scrollOffset,\n    lanes\n  } = _ref8;\n  const lastIndex = measurements.length - 1;\n  const getOffset = index => measurements[index].start;\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex\n    };\n  }\n  let startIndex = findNearestBinarySearch(0, lastIndex, getOffset, scrollOffset);\n  let endIndex = startIndex;\n  if (lanes === 1) {\n    while (endIndex < lastIndex && measurements[endIndex].end < scrollOffset + outerSize) {\n      endIndex++;\n    }\n  } else if (lanes > 1) {\n    const endPerLane = Array(lanes).fill(0);\n    while (endIndex < lastIndex && endPerLane.some(pos => pos < scrollOffset + outerSize)) {\n      const item = measurements[endIndex];\n      endPerLane[item.lane] = item.end;\n      endIndex++;\n    }\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize);\n    while (startIndex >= 0 && startPerLane.some(pos => pos >= scrollOffset)) {\n      const item = measurements[startIndex];\n      startPerLane[item.lane] = item.start;\n      startIndex--;\n    }\n    startIndex = Math.max(0, startIndex - startIndex % lanes);\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - endIndex % lanes));\n  }\n  return {\n    startIndex,\n    endIndex\n  };\n}\nexport { Virtualizer, approxEqual, debounce, defaultKeyExtractor, defaultRangeExtractor, elementScroll, measureElement, memo, notUndefined, observeElementOffset, observeElementRect, observeWindowOffset, observeWindowRect, windowScroll };", "map": {"version": 3, "names": ["getRect", "element", "offsetWidth", "offsetHeight", "width", "height", "defaultKeyExtractor", "index", "defaultRangeExtractor", "range", "start", "Math", "max", "startIndex", "overscan", "end", "min", "endIndex", "count", "arr", "i", "push", "observeElementRect", "instance", "cb", "scrollElement", "targetWindow", "handler", "rect", "round", "ResizeObserver", "observer", "entries", "run", "entry", "borderBoxSize", "box", "inlineSize", "blockSize", "options", "useAnimationFrameWithResizeObserver", "requestAnimationFrame", "observe", "unobserve", "addEventListenerOptions", "passive", "observeWindowRect", "innerWidth", "innerHeight", "addEventListener", "removeEventListener", "supportsScrollend", "window", "observeElementOffset", "offset", "fallback", "useScrollendEvent", "debounce", "isScrollingResetDelay", "createHandler", "isScrolling", "horizontal", "isRtl", "end<PERSON><PERSON><PERSON>", "registerScrollendEvent", "observeWindowOffset", "measureElement", "size", "windowScroll", "_ref", "adjustments", "behavior", "toOffset", "_b", "_a", "scrollTo", "call", "elementScroll", "_ref2", "Virtualizer", "constructor", "opts", "_this", "unsubs", "scrollToIndexTimeoutId", "measurementsCache", "itemSizeCache", "Map", "pendingMeasuredCacheIndexes", "scrollRect", "scrollOffset", "scrollDirection", "scrollAdjustments", "elementsCache", "_ro", "get", "for<PERSON>ach", "_measureElement", "target", "disconnect", "setOptions", "opts2", "Object", "_ref3", "key", "value", "_objectSpread", "debug", "initialOffset", "paddingStart", "paddingEnd", "scrollPaddingStart", "scrollPaddingEnd", "getItemKey", "rangeExtractor", "onChange", "initialRect", "scrollMargin", "gap", "indexAttribute", "initialMeasurementsCache", "lanes", "enabled", "notify", "sync", "maybeNotify", "memo", "calculateRange", "process", "env", "NODE_ENV", "initialDeps", "cleanup", "filter", "Boolean", "d", "_didMount", "_willUpdate", "getScrollElement", "ownerDocument", "defaultView", "_ref4", "cached", "_scrollToOffset", "getScrollOffset", "getSize", "_this$scrollRect", "_this$scrollOffset", "getFurthestMeasurement", "measurements", "furthestMeasurementsFound", "furthestMeasurements", "m", "measurement", "has", "lane", "previousFurthestMeasurement", "set", "Array", "from", "values", "sort", "a", "b", "getMeasurementOptions", "getMeasurements", "_ref5", "clear", "length", "item", "slice", "furthestMeasurement", "measuredSize", "estimateSize", "outerSize", "getVirtualIndexes", "updateDeps", "indexFromElement", "node", "attributeName", "indexStr", "getAttribute", "console", "warn", "concat", "parseInt", "prevNode", "isConnected", "resizeItem", "_this$itemSizeCache$g", "itemSize", "delta", "shouldAdjustScrollPositionOnItemSizeChange", "info", "delete", "getVirtualItems", "indexes", "virtualItems", "k", "len", "getVirtualItemForOffset", "notUndefined", "findNearestBinarySearch", "getOffsetForAlignment", "align", "arguments", "undefined", "maxOffset", "getTotalSize", "getOffsetForIndex", "isDynamicMode", "cancelScrollToIndex", "clearTimeout", "scrollToOffset", "scrollToIndex", "initialAlign", "offsetAndAlign", "setTimeout", "elementInDOM", "result", "latestOffset", "currentScrollOffset", "approxEqual", "scrollBy", "_ref6", "endByLane", "fill", "some", "val", "_ref7", "scrollToFn", "measure", "low", "high", "getCurrentValue", "middle", "currentValue", "_ref8", "lastIndex", "getOffset", "endPerLane", "pos", "startPerLane"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@tanstack\\virtual-core\\src\\index.ts"], "sourcesContent": ["import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nconst getRect = (element: HTMLElement): Rect => {\n  const { offsetWidth, offsetHeight } = element\n  return { width: offsetWidth, height: offsetHeight }\n}\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(getRect(element as unknown as HTMLElement))\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const run = () => {\n      const entry = entries[0]\n      if (entry?.borderBoxSize) {\n        const box = entry.borderBoxSize[0]\n        if (box) {\n          handler({ width: box.inlineSize, height: box.blockSize })\n          return\n        }\n      }\n      handler(getRect(element as unknown as HTMLElement))\n    }\n\n    instance.options.useAnimationFrameWithResizeObserver\n      ? requestAnimationFrame(run)\n      : run()\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n\n  return (element as unknown as HTMLElement)[\n    instance.options.horizontal ? 'offsetWidth' : 'offsetHeight'\n  ]\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n  useAnimationFrameWithResizeObserver?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  private scrollToIndexTimeoutId: number | null = null\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          const run = () => {\n            this._measureElement(entry.target as TItemElement, entry)\n          }\n          this.options.useAnimationFrameWithResizeObserver\n            ? requestAnimationFrame(run)\n            : run()\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: false,\n      useAnimationFrameWithResizeObserver: false,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [\n      this.getMeasurements(),\n      this.getSize(),\n      this.getScrollOffset(),\n      this.options.lanes,\n    ],\n    (measurements, outerSize, scrollOffset, lanes) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n              lanes,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex])\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : this.scrollDirection === 'backward' &&\n            item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getVirtualIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (\n    toOffset: number,\n    align: ScrollAlignment,\n    itemSize = 0,\n  ) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      align = toOffset >= scrollOffset + size ? 'end' : 'start'\n    }\n\n    if (align === 'center') {\n      // When aligning to a particular item (e.g. with scrollToIndex),\n      // adjust offset by the size of the item to center on the item\n      toOffset += (itemSize - size) / 2\n    } else if (align === 'end') {\n      toOffset -= size\n    }\n\n    const maxOffset = this.getTotalSize() - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const toOffset =\n      align === 'end'\n        ? item.end + this.options.scrollPaddingEnd\n        : item.start - this.options.scrollPaddingStart\n\n    return [\n      this.getOffsetForAlignment(toOffset, align, item.size),\n      align,\n    ] as const\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  private cancelScrollToIndex = () => {\n    if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n      this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId)\n      this.scrollToIndexTimeoutId = null\n    }\n  }\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    const offsetAndAlign = this.getOffsetForIndex(index, initialAlign)\n    if (!offsetAndAlign) return\n\n    const [offset, align] = offsetAndAlign\n\n    this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n    if (behavior !== 'smooth' && this.isDynamicMode() && this.targetWindow) {\n      this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n        this.scrollToIndexTimeoutId = null\n\n        const elementInDOM = this.elementsCache.has(\n          this.options.getItemKey(index),\n        )\n\n        if (elementInDOM) {\n          const result = this.getOffsetForIndex(index, align)\n          if (!result) return\n          const [latestOffset] = result\n\n          const currentScrollOffset = this.getScrollOffset()\n          if (!approxEqual(latestOffset, currentScrollOffset)) {\n            this.scrollToIndex(index, { align, behavior })\n          }\n        } else {\n          this.scrollToIndex(index, { align, behavior })\n        }\n      })\n    }\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    // If there is only one lane, use the last measurement's end\n    // Otherwise find the maximum end value among all measurements\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else if (this.options.lanes === 1) {\n      end = measurements[measurements.length - 1]?.end ?? 0\n    } else {\n      const endByLane = Array<number | null>(this.options.lanes).fill(null)\n      let endIndex = measurements.length - 1\n      while (endIndex >= 0 && endByLane.some((val) => val === null)) {\n        const item = measurements[endIndex]!\n        if (endByLane[item.lane] === null) {\n          endByLane[item.lane] = item.end\n        }\n\n        endIndex--\n      }\n\n      end = Math.max(...endByLane.filter((val): val is number => val !== null))\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n  lanes: number\n}) {\n  const lastIndex = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  // handle case when item count is less than or equal to lanes\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex,\n    }\n  }\n\n  let startIndex = findNearestBinarySearch(\n    0,\n    lastIndex,\n    getOffset,\n    scrollOffset,\n  )\n  let endIndex = startIndex\n\n  if (lanes === 1) {\n    while (\n      endIndex < lastIndex &&\n      measurements[endIndex]!.end < scrollOffset + outerSize\n    ) {\n      endIndex++\n    }\n  } else if (lanes > 1) {\n    // Expand forward until we include the visible items from all lanes\n    // which are closer to the end of the virtualizer window\n    const endPerLane = Array(lanes).fill(0)\n    while (\n      endIndex < lastIndex &&\n      endPerLane.some((pos) => pos < scrollOffset + outerSize)\n    ) {\n      const item = measurements[endIndex]!\n      endPerLane[item.lane] = item.end\n      endIndex++\n    }\n\n    // Expand backward until we include all lanes' visible items\n    // closer to the top\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize)\n    while (startIndex >= 0 && startPerLane.some((pos) => pos >= scrollOffset)) {\n      const item = measurements[startIndex]!\n      startPerLane[item.lane] = item.start\n      startIndex--\n    }\n\n    // Align startIndex to the beginning of its lane\n    startIndex = Math.max(0, startIndex - (startIndex % lanes))\n    // Align endIndex to the end of its lane\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - (endIndex % lanes)))\n  }\n\n  return { startIndex, endIndex }\n}\n"], "mappings": ";;AA8CA,MAAMA,OAAA,GAAWC,OAAA,IAA+B;EACxC;IAAEC,WAAA;IAAaC;EAAA,IAAiBF,OAAA;EACtC,OAAO;IAAEG,KAAA,EAAOF,WAAA;IAAaG,MAAA,EAAQF;EAAa;AACpD;AAEa,MAAAG,mBAAA,GAAuBC,KAAA,IAAkBA,KAAA;AAEzC,MAAAC,qBAAA,GAAyBC,KAAA,IAAiB;EACrD,MAAMC,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAIH,KAAA,CAAMI,UAAA,GAAaJ,KAAA,CAAMK,QAAA,EAAU,CAAC;EACrD,MAAAC,GAAA,GAAMJ,IAAA,CAAKK,GAAA,CAAIP,KAAA,CAAMQ,QAAA,GAAWR,KAAA,CAAMK,QAAA,EAAUL,KAAA,CAAMS,KAAA,GAAQ,CAAC;EAErE,MAAMC,GAAA,GAAM,EAAC;EAEb,SAASC,CAAA,GAAIV,KAAA,EAAOU,CAAA,IAAKL,GAAA,EAAKK,CAAA,IAAK;IACjCD,GAAA,CAAIE,IAAA,CAAKD,CAAC;EAAA;EAGL,OAAAD,GAAA;AACT;AAEa,MAAAG,kBAAA,GAAqBA,CAChCC,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAEF,MAAMyB,YAAA,GAAeH,QAAA,CAASG,YAAA;EAC9B,IAAI,CAACA,YAAA,EAAc;IACjB;EAAA;EAGI,MAAAC,OAAA,GAAWC,IAAA,IAAe;IACxB;MAAExB,KAAA;MAAOC;IAAA,IAAWuB,IAAA;IACvBJ,EAAA;MAAEpB,KAAA,EAAOO,IAAA,CAAKkB,KAAA,CAAMzB,KAAK;MAAGC,MAAA,EAAQM,IAAA,CAAKkB,KAAA,CAAMxB,MAAM;IAAA,CAAG;EAC7D;EAEQsB,OAAA,CAAA3B,OAAA,CAAQC,OAAiC,CAAC;EAE9C,KAACyB,YAAA,CAAaI,cAAA,EAAgB;IAChC,OAAO,MAAM,CAAC;EAAA;EAGhB,MAAMC,QAAA,GAAW,IAAIL,YAAA,CAAaI,cAAA,CAAgBE,OAAA,IAAY;IAC5D,MAAMC,GAAA,GAAMA,CAAA,KAAM;MACV,MAAAC,KAAA,GAAQF,OAAA,CAAQ,CAAC;MACvB,IAAIE,KAAA,oBAAAA,KAAA,CAAOC,aAAA,EAAe;QAClB,MAAAC,GAAA,GAAMF,KAAA,CAAMC,aAAA,CAAc,CAAC;QACjC,IAAIC,GAAA,EAAK;UACPT,OAAA,CAAQ;YAAEvB,KAAA,EAAOgC,GAAA,CAAIC,UAAA;YAAYhC,MAAA,EAAQ+B,GAAA,CAAIE;UAAA,CAAW;UACxD;QAAA;MACF;MAEMX,OAAA,CAAA3B,OAAA,CAAQC,OAAiC,CAAC;IACpD;IAEAsB,QAAA,CAASgB,OAAA,CAAQC,mCAAA,GACbC,qBAAA,CAAsBR,GAAG,IACzBA,GAAA,CAAI;EAAA,CACT;EAEDF,QAAA,CAASW,OAAA,CAAQzC,OAAA,EAAS;IAAEmC,GAAA,EAAK;EAAA,CAAc;EAE/C,OAAO,MAAM;IACXL,QAAA,CAASY,SAAA,CAAU1C,OAAO;EAC5B;AACF;AAEA,MAAM2C,uBAAA,GAA0B;EAC9BC,OAAA,EAAS;AACX;AAEa,MAAAC,iBAAA,GAAoBA,CAC/BvB,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAGF,MAAM0B,OAAA,GAAUA,CAAA,KAAM;IACpBH,EAAA,CAAG;MAAEpB,KAAA,EAAOH,OAAA,CAAQ8C,UAAA;MAAY1C,MAAA,EAAQJ,OAAA,CAAQ+C;IAAA,CAAa;EAC/D;EACQrB,OAAA;EAEA1B,OAAA,CAAAgD,gBAAA,CAAiB,UAAUtB,OAAA,EAASiB,uBAAuB;EAEnE,OAAO,MAAM;IACH3C,OAAA,CAAAiD,mBAAA,CAAoB,UAAUvB,OAAO;EAC/C;AACF;AAEA,MAAMwB,iBAAA,GACJ,OAAOC,MAAA,IAAU,cAAc,OAAO,iBAAiBA,MAAA;AAI5C,MAAAC,oBAAA,GAAuBA,CAClC9B,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAEF,MAAMyB,YAAA,GAAeH,QAAA,CAASG,YAAA;EAC9B,IAAI,CAACA,YAAA,EAAc;IACjB;EAAA;EAGF,IAAI4B,MAAA,GAAS;EACb,MAAMC,QAAA,GACJhC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA,GAClC,MAAM,SACNM,QAAA,CACE/B,YAAA,EACA,MAAM;IACJF,EAAA,CAAG8B,MAAA,EAAQ,KAAK;EAClB,GACA/B,QAAA,CAASgB,OAAA,CAAQmB,qBACnB;EAEA,MAAAC,aAAA,GAAiBC,WAAA,IAAyB,MAAM;IACpD,MAAM;MAAEC,UAAA;MAAYC;IAAM,IAAIvC,QAAA,CAASgB,OAAA;IAC9Be,MAAA,GAAAO,UAAA,GACL5D,OAAA,CAAQ,YAAY,KAAM6D,KAAA,IAAS,MAAO,KAC1C7D,OAAA,CAAQ,WAAW;IACdsD,QAAA;IACT/B,EAAA,CAAG8B,MAAA,EAAQM,WAAW;EACxB;EACM,MAAAjC,OAAA,GAAUgC,aAAA,CAAc,IAAI;EAC5B,MAAAI,UAAA,GAAaJ,aAAA,CAAc,KAAK;EAC3BI,UAAA;EAEH9D,OAAA,CAAAgD,gBAAA,CAAiB,UAAUtB,OAAA,EAASiB,uBAAuB;EAC7D,MAAAoB,sBAAA,GACJzC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA;EACxC,IAAIa,sBAAA,EAAwB;IAClB/D,OAAA,CAAAgD,gBAAA,CAAiB,aAAac,UAAA,EAAYnB,uBAAuB;EAAA;EAE3E,OAAO,MAAM;IACH3C,OAAA,CAAAiD,mBAAA,CAAoB,UAAUvB,OAAO;IAC7C,IAAIqC,sBAAA,EAAwB;MAClB/D,OAAA,CAAAiD,mBAAA,CAAoB,aAAaa,UAAU;IAAA;EAEvD;AACF;AAEa,MAAAE,mBAAA,GAAsBA,CACjC1C,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAEF,MAAMyB,YAAA,GAAeH,QAAA,CAASG,YAAA;EAC9B,IAAI,CAACA,YAAA,EAAc;IACjB;EAAA;EAGF,IAAI4B,MAAA,GAAS;EACb,MAAMC,QAAA,GACJhC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA,GAClC,MAAM,SACNM,QAAA,CACE/B,YAAA,EACA,MAAM;IACJF,EAAA,CAAG8B,MAAA,EAAQ,KAAK;EAClB,GACA/B,QAAA,CAASgB,OAAA,CAAQmB,qBACnB;EAEA,MAAAC,aAAA,GAAiBC,WAAA,IAAyB,MAAM;IACpDN,MAAA,GAASrD,OAAA,CAAQsB,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,YAAY,SAAS;IAC3DN,QAAA;IACT/B,EAAA,CAAG8B,MAAA,EAAQM,WAAW;EACxB;EACM,MAAAjC,OAAA,GAAUgC,aAAA,CAAc,IAAI;EAC5B,MAAAI,UAAA,GAAaJ,aAAA,CAAc,KAAK;EAC3BI,UAAA;EAEH9D,OAAA,CAAAgD,gBAAA,CAAiB,UAAUtB,OAAA,EAASiB,uBAAuB;EAC7D,MAAAoB,sBAAA,GACJzC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA;EACxC,IAAIa,sBAAA,EAAwB;IAClB/D,OAAA,CAAAgD,gBAAA,CAAiB,aAAac,UAAA,EAAYnB,uBAAuB;EAAA;EAE3E,OAAO,MAAM;IACH3C,OAAA,CAAAiD,mBAAA,CAAoB,UAAUvB,OAAO;IAC7C,IAAIqC,sBAAA,EAAwB;MAClB/D,OAAA,CAAAiD,mBAAA,CAAoB,aAAaa,UAAU;IAAA;EAEvD;AACF;AAEO,MAAMG,cAAA,GAAiBA,CAC5BjE,OAAA,EACAiC,KAAA,EACAX,QAAA,KACG;EACH,IAAIW,KAAA,oBAAAA,KAAA,CAAOC,aAAA,EAAe;IAClB,MAAAC,GAAA,GAAMF,KAAA,CAAMC,aAAA,CAAc,CAAC;IACjC,IAAIC,GAAA,EAAK;MACP,MAAM+B,IAAA,GAAOxD,IAAA,CAAKkB,KAAA,CAChBO,GAAA,CAAIb,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,eAAe,WAAW,CAC9D;MACO,OAAAM,IAAA;IAAA;EACT;EAGF,OAAQlE,OAAA,CACNsB,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,gBAAgB,cAChD;AACF;AAEa,MAAAO,YAAA,GAAeA,CAC1Bd,MAAA,EAAAe,IAAA,EAKA9C,QAAA,KACG;EAAA,IALH;IACE+C,WAAA,GAAc;IACdC;EACF,IAAAF,IAAA;;EAGA,MAAMG,QAAA,GAAWlB,MAAA,GAASgB,WAAA;EAE1B,CAAAG,EAAA,IAAAC,EAAA,GAAAnD,QAAA,CAASE,aAAA,KAAT,gBAAAiD,EAAA,CAAwBC,QAAA,KAAxB,gBAAAF,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAmC;IACjC,CAACnD,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,SAAS,KAAK,GAAGW,QAAA;IAChDD;EAAA;AAEJ;AAEa,MAAAM,aAAA,GAAgBA,CAC3BvB,MAAA,EAAAwB,KAAA,EAKAvD,QAAA,KACG;EAAA,IALH;IACE+C,WAAA,GAAc;IACdC;EACF,IAAAO,KAAA;;EAGA,MAAMN,QAAA,GAAWlB,MAAA,GAASgB,WAAA;EAE1B,CAAAG,EAAA,IAAAC,EAAA,GAAAnD,QAAA,CAASE,aAAA,KAAT,gBAAAiD,EAAA,CAAwBC,QAAA,KAAxB,gBAAAF,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAmC;IACjC,CAACnD,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,SAAS,KAAK,GAAGW,QAAA;IAChDD;EAAA;AAEJ;AA0DO,MAAMQ,WAAA,CAGX;EA0DAC,YAAYC,IAAA,EAAwD;IAAA,IAAAC,KAAA;IAzDpE,KAAQC,MAAA,GAAqC,EAAC;IAEP,KAAA1D,aAAA;IACa,KAAAC,YAAA;IACtC,KAAAkC,WAAA;IACd,KAAQwB,sBAAA,GAAwC;IAChD,KAAAC,iBAAA,GAAwC,EAAC;IACjC,KAAAC,aAAA,sBAAoBC,GAAA,CAAiB;IAC7C,KAAQC,2BAAA,GAA6C,EAAC;IAC5B,KAAAC,UAAA;IACI,KAAAC,YAAA;IACY,KAAAC,eAAA;IAC1C,KAAQC,iBAAA,GAAoB;IAQ5B,KAAAC,aAAA,sBAAoBN,GAAA,CAAuB;IAC3C,KAAQxD,QAAA,GAAkB;MACxB,IAAI+D,GAAA,GAA6B;MAEjC,MAAMC,GAAA,GAAMA,CAAA,KAAM;QAChB,IAAID,GAAA,EAAK;UACA,OAAAA,GAAA;QAAA;QAGT,IAAI,CAAC,KAAKpE,YAAA,IAAgB,CAAC,KAAKA,YAAA,CAAaI,cAAA,EAAgB;UACpD;QAAA;QAGT,OAAQgE,GAAA,GAAM,IAAI,KAAKpE,YAAA,CAAaI,cAAA,CAAgBE,OAAA,IAAY;UACtDA,OAAA,CAAAgE,OAAA,CAAS9D,KAAA,IAAU;YACzB,MAAMD,GAAA,GAAMA,CAAA,KAAM;cACX,KAAAgE,eAAA,CAAgB/D,KAAA,CAAMgE,MAAA,EAAwBhE,KAAK;YAC1D;YACA,KAAKK,OAAA,CAAQC,mCAAA,GACTC,qBAAA,CAAsBR,GAAG,IACzBA,GAAA,CAAI;UAAA,CACT;QAAA,CACF;MACH;MAEO;QACLkE,UAAA,EAAYA,CAAA,KAAM;;UAChB,CAAAzB,EAAA,GAAAqB,GAAA,uBAAArB,EAAA,CAAOyB,UAAA;UACDL,GAAA;QACR;QACApD,OAAA,EAAUwD,MAAA;;UACR,QAAAxB,EAAA,GAAAqB,GAAA,CAAI,MAAJ,gBAAArB,EAAA,CAAOhC,OAAA,CAAQwD,MAAA,EAAQ;YAAE9D,GAAA,EAAK;UAAA;;QAChCO,SAAA,EAAYuD,MAAA;;UAAoB,QAAAxB,EAAA,GAAAqB,GAAA,CAAI,MAAJ,gBAAArB,EAAA,CAAO/B,SAAA,CAAUuD,MAAA;QAAA;MACnD;IAAA,GACC;IACsD,KAAAzF,KAAA;IAMzD,KAAA2F,UAAA,GAAcC,KAAA,IAA2D;MAChEC,MAAA,CAAAtE,OAAA,CAAQqE,KAAI,EAAEL,OAAA,CAAQO,KAAA,IAAkB;QAAA,IAAjB,CAACC,GAAA,EAAKC,KAAK,IAAAF,KAAA;QACvC,IAAI,OAAOE,KAAA,KAAU,aAAa,OAAQJ,KAAA,CAAaG,GAAG;MAAA,CAC3D;MAED,KAAKjE,OAAA,GAAAmE,aAAA;QACHC,KAAA,EAAO;QACPC,aAAA,EAAe;QACf9F,QAAA,EAAU;QACV+F,YAAA,EAAc;QACdC,UAAA,EAAY;QACZC,kBAAA,EAAoB;QACpBC,gBAAA,EAAkB;QAClBnD,UAAA,EAAY;QACZoD,UAAA,EAAY3G,mBAAA;QACZ4G,cAAA,EAAgB1G,qBAAA;QAChB2G,QAAA,EAAUA,CAAA,KAAM,CAAC;QACjBjD,cAAA;QACAkD,WAAA,EAAa;UAAEhH,KAAA,EAAO;UAAGC,MAAA,EAAQ;QAAE;QACnCgH,YAAA,EAAc;QACdC,GAAA,EAAK;QACLC,cAAA,EAAgB;QAChBC,wBAAA,EAA0B,EAAC;QAC3BC,KAAA,EAAO;QACP/D,qBAAA,EAAuB;QACvBgE,OAAA,EAAS;QACT5D,KAAA,EAAO;QACPN,iBAAA,EAAmB;QACnBhB,mCAAA,EAAqC;MAAA,GAClC6D,KAAA,CACL;IACF;IAEQ,KAAAsB,MAAA,GAAUC,IAAA,IAAkB;;MAC7B,CAAAnD,EAAA,IAAAC,EAAA,QAAAnC,OAAA,EAAQ4E,QAAA,KAAR,gBAAA1C,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAmB,MAAMkD,IAAA;IAChC;IAEA,KAAQC,WAAA,GAAcC,IAAA,CACpB,MAAM;MACJ,KAAKC,cAAA,CAAe;MAEb,QACL,KAAKnE,WAAA,EACL,KAAKnD,KAAA,GAAQ,KAAKA,KAAA,CAAMI,UAAA,GAAa,MACrC,KAAKJ,KAAA,GAAQ,KAAKA,KAAA,CAAMQ,QAAA,GAAW,KACrC;IACF,GACC2C,WAAA,IAAgB;MACf,KAAK+D,MAAA,CAAO/D,WAAW;IACzB,GACA;MACE4C,GAAA,EAAKwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAKpE,OAAA,CAAQoE,KAAA;MAC1BwB,WAAA,EAAa,CACX,KAAKvE,WAAA,EACL,KAAKnD,KAAA,GAAQ,KAAKA,KAAA,CAAMI,UAAA,GAAa,MACrC,KAAKJ,KAAA,GAAQ,KAAKA,KAAA,CAAMQ,QAAA,GAAW;IACrC,CAEJ;IAEA,KAAQmH,OAAA,GAAU,MAAM;MACjB,KAAAjD,MAAA,CAAOkD,MAAA,CAAOC,OAAO,EAAEtC,OAAA,CAASuC,CAAA,IAAMA,CAAA,EAAI;MAC/C,KAAKpD,MAAA,GAAS,EAAC;MACf,KAAKpD,QAAA,CAASoE,UAAA,CAAW;MACzB,KAAK1E,aAAA,GAAgB;MACrB,KAAKC,YAAA,GAAe;IACtB;IAEA,KAAA8G,SAAA,GAAY,MAAM;MAChB,OAAO,MAAM;QACX,KAAKJ,OAAA,CAAQ;MACf;IACF;IAEA,KAAAK,WAAA,GAAc,MAAM;;MAClB,MAAMhH,aAAA,GAAgB,KAAKc,OAAA,CAAQmF,OAAA,GAC/B,KAAKnF,OAAA,CAAQmG,gBAAA,KACb;MAEA,SAAKjH,aAAA,KAAkBA,aAAA,EAAe;QACxC,KAAK2G,OAAA,CAAQ;QAEb,IAAI,CAAC3G,aAAA,EAAe;UAClB,KAAKoG,WAAA,CAAY;UACjB;QAAA;QAGF,KAAKpG,aAAA,GAAgBA,aAAA;QAErB,IAAI,KAAKA,aAAA,IAAiB,mBAAmB,KAAKA,aAAA,EAAe;UAC1D,KAAAC,YAAA,GAAe,KAAKD,aAAA,CAAckH,aAAA,CAAcC,WAAA;QAAA,OAChD;UAAA,IAAAC,KAAA;UACA,KAAAnH,YAAA,IAAAmH,KAAA,IAAenE,EAAA,QAAKjD,aAAA,KAAL,gBAAAiD,EAAA,CAAoBtB,MAAA,cAAAyF,KAAA,cAAAA,KAAA,GAAU;QAAA;QAG/C,KAAAhD,aAAA,CAAcG,OAAA,CAAS8C,MAAA,IAAW;UAChC,KAAA/G,QAAA,CAASW,OAAA,CAAQoG,MAAM;QAAA,CAC7B;QAEI,KAAAC,eAAA,CAAgB,KAAKC,eAAA,IAAmB;UAC3C1E,WAAA,EAAa;UACbC,QAAA,EAAU;QAAA,CACX;QAED,KAAKY,MAAA,CAAO9D,IAAA,CACV,KAAKkB,OAAA,CAAQjB,kBAAA,CAAmB,MAAOM,IAAA,IAAS;UAC9C,KAAK6D,UAAA,GAAa7D,IAAA;UAClB,KAAKiG,WAAA,CAAY;QAClB,EACH;QAEA,KAAK1C,MAAA,CAAO9D,IAAA,CACV,KAAKkB,OAAA,CAAQc,oBAAA,CAAqB,MAAM,CAACC,MAAA,EAAQM,WAAA,KAAgB;UAC/D,KAAKgC,iBAAA,GAAoB;UACzB,KAAKD,eAAA,GAAkB/B,WAAA,GACnB,KAAKoF,eAAA,KAAoB1F,MAAA,GACvB,YACA,aACF;UACJ,KAAKoC,YAAA,GAAepC,MAAA;UACpB,KAAKM,WAAA,GAAcA,WAAA;UAEnB,KAAKiE,WAAA,CAAY;QAClB,EACH;MAAA;IAEJ;IAEA,KAAQoB,OAAA,GAAU,MAAM;MAAA,IAAAC,gBAAA;MAClB,KAAC,KAAK3G,OAAA,CAAQmF,OAAA,EAAS;QACzB,KAAKjC,UAAA,GAAa;QACX;MAAA;MAGT,KAAKA,UAAA,IAAAyD,gBAAA,GAAa,KAAKzD,UAAA,cAAAyD,gBAAA,cAAAA,gBAAA,GAAc,KAAK3G,OAAA,CAAQ6E,WAAA;MAElD,OAAO,KAAK3B,UAAA,CAAW,KAAKlD,OAAA,CAAQsB,UAAA,GAAa,UAAU,QAAQ;IACrE;IAEA,KAAQmF,eAAA,GAAkB,MAAM;MAAA,IAAAG,kBAAA;MAC1B,KAAC,KAAK5G,OAAA,CAAQmF,OAAA,EAAS;QACzB,KAAKhC,YAAA,GAAe;QACb;MAAA;MAGT,KAAKA,YAAA,IAAAyD,kBAAA,GACH,KAAKzD,YAAA,cAAAyD,kBAAA,cAAAA,kBAAA,GACJ,OAAO,KAAK5G,OAAA,CAAQqE,aAAA,KAAkB,aACnC,KAAKrE,OAAA,CAAQqE,aAAA,CAAc,IAC3B,KAAKrE,OAAA,CAAQqE,aAAA;MAEnB,OAAO,KAAKlB,YAAA;IACd;IAEQ,KAAA0D,sBAAA,GAAyB,CAC/BC,YAAA,EACA9I,KAAA,KACG;MACG,MAAA+I,yBAAA,sBAAgC/D,GAAA,CAAkB;MAClD,MAAAgE,oBAAA,sBAA2BhE,GAAA,CAAyB;MAC1D,SAASiE,CAAA,GAAIjJ,KAAA,GAAQ,GAAGiJ,CAAA,IAAK,GAAGA,CAAA,IAAK;QAC7B,MAAAC,WAAA,GAAcJ,YAAA,CAAaG,CAAC;QAElC,IAAIF,yBAAA,CAA0BI,GAAA,CAAID,WAAA,CAAYE,IAAI,GAAG;UACnD;QAAA;QAGF,MAAMC,2BAAA,GAA8BL,oBAAA,CAAqBxD,GAAA,CACvD0D,WAAA,CAAYE,IACd;QACA,IACEC,2BAAA,IAA+B,QAC/BH,WAAA,CAAY1I,GAAA,GAAM6I,2BAAA,CAA4B7I,GAAA,EAC9C;UACqBwI,oBAAA,CAAAM,GAAA,CAAIJ,WAAA,CAAYE,IAAA,EAAMF,WAAW;QAC7C,WAAAA,WAAA,CAAY1I,GAAA,GAAM6I,2BAAA,CAA4B7I,GAAA,EAAK;UAClCuI,yBAAA,CAAAO,GAAA,CAAIJ,WAAA,CAAYE,IAAA,EAAM,IAAI;QAAA;QAGtD,IAAIL,yBAAA,CAA0BnF,IAAA,KAAS,KAAK5B,OAAA,CAAQkF,KAAA,EAAO;UACzD;QAAA;MACF;MAGF,OAAO8B,oBAAA,CAAqBpF,IAAA,KAAS,KAAK5B,OAAA,CAAQkF,KAAA,GAC9CqC,KAAA,CAAMC,IAAA,CAAKR,oBAAA,CAAqBS,MAAA,EAAQ,EAAEC,IAAA,CAAK,CAACC,CAAA,EAAGC,CAAA,KAAM;QACnD,IAAAD,CAAA,CAAEnJ,GAAA,KAAQoJ,CAAA,CAAEpJ,GAAA,EAAK;UACZ,OAAAmJ,CAAA,CAAE3J,KAAA,GAAQ4J,CAAA,CAAE5J,KAAA;QAAA;QAGd,OAAA2J,CAAA,CAAEnJ,GAAA,GAAMoJ,CAAA,CAAEpJ,GAAA;MAAA,CAClB,EAAE,CAAC,IACJ;IACN;IAEA,KAAQqJ,qBAAA,GAAwBtC,IAAA,CAC9B,MAAM,CACJ,KAAKvF,OAAA,CAAQrB,KAAA,EACb,KAAKqB,OAAA,CAAQsE,YAAA,EACb,KAAKtE,OAAA,CAAQ8E,YAAA,EACb,KAAK9E,OAAA,CAAQ0E,UAAA,EACb,KAAK1E,OAAA,CAAQmF,OAAA,CACf,EACA,CAACxG,KAAA,EAAO2F,YAAA,EAAcQ,YAAA,EAAcJ,UAAA,EAAYS,OAAA,KAAY;MAC1D,KAAKlC,2BAAA,GAA8B,EAAC;MAC7B;QACLtE,KAAA;QACA2F,YAAA;QACAQ,YAAA;QACAJ,UAAA;QACAS;MACF;IACF,GACA;MACElB,GAAA,EAAK;IAAA,CAET;IAEA,KAAQ6D,eAAA,GAAkBvC,IAAA,CACxB,MAAM,CAAC,KAAKsC,qBAAA,IAAyB,KAAK9E,aAAa,GACvD,CAAAgF,KAAA,EAEEhF,aAAA,KACG;MAAA,IAFH;QAAEpE,KAAA;QAAO2F,YAAA;QAAcQ,YAAA;QAAcJ,UAAA;QAAYS;MAAA,IAAA4C,KAAA;MAGjD,IAAI,CAAC5C,OAAA,EAAS;QACZ,KAAKrC,iBAAA,GAAoB,EAAC;QAC1B,KAAKC,aAAA,CAAciF,KAAA,CAAM;QACzB,OAAO,EAAC;MAAA;MAGN,SAAKlF,iBAAA,CAAkBmF,MAAA,KAAW,GAAG;QAClC,KAAAnF,iBAAA,GAAoB,KAAK9C,OAAA,CAAQiF,wBAAA;QACjC,KAAAnC,iBAAA,CAAkBW,OAAA,CAASyE,IAAA,IAAS;UACvC,KAAKnF,aAAA,CAAcuE,GAAA,CAAIY,IAAA,CAAKjE,GAAA,EAAKiE,IAAA,CAAKtG,IAAI;QAAA,CAC3C;MAAA;MAGG,MAAAnD,GAAA,GACJ,KAAKwE,2BAAA,CAA4BgF,MAAA,GAAS,IACtC7J,IAAA,CAAKK,GAAA,CAAI,GAAG,KAAKwE,2BAA2B,IAC5C;MACN,KAAKA,2BAAA,GAA8B,EAAC;MAEpC,MAAM6D,YAAA,GAAe,KAAKhE,iBAAA,CAAkBqF,KAAA,CAAM,GAAG1J,GAAG;MAExD,SAASI,CAAA,GAAIJ,GAAA,EAAKI,CAAA,GAAIF,KAAA,EAAOE,CAAA,IAAK;QAC1B,MAAAoF,GAAA,GAAMS,UAAA,CAAW7F,CAAC;QAExB,MAAMuJ,mBAAA,GACJ,KAAKpI,OAAA,CAAQkF,KAAA,KAAU,IACnB4B,YAAA,CAAajI,CAAA,GAAI,CAAC,IAClB,KAAKgI,sBAAA,CAAuBC,YAAA,EAAcjI,CAAC;QAEjD,MAAMV,KAAA,GAAQiK,mBAAA,GACVA,mBAAA,CAAoB5J,GAAA,GAAM,KAAKwB,OAAA,CAAQ+E,GAAA,GACvCT,YAAA,GAAeQ,YAAA;QAEb,MAAAuD,YAAA,GAAetF,aAAA,CAAcS,GAAA,CAAIS,GAAG;QACpC,MAAArC,IAAA,GACJ,OAAOyG,YAAA,KAAiB,WACpBA,YAAA,GACA,KAAKrI,OAAA,CAAQsI,YAAA,CAAazJ,CAAC;QAEjC,MAAML,GAAA,GAAML,KAAA,GAAQyD,IAAA;QAEpB,MAAMwF,IAAA,GAAOgB,mBAAA,GACTA,mBAAA,CAAoBhB,IAAA,GACpBvI,CAAA,GAAI,KAAKmB,OAAA,CAAQkF,KAAA;QAErB4B,YAAA,CAAajI,CAAC,IAAI;UAChBb,KAAA,EAAOa,CAAA;UACPV,KAAA;UACAyD,IAAA;UACApD,GAAA;UACAyF,GAAA;UACAmD;QACF;MAAA;MAGF,KAAKtE,iBAAA,GAAoBgE,YAAA;MAElB,OAAAA,YAAA;IACT,GACA;MACE7C,GAAA,EAAKwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAKpE,OAAA,CAAQoE;IAAA,CAE9B;IAEiB,KAAAoB,cAAA,GAAAD,IAAA,CACf,MAAM,CACJ,KAAKuC,eAAA,CAAgB,GACrB,KAAKpB,OAAA,CAAQ,GACb,KAAKD,eAAA,CAAgB,GACrB,KAAKzG,OAAA,CAAQkF,KAAA,CACf,EACA,CAAC4B,YAAA,EAAcyB,SAAA,EAAWpF,YAAA,EAAc+B,KAAA,KAAU;MAChD,OAAQ,KAAKhH,KAAA,GACX4I,YAAA,CAAamB,MAAA,GAAS,KAAKM,SAAA,GAAY,IACnC/C,cAAA,CAAe;QACbsB,YAAA;QACAyB,SAAA;QACApF,YAAA;QACA+B;MACD,KACD;IACR,GACA;MACEjB,GAAA,EAAKwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAKpE,OAAA,CAAQoE;IAAA,CAE9B;IAEoB,KAAAoE,iBAAA,GAAAjD,IAAA,CAClB,MAAM;MACJ,IAAIjH,UAAA,GAA4B;MAChC,IAAII,QAAA,GAA0B;MACxB,MAAAR,KAAA,GAAQ,KAAKsH,cAAA,CAAe;MAClC,IAAItH,KAAA,EAAO;QACTI,UAAA,GAAaJ,KAAA,CAAMI,UAAA;QACnBI,QAAA,GAAWR,KAAA,CAAMQ,QAAA;MAAA;MAEnB,KAAK4G,WAAA,CAAYmD,UAAA,CAAW,CAAC,KAAKpH,WAAA,EAAa/C,UAAA,EAAYI,QAAQ,CAAC;MAC7D,QACL,KAAKsB,OAAA,CAAQ2E,cAAA,EACb,KAAK3E,OAAA,CAAQzB,QAAA,EACb,KAAKyB,OAAA,CAAQrB,KAAA,EACbL,UAAA,EACAI,QAAA,CACF;IACF,GACA,CAACiG,cAAA,EAAgBpG,QAAA,EAAUI,KAAA,EAAOL,UAAA,EAAYI,QAAA,KAAa;MACzD,OAAOJ,UAAA,KAAe,QAAQI,QAAA,KAAa,OACvC,KACAiG,cAAA,CAAe;QACbrG,UAAA;QACAI,QAAA;QACAH,QAAA;QACAI;MAAA,CACD;IACP,GACA;MACEsF,GAAA,EAAKwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAKpE,OAAA,CAAQoE;IAAA,CAE9B;IAEA,KAAAsE,gBAAA,GAAoBC,IAAA,IAAuB;MACnC,MAAAC,aAAA,GAAgB,KAAK5I,OAAA,CAAQgF,cAAA;MAC7B,MAAA6D,QAAA,GAAWF,IAAA,CAAKG,YAAA,CAAaF,aAAa;MAEhD,IAAI,CAACC,QAAA,EAAU;QACLE,OAAA,CAAAC,IAAA,4BAAAC,MAAA,CACqBL,aAAa,mCAC1C;QACO;MAAA;MAGF,OAAAM,QAAA,CAASL,QAAA,EAAU,EAAE;IAC9B;IAEQ,KAAAnF,eAAA,GAAkB,CACxBiF,IAAA,EACAhJ,KAAA,KACG;MACG,MAAA3B,KAAA,GAAQ,KAAK0K,gBAAA,CAAiBC,IAAI;MAClC,MAAAT,IAAA,GAAO,KAAKpF,iBAAA,CAAkB9E,KAAK;MACzC,IAAI,CAACkK,IAAA,EAAM;QACT;MAAA;MAEF,MAAMjE,GAAA,GAAMiE,IAAA,CAAKjE,GAAA;MACjB,MAAMkF,QAAA,GAAW,KAAK7F,aAAA,CAAcE,GAAA,CAAIS,GAAG;MAE3C,IAAIkF,QAAA,KAAaR,IAAA,EAAM;QACrB,IAAIQ,QAAA,EAAU;UACP,KAAA3J,QAAA,CAASY,SAAA,CAAU+I,QAAQ;QAAA;QAE7B,KAAA3J,QAAA,CAASW,OAAA,CAAQwI,IAAI;QACrB,KAAArF,aAAA,CAAcgE,GAAA,CAAIrD,GAAA,EAAK0E,IAAI;MAAA;MAGlC,IAAIA,IAAA,CAAKS,WAAA,EAAa;QACf,KAAAC,UAAA,CAAWrL,KAAA,EAAO,KAAKgC,OAAA,CAAQ2B,cAAA,CAAegH,IAAA,EAAMhJ,KAAA,EAAO,IAAI,CAAC;MAAA;IAEzE;IAEa,KAAA0J,UAAA,IAACrL,KAAA,EAAe4D,IAAA,KAAiB;MAAA,IAAA0H,qBAAA;MACtC,MAAApB,IAAA,GAAO,KAAKpF,iBAAA,CAAkB9E,KAAK;MACzC,IAAI,CAACkK,IAAA,EAAM;QACT;MAAA;MAEF,MAAMqB,QAAA,IAAAD,qBAAA,GAAW,KAAKvG,aAAA,CAAcS,GAAA,CAAI0E,IAAA,CAAKjE,GAAG,eAAAqF,qBAAA,cAAAA,qBAAA,GAAKpB,IAAA,CAAKtG,IAAA;MAC1D,MAAM4H,KAAA,GAAQ5H,IAAA,GAAO2H,QAAA;MAErB,IAAIC,KAAA,KAAU,GAAG;QACf,IACE,KAAKC,0CAAA,KAA+C,SAChD,KAAKA,0CAAA,CAA2CvB,IAAA,EAAMsB,KAAA,EAAO,IAAI,IACjE,KAAKpG,eAAA,KAAoB,cACzB8E,IAAA,CAAK/J,KAAA,GAAQ,KAAKsI,eAAA,CAAgB,IAAI,KAAKpD,iBAAA,EAC/C;UACA,IAAIoC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,KAAK3F,OAAA,CAAQoE,KAAA,EAAO;YACvD2E,OAAA,CAAAW,IAAA,CAAK,cAAcF,KAAK;UAAA;UAG7B,KAAAhD,eAAA,CAAgB,KAAKC,eAAA,IAAmB;YAC3C1E,WAAA,EAAc,KAAKsB,iBAAA,IAAqBmG,KAAA;YACxCxH,QAAA,EAAU;UAAA,CACX;QAAA;QAGE,KAAAiB,2BAAA,CAA4BnE,IAAA,CAAKoJ,IAAA,CAAKlK,KAAK;QAC3C,KAAA+E,aAAA,GAAgB,IAAIC,GAAA,CAAI,KAAKD,aAAA,CAAcuE,GAAA,CAAIY,IAAA,CAAKjE,GAAA,EAAKrC,IAAI,CAAC;QAEnE,KAAKwD,MAAA,CAAO,KAAK;MAAA;IAErB;IAEA,KAAAzD,cAAA,GAAkBgH,IAAA,IAA0C;MAC1D,IAAI,CAACA,IAAA,EAAM;QACT,KAAKrF,aAAA,CAAcG,OAAA,CAAQ,CAAC8C,MAAA,EAAQtC,GAAA,KAAQ;UACtC,KAACsC,MAAA,CAAO6C,WAAA,EAAa;YAClB,KAAA5J,QAAA,CAASY,SAAA,CAAUmG,MAAM;YACzB,KAAAjD,aAAA,CAAcqG,MAAA,CAAO1F,GAAG;UAAA;QAC/B,CACD;QACD;MAAA;MAGG,KAAAP,eAAA,CAAgBiF,IAAA,EAAM,MAAS;IACtC;IAEkB,KAAAiB,eAAA,GAAArE,IAAA,CAChB,MAAM,CAAC,KAAKiD,iBAAA,CAAqB,QAAKV,eAAA,EAAiB,GACvD,CAAC+B,OAAA,EAAS/C,YAAA,KAAiB;MACzB,MAAMgD,YAAA,GAAmC,EAAC;MAE1C,SAASC,CAAA,GAAI,GAAGC,GAAA,GAAMH,OAAA,CAAQ5B,MAAA,EAAQ8B,CAAA,GAAIC,GAAA,EAAKD,CAAA,IAAK;QAC5C,MAAAlL,CAAA,GAAIgL,OAAA,CAAQE,CAAC;QACb,MAAA7C,WAAA,GAAcJ,YAAA,CAAajI,CAAC;QAElCiL,YAAA,CAAahL,IAAA,CAAKoI,WAAW;MAAA;MAGxB,OAAA4C,YAAA;IACT,GACA;MACE7F,GAAA,EAAKwB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAKpE,OAAA,CAAQoE;IAAA,CAE9B;IAEA,KAAA6F,uBAAA,GAA2BlJ,MAAA,IAAmB;MACtC,MAAA+F,YAAA,GAAe,KAAKgB,eAAA,CAAgB;MACtC,IAAAhB,YAAA,CAAamB,MAAA,KAAW,GAAG;QACtB;MAAA;MAEF,OAAAiC,YAAA,CACLpD,YAAA,CACEqD,uBAAA,CACE,GACArD,YAAA,CAAamB,MAAA,GAAS,GACrBjK,KAAA,IAAkBkM,YAAA,CAAapD,YAAA,CAAa9I,KAAK,CAAC,EAAEG,KAAA,EACrD4C,MAEJ,EACF;IACF;IAEA,KAAAqJ,qBAAA,GAAwB,UACtBnI,QAAA,EACAoI,KAAA,EAEG;MAAA,IADHd,QAAA,GAAAe,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAW;MAEL,MAAA1I,IAAA,GAAOe,KAAA,CAAK+D,OAAA,CAAQ;MACpB,MAAAvD,YAAA,GAAeR,KAAA,CAAK8D,eAAA,CAAgB;MAE1C,IAAI4D,KAAA,KAAU,QAAQ;QACZA,KAAA,GAAApI,QAAA,IAAYkB,YAAA,GAAevB,IAAA,GAAO,QAAQ;MAAA;MAGpD,IAAIyI,KAAA,KAAU,UAAU;QAGtBpI,QAAA,KAAasH,QAAA,GAAW3H,IAAA,IAAQ;MAAA,WACvByI,KAAA,KAAU,OAAO;QACdpI,QAAA,IAAAL,IAAA;MAAA;MAGR,MAAA4I,SAAA,GAAY7H,KAAA,CAAK8H,YAAA,KAAiB7I,IAAA;MAExC,OAAOxD,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKK,GAAA,CAAI+L,SAAA,EAAWvI,QAAQ,GAAG,CAAC;IAClD;IAEoB,KAAAyI,iBAAA,aAAC1M,KAAA,EAAmD;MAAA,IAApCqM,KAAA,GAAAC,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAyB;MACnDtM,KAAA,GAAAI,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKK,GAAA,CAAIT,KAAA,EAAO2E,KAAA,CAAK3C,OAAA,CAAQrB,KAAA,GAAQ,CAAC,CAAC;MAErD,MAAAuJ,IAAA,GAAOvF,KAAA,CAAKG,iBAAA,CAAkB9E,KAAK;MACzC,IAAI,CAACkK,IAAA,EAAM;QACF;MAAA;MAGH,MAAAtG,IAAA,GAAOe,KAAA,CAAK+D,OAAA,CAAQ;MACpB,MAAAvD,YAAA,GAAeR,KAAA,CAAK8D,eAAA,CAAgB;MAE1C,IAAI4D,KAAA,KAAU,QAAQ;QACpB,IAAInC,IAAA,CAAK1J,GAAA,IAAO2E,YAAA,GAAevB,IAAA,GAAOe,KAAA,CAAK3C,OAAA,CAAQyE,gBAAA,EAAkB;UAC3D4F,KAAA;QAAA,WACCnC,IAAA,CAAK/J,KAAA,IAASgF,YAAA,GAAeR,KAAA,CAAK3C,OAAA,CAAQwE,kBAAA,EAAoB;UAC/D6F,KAAA;QAAA,OACH;UACE,QAAClH,YAAA,EAAckH,KAAK;QAAA;MAC7B;MAGI,MAAApI,QAAA,GACJoI,KAAA,KAAU,QACNnC,IAAA,CAAK1J,GAAA,GAAMmE,KAAA,CAAK3C,OAAA,CAAQyE,gBAAA,GACxByD,IAAA,CAAK/J,KAAA,GAAQwE,KAAA,CAAK3C,OAAA,CAAQwE,kBAAA;MAEzB,QACL7B,KAAA,CAAKyH,qBAAA,CAAsBnI,QAAA,EAAUoI,KAAA,EAAOnC,IAAA,CAAKtG,IAAI,GACrDyI,KAAA,CACF;IACF;IAEA,KAAQM,aAAA,GAAgB,MAAM,KAAKrH,aAAA,CAAc1B,IAAA,GAAO;IAExD,KAAQgJ,mBAAA,GAAsB,MAAM;MAClC,IAAI,KAAK/H,sBAAA,KAA2B,QAAQ,KAAK1D,YAAA,EAAc;QACxD,KAAAA,YAAA,CAAa0L,YAAA,CAAa,KAAKhI,sBAAsB;QAC1D,KAAKA,sBAAA,GAAyB;MAAA;IAElC;IAEiB,KAAAiI,cAAA,aACf7I,QAAA,EAEG;MAAA,IADH;QAAEoI,KAAA,GAAQ;QAASrI;MAAS,IAAAsI,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAA2B;MAEvD3H,KAAA,CAAKiI,mBAAA,CAAoB;MAEzB,IAAI5I,QAAA,KAAa,YAAYW,KAAA,CAAKgI,aAAA,IAAiB;QACzC5B,OAAA,CAAAC,IAAA,CACN,wEACF;MAAA;MAGFrG,KAAA,CAAK6D,eAAA,CAAgB7D,KAAA,CAAKyH,qBAAA,CAAsBnI,QAAA,EAAUoI,KAAK,GAAG;QAChEtI,WAAA,EAAa;QACbC;MAAA,CACD;IACH;IAEgB,KAAA+I,aAAA,aACd/M,KAAA,EAEG;MAAA,IADH;QAAEqM,KAAA,EAAOW,YAAA,GAAe;QAAQhJ;MAAmC,IAAAsI,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAE3DtM,KAAA,GAAAI,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKK,GAAA,CAAIT,KAAA,EAAO2E,KAAA,CAAK3C,OAAA,CAAQrB,KAAA,GAAQ,CAAC,CAAC;MAE3DgE,KAAA,CAAKiI,mBAAA,CAAoB;MAEzB,IAAI5I,QAAA,KAAa,YAAYW,KAAA,CAAKgI,aAAA,IAAiB;QACzC5B,OAAA,CAAAC,IAAA,CACN,wEACF;MAAA;MAGF,MAAMiC,cAAA,GAAiBtI,KAAA,CAAK+H,iBAAA,CAAkB1M,KAAA,EAAOgN,YAAY;MACjE,IAAI,CAACC,cAAA,EAAgB;MAEf,OAAClK,MAAA,EAAQsJ,KAAK,IAAIY,cAAA;MAExBtI,KAAA,CAAK6D,eAAA,CAAgBzF,MAAA,EAAQ;QAAEgB,WAAA,EAAa;QAAWC;MAAA,CAAU;MAEjE,IAAIA,QAAA,KAAa,YAAYW,KAAA,CAAKgI,aAAA,CAAc,KAAKhI,KAAA,CAAKxD,YAAA,EAAc;QACtEwD,KAAA,CAAKE,sBAAA,GAAyBF,KAAA,CAAKxD,YAAA,CAAa+L,UAAA,CAAW,MAAM;UAC/DvI,KAAA,CAAKE,sBAAA,GAAyB;UAExB,MAAAsI,YAAA,GAAexI,KAAA,CAAKW,aAAA,CAAc6D,GAAA,CACtCxE,KAAA,CAAK3C,OAAA,CAAQ0E,UAAA,CAAW1G,KAAK,CAC/B;UAEA,IAAImN,YAAA,EAAc;YAChB,MAAMC,MAAA,GAASzI,KAAA,CAAK+H,iBAAA,CAAkB1M,KAAA,EAAOqM,KAAK;YAClD,IAAI,CAACe,MAAA,EAAQ;YACP,OAACC,YAAY,IAAID,MAAA;YAEjB,MAAAE,mBAAA,GAAsB3I,KAAA,CAAK8D,eAAA,CAAgB;YACjD,IAAI,CAAC8E,WAAA,CAAYF,YAAA,EAAcC,mBAAmB,GAAG;cACnD3I,KAAA,CAAKoI,aAAA,CAAc/M,KAAA,EAAO;gBAAEqM,KAAA;gBAAOrI;cAAA,CAAU;YAAA;UAC/C,OACK;YACLW,KAAA,CAAKoI,aAAA,CAAc/M,KAAA,EAAO;cAAEqM,KAAA;cAAOrI;YAAA,CAAU;UAAA;QAC/C,CACD;MAAA;IAEL;IAEA,KAAAwJ,QAAA,GAAW,UAAChC,KAAA,EAA4D;MAAA,IAA7C;QAAExH;MAAS,IAAAsI,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAA2B;MAC/D3H,KAAA,CAAKiI,mBAAA,CAAoB;MAEzB,IAAI5I,QAAA,KAAa,YAAYW,KAAA,CAAKgI,aAAA,IAAiB;QACzC5B,OAAA,CAAAC,IAAA,CACN,wEACF;MAAA;MAGFrG,KAAA,CAAK6D,eAAA,CAAgB7D,KAAA,CAAK8D,eAAA,CAAgB,IAAI+C,KAAA,EAAO;QACnDzH,WAAA,EAAa;QACbC;MAAA,CACD;IACH;IAEA,KAAAyI,YAAA,GAAe,MAAM;;MACb,MAAA3D,YAAA,GAAe,KAAKgB,eAAA,CAAgB;MAEtC,IAAAtJ,GAAA;MAIA,IAAAsI,YAAA,CAAamB,MAAA,KAAW,GAAG;QAC7BzJ,GAAA,GAAM,KAAKwB,OAAA,CAAQsE,YAAA;MACV,gBAAKtE,OAAA,CAAQkF,KAAA,KAAU,GAAG;QAAA,IAAAuG,KAAA;QACnCjN,GAAA,IAAAiN,KAAA,IAAMtJ,EAAA,GAAA2E,YAAA,CAAaA,YAAA,CAAamB,MAAA,GAAS,CAAC,MAApC,gBAAA9F,EAAA,CAAuC3D,GAAA,cAAAiN,KAAA,cAAAA,KAAA,GAAO;MAAA,OAC/C;QACL,MAAMC,SAAA,GAAYnE,KAAA,CAAqB,KAAKvH,OAAA,CAAQkF,KAAK,EAAEyG,IAAA,CAAK,IAAI;QAChE,IAAAjN,QAAA,GAAWoI,YAAA,CAAamB,MAAA,GAAS;QAC9B,OAAAvJ,QAAA,IAAY,KAAKgN,SAAA,CAAUE,IAAA,CAAMC,GAAA,IAAQA,GAAA,KAAQ,IAAI,GAAG;UACvD,MAAA3D,IAAA,GAAOpB,YAAA,CAAapI,QAAQ;UAClC,IAAIgN,SAAA,CAAUxD,IAAA,CAAKd,IAAI,MAAM,MAAM;YACvBsE,SAAA,CAAAxD,IAAA,CAAKd,IAAI,IAAIc,IAAA,CAAK1J,GAAA;UAAA;UAG9BE,QAAA;QAAA;QAGIF,GAAA,GAAAJ,IAAA,CAAKC,GAAA,CAAI,GAAGqN,SAAA,CAAU5F,MAAA,CAAQ+F,GAAA,IAAuBA,GAAA,KAAQ,IAAI,CAAC;MAAA;MAG1E,OAAOzN,IAAA,CAAKC,GAAA,CACVG,GAAA,GAAM,KAAKwB,OAAA,CAAQ8E,YAAA,GAAe,KAAK9E,OAAA,CAAQuE,UAAA,EAC/C,CACF;IACF;IAEQ,KAAAiC,eAAA,GAAkB,CACxBzF,MAAA,EAAA+K,KAAA,KAQG;MAAA,IAPH;QACE/J,WAAA;QACAC;MAAA,IAAA8J,KAAA;MAMF,KAAK9L,OAAA,CAAQ+L,UAAA,CAAWhL,MAAA,EAAQ;QAAEiB,QAAA;QAAUD;MAAA,GAAe,IAAI;IACjE;IAEA,KAAAiK,OAAA,GAAU,MAAM;MACT,KAAAjJ,aAAA,sBAAoBC,GAAA,CAAI;MAC7B,KAAKoC,MAAA,CAAO,KAAK;IACnB;IAzpBE,KAAKvB,UAAA,CAAWnB,IAAI;EAAA;AA0pBxB;AAEA,MAAMyH,uBAAA,GAA0BA,CAC9B8B,GAAA,EACAC,IAAA,EACAC,eAAA,EACAjI,KAAA,KACG;EACH,OAAO+H,GAAA,IAAOC,IAAA,EAAM;IACZ,MAAAE,MAAA,IAAWH,GAAA,GAAMC,IAAA,IAAQ,IAAK;IAC9B,MAAAG,YAAA,GAAeF,eAAA,CAAgBC,MAAM;IAE3C,IAAIC,YAAA,GAAenI,KAAA,EAAO;MACxB+H,GAAA,GAAMG,MAAA,GAAS;IAAA,WACNC,YAAA,GAAenI,KAAA,EAAO;MAC/BgI,IAAA,GAAOE,MAAA,GAAS;IAAA,OACX;MACE,OAAAA,MAAA;IAAA;EACT;EAGF,IAAIH,GAAA,GAAM,GAAG;IACX,OAAOA,GAAA,GAAM;EAAA,OACR;IACE;EAAA;AAEX;AAEA,SAASzG,eAAA8G,KAAA,EAUN;EAAA,IAVqB;IACtBxF,YAAA;IACAyB,SAAA;IACApF,YAAA;IACA+B;EACF,IAAAoH,KAAA;EAMQ,MAAAC,SAAA,GAAYzF,YAAA,CAAamB,MAAA,GAAS;EACxC,MAAMuE,SAAA,GAAaxO,KAAA,IAAkB8I,YAAA,CAAa9I,KAAK,EAAGG,KAAA;EAGtD,IAAA2I,YAAA,CAAamB,MAAA,IAAU/C,KAAA,EAAO;IACzB;MACL5G,UAAA,EAAY;MACZI,QAAA,EAAU6N;IACZ;EAAA;EAGF,IAAIjO,UAAA,GAAa6L,uBAAA,CACf,GACAoC,SAAA,EACAC,SAAA,EACArJ,YACF;EACA,IAAIzE,QAAA,GAAWJ,UAAA;EAEf,IAAI4G,KAAA,KAAU,GAAG;IACf,OACExG,QAAA,GAAW6N,SAAA,IACXzF,YAAA,CAAapI,QAAQ,EAAGF,GAAA,GAAM2E,YAAA,GAAeoF,SAAA,EAC7C;MACA7J,QAAA;IAAA;EACF,WACSwG,KAAA,GAAQ,GAAG;IAGpB,MAAMuH,UAAA,GAAalF,KAAA,CAAMrC,KAAK,EAAEyG,IAAA,CAAK,CAAC;IAEpC,OAAAjN,QAAA,GAAW6N,SAAA,IACXE,UAAA,CAAWb,IAAA,CAAMc,GAAA,IAAQA,GAAA,GAAMvJ,YAAA,GAAeoF,SAAS,GACvD;MACM,MAAAL,IAAA,GAAOpB,YAAA,CAAapI,QAAQ;MACvB+N,UAAA,CAAAvE,IAAA,CAAKd,IAAI,IAAIc,IAAA,CAAK1J,GAAA;MAC7BE,QAAA;IAAA;IAKF,MAAMiO,YAAA,GAAepF,KAAA,CAAMrC,KAAK,EAAEyG,IAAA,CAAKxI,YAAA,GAAeoF,SAAS;IACxD,OAAAjK,UAAA,IAAc,KAAKqO,YAAA,CAAaf,IAAA,CAAMc,GAAA,IAAQA,GAAA,IAAOvJ,YAAY,GAAG;MACnE,MAAA+E,IAAA,GAAOpB,YAAA,CAAaxI,UAAU;MACvBqO,YAAA,CAAAzE,IAAA,CAAKd,IAAI,IAAIc,IAAA,CAAK/J,KAAA;MAC/BG,UAAA;IAAA;IAIFA,UAAA,GAAaF,IAAA,CAAKC,GAAA,CAAI,GAAGC,UAAA,GAAcA,UAAA,GAAa4G,KAAM;IAE1DxG,QAAA,GAAWN,IAAA,CAAKK,GAAA,CAAI8N,SAAA,EAAW7N,QAAA,IAAYwG,KAAA,GAAQ,IAAKxG,QAAA,GAAWwG,KAAA,CAAO;EAAA;EAGrE;IAAE5G,UAAA;IAAYI;EAAS;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}