{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport p from \"react\";\nimport { forwardRefWithAs as r } from '../../utils/render.js';\nimport { Label as e } from '../label/label.js';\nlet a = e;\nfunction o(t, n) {\n  return p.createElement(e, _objectSpread({\n    as: \"div\",\n    ref: n\n  }, t));\n}\nlet d = r(o);\nexport { d as Legend };", "map": {"version": 3, "names": ["_objectSpread", "p", "forwardRefWithAs", "r", "Label", "e", "a", "o", "t", "n", "createElement", "as", "ref", "d", "Legend"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/legend/legend.js"], "sourcesContent": ["\"use client\";import p from\"react\";import{forwardRefWithAs as r}from'../../utils/render.js';import{Label as e}from'../label/label.js';let a=e;function o(t,n){return p.createElement(e,{as:\"div\",ref:n,...t})}let d=r(o);export{d as Legend};\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAOC,CAAC,MAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAACD,CAAC;AAAC,SAASE,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOR,CAAC,CAACS,aAAa,CAACL,CAAC,EAAAL,aAAA;IAAEW,EAAE,EAAC,KAAK;IAACC,GAAG,EAACH;EAAC,GAAID,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIK,CAAC,GAACV,CAAC,CAACI,CAAC,CAAC;AAAC,SAAOM,CAAC,IAAIC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}