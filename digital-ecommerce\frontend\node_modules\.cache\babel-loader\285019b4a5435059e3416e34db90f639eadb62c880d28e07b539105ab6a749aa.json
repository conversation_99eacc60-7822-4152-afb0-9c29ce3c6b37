{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{motion}from'framer-motion';import{UserIcon,CogIcon,ShoppingBagIcon,HeartIcon,MapPinIcon,CreditCardIcon,BellIcon,ShieldCheckIcon,PencilIcon,PlusIcon}from'@heroicons/react/24/outline';import{useUser}from'../contexts/UserContext';import Button from'../components/Button';import Input from'../components/Input';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AccountPage=()=>{const{user,updateProfile,isLoading}=useUser();const[activeTab,setActiveTab]=useState('profile');const[isEditing,setIsEditing]=useState(false);const[formData,setFormData]=useState({firstName:(user===null||user===void 0?void 0:user.firstName)||'',lastName:(user===null||user===void 0?void 0:user.lastName)||'',email:(user===null||user===void 0?void 0:user.email)||'',phone:(user===null||user===void 0?void 0:user.phone)||''});const tabs=[{id:'profile',name:'Profile',icon:UserIcon},{id:'orders',name:'Orders',icon:ShoppingBagIcon},{id:'wishlist',name:'Wishlist',icon:HeartIcon},{id:'addresses',name:'Addresses',icon:MapPinIcon},{id:'payments',name:'Payment Methods',icon:CreditCardIcon},{id:'notifications',name:'Notifications',icon:BellIcon},{id:'security',name:'Security',icon:ShieldCheckIcon}];const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));};const handleSaveProfile=async()=>{const result=await updateProfile(formData);if(result.success){toast.success('Profile updated successfully!');setIsEditing(false);}else{toast.error(result.error);}};const handleCancelEdit=()=>{setFormData({firstName:(user===null||user===void 0?void 0:user.firstName)||'',lastName:(user===null||user===void 0?void 0:user.lastName)||'',email:(user===null||user===void 0?void 0:user.email)||'',phone:(user===null||user===void 0?void 0:user.phone)||''});setIsEditing(false);};const renderProfileTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Profile Information\"}),!isEditing&&/*#__PURE__*/_jsx(Button,{onClick:()=>setIsEditing(true),variant:\"outline\",icon:PencilIcon,children:\"Edit Profile\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[user!==null&&user!==void 0&&user.profilePicture?/*#__PURE__*/_jsx(\"img\",{src:user.profilePicture,alt:\"Profile\",className:\"w-24 h-24 rounded-full object-cover\"}):/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-24 bg-light-orange-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-12 h-12 text-light-orange-600\"})}),isEditing&&/*#__PURE__*/_jsx(\"button\",{className:\"absolute bottom-0 right-0 bg-light-orange-500 text-white rounded-full p-2 hover:bg-light-orange-600 transition-colors\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"w-4 h-4\"})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-xl font-semibold text-gray-900\",children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:user===null||user===void 0?void 0:user.email}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"Member since \",new Date(user===null||user===void 0?void 0:user.createdAt).toLocaleDateString()]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsx(Input,{label:\"First Name\",name:\"firstName\",value:formData.firstName,onChange:handleInputChange,disabled:!isEditing,required:true}),/*#__PURE__*/_jsx(Input,{label:\"Last Name\",name:\"lastName\",value:formData.lastName,onChange:handleInputChange,disabled:!isEditing,required:true}),/*#__PURE__*/_jsx(Input,{label:\"Email Address\",type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,disabled:!isEditing,required:true}),/*#__PURE__*/_jsx(Input,{label:\"Phone Number\",type:\"tel\",name:\"phone\",value:formData.phone,onChange:handleInputChange,disabled:!isEditing,placeholder:\"+****************\"})]}),isEditing&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-4 mt-8\",children:[/*#__PURE__*/_jsx(Button,{onClick:handleSaveProfile,loading:isLoading,children:\"Save Changes\"}),/*#__PURE__*/_jsx(Button,{onClick:handleCancelEdit,variant:\"outline\",children:\"Cancel\"})]})]})]});const renderOrdersTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Order History\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-16 h-16 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"No orders yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Start shopping to see your orders here.\"}),/*#__PURE__*/_jsx(Button,{children:\"Start Shopping\"})]})})]});const renderWishlistTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Wishlist\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(HeartIcon,{className:\"w-16 h-16 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"Your wishlist is empty\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Save items you love for later.\"}),/*#__PURE__*/_jsx(Button,{children:\"Browse Products\"})]})})]});const renderAddressesTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Addresses\"}),/*#__PURE__*/_jsx(Button,{icon:PlusIcon,children:\"Add Address\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(MapPinIcon,{className:\"w-16 h-16 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"No addresses saved\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Add your shipping and billing addresses.\"}),/*#__PURE__*/_jsx(Button,{icon:PlusIcon,children:\"Add Your First Address\"})]})})]});const renderPaymentMethodsTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Payment Methods\"}),/*#__PURE__*/_jsx(Button,{icon:PlusIcon,children:\"Add Payment Method\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(CreditCardIcon,{className:\"w-16 h-16 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"No payment methods\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Add your credit cards and payment methods.\"}),/*#__PURE__*/_jsx(Button,{icon:PlusIcon,children:\"Add Payment Method\"})]})})]});const renderNotificationsTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Notification Preferences\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:[{id:'email',label:'Email Notifications',description:'Receive order updates and promotions via email'},{id:'sms',label:'SMS Notifications',description:'Get important updates via text message'},{id:'marketing',label:'Marketing Emails',description:'Receive promotional offers and new product announcements'}].map(setting=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900\",children:setting.label}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:setting.description})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"relative inline-flex items-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"sr-only peer\",defaultChecked:setting.id==='email'}),/*#__PURE__*/_jsx(\"div\",{className:\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-light-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-light-orange-500\"})]})]},setting.id))})})]});const renderSecurityTab=()=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:\"Security Settings\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border border-gray-200 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-2\",children:\"Password\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-4\",children:\"Last changed 30 days ago\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",children:\"Change Password\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border border-gray-200 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-2\",children:\"Two-Factor Authentication\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-4\",children:\"Add an extra layer of security to your account\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",children:\"Enable 2FA\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border border-gray-200 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-2\",children:\"Login Sessions\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-4\",children:\"Manage your active login sessions\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",children:\"View Sessions\"})]})]})})]});const renderTabContent=()=>{switch(activeTab){case'profile':return renderProfileTab();case'orders':return renderOrdersTab();case'wishlist':return renderWishlistTab();case'addresses':return renderAddressesTab();case'payments':return renderPaymentMethodsTab();case'notifications':return renderNotificationsTab();case'security':return renderSecurityTab();default:return renderProfileTab();}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:\"My Account\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage your account settings and preferences\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-4 gap-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"space-y-2\",children:tabs.map(tab=>{const Icon=tab.icon;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors \".concat(activeTab===tab.id?'bg-light-orange-100 text-light-orange-700 font-medium':'text-gray-600 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(Icon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:tab.name})]},tab.id);})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-3\",children:renderTabContent()})]})})]});};export default AccountPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "UserIcon", "CogIcon", "ShoppingBagIcon", "HeartIcon", "MapPinIcon", "CreditCardIcon", "BellIcon", "ShieldCheckIcon", "PencilIcon", "PlusIcon", "useUser", "<PERSON><PERSON>", "Input", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "AccountPage", "user", "updateProfile", "isLoading", "activeTab", "setActiveTab", "isEditing", "setIsEditing", "formData", "setFormData", "firstName", "lastName", "email", "phone", "tabs", "id", "name", "icon", "handleInputChange", "e", "value", "target", "prev", "_objectSpread", "handleSaveProfile", "result", "success", "error", "handleCancelEdit", "renderProfileTab", "div", "initial", "opacity", "y", "animate", "className", "children", "onClick", "variant", "profilePicture", "src", "alt", "Date", "createdAt", "toLocaleDateString", "label", "onChange", "disabled", "required", "type", "placeholder", "loading", "renderOrdersTab", "renderWishlistTab", "renderAddressesTab", "renderPaymentMethodsTab", "renderNotificationsTab", "description", "map", "setting", "defaultChecked", "renderSecurityTab", "renderTabContent", "position", "tab", "Icon", "concat"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AccountPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  UserIcon,\n  CogIcon,\n  ShoppingBagIcon,\n  HeartIcon,\n  MapPinIcon,\n  CreditCardIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  PencilIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst AccountPage = () => {\n  const { user, updateProfile, isLoading } = useUser();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: user?.firstName || '',\n    lastName: user?.lastName || '',\n    email: user?.email || '',\n    phone: user?.phone || ''\n  });\n\n  const tabs = [\n    { id: 'profile', name: 'Profile', icon: UserIcon },\n    { id: 'orders', name: 'Orders', icon: ShoppingBagIcon },\n    { id: 'wishlist', name: 'Wishlist', icon: HeartIcon },\n    { id: 'addresses', name: 'Addresses', icon: MapPinIcon },\n    { id: 'payments', name: 'Payment Methods', icon: CreditCardIcon },\n    { id: 'notifications', name: 'Notifications', icon: BellIcon },\n    { id: 'security', name: 'Security', icon: ShieldCheckIcon }\n  ];\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSaveProfile = async () => {\n    const result = await updateProfile(formData);\n    if (result.success) {\n      toast.success('Profile updated successfully!');\n      setIsEditing(false);\n    } else {\n      toast.error(result.error);\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setFormData({\n      firstName: user?.firstName || '',\n      lastName: user?.lastName || '',\n      email: user?.email || '',\n      phone: user?.phone || ''\n    });\n    setIsEditing(false);\n  };\n\n  const renderProfileTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Profile Information</h2>\n        {!isEditing && (\n          <Button\n            onClick={() => setIsEditing(true)}\n            variant=\"outline\"\n            icon={PencilIcon}\n          >\n            Edit Profile\n          </Button>\n        )}\n      </div>\n\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        {/* Profile Picture */}\n        <div className=\"flex items-center space-x-6 mb-8\">\n          <div className=\"relative\">\n            {user?.profilePicture ? (\n              <img\n                src={user.profilePicture}\n                alt=\"Profile\"\n                className=\"w-24 h-24 rounded-full object-cover\"\n              />\n            ) : (\n              <div className=\"w-24 h-24 bg-light-orange-100 rounded-full flex items-center justify-center\">\n                <UserIcon className=\"w-12 h-12 text-light-orange-600\" />\n              </div>\n            )}\n            {isEditing && (\n              <button className=\"absolute bottom-0 right-0 bg-light-orange-500 text-white rounded-full p-2 hover:bg-light-orange-600 transition-colors\">\n                <PencilIcon className=\"w-4 h-4\" />\n              </button>\n            )}\n          </div>\n          <div>\n            <h3 className=\"text-xl font-semibold text-gray-900\">\n              {user?.firstName} {user?.lastName}\n            </h3>\n            <p className=\"text-gray-600\">{user?.email}</p>\n            <p className=\"text-sm text-gray-500\">\n              Member since {new Date(user?.createdAt).toLocaleDateString()}\n            </p>\n          </div>\n        </div>\n\n        {/* Profile Form */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Input\n            label=\"First Name\"\n            name=\"firstName\"\n            value={formData.firstName}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            required\n          />\n          <Input\n            label=\"Last Name\"\n            name=\"lastName\"\n            value={formData.lastName}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            required\n          />\n          <Input\n            label=\"Email Address\"\n            type=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            required\n          />\n          <Input\n            label=\"Phone Number\"\n            type=\"tel\"\n            name=\"phone\"\n            value={formData.phone}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            placeholder=\"+****************\"\n          />\n        </div>\n\n        {isEditing && (\n          <div className=\"flex space-x-4 mt-8\">\n            <Button\n              onClick={handleSaveProfile}\n              loading={isLoading}\n            >\n              Save Changes\n            </Button>\n            <Button\n              onClick={handleCancelEdit}\n              variant=\"outline\"\n            >\n              Cancel\n            </Button>\n          </div>\n        )}\n      </div>\n    </motion.div>\n  );\n\n  const renderOrdersTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Order History</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <ShoppingBagIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No orders yet</h3>\n          <p className=\"text-gray-600 mb-6\">Start shopping to see your orders here.</p>\n          <Button>Start Shopping</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderWishlistTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Wishlist</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <HeartIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Your wishlist is empty</h3>\n          <p className=\"text-gray-600 mb-6\">Save items you love for later.</p>\n          <Button>Browse Products</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderAddressesTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Addresses</h2>\n        <Button icon={PlusIcon}>Add Address</Button>\n      </div>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <MapPinIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No addresses saved</h3>\n          <p className=\"text-gray-600 mb-6\">Add your shipping and billing addresses.</p>\n          <Button icon={PlusIcon}>Add Your First Address</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderPaymentMethodsTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Payment Methods</h2>\n        <Button icon={PlusIcon}>Add Payment Method</Button>\n      </div>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <CreditCardIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No payment methods</h3>\n          <p className=\"text-gray-600 mb-6\">Add your credit cards and payment methods.</p>\n          <Button icon={PlusIcon}>Add Payment Method</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderNotificationsTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Notification Preferences</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"space-y-6\">\n          {[\n            { id: 'email', label: 'Email Notifications', description: 'Receive order updates and promotions via email' },\n            { id: 'sms', label: 'SMS Notifications', description: 'Get important updates via text message' },\n            { id: 'marketing', label: 'Marketing Emails', description: 'Receive promotional offers and new product announcements' }\n          ].map((setting) => (\n            <div key={setting.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">{setting.label}</h4>\n                <p className=\"text-sm text-gray-600\">{setting.description}</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input type=\"checkbox\" className=\"sr-only peer\" defaultChecked={setting.id === 'email'} />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-light-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-light-orange-500\"></div>\n              </label>\n            </div>\n          ))}\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderSecurityTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Security Settings</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"space-y-6\">\n          <div className=\"p-4 border border-gray-200 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">Password</h4>\n            <p className=\"text-sm text-gray-600 mb-4\">Last changed 30 days ago</p>\n            <Button variant=\"outline\">Change Password</Button>\n          </div>\n          <div className=\"p-4 border border-gray-200 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">Two-Factor Authentication</h4>\n            <p className=\"text-sm text-gray-600 mb-4\">Add an extra layer of security to your account</p>\n            <Button variant=\"outline\">Enable 2FA</Button>\n          </div>\n          <div className=\"p-4 border border-gray-200 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">Login Sessions</h4>\n            <p className=\"text-sm text-gray-600 mb-4\">Manage your active login sessions</p>\n            <Button variant=\"outline\">View Sessions</Button>\n          </div>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'profile': return renderProfileTab();\n      case 'orders': return renderOrdersTab();\n      case 'wishlist': return renderWishlistTab();\n      case 'addresses': return renderAddressesTab();\n      case 'payments': return renderPaymentMethodsTab();\n      case 'notifications': return renderNotificationsTab();\n      case 'security': return renderSecurityTab();\n      default: return renderProfileTab();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      \n      {/* Header */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">My Account</h1>\n          <p className=\"text-gray-600\">Manage your account settings and preferences</p>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6\">\n              <nav className=\"space-y-2\">\n                {tabs.map((tab) => {\n                  const Icon = tab.icon;\n                  return (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-light-orange-100 text-light-orange-700 font-medium'\n                          : 'text-gray-600 hover:bg-gray-100'\n                      }`}\n                    >\n                      <Icon className=\"w-5 h-5\" />\n                      <span>{tab.name}</span>\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"lg:col-span-3\">\n            {renderTabContent()}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountPage;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OACEC,QAAQ,CACRC,OAAO,CACPC,eAAe,CACfC,SAAS,CACTC,UAAU,CACVC,cAAc,CACdC,QAAQ,CACRC,eAAe,CACfC,UAAU,CACVC,QAAQ,KACH,6BAA6B,CACpC,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,IAAI,CAAEC,aAAa,CAAEC,SAAU,CAAC,CAAGZ,OAAO,CAAC,CAAC,CACpD,KAAM,CAACa,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,SAAS,CAAC,CACrD,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,CACvC+B,SAAS,CAAE,CAAAT,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,GAAI,EAAE,CAChCC,QAAQ,CAAE,CAAAV,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,QAAQ,GAAI,EAAE,CAC9BC,KAAK,CAAE,CAAAX,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEW,KAAK,GAAI,EAAE,CACxBC,KAAK,CAAE,CAAAZ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEY,KAAK,GAAI,EACxB,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,CACX,CAAEC,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAEpC,QAAS,CAAC,CAClD,CAAEkC,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAElC,eAAgB,CAAC,CACvD,CAAEgC,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEjC,SAAU,CAAC,CACrD,CAAE+B,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAEhC,UAAW,CAAC,CACxD,CAAE8B,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE/B,cAAe,CAAC,CACjE,CAAE6B,EAAE,CAAE,eAAe,CAAEC,IAAI,CAAE,eAAe,CAAEC,IAAI,CAAE9B,QAAS,CAAC,CAC9D,CAAE4B,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE7B,eAAgB,CAAC,CAC5D,CAED,KAAM,CAAA8B,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEH,IAAI,CAAEI,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCZ,WAAW,CAACa,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACN,IAAI,EAAGI,KAAK,EAAG,CAAC,CACnD,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAvB,aAAa,CAACM,QAAQ,CAAC,CAC5C,GAAIiB,MAAM,CAACC,OAAO,CAAE,CAClBhC,KAAK,CAACgC,OAAO,CAAC,+BAA+B,CAAC,CAC9CnB,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLb,KAAK,CAACiC,KAAK,CAACF,MAAM,CAACE,KAAK,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BnB,WAAW,CAAC,CACVC,SAAS,CAAE,CAAAT,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,GAAI,EAAE,CAChCC,QAAQ,CAAE,CAAAV,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,QAAQ,GAAI,EAAE,CAC9BC,KAAK,CAAE,CAAAX,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEW,KAAK,GAAI,EAAE,CACxBC,KAAK,CAAE,CAAAZ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEY,KAAK,GAAI,EACxB,CAAC,CAAC,CACFN,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,KAAM,CAAAsB,gBAAgB,CAAGA,CAAA,gBACvB9B,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBrC,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,CACxE,CAAC9B,SAAS,eACTT,IAAA,CAACL,MAAM,EACL6C,OAAO,CAAEA,CAAA,GAAM9B,YAAY,CAAC,IAAI,CAAE,CAClC+B,OAAO,CAAC,SAAS,CACjBrB,IAAI,CAAE5B,UAAW,CAAA+C,QAAA,CAClB,cAED,CAAQ,CACT,EACE,CAAC,cAENrC,KAAA,QAAKoC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAEjDrC,KAAA,QAAKoC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CrC,KAAA,QAAKoC,SAAS,CAAC,UAAU,CAAAC,QAAA,EACtBnC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEsC,cAAc,cACnB1C,IAAA,QACE2C,GAAG,CAAEvC,IAAI,CAACsC,cAAe,CACzBE,GAAG,CAAC,SAAS,CACbN,SAAS,CAAC,qCAAqC,CAChD,CAAC,cAEFtC,IAAA,QAAKsC,SAAS,CAAC,6EAA6E,CAAAC,QAAA,cAC1FvC,IAAA,CAAChB,QAAQ,EAACsD,SAAS,CAAC,iCAAiC,CAAE,CAAC,CACrD,CACN,CACA7B,SAAS,eACRT,IAAA,WAAQsC,SAAS,CAAC,uHAAuH,CAAAC,QAAA,cACvIvC,IAAA,CAACR,UAAU,EAAC8C,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CACT,EACE,CAAC,cACNpC,KAAA,QAAAqC,QAAA,eACErC,KAAA,OAAIoC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,EAChDnC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAAC,GAAC,CAACT,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,QAAQ,EAC/B,CAAC,cACLd,IAAA,MAAGsC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEW,KAAK,CAAI,CAAC,cAC9Cb,KAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,eACtB,CAAC,GAAI,CAAAM,IAAI,CAACzC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE0C,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAC3D,CAAC,EACD,CAAC,EACH,CAAC,cAGN7C,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvC,IAAA,CAACJ,KAAK,EACJoD,KAAK,CAAC,YAAY,CAClB7B,IAAI,CAAC,WAAW,CAChBI,KAAK,CAAEZ,QAAQ,CAACE,SAAU,CAC1BoC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,CAAE,CAACzC,SAAU,CACrB0C,QAAQ,MACT,CAAC,cACFnD,IAAA,CAACJ,KAAK,EACJoD,KAAK,CAAC,WAAW,CACjB7B,IAAI,CAAC,UAAU,CACfI,KAAK,CAAEZ,QAAQ,CAACG,QAAS,CACzBmC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,CAAE,CAACzC,SAAU,CACrB0C,QAAQ,MACT,CAAC,cACFnD,IAAA,CAACJ,KAAK,EACJoD,KAAK,CAAC,eAAe,CACrBI,IAAI,CAAC,OAAO,CACZjC,IAAI,CAAC,OAAO,CACZI,KAAK,CAAEZ,QAAQ,CAACI,KAAM,CACtBkC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,CAAE,CAACzC,SAAU,CACrB0C,QAAQ,MACT,CAAC,cACFnD,IAAA,CAACJ,KAAK,EACJoD,KAAK,CAAC,cAAc,CACpBI,IAAI,CAAC,KAAK,CACVjC,IAAI,CAAC,OAAO,CACZI,KAAK,CAAEZ,QAAQ,CAACK,KAAM,CACtBiC,QAAQ,CAAE5B,iBAAkB,CAC5B6B,QAAQ,CAAE,CAACzC,SAAU,CACrB4C,WAAW,CAAC,mBAAmB,CAChC,CAAC,EACC,CAAC,CAEL5C,SAAS,eACRP,KAAA,QAAKoC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCvC,IAAA,CAACL,MAAM,EACL6C,OAAO,CAAEb,iBAAkB,CAC3B2B,OAAO,CAAEhD,SAAU,CAAAiC,QAAA,CACpB,cAED,CAAQ,CAAC,cACTvC,IAAA,CAACL,MAAM,EACL6C,OAAO,CAAET,gBAAiB,CAC1BU,OAAO,CAAC,SAAS,CAAAF,QAAA,CAClB,QAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,EACI,CACb,CAED,KAAM,CAAAgB,eAAe,CAAGA,CAAA,gBACtBrD,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACnEvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvC,IAAA,CAACd,eAAe,EAACoD,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACpEtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACzEvC,IAAA,MAAGsC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,yCAAuC,CAAG,CAAC,cAC7EvC,IAAA,CAACL,MAAM,EAAA4C,QAAA,CAAC,gBAAc,CAAQ,CAAC,EAC5B,CAAC,CACH,CAAC,EACI,CACb,CAED,KAAM,CAAAiB,iBAAiB,CAAGA,CAAA,gBACxBtD,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC9DvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvC,IAAA,CAACb,SAAS,EAACmD,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC9DtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cAClFvC,IAAA,MAAGsC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gCAA8B,CAAG,CAAC,cACpEvC,IAAA,CAACL,MAAM,EAAA4C,QAAA,CAAC,iBAAe,CAAQ,CAAC,EAC7B,CAAC,CACH,CAAC,EACI,CACb,CAED,KAAM,CAAAkB,kBAAkB,CAAGA,CAAA,gBACzBvD,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBrC,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cAC/DvC,IAAA,CAACL,MAAM,EAACyB,IAAI,CAAE3B,QAAS,CAAA8C,QAAA,CAAC,aAAW,CAAQ,CAAC,EACzC,CAAC,cACNvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvC,IAAA,CAACZ,UAAU,EAACkD,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC/DtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC9EvC,IAAA,MAAGsC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,0CAAwC,CAAG,CAAC,cAC9EvC,IAAA,CAACL,MAAM,EAACyB,IAAI,CAAE3B,QAAS,CAAA8C,QAAA,CAAC,wBAAsB,CAAQ,CAAC,EACpD,CAAC,CACH,CAAC,EACI,CACb,CAED,KAAM,CAAAmB,uBAAuB,CAAGA,CAAA,gBAC9BxD,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBrC,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrEvC,IAAA,CAACL,MAAM,EAACyB,IAAI,CAAE3B,QAAS,CAAA8C,QAAA,CAAC,oBAAkB,CAAQ,CAAC,EAChD,CAAC,cACNvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvC,IAAA,CAACX,cAAc,EAACiD,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACnEtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC9EvC,IAAA,MAAGsC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,4CAA0C,CAAG,CAAC,cAChFvC,IAAA,CAACL,MAAM,EAACyB,IAAI,CAAE3B,QAAS,CAAA8C,QAAA,CAAC,oBAAkB,CAAQ,CAAC,EAChD,CAAC,CACH,CAAC,EACI,CACb,CAED,KAAM,CAAAoB,sBAAsB,CAAGA,CAAA,gBAC7BzD,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAC9EvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDvC,IAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CACC,CAAErB,EAAE,CAAE,OAAO,CAAE8B,KAAK,CAAE,qBAAqB,CAAEY,WAAW,CAAE,gDAAiD,CAAC,CAC5G,CAAE1C,EAAE,CAAE,KAAK,CAAE8B,KAAK,CAAE,mBAAmB,CAAEY,WAAW,CAAE,wCAAyC,CAAC,CAChG,CAAE1C,EAAE,CAAE,WAAW,CAAE8B,KAAK,CAAE,kBAAkB,CAAEY,WAAW,CAAE,0DAA2D,CAAC,CACxH,CAACC,GAAG,CAAEC,OAAO,eACZ5D,KAAA,QAAsBoC,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eACvGrC,KAAA,QAAAqC,QAAA,eACEvC,IAAA,OAAIsC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEuB,OAAO,CAACd,KAAK,CAAK,CAAC,cAC9DhD,IAAA,MAAGsC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEuB,OAAO,CAACF,WAAW,CAAI,CAAC,EAC3D,CAAC,cACN1D,KAAA,UAAOoC,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eACjEvC,IAAA,UAAOoD,IAAI,CAAC,UAAU,CAACd,SAAS,CAAC,cAAc,CAACyB,cAAc,CAAED,OAAO,CAAC5C,EAAE,GAAK,OAAQ,CAAE,CAAC,cAC1FlB,IAAA,QAAKsC,SAAS,CAAC,yYAAyY,CAAM,CAAC,EAC1Z,CAAC,GARAwB,OAAO,CAAC5C,EASb,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACI,CACb,CAED,KAAM,CAAA8C,iBAAiB,CAAGA,CAAA,gBACxB9D,KAAA,CAACnB,MAAM,CAACkD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAErBvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACvEvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDrC,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBrC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvC,IAAA,OAAIsC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC5DvC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,0BAAwB,CAAG,CAAC,cACtEvC,IAAA,CAACL,MAAM,EAAC8C,OAAO,CAAC,SAAS,CAAAF,QAAA,CAAC,iBAAe,CAAQ,CAAC,EAC/C,CAAC,cACNrC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvC,IAAA,OAAIsC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cAC7EvC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,cAC5FvC,IAAA,CAACL,MAAM,EAAC8C,OAAO,CAAC,SAAS,CAAAF,QAAA,CAAC,YAAU,CAAQ,CAAC,EAC1C,CAAC,cACNrC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvC,IAAA,OAAIsC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAClEvC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mCAAiC,CAAG,CAAC,cAC/EvC,IAAA,CAACL,MAAM,EAAC8C,OAAO,CAAC,SAAS,CAAAF,QAAA,CAAC,eAAa,CAAQ,CAAC,EAC7C,CAAC,EACH,CAAC,CACH,CAAC,EACI,CACb,CAED,KAAM,CAAA0B,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQ1D,SAAS,EACf,IAAK,SAAS,CAAE,MAAO,CAAAyB,gBAAgB,CAAC,CAAC,CACzC,IAAK,QAAQ,CAAE,MAAO,CAAAuB,eAAe,CAAC,CAAC,CACvC,IAAK,UAAU,CAAE,MAAO,CAAAC,iBAAiB,CAAC,CAAC,CAC3C,IAAK,WAAW,CAAE,MAAO,CAAAC,kBAAkB,CAAC,CAAC,CAC7C,IAAK,UAAU,CAAE,MAAO,CAAAC,uBAAuB,CAAC,CAAC,CACjD,IAAK,eAAe,CAAE,MAAO,CAAAC,sBAAsB,CAAC,CAAC,CACrD,IAAK,UAAU,CAAE,MAAO,CAAAK,iBAAiB,CAAC,CAAC,CAC3C,QAAS,MAAO,CAAAhC,gBAAgB,CAAC,CAAC,CACpC,CACF,CAAC,CAED,mBACE9B,KAAA,QAAKoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCvC,IAAA,CAACF,OAAO,EAACoE,QAAQ,CAAC,WAAW,CAAE,CAAC,cAGhClE,IAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCrC,KAAA,QAAKoC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DvC,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAChEvC,IAAA,MAAGsC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8CAA4C,CAAG,CAAC,EAC1E,CAAC,CACH,CAAC,cAENvC,IAAA,QAAKsC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DrC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDvC,IAAA,QAAKsC,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BvC,IAAA,QAAKsC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDvC,IAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBtB,IAAI,CAAC4C,GAAG,CAAEM,GAAG,EAAK,CACjB,KAAM,CAAAC,IAAI,CAAGD,GAAG,CAAC/C,IAAI,CACrB,mBACElB,KAAA,WAEEsC,OAAO,CAAEA,CAAA,GAAMhC,YAAY,CAAC2D,GAAG,CAACjD,EAAE,CAAE,CACpCoB,SAAS,wFAAA+B,MAAA,CACP9D,SAAS,GAAK4D,GAAG,CAACjD,EAAE,CAChB,uDAAuD,CACvD,iCAAiC,CACpC,CAAAqB,QAAA,eAEHvC,IAAA,CAACoE,IAAI,EAAC9B,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BtC,IAAA,SAAAuC,QAAA,CAAO4B,GAAG,CAAChD,IAAI,CAAO,CAAC,GATlBgD,GAAG,CAACjD,EAUH,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAGNlB,IAAA,QAAKsC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3B0B,gBAAgB,CAAC,CAAC,CAChB,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9D,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}