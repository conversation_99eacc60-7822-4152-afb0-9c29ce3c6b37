{"ast": null, "code": "import { useEffectEvent as $ispOf$useEffectEvent, getOwnerDocument as $ispOf$getOwnerDocument } from \"@react-aria/utils\";\nimport { useRef as $ispOf$useRef, useEffect as $ispOf$useEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nfunction $e0b6e0b68ec7f50f$export$872b660ac5a1ff98(props) {\n  let {\n    ref: ref,\n    onInteractOutside: onInteractOutside,\n    isDisabled: isDisabled,\n    onInteractOutsideStart: onInteractOutsideStart\n  } = props;\n  let stateRef = (0, $ispOf$useRef)({\n    isPointerDown: false,\n    ignoreEmulatedMouseEvents: false\n  });\n  let onPointerDown = (0, $ispOf$useEffectEvent)(e => {\n    if (onInteractOutside && $e0b6e0b68ec7f50f$var$isValidEvent(e, ref)) {\n      if (onInteractOutsideStart) onInteractOutsideStart(e);\n      stateRef.current.isPointerDown = true;\n    }\n  });\n  let triggerInteractOutside = (0, $ispOf$useEffectEvent)(e => {\n    if (onInteractOutside) onInteractOutside(e);\n  });\n  (0, $ispOf$useEffect)(() => {\n    let state = stateRef.current;\n    if (isDisabled) return;\n    const element = ref.current;\n    const documentObject = (0, $ispOf$getOwnerDocument)(element);\n    // Use pointer events if available. Otherwise, fall back to mouse and touch events.\n    if (typeof PointerEvent !== 'undefined') {\n      let onClick = e => {\n        if (state.isPointerDown && $e0b6e0b68ec7f50f$var$isValidEvent(e, ref)) triggerInteractOutside(e);\n        state.isPointerDown = false;\n      };\n      // changing these to capture phase fixed combobox\n      // Use click instead of pointerup to avoid Android Chrome issue\n      // https://issues.chromium.org/issues/40732224\n      documentObject.addEventListener('pointerdown', onPointerDown, true);\n      documentObject.addEventListener('click', onClick, true);\n      return () => {\n        documentObject.removeEventListener('pointerdown', onPointerDown, true);\n        documentObject.removeEventListener('click', onClick, true);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      let onMouseUp = e => {\n        if (state.ignoreEmulatedMouseEvents) state.ignoreEmulatedMouseEvents = false;else if (state.isPointerDown && $e0b6e0b68ec7f50f$var$isValidEvent(e, ref)) triggerInteractOutside(e);\n        state.isPointerDown = false;\n      };\n      let onTouchEnd = e => {\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.isPointerDown && $e0b6e0b68ec7f50f$var$isValidEvent(e, ref)) triggerInteractOutside(e);\n        state.isPointerDown = false;\n      };\n      documentObject.addEventListener('mousedown', onPointerDown, true);\n      documentObject.addEventListener('mouseup', onMouseUp, true);\n      documentObject.addEventListener('touchstart', onPointerDown, true);\n      documentObject.addEventListener('touchend', onTouchEnd, true);\n      return () => {\n        documentObject.removeEventListener('mousedown', onPointerDown, true);\n        documentObject.removeEventListener('mouseup', onMouseUp, true);\n        documentObject.removeEventListener('touchstart', onPointerDown, true);\n        documentObject.removeEventListener('touchend', onTouchEnd, true);\n      };\n    }\n  }, [ref, isDisabled, onPointerDown, triggerInteractOutside]);\n}\nfunction $e0b6e0b68ec7f50f$var$isValidEvent(event, ref) {\n  if (event.button > 0) return false;\n  if (event.target) {\n    // if the event target is no longer in the document, ignore\n    const ownerDocument = event.target.ownerDocument;\n    if (!ownerDocument || !ownerDocument.documentElement.contains(event.target)) return false;\n    // If the target is within a top layer element (e.g. toasts), ignore.\n    if (event.target.closest('[data-react-aria-top-layer]')) return false;\n  }\n  if (!ref.current) return false;\n  // When the event source is inside a Shadow DOM, event.target is just the shadow root.\n  // Using event.composedPath instead means we can get the actual element inside the shadow root.\n  // This only works if the shadow root is open, there is no way to detect if it is closed.\n  // If the event composed path contains the ref, interaction is inside.\n  return !event.composedPath().includes(ref.current);\n}\nexport { $e0b6e0b68ec7f50f$export$872b660ac5a1ff98 as useInteractOutside };", "map": {"version": 3, "names": ["$e0b6e0b68ec7f50f$export$872b660ac5a1ff98", "props", "ref", "onInteractOutside", "isDisabled", "onInteractOutsideStart", "stateRef", "$ispOf$useRef", "isPointerDown", "ignoreEmulatedMouseEvents", "onPointerDown", "$ispOf$useEffectEvent", "e", "$e0b6e0b68ec7f50f$var$isValidEvent", "current", "triggerInteractOutside", "$ispOf$useEffect", "state", "element", "documentObject", "$ispOf$getOwnerDocument", "PointerEvent", "onClick", "addEventListener", "removeEventListener", "process", "env", "NODE_ENV", "onMouseUp", "onTouchEnd", "event", "button", "target", "ownerDocument", "documentElement", "contains", "closest", "<PERSON><PERSON><PERSON>", "includes"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useInteractOutside.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, useEffectEvent} from '@react-aria/utils';\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\n\nexport interface InteractOutsideProps {\n  ref: RefObject<Element | null>,\n  onInteractOutside?: (e: PointerEvent) => void,\n  onInteractOutsideStart?: (e: PointerEvent) => void,\n  /** Whether the interact outside events should be disabled. */\n  isDisabled?: boolean\n}\n\n/**\n * Example, used in components like Dialogs and Popovers so they can close\n * when a user clicks outside them.\n */\nexport function useInteractOutside(props: InteractOutsideProps): void {\n  let {ref, onInteractOutside, isDisabled, onInteractOutsideStart} = props;\n  let stateRef = useRef({\n    isPointerDown: false,\n    ignoreEmulatedMouseEvents: false\n  });\n\n  let onPointerDown = useEffectEvent((e) => {\n    if (onInteractOutside && isValidEvent(e, ref)) {\n      if (onInteractOutsideStart) {\n        onInteractOutsideStart(e);\n      }\n      stateRef.current.isPointerDown = true;\n    }\n  });\n\n  let triggerInteractOutside = useEffectEvent((e: PointerEvent) => {\n    if (onInteractOutside) {\n      onInteractOutside(e);\n    }\n  });\n\n  useEffect(() => {\n    let state = stateRef.current;\n    if (isDisabled) {\n      return;\n    }\n\n    const element = ref.current;\n    const documentObject = getOwnerDocument(element);\n\n    // Use pointer events if available. Otherwise, fall back to mouse and touch events.\n    if (typeof PointerEvent !== 'undefined') {\n      let onClick = (e) => {\n        if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      // changing these to capture phase fixed combobox\n      // Use click instead of pointerup to avoid Android Chrome issue\n      // https://issues.chromium.org/issues/40732224\n      documentObject.addEventListener('pointerdown', onPointerDown, true);\n      documentObject.addEventListener('click', onClick, true);\n\n      return () => {\n        documentObject.removeEventListener('pointerdown', onPointerDown, true);\n        documentObject.removeEventListener('click', onClick, true);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      let onMouseUp = (e) => {\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n        } else if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      let onTouchEnd = (e) => {\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      documentObject.addEventListener('mousedown', onPointerDown, true);\n      documentObject.addEventListener('mouseup', onMouseUp, true);\n      documentObject.addEventListener('touchstart', onPointerDown, true);\n      documentObject.addEventListener('touchend', onTouchEnd, true);\n\n      return () => {\n        documentObject.removeEventListener('mousedown', onPointerDown, true);\n        documentObject.removeEventListener('mouseup', onMouseUp, true);\n        documentObject.removeEventListener('touchstart', onPointerDown, true);\n        documentObject.removeEventListener('touchend', onTouchEnd, true);\n      };\n    }\n  }, [ref, isDisabled, onPointerDown, triggerInteractOutside]);\n}\n\nfunction isValidEvent(event, ref) {\n  if (event.button > 0) {\n    return false;\n  }\n  if (event.target) {\n    // if the event target is no longer in the document, ignore\n    const ownerDocument = event.target.ownerDocument;\n    if (!ownerDocument || !ownerDocument.documentElement.contains(event.target)) {\n      return false;\n    }\n    // If the target is within a top layer element (e.g. toasts), ignore.\n    if (event.target.closest('[data-react-aria-top-layer]')) {\n      return false;\n    }\n  }\n\n  if (!ref.current) {\n    return false;\n  }\n\n  // When the event source is inside a Shadow DOM, event.target is just the shadow root.\n  // Using event.composedPath instead means we can get the actual element inside the shadow root.\n  // This only works if the shadow root is open, there is no way to detect if it is closed.\n  // If the event composed path contains the ref, interaction is inside.\n  return !event.composedPath().includes(ref.current);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;;AAkBO,SAASA,0CAAmBC,KAA2B;EAC5D,IAAI;IAAAC,GAAA,EAACA,GAAG;IAAAC,iBAAA,EAAEA,iBAAiB;IAAAC,UAAA,EAAEA,UAAU;IAAAC,sBAAA,EAAEA;EAAsB,CAAC,GAAGJ,KAAA;EACnE,IAAIK,QAAA,GAAW,IAAAC,aAAK,EAAE;IACpBC,aAAA,EAAe;IACfC,yBAAA,EAA2B;EAC7B;EAEA,IAAIC,aAAA,GAAgB,IAAAC,qBAAa,EAAGC,CAAA;IAClC,IAAIT,iBAAA,IAAqBU,kCAAA,CAAaD,CAAA,EAAGV,GAAA,GAAM;MAC7C,IAAIG,sBAAA,EACFA,sBAAA,CAAuBO,CAAA;MAEzBN,QAAA,CAASQ,OAAO,CAACN,aAAa,GAAG;IACnC;EACF;EAEA,IAAIO,sBAAA,GAAyB,IAAAJ,qBAAa,EAAGC,CAAA;IAC3C,IAAIT,iBAAA,EACFA,iBAAA,CAAkBS,CAAA;EAEtB;EAEA,IAAAI,gBAAQ,EAAE;IACR,IAAIC,KAAA,GAAQX,QAAA,CAASQ,OAAO;IAC5B,IAAIV,UAAA,EACF;IAGF,MAAMc,OAAA,GAAUhB,GAAA,CAAIY,OAAO;IAC3B,MAAMK,cAAA,GAAiB,IAAAC,uBAAe,EAAEF,OAAA;IAExC;IACA,IAAI,OAAOG,YAAA,KAAiB,aAAa;MACvC,IAAIC,OAAA,GAAWV,CAAA;QACb,IAAIK,KAAA,CAAMT,aAAa,IAAIK,kCAAA,CAAaD,CAAA,EAAGV,GAAA,GACzCa,sBAAA,CAAuBH,CAAA;QAEzBK,KAAA,CAAMT,aAAa,GAAG;MACxB;MAEA;MACA;MACA;MACAW,cAAA,CAAeI,gBAAgB,CAAC,eAAeb,aAAA,EAAe;MAC9DS,cAAA,CAAeI,gBAAgB,CAAC,SAASD,OAAA,EAAS;MAElD,OAAO;QACLH,cAAA,CAAeK,mBAAmB,CAAC,eAAed,aAAA,EAAe;QACjES,cAAA,CAAeK,mBAAmB,CAAC,SAASF,OAAA,EAAS;MACvD;IACF,OAAO,IAAIG,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;MAC1C,IAAIC,SAAA,GAAahB,CAAA;QACf,IAAIK,KAAA,CAAMR,yBAAyB,EACjCQ,KAAA,CAAMR,yBAAyB,GAAG,WAC7B,IAAIQ,KAAA,CAAMT,aAAa,IAAIK,kCAAA,CAAaD,CAAA,EAAGV,GAAA,GAChDa,sBAAA,CAAuBH,CAAA;QAEzBK,KAAA,CAAMT,aAAa,GAAG;MACxB;MAEA,IAAIqB,UAAA,GAAcjB,CAAA;QAChBK,KAAA,CAAMR,yBAAyB,GAAG;QAClC,IAAIQ,KAAA,CAAMT,aAAa,IAAIK,kCAAA,CAAaD,CAAA,EAAGV,GAAA,GACzCa,sBAAA,CAAuBH,CAAA;QAEzBK,KAAA,CAAMT,aAAa,GAAG;MACxB;MAEAW,cAAA,CAAeI,gBAAgB,CAAC,aAAab,aAAA,EAAe;MAC5DS,cAAA,CAAeI,gBAAgB,CAAC,WAAWK,SAAA,EAAW;MACtDT,cAAA,CAAeI,gBAAgB,CAAC,cAAcb,aAAA,EAAe;MAC7DS,cAAA,CAAeI,gBAAgB,CAAC,YAAYM,UAAA,EAAY;MAExD,OAAO;QACLV,cAAA,CAAeK,mBAAmB,CAAC,aAAad,aAAA,EAAe;QAC/DS,cAAA,CAAeK,mBAAmB,CAAC,WAAWI,SAAA,EAAW;QACzDT,cAAA,CAAeK,mBAAmB,CAAC,cAAcd,aAAA,EAAe;QAChES,cAAA,CAAeK,mBAAmB,CAAC,YAAYK,UAAA,EAAY;MAC7D;IACF;EACF,GAAG,CAAC3B,GAAA,EAAKE,UAAA,EAAYM,aAAA,EAAeK,sBAAA,CAAuB;AAC7D;AAEA,SAASF,mCAAaiB,KAAK,EAAE5B,GAAG;EAC9B,IAAI4B,KAAA,CAAMC,MAAM,GAAG,GACjB,OAAO;EAET,IAAID,KAAA,CAAME,MAAM,EAAE;IAChB;IACA,MAAMC,aAAA,GAAgBH,KAAA,CAAME,MAAM,CAACC,aAAa;IAChD,IAAI,CAACA,aAAA,IAAiB,CAACA,aAAA,CAAcC,eAAe,CAACC,QAAQ,CAACL,KAAA,CAAME,MAAM,GACxE,OAAO;IAET;IACA,IAAIF,KAAA,CAAME,MAAM,CAACI,OAAO,CAAC,gCACvB,OAAO;EAEX;EAEA,IAAI,CAAClC,GAAA,CAAIY,OAAO,EACd,OAAO;EAGT;EACA;EACA;EACA;EACA,OAAO,CAACgB,KAAA,CAAMO,YAAY,GAAGC,QAAQ,CAACpC,GAAA,CAAIY,OAAO;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}