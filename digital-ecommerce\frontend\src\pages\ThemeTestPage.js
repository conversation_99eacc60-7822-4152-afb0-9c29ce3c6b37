import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import ThemeToggle from '../components/ThemeToggle';

const ThemeTestPage = () => {
  const { theme, getThemeClasses, isDark } = useTheme();

  console.log('Current theme:', theme);
  console.log('Is dark:', isDark);
  console.log('Document has dark class:', document.documentElement.classList.contains('dark'));

  return (
    <div className="min-h-screen p-8 transition-all duration-300 bg-gradient-to-br from-light-orange-50 to-white dark:bg-gradient-to-br dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Theme Toggle Test
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            Current theme: <span className="font-semibold capitalize">{theme}</span>
          </p>
          <ThemeToggle size="lg" showLabel />
        </div>

        {/* Test Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="p-6 rounded-xl shadow-lg transition-all duration-300 bg-white dark:bg-slate-800">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Card Title
            </h2>
            <p className="text-gray-600 mb-4">
              This is a test card to verify that the theme toggle is working properly across all components.
            </p>
            <button className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
              getThemeClasses(
                'bg-light-orange-500 text-white hover:bg-light-orange-600',
                'bg-light-orange-600 text-white hover:bg-light-orange-500'
              )
            }`}>
              Test Button
            </button>
          </div>

          <div className="p-6 rounded-xl shadow-lg transition-all duration-300 bg-white dark:bg-slate-800">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Another Card
            </h2>
            <p className="text-gray-600 mb-4">
              All text should remain clearly visible in both light and dark modes.
            </p>
            <div className="p-4 rounded-lg transition-all duration-300 bg-gray-100 dark:bg-slate-700">
              <p className="text-sm text-gray-700">
                Nested content should also adapt to the theme.
              </p>
            </div>
          </div>
        </div>

        {/* Text Samples */}
        <div className="p-8 rounded-xl shadow-lg transition-all duration-300 bg-white dark:bg-slate-800">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Background Color Test
          </h2>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-800">
              Notice how backgrounds change, but text stays readable
            </h3>

            <p className="text-lg text-gray-700">
              Large paragraph text remains consistent and readable in both themes.
            </p>

            <p className="text-gray-600">
              Regular paragraph text maintains good contrast ratios automatically.
            </p>

            <p className="text-sm text-gray-500">
              Small text is still readable thanks to CSS overrides in dark mode.
            </p>

            <div className="flex space-x-4 mt-6">
              <span className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${
                getThemeClasses('bg-green-100 text-green-800', 'bg-green-900/30 text-green-400')
              }`}>
                Success
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${
                getThemeClasses('bg-blue-100 text-blue-800', 'bg-blue-900/30 text-blue-400')
              }`}>
                Info
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${
                getThemeClasses('bg-yellow-100 text-yellow-800', 'bg-yellow-900/30 text-yellow-400')
              }`}>
                Warning
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${
                getThemeClasses('bg-red-100 text-red-800', 'bg-red-900/30 text-red-400')
              }`}>
                Error
              </span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className={`mt-12 p-6 rounded-xl border-2 border-dashed transition-all duration-300 ${
          getThemeClasses(
            'border-gray-300 bg-gray-50',
            'border-slate-600 bg-slate-700/50'
          )
        }`}>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Testing Instructions:
          </h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• Click the theme toggle button above to switch between light and dark modes</li>
            <li>• Notice how page backgrounds and cards change color</li>
            <li>• Verify that text remains readable with automatic contrast adjustments</li>
            <li>• Check that transitions are smooth without jarring changes</li>
            <li>• Refresh the page to ensure theme preference persists</li>
            <li>• Test on both desktop and mobile devices</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ThemeTestPage;
