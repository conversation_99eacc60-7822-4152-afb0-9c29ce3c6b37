[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "22", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js": "23", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js": "24", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "25", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js": "26", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js": "27", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js": "28", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js": "29", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js": "30", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js": "31", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js": "32", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js": "33", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js": "34", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js": "35", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js": "36", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js": "37", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js": "38", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js": "39", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js": "40", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js": "41", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js": "42", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js": "43"}, {"size": 584, "mtime": 1750249986668, "results": "44", "hashOfConfig": "45"}, {"size": 10289, "mtime": 1750273390085, "results": "46", "hashOfConfig": "45"}, {"size": 5003, "mtime": 1750108593800, "results": "47", "hashOfConfig": "45"}, {"size": 5867, "mtime": 1750158880553, "results": "48", "hashOfConfig": "45"}, {"size": 7488, "mtime": 1750158198494, "results": "49", "hashOfConfig": "45"}, {"size": 9872, "mtime": 1750164540216, "results": "50", "hashOfConfig": "45"}, {"size": 225, "mtime": 1750155963978, "results": "51", "hashOfConfig": "45"}, {"size": 12437, "mtime": 1750159769866, "results": "52", "hashOfConfig": "45"}, {"size": 10621, "mtime": 1750159108346, "results": "53", "hashOfConfig": "45"}, {"size": 13106, "mtime": 1750159208689, "results": "54", "hashOfConfig": "45"}, {"size": 9203, "mtime": 1750159280862, "results": "55", "hashOfConfig": "45"}, {"size": 237, "mtime": 1750156242633, "results": "56", "hashOfConfig": "45"}, {"size": 8712, "mtime": 1750159406548, "results": "57", "hashOfConfig": "45"}, {"size": 8223, "mtime": 1750159345006, "results": "58", "hashOfConfig": "45"}, {"size": 7010, "mtime": 1750159469458, "results": "59", "hashOfConfig": "45"}, {"size": 23203, "mtime": 1750273223190, "results": "60", "hashOfConfig": "45"}, {"size": 11440, "mtime": 1750164163184, "results": "61", "hashOfConfig": "45"}, {"size": 12667, "mtime": 1750164007467, "results": "62", "hashOfConfig": "45"}, {"size": 23909, "mtime": 1750270542017, "results": "63", "hashOfConfig": "45"}, {"size": 19371, "mtime": 1750271571376, "results": "64", "hashOfConfig": "45"}, {"size": 58693, "mtime": 1750167689845, "results": "65", "hashOfConfig": "45"}, {"size": 12456, "mtime": 1750164967846, "results": "66", "hashOfConfig": "45"}, {"size": 1653, "mtime": 1750164096843, "results": "67", "hashOfConfig": "45"}, {"size": 2903, "mtime": 1750164073874, "results": "68", "hashOfConfig": "45"}, {"size": 15294, "mtime": 1750164732603, "results": "69", "hashOfConfig": "45"}, {"size": 3218, "mtime": 1750164772685, "results": "70", "hashOfConfig": "45"}, {"size": 7547, "mtime": 1750166070393, "results": "71", "hashOfConfig": "45"}, {"size": 9102, "mtime": 1750166125481, "results": "72", "hashOfConfig": "45"}, {"size": 12346, "mtime": 1750166411028, "results": "73", "hashOfConfig": "45"}, {"size": 7212, "mtime": 1750166452419, "results": "74", "hashOfConfig": "45"}, {"size": 13681, "mtime": 1750166786153, "results": "75", "hashOfConfig": "45"}, {"size": 9048, "mtime": 1750166894082, "results": "76", "hashOfConfig": "45"}, {"size": 994, "mtime": 1750166931473, "results": "77", "hashOfConfig": "45"}, {"size": 1876, "mtime": 1750247300183, "results": "78", "hashOfConfig": "45"}, {"size": 8128, "mtime": 1750271460156, "results": "79", "hashOfConfig": "45"}, {"size": 9559, "mtime": 1750271826930, "results": "80", "hashOfConfig": "45"}, {"size": 4196, "mtime": 1750250970753, "results": "81", "hashOfConfig": "45"}, {"size": 8640, "mtime": 1750268837595, "results": "82", "hashOfConfig": "45"}, {"size": 31208, "mtime": 1750268789589, "results": "83", "hashOfConfig": "45"}, {"size": 6348, "mtime": 1750269567749, "results": "84", "hashOfConfig": "45"}, {"size": 12427, "mtime": 1750270320074, "results": "85", "hashOfConfig": "45"}, {"size": 10938, "mtime": 1750271106847, "results": "86", "hashOfConfig": "45"}, {"size": 21582, "mtime": 1750273332210, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 20, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js", ["217"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js", ["218"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js", ["219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js", ["246"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js", ["247"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js", ["248"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js", ["249"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js", ["250", "251", "252", "253"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js", ["254"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ModernNavigation.js", ["255"], [], {"ruleId": "256", "severity": 1, "message": "257", "line": 5, "column": 7, "nodeType": "258", "messageId": "259", "endLine": 5, "endColumn": 15}, {"ruleId": "256", "severity": 1, "message": "260", "line": 41, "column": 22, "nodeType": "258", "messageId": "259", "endLine": 41, "endColumn": 35}, {"ruleId": "256", "severity": 1, "message": "261", "line": 4, "column": 10, "nodeType": "258", "messageId": "259", "endLine": 4, "endColumn": 20}, {"ruleId": "256", "severity": 1, "message": "262", "line": 4, "column": 22, "nodeType": "258", "messageId": "259", "endLine": 4, "endColumn": 26}, {"ruleId": "256", "severity": 1, "message": "263", "line": 4, "column": 28, "nodeType": "258", "messageId": "259", "endLine": 4, "endColumn": 38}, {"ruleId": "256", "severity": 1, "message": "264", "line": 17, "column": 3, "nodeType": "258", "messageId": "259", "endLine": 17, "endColumn": 11}, {"ruleId": "256", "severity": 1, "message": "265", "line": 22, "column": 10, "nodeType": "258", "messageId": "259", "endLine": 22, "endColumn": 20}, {"ruleId": "256", "severity": 1, "message": "266", "line": 48, "column": 9, "nodeType": "258", "messageId": "259", "endLine": 48, "endColumn": 19}, {"ruleId": "256", "severity": 1, "message": "267", "line": 56, "column": 9, "nodeType": "258", "messageId": "259", "endLine": 56, "endColumn": 23}, {"ruleId": "268", "severity": 2, "message": "269", "line": 104, "column": 16, "nodeType": "258", "messageId": "270", "endLine": 104, "endColumn": 31}, {"ruleId": "268", "severity": 2, "message": "271", "line": 197, "column": 36, "nodeType": "258", "messageId": "270", "endLine": 197, "endColumn": 55}, {"ruleId": "268", "severity": 2, "message": "272", "line": 197, "column": 57, "nodeType": "258", "messageId": "270", "endLine": 197, "endColumn": 73}, {"ruleId": "268", "severity": 2, "message": "272", "line": 218, "column": 94, "nodeType": "258", "messageId": "270", "endLine": 218, "endColumn": 110}, {"ruleId": "268", "severity": 2, "message": "272", "line": 223, "column": 22, "nodeType": "258", "messageId": "270", "endLine": 223, "endColumn": 38}, {"ruleId": "268", "severity": 2, "message": "271", "line": 255, "column": 44, "nodeType": "258", "messageId": "270", "endLine": 255, "endColumn": 63}, {"ruleId": "268", "severity": 2, "message": "271", "line": 263, "column": 44, "nodeType": "258", "messageId": "270", "endLine": 263, "endColumn": 63}, {"ruleId": "268", "severity": 2, "message": "271", "line": 271, "column": 44, "nodeType": "258", "messageId": "270", "endLine": 271, "endColumn": 63}, {"ruleId": "268", "severity": 2, "message": "271", "line": 281, "column": 33, "nodeType": "258", "messageId": "270", "endLine": 281, "endColumn": 52}, {"ruleId": "268", "severity": 2, "message": "273", "line": 325, "column": 32, "nodeType": "258", "messageId": "270", "endLine": 325, "endColumn": 41}, {"ruleId": "268", "severity": 2, "message": "274", "line": 325, "column": 43, "nodeType": "258", "messageId": "270", "endLine": 325, "endColumn": 49}, {"ruleId": "268", "severity": 2, "message": "274", "line": 333, "column": 38, "nodeType": "258", "messageId": "270", "endLine": 333, "endColumn": 44}, {"ruleId": "268", "severity": 2, "message": "274", "line": 336, "column": 20, "nodeType": "258", "messageId": "270", "endLine": 336, "endColumn": 26}, {"ruleId": "268", "severity": 2, "message": "274", "line": 349, "column": 12, "nodeType": "258", "messageId": "270", "endLine": 349, "endColumn": 18}, {"ruleId": "268", "severity": 2, "message": "269", "line": 385, "column": 20, "nodeType": "258", "messageId": "270", "endLine": 385, "endColumn": 35}, {"ruleId": "268", "severity": 2, "message": "273", "line": 394, "column": 40, "nodeType": "258", "messageId": "270", "endLine": 394, "endColumn": 49}, {"ruleId": "268", "severity": 2, "message": "273", "line": 422, "column": 73, "nodeType": "258", "messageId": "270", "endLine": 422, "endColumn": 82}, {"ruleId": "268", "severity": 2, "message": "273", "line": 427, "column": 76, "nodeType": "258", "messageId": "270", "endLine": 427, "endColumn": 85}, {"ruleId": "268", "severity": 2, "message": "273", "line": 442, "column": 55, "nodeType": "258", "messageId": "270", "endLine": 442, "endColumn": 64}, {"ruleId": "268", "severity": 2, "message": "273", "line": 448, "column": 54, "nodeType": "258", "messageId": "270", "endLine": 448, "endColumn": 63}, {"ruleId": "256", "severity": 1, "message": "275", "line": 8, "column": 3, "nodeType": "258", "messageId": "259", "endLine": 8, "endColumn": 17}, {"ruleId": "256", "severity": 1, "message": "276", "line": 5, "column": 3, "nodeType": "258", "messageId": "259", "endLine": 5, "endColumn": 10}, {"ruleId": "256", "severity": 1, "message": "277", "line": 18, "column": 37, "nodeType": "258", "messageId": "259", "endLine": 18, "endColumn": 49}, {"ruleId": "278", "severity": 1, "message": "279", "line": 54, "column": 6, "nodeType": "280", "endLine": 54, "endColumn": 8, "suggestions": "281"}, {"ruleId": "256", "severity": 1, "message": "282", "line": 5, "column": 3, "nodeType": "258", "messageId": "259", "endLine": 5, "endColumn": 12}, {"ruleId": "278", "severity": 1, "message": "283", "line": 47, "column": 6, "nodeType": "280", "endLine": 47, "endColumn": 21, "suggestions": "284"}, {"ruleId": "285", "severity": 1, "message": "286", "line": 60, "column": 5, "nodeType": "287", "messageId": "288", "endLine": 87, "endColumn": 6}, {"ruleId": "256", "severity": 1, "message": "289", "line": 152, "column": 9, "nodeType": "258", "messageId": "259", "endLine": 152, "endColumn": 18}, {"ruleId": "256", "severity": 1, "message": "290", "line": 67, "column": 9, "nodeType": "258", "messageId": "259", "endLine": 67, "endColumn": 24}, {"ruleId": "256", "severity": 1, "message": "291", "line": 3, "column": 18, "nodeType": "258", "messageId": "259", "endLine": 3, "endColumn": 33}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'setPromotions' is assigned a value but never used.", "'Disclosure' is defined but never used.", "'Menu' is defined but never used.", "'Transition' is defined but never used.", "'BellIcon' is defined but never used.", "'classNames' is defined but never used.", "'navigation' is assigned a value but never used.", "'userNavigation' is assigned a value but never used.", "no-undef", "'navigationItems' is not defined.", "undef", "'setShowUserDropdown' is not defined.", "'showUserDropdown' is not defined.", "'setIsOpen' is not defined.", "'isOpen' is not defined.", "'LockClosedIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'isInWishlist' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'categories.length' and 'products'. Either include them or remove the dependency array.", "ArrayExpression", ["292"], "'PhotoIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'formData.sku'. Either include it or remove the dependency array.", ["293"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'moveImage' is assigned a value but never used.", "'handleSelectAll' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", {"desc": "294", "fix": "295"}, {"desc": "296", "fix": "297"}, "Update the dependencies array to be: [categories.length, products]", {"range": "298", "text": "299"}, "Update the dependencies array to be: [formData.name, formData.sku]", {"range": "300", "text": "301"}, [1651, 1653], "[categories.length, products]", [1294, 1309], "[formData.name, formData.sku]"]