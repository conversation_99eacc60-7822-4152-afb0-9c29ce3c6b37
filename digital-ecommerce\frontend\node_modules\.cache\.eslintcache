[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "22", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js": "23", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js": "24", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "25", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js": "26", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js": "27", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js": "28", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js": "29", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js": "30", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js": "31", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js": "32", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js": "33", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js": "34", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js": "35", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js": "36", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js": "37", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js": "38", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js": "39", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js": "40", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js": "41"}, {"size": 584, "mtime": 1750249986668, "results": "42", "hashOfConfig": "43"}, {"size": 10271, "mtime": 1750268906967, "results": "44", "hashOfConfig": "43"}, {"size": 5003, "mtime": 1750108593800, "results": "45", "hashOfConfig": "43"}, {"size": 5867, "mtime": 1750158880553, "results": "46", "hashOfConfig": "43"}, {"size": 7488, "mtime": 1750158198494, "results": "47", "hashOfConfig": "43"}, {"size": 9872, "mtime": 1750164540216, "results": "48", "hashOfConfig": "43"}, {"size": 225, "mtime": 1750155963978, "results": "49", "hashOfConfig": "43"}, {"size": 12437, "mtime": 1750159769866, "results": "50", "hashOfConfig": "43"}, {"size": 10621, "mtime": 1750159108346, "results": "51", "hashOfConfig": "43"}, {"size": 13106, "mtime": 1750159208689, "results": "52", "hashOfConfig": "43"}, {"size": 9203, "mtime": 1750159280862, "results": "53", "hashOfConfig": "43"}, {"size": 237, "mtime": 1750156242633, "results": "54", "hashOfConfig": "43"}, {"size": 8712, "mtime": 1750159406548, "results": "55", "hashOfConfig": "43"}, {"size": 8223, "mtime": 1750159345006, "results": "56", "hashOfConfig": "43"}, {"size": 7010, "mtime": 1750159469458, "results": "57", "hashOfConfig": "43"}, {"size": 13965, "mtime": 1750268343880, "results": "58", "hashOfConfig": "43"}, {"size": 11440, "mtime": 1750164163184, "results": "59", "hashOfConfig": "43"}, {"size": 12667, "mtime": 1750164007467, "results": "60", "hashOfConfig": "43"}, {"size": 24414, "mtime": 1750248495502, "results": "61", "hashOfConfig": "43"}, {"size": 20349, "mtime": 1750248186883, "results": "62", "hashOfConfig": "43"}, {"size": 58693, "mtime": 1750167689845, "results": "63", "hashOfConfig": "43"}, {"size": 12456, "mtime": 1750164967846, "results": "64", "hashOfConfig": "43"}, {"size": 1653, "mtime": 1750164096843, "results": "65", "hashOfConfig": "43"}, {"size": 2903, "mtime": 1750164073874, "results": "66", "hashOfConfig": "43"}, {"size": 15294, "mtime": 1750164732603, "results": "67", "hashOfConfig": "43"}, {"size": 3218, "mtime": 1750164772685, "results": "68", "hashOfConfig": "43"}, {"size": 7547, "mtime": 1750166070393, "results": "69", "hashOfConfig": "43"}, {"size": 9102, "mtime": 1750166125481, "results": "70", "hashOfConfig": "43"}, {"size": 12346, "mtime": 1750166411028, "results": "71", "hashOfConfig": "43"}, {"size": 7212, "mtime": 1750166452419, "results": "72", "hashOfConfig": "43"}, {"size": 13681, "mtime": 1750166786153, "results": "73", "hashOfConfig": "43"}, {"size": 9048, "mtime": 1750166894082, "results": "74", "hashOfConfig": "43"}, {"size": 994, "mtime": 1750166931473, "results": "75", "hashOfConfig": "43"}, {"size": 1876, "mtime": 1750247300183, "results": "76", "hashOfConfig": "43"}, {"size": 20270, "mtime": 1750269151539, "results": "77", "hashOfConfig": "43"}, {"size": 9553, "mtime": 1750252078182, "results": "78", "hashOfConfig": "43"}, {"size": 10586, "mtime": 1750268999096, "results": "79", "hashOfConfig": "43"}, {"size": 4196, "mtime": 1750250970753, "results": "80", "hashOfConfig": "43"}, {"size": 13797, "mtime": 1750269260136, "results": "81", "hashOfConfig": "43"}, {"size": 8640, "mtime": 1750268837595, "results": "82", "hashOfConfig": "43"}, {"size": 31208, "mtime": 1750268789589, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 31, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 28, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js", ["207"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js", ["208"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js", ["209"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js", ["210"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js", ["211"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js", ["212"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminProductsPage.js", ["213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminDashboardPage.js", ["246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\AdminContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AdminCategoriesPage.js", ["262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\ProductContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AddProductModal.js", ["290", "291", "292", "293"], [], {"ruleId": "294", "severity": 1, "message": "295", "line": 5, "column": 7, "nodeType": "296", "messageId": "297", "endLine": 5, "endColumn": 15}, {"ruleId": "294", "severity": 1, "message": "298", "line": 41, "column": 22, "nodeType": "296", "messageId": "297", "endLine": 41, "endColumn": 35}, {"ruleId": "294", "severity": 1, "message": "299", "line": 26, "column": 9, "nodeType": "296", "messageId": "297", "endLine": 26, "endColumn": 21}, {"ruleId": "294", "severity": 1, "message": "300", "line": 8, "column": 3, "nodeType": "296", "messageId": "297", "endLine": 8, "endColumn": 17}, {"ruleId": "294", "severity": 1, "message": "301", "line": 5, "column": 3, "nodeType": "296", "messageId": "297", "endLine": 5, "endColumn": 10}, {"ruleId": "294", "severity": 1, "message": "302", "line": 18, "column": 37, "nodeType": "296", "messageId": "297", "endLine": 18, "endColumn": 49}, {"ruleId": "294", "severity": 1, "message": "303", "line": 13, "column": 10, "nodeType": "296", "messageId": "297", "endLine": 13, "endColumn": 18}, {"ruleId": "304", "severity": 1, "message": "305", "line": 58, "column": 6, "nodeType": "306", "endLine": 58, "endColumn": 59, "suggestions": "307"}, {"ruleId": "308", "severity": 2, "message": "309", "line": 83, "column": 9, "nodeType": "296", "messageId": "310", "endLine": 83, "endColumn": 24}, {"ruleId": "308", "severity": 2, "message": "309", "line": 113, "column": 11, "nodeType": "296", "messageId": "310", "endLine": 113, "endColumn": 26}, {"ruleId": "308", "severity": 2, "message": "309", "line": 118, "column": 11, "nodeType": "296", "messageId": "310", "endLine": 118, "endColumn": 26}, {"ruleId": "308", "severity": 2, "message": "309", "line": 128, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 128, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 139, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 139, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 145, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 145, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 152, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 152, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 176, "column": 9, "nodeType": "296", "messageId": "310", "endLine": 176, "endColumn": 24}, {"ruleId": "308", "severity": 2, "message": "309", "line": 196, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 196, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 201, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 201, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 234, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 234, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 240, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 240, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 247, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 247, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 264, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 264, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 269, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 269, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 289, "column": 11, "nodeType": "296", "messageId": "310", "endLine": 289, "endColumn": 26}, {"ruleId": "308", "severity": 2, "message": "309", "line": 301, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 301, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 315, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 315, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 362, "column": 23, "nodeType": "296", "messageId": "310", "endLine": 362, "endColumn": 38}, {"ruleId": "308", "severity": 2, "message": "309", "line": 380, "column": 23, "nodeType": "296", "messageId": "310", "endLine": 380, "endColumn": 38}, {"ruleId": "308", "severity": 2, "message": "309", "line": 395, "column": 23, "nodeType": "296", "messageId": "310", "endLine": 395, "endColumn": 38}, {"ruleId": "308", "severity": 2, "message": "309", "line": 415, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 415, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 422, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 422, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 444, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 444, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 447, "column": 33, "nodeType": "296", "messageId": "310", "endLine": 447, "endColumn": 48}, {"ruleId": "308", "severity": 2, "message": "309", "line": 458, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 458, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 463, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 463, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 468, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 468, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 473, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 473, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 478, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 478, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 485, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 485, "endColumn": 32}, {"ruleId": "304", "severity": 1, "message": "311", "line": 54, "column": 6, "nodeType": "306", "endLine": 54, "endColumn": 8, "suggestions": "312"}, {"ruleId": "308", "severity": 2, "message": "309", "line": 61, "column": 9, "nodeType": "296", "messageId": "310", "endLine": 61, "endColumn": 24}, {"ruleId": "308", "severity": 2, "message": "309", "line": 67, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 67, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 72, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 72, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 115, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 115, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 120, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 120, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 177, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 177, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 182, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 182, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 187, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 187, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 202, "column": 23, "nodeType": "296", "messageId": "310", "endLine": 202, "endColumn": 38}, {"ruleId": "308", "severity": 2, "message": "309", "line": 207, "column": 23, "nodeType": "296", "messageId": "310", "endLine": 207, "endColumn": 38}, {"ruleId": "308", "severity": 2, "message": "309", "line": 213, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 213, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 227, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 227, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 232, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 232, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 252, "column": 27, "nodeType": "296", "messageId": "310", "endLine": 252, "endColumn": 42}, {"ruleId": "308", "severity": 2, "message": "309", "line": 268, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 268, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 74, "column": 9, "nodeType": "296", "messageId": "310", "endLine": 74, "endColumn": 24}, {"ruleId": "308", "severity": 2, "message": "309", "line": 82, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 82, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 87, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 87, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 98, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 98, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 106, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 106, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 118, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 118, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 127, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 127, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 158, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 158, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 163, "column": 17, "nodeType": "296", "messageId": "310", "endLine": 163, "endColumn": 32}, {"ruleId": "308", "severity": 2, "message": "309", "line": 170, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 170, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 180, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 180, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 189, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 189, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 200, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 200, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 209, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 209, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 219, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 219, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 229, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 229, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 242, "column": 21, "nodeType": "296", "messageId": "310", "endLine": 242, "endColumn": 36}, {"ruleId": "308", "severity": 2, "message": "309", "line": 271, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 271, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 276, "column": 15, "nodeType": "296", "messageId": "310", "endLine": 276, "endColumn": 30}, {"ruleId": "308", "severity": 2, "message": "309", "line": 297, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 297, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 305, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 305, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 310, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 310, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 319, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 319, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 327, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 327, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 332, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 332, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 341, "column": 13, "nodeType": "296", "messageId": "310", "endLine": 341, "endColumn": 28}, {"ruleId": "308", "severity": 2, "message": "309", "line": 349, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 349, "endColumn": 34}, {"ruleId": "308", "severity": 2, "message": "309", "line": 354, "column": 19, "nodeType": "296", "messageId": "310", "endLine": 354, "endColumn": 34}, {"ruleId": "294", "severity": 1, "message": "313", "line": 5, "column": 3, "nodeType": "296", "messageId": "297", "endLine": 5, "endColumn": 12}, {"ruleId": "304", "severity": 1, "message": "314", "line": 47, "column": 6, "nodeType": "306", "endLine": 47, "endColumn": 21, "suggestions": "315"}, {"ruleId": "316", "severity": 1, "message": "317", "line": 60, "column": 5, "nodeType": "318", "messageId": "319", "endLine": 87, "endColumn": 6}, {"ruleId": "294", "severity": 1, "message": "320", "line": 152, "column": 9, "nodeType": "296", "messageId": "297", "endLine": 152, "endColumn": 18}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'setPromotions' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'LockClosedIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'isInWishlist' is assigned a value but never used.", "'useTheme' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'products'. Either include it or remove the dependency array.", "ArrayExpression", ["321"], "no-undef", "'getThemeClasses' is not defined.", "undef", "React Hook useEffect has missing dependencies: 'categories.length' and 'products'. Either include them or remove the dependency array.", ["322"], "'PhotoIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'formData.sku'. Either include it or remove the dependency array.", ["323"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'moveImage' is assigned a value but never used.", {"desc": "324", "fix": "325"}, {"desc": "326", "fix": "327"}, {"desc": "328", "fix": "329"}, "Update the dependencies array to be: [products, searchQuery, selectedCategory, selectedType, sortBy]", {"range": "330", "text": "331"}, "Update the dependencies array to be: [categories.length, products]", {"range": "332", "text": "333"}, "Update the dependencies array to be: [formData.name, formData.sku]", {"range": "334", "text": "335"}, [2153, 2206], "[products, searchQuery, selectedCategory, selectedType, sortBy]", [1651, 1653], "[categories.length, products]", [1294, 1309], "[formData.name, formData.sku]"]