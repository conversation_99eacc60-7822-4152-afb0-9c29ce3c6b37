{"ast": null, "code": "import n, { createContext as r, useContext as i } from \"react\";\nlet e = r(void 0);\nfunction a() {\n  return i(e);\n}\nfunction l(_ref) {\n  let {\n    value: t,\n    children: o\n  } = _ref;\n  return n.createElement(e.Provider, {\n    value: t\n  }, o);\n}\nexport { l as DisabledProvider, a as useDisabled };", "map": {"version": 3, "names": ["n", "createContext", "r", "useContext", "i", "e", "a", "l", "_ref", "value", "t", "children", "o", "createElement", "Provider", "Disable<PERSON><PERSON><PERSON><PERSON>", "useDisabled"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/internal/disabled.js"], "sourcesContent": ["import n,{createContext as r,useContext as i}from\"react\";let e=r(void 0);function a(){return i(e)}function l({value:t,children:o}){return n.createElement(e.Provider,{value:t},o)}export{l as DisabledProvider,a as useDisabled};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,IAAIC,CAAC,GAACH,CAAC,CAAC,KAAK,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAAAC,IAAA,EAAsB;EAAA,IAArB;IAACC,KAAK,EAACC,CAAC;IAACC,QAAQ,EAACC;EAAC,CAAC,GAAAJ,IAAA;EAAE,OAAOR,CAAC,CAACa,aAAa,CAACR,CAAC,CAACS,QAAQ,EAAC;IAACL,KAAK,EAACC;EAAC,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIQ,gBAAgB,EAACT,CAAC,IAAIU,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}