{"ast": null, "code": "import { useMemo as t } from \"react\";\nimport { getOwnerDocument as o } from '../utils/owner.js';\nfunction n() {\n  for (var _len = arguments.length, e = new Array(_len), _key = 0; _key < _len; _key++) {\n    e[_key] = arguments[_key];\n  }\n  return t(() => o(...e), [...e]);\n}\nexport { n as useOwnerDocument };", "map": {"version": 3, "names": ["useMemo", "t", "getOwnerDocument", "o", "n", "_len", "arguments", "length", "e", "Array", "_key", "useOwnerDocument"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-owner.js"], "sourcesContent": ["import{useMemo as t}from\"react\";import{getOwnerDocument as o}from'../utils/owner.js';function n(...e){return t(()=>o(...e),[...e])}export{n as useOwnerDocument};\n"], "mappings": "AAAA,SAAOA,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAASC,CAACA,CAAA,EAAM;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAFC,CAAC,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAADF,CAAC,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAAE,OAAOT,CAAC,CAAC,MAAIE,CAAC,CAAC,GAAGK,CAAC,CAAC,EAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}