{"ast": null, "code": "import { createSyntheticEvent as $8a9cb279dc87e130$export$525bc4921d56d4a, setEventTarget as $8a9cb279dc87e130$export$c2b7abe5d61ec696, useSyntheticBlurEvent as $8a9cb279dc87e130$export$715c682d09d639cc } from \"./utils.mjs\";\nimport { useRef as $3b9Q0$useRef, useCallback as $3b9Q0$useCallback } from \"react\";\nimport { useGlobalListeners as $3b9Q0$useGlobalListeners, getOwnerDocument as $3b9Q0$getOwnerDocument, getActiveElement as $3b9Q0$getActiveElement, getEventTarget as $3b9Q0$getEventTarget, nodeContains as $3b9Q0$nodeContains } from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nfunction $9ab94262bd0047c7$export$420e68273165f4ec(props) {\n  let {\n    isDisabled: isDisabled,\n    onBlurWithin: onBlurWithin,\n    onFocusWithin: onFocusWithin,\n    onFocusWithinChange: onFocusWithinChange\n  } = props;\n  let state = (0, $3b9Q0$useRef)({\n    isFocusWithin: false\n  });\n  let {\n    addGlobalListener: addGlobalListener,\n    removeAllGlobalListeners: removeAllGlobalListeners\n  } = (0, $3b9Q0$useGlobalListeners)();\n  let onBlur = (0, $3b9Q0$useCallback)(e => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) return;\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !e.currentTarget.contains(e.relatedTarget)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n      if (onBlurWithin) onBlurWithin(e);\n      if (onFocusWithinChange) onFocusWithinChange(false);\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n  let onSyntheticFocus = (0, $8a9cb279dc87e130$export$715c682d09d639cc)(onBlur);\n  let onFocus = (0, $3b9Q0$useCallback)(e => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) return;\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = (0, $3b9Q0$getOwnerDocument)(e.target);\n    const activeElement = (0, $3b9Q0$getActiveElement)(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === (0, $3b9Q0$getEventTarget)(e.nativeEvent)) {\n      if (onFocusWithin) onFocusWithin(e);\n      if (onFocusWithinChange) onFocusWithinChange(true);\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !(0, $3b9Q0$nodeContains)(currentTarget, e.target)) {\n          let nativeEvent = new ownerDocument.defaultView.FocusEvent('blur', {\n            relatedTarget: e.target\n          });\n          (0, $8a9cb279dc87e130$export$c2b7abe5d61ec696)(nativeEvent, currentTarget);\n          let event = (0, $8a9cb279dc87e130$export$525bc4921d56d4a)(nativeEvent);\n          onBlur(event);\n        }\n      }, {\n        capture: true\n      });\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n  if (isDisabled) return {\n    focusWithinProps: {\n      // These cannot be null, that would conflict in mergeProps\n      onFocus: undefined,\n      onBlur: undefined\n    }\n  };\n  return {\n    focusWithinProps: {\n      onFocus: onFocus,\n      onBlur: onBlur\n    }\n  };\n}\nexport { $9ab94262bd0047c7$export$420e68273165f4ec as useFocusWithin };", "map": {"version": 3, "names": ["$9ab94262bd0047c7$export$420e68273165f4ec", "props", "isDisabled", "onBlurWithin", "onFocusWithin", "onFocusWithinChange", "state", "$3b9Q0$useRef", "isFocusWithin", "addGlobalListener", "removeAllGlobalListeners", "$3b9Q0$useGlobalListeners", "onBlur", "$3b9Q0$useCallback", "e", "currentTarget", "contains", "target", "current", "relatedTarget", "onSyntheticFocus", "$8a9cb279dc87e130$export$715c682d09d639cc", "onFocus", "ownerDocument", "$3b9Q0$getOwnerDocument", "activeElement", "$3b9Q0$getActiveElement", "$3b9Q0$getEventTarget", "nativeEvent", "$3b9Q0$nodeContains", "defaultView", "FocusEvent", "$8a9cb279dc87e130$export$c2b7abe5d61ec696", "event", "$8a9cb279dc87e130$export$525bc4921d56d4a", "capture", "focusWithinProps", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\interactions\\dist\\packages\\@react-aria\\interactions\\src\\useFocusWithin.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {createSyntheticEvent, setEventTarget, useSyntheticBlurEvent} from './utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {FocusEvent, useCallback, useRef} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\n\nexport interface FocusWithinProps {\n  /** Whether the focus within events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when the target element or a descendant receives focus. */\n  onFocusWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the target element and all descendants lose focus. */\n  onBlurWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the the focus within state changes. */\n  onFocusWithinChange?: (isFocusWithin: boolean) => void\n}\n\nexport interface FocusWithinResult {\n  /** Props to spread onto the target element. */\n  focusWithinProps: DOMAttributes\n}\n\n/**\n * Handles focus events for the target and its descendants.\n */\nexport function useFocusWithin(props: FocusWithinProps): FocusWithinResult {\n  let {\n    isDisabled,\n    onBlurWithin,\n    onFocusWithin,\n    onFocusWithinChange\n  } = props;\n  let state = useRef({\n    isFocusWithin: false\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let onBlur = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !(e.currentTarget as Element).contains(e.relatedTarget as Element)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n\n      if (onBlurWithin) {\n        onBlurWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(false);\n      }\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n\n  let onSyntheticFocus = useSyntheticBlurEvent(onBlur);\n  let onFocus = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = getActiveElement(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusWithin) {\n        onFocusWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(true);\n      }\n\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !nodeContains(currentTarget, e.target as Element)) {\n          let nativeEvent = new ownerDocument.defaultView!.FocusEvent('blur', {relatedTarget: e.target});\n          setEventTarget(nativeEvent, currentTarget);\n          let event = createSyntheticEvent<FocusEvent>(nativeEvent);\n          onBlur(event);\n        }\n      }, {capture: true});\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n\n  if (isDisabled) {\n    return {\n      focusWithinProps: {\n        // These cannot be null, that would conflict in mergeProps\n        onFocus: undefined,\n        onBlur: undefined\n      }\n    };\n  }\n\n  return {\n    focusWithinProps: {\n      onFocus,\n      onBlur\n    }\n  };\n}\n"], "mappings": ";;;;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;AACA;;AA0BO,SAASA,0CAAeC,KAAuB;EACpD,IAAI;IAAAC,UAAA,EACFA,UAAU;IAAAC,YAAA,EACVA,YAAY;IAAAC,aAAA,EACZA,aAAa;IAAAC,mBAAA,EACbA;EAAmB,CACpB,GAAGJ,KAAA;EACJ,IAAIK,KAAA,GAAQ,IAAAC,aAAK,EAAE;IACjBC,aAAA,EAAe;EACjB;EAEA,IAAI;IAAAC,iBAAA,EAACA,iBAAiB;IAAAC,wBAAA,EAAEA;EAAwB,CAAC,GAAG,IAAAC,yBAAiB;EAErE,IAAIC,MAAA,GAAS,IAAAC,kBAAU,EAAGC,CAAA;IACxB;IACA,IAAI,CAACA,CAAA,CAAEC,aAAa,CAACC,QAAQ,CAACF,CAAA,CAAEG,MAAM,GACpC;IAGF;IACA;IACA;IACA,IAAIX,KAAA,CAAMY,OAAO,CAACV,aAAa,IAAI,CAACM,CAAC,CAAEC,aAAa,CAAaC,QAAQ,CAACF,CAAA,CAAEK,aAAa,GAAc;MACrGb,KAAA,CAAMY,OAAO,CAACV,aAAa,GAAG;MAC9BE,wBAAA;MAEA,IAAIP,YAAA,EACFA,YAAA,CAAaW,CAAA;MAGf,IAAIT,mBAAA,EACFA,mBAAA,CAAoB;IAExB;EACF,GAAG,CAACF,YAAA,EAAcE,mBAAA,EAAqBC,KAAA,EAAOI,wBAAA,CAAyB;EAEvE,IAAIU,gBAAA,GAAmB,IAAAC,yCAAoB,EAAET,MAAA;EAC7C,IAAIU,OAAA,GAAU,IAAAT,kBAAU,EAAGC,CAAA;IACzB;IACA,IAAI,CAACA,CAAA,CAAEC,aAAa,CAACC,QAAQ,CAACF,CAAA,CAAEG,MAAM,GACpC;IAGF;IACA;IACA,MAAMM,aAAA,GAAgB,IAAAC,uBAAe,EAAEV,CAAA,CAAEG,MAAM;IAC/C,MAAMQ,aAAA,GAAgB,IAAAC,uBAAe,EAAEH,aAAA;IACvC,IAAI,CAACjB,KAAA,CAAMY,OAAO,CAACV,aAAa,IAAIiB,aAAA,KAAkB,IAAAE,qBAAa,EAAEb,CAAA,CAAEc,WAAW,GAAG;MACnF,IAAIxB,aAAA,EACFA,aAAA,CAAcU,CAAA;MAGhB,IAAIT,mBAAA,EACFA,mBAAA,CAAoB;MAGtBC,KAAA,CAAMY,OAAO,CAACV,aAAa,GAAG;MAC9BY,gBAAA,CAAiBN,CAAA;MAEjB;MACA;MACA;MACA,IAAIC,aAAA,GAAgBD,CAAA,CAAEC,aAAa;MACnCN,iBAAA,CAAkBc,aAAA,EAAe,SAAST,CAAA;QACxC,IAAIR,KAAA,CAAMY,OAAO,CAACV,aAAa,IAAI,CAAC,IAAAqB,mBAAW,EAAEd,aAAA,EAAeD,CAAA,CAAEG,MAAM,GAAc;UACpF,IAAIW,WAAA,GAAc,IAAIL,aAAA,CAAcO,WAAW,CAAEC,UAAU,CAAC,QAAQ;YAACZ,aAAA,EAAeL,CAAA,CAAEG;UAAM;UAC5F,IAAAe,yCAAa,EAAEJ,WAAA,EAAab,aAAA;UAC5B,IAAIkB,KAAA,GAAQ,IAAAC,wCAAmB,EAAcN,WAAA;UAC7ChB,MAAA,CAAOqB,KAAA;QACT;MACF,GAAG;QAACE,OAAA,EAAS;MAAI;IACnB;EACF,GAAG,CAAC/B,aAAA,EAAeC,mBAAA,EAAqBe,gBAAA,EAAkBX,iBAAA,EAAmBG,MAAA,CAAO;EAEpF,IAAIV,UAAA,EACF,OAAO;IACLkC,gBAAA,EAAkB;MAChB;MACAd,OAAA,EAASe,SAAA;MACTzB,MAAA,EAAQyB;IACV;EACF;EAGF,OAAO;IACLD,gBAAA,EAAkB;eAChBd,OAAA;cACAV;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}