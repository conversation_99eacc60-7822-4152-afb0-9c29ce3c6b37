{"ast": null, "code": "import { useCallback as r, useState as a } from \"react\";\nimport * as i from '../utils/dom.js';\nfunction d(t) {\n  let e = typeof t == \"string\" ? t : void 0,\n    [s, o] = a(e);\n  return [e != null ? e : s, r(n => {\n    e || i.isHTMLElement(n) && o(n.tagName.toLowerCase());\n  }, [e])];\n}\nexport { d as useResolvedTag };", "map": {"version": 3, "names": ["useCallback", "r", "useState", "a", "i", "d", "t", "e", "s", "o", "n", "isHTMLElement", "tagName", "toLowerCase", "useResolvedTag"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-resolved-tag.js"], "sourcesContent": ["import{useCallback as r,useState as a}from\"react\";import*as i from'../utils/dom.js';function d(t){let e=typeof t==\"string\"?t:void 0,[s,o]=a(e);return[e!=null?e:s,r(n=>{e||i.isHTMLElement(n)&&o(n.tagName.toLowerCase())},[e])]}export{d as useResolvedTag};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,OAAM,KAAIC,CAAC,MAAK,iBAAiB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC,OAAOD,CAAC,IAAE,QAAQ,GAACA,CAAC,GAAC,KAAK,CAAC;IAAC,CAACE,CAAC,EAACC,CAAC,CAAC,GAACN,CAAC,CAACI,CAAC,CAAC;EAAC,OAAM,CAACA,CAAC,IAAE,IAAI,GAACA,CAAC,GAACC,CAAC,EAACP,CAAC,CAACS,CAAC,IAAE;IAACH,CAAC,IAAEH,CAAC,CAACO,aAAa,CAACD,CAAC,CAAC,IAAED,CAAC,CAACC,CAAC,CAACE,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACN,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}