{"ast": null, "code": "import { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useRef as $azsE2$useRef } from \"react\";\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $ca9b37712f007381$export$72ef708ab07251f1(effect, dependencies) {\n  const isInitialMount = (0, $azsE2$useRef)(true);\n  const lastDeps = (0, $azsE2$useRef)(null);\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    if (isInitialMount.current) isInitialMount.current = false;else if (!lastDeps.current || dependencies.some((dep, i) => !Object.is(dep, lastDeps[i]))) effect();\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\nexport { $ca9b37712f007381$export$72ef708ab07251f1 as useUpdateLayoutEffect };", "map": {"version": 3, "names": ["$ca9b37712f007381$export$72ef708ab07251f1", "effect", "dependencies", "isInitialMount", "$azsE2$useRef", "lastDeps", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "current", "some", "dep", "i", "Object", "is"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useUpdateLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {EffectCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\n// Like useLayoutEffect, but only called for updates after the initial render.\nexport function useUpdateLayoutEffect(effect: EffectCallback, dependencies: any[]): void {\n  const isInitialMount = useRef(true);\n  const lastDeps = useRef<any[] | null>(null);\n\n  useLayoutEffect(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n\n  useLayoutEffect(() => {\n    if (isInitialMount.current) {\n      isInitialMount.current = false;\n    } else if (!lastDeps.current || dependencies.some((dep, i) => !Object.is(dep, lastDeps[i]))) {\n      effect();\n    }\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAgBO,SAASA,0CAAsBC,MAAsB,EAAEC,YAAmB;EAC/E,MAAMC,cAAA,GAAiB,IAAAC,aAAK,EAAE;EAC9B,MAAMC,QAAA,GAAW,IAAAD,aAAK,EAAgB;EAEtC,IAAAE,yCAAc,EAAE;IACdH,cAAA,CAAeI,OAAO,GAAG;IACzB,OAAO;MACLJ,cAAA,CAAeI,OAAO,GAAG;IAC3B;EACF,GAAG,EAAE;EAEL,IAAAD,yCAAc,EAAE;IACd,IAAIH,cAAA,CAAeI,OAAO,EACxBJ,cAAA,CAAeI,OAAO,GAAG,WACpB,IAAI,CAACF,QAAA,CAASE,OAAO,IAAIL,YAAA,CAAaM,IAAI,CAAC,CAACC,GAAA,EAAKC,CAAA,KAAM,CAACC,MAAA,CAAOC,EAAE,CAACH,GAAA,EAAKJ,QAAQ,CAACK,CAAA,CAAE,IACvFT,MAAA;IAEFI,QAAA,CAASE,OAAO,GAAGL,YAAA;IACnB;EACF,GAAGA,YAAA;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}