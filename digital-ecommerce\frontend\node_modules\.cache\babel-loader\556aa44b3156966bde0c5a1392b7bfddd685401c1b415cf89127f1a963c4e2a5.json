{"ast": null, "code": "function u(r, n) {\n  if (r in n) {\n    let e = n[r];\n    for (var _len = arguments.length, a = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      a[_key - 2] = arguments[_key];\n    }\n    return typeof e == \"function\" ? e(...a) : e;\n  }\n  let t = new Error(\"Tried to handle \\\"\".concat(r, \"\\\" but there is no handler defined. Only defined handlers are: \").concat(Object.keys(n).map(e => \"\\\"\".concat(e, \"\\\"\")).join(\", \"), \".\"));\n  throw Error.captureStackTrace && Error.captureStackTrace(t, u), t;\n}\nexport { u as match };", "map": {"version": 3, "names": ["u", "r", "n", "e", "_len", "arguments", "length", "a", "Array", "_key", "t", "Error", "concat", "Object", "keys", "map", "join", "captureStackTrace", "match"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/utils/match.js"], "sourcesContent": ["function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAACC,CAAC,EAAM;EAAC,IAAGD,CAAC,IAAIC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACD,CAAC,CAAC;IAAC,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAzBC,CAAC,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAADF,CAAC,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAwB,OAAO,OAAON,CAAC,IAAE,UAAU,GAACA,CAAC,CAAC,GAAGI,CAAC,CAAC,GAACJ,CAAC;EAAA;EAAC,IAAIO,CAAC,GAAC,IAAIC,KAAK,sBAAAC,MAAA,CAAqBX,CAAC,qEAAAW,MAAA,CAAiEC,MAAM,CAACC,IAAI,CAACZ,CAAC,CAAC,CAACa,GAAG,CAACZ,CAAC,SAAAS,MAAA,CAAMT,CAAC,OAAG,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC,MAAG,CAAC;EAAC,MAAML,KAAK,CAACM,iBAAiB,IAAEN,KAAK,CAACM,iBAAiB,CAACP,CAAC,EAACV,CAAC,CAAC,EAACU,CAAC;AAAA;AAAC,SAAOV,CAAC,IAAIkB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}