{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useState,useEffect}from'react';import{products as initialProducts,categories as initialCategories}from'../data/products';import{jsx as _jsx}from\"react/jsx-runtime\";const ProductContext=/*#__PURE__*/createContext();export const useProducts=()=>{const context=useContext(ProductContext);if(!context){throw new Error('useProducts must be used within a ProductProvider');}return context;};export const ProductProvider=_ref=>{let{children}=_ref;const[products,setProducts]=useState(initialProducts);const[categories,setCategories]=useState(initialCategories);const[isLoading,setIsLoading]=useState(false);// Load products from localStorage on mount\nuseEffect(()=>{const savedProducts=localStorage.getItem('products');const savedCategories=localStorage.getItem('categories');if(savedProducts){try{setProducts(JSON.parse(savedProducts));}catch(error){console.error('Error loading products from localStorage:',error);}}if(savedCategories){try{setCategories(JSON.parse(savedCategories));}catch(error){console.error('Error loading categories from localStorage:',error);}}},[]);// Save products to localStorage whenever products change\nuseEffect(()=>{localStorage.setItem('products',JSON.stringify(products));},[products]);// Save categories to localStorage whenever categories change\nuseEffect(()=>{localStorage.setItem('categories',JSON.stringify(categories));},[categories]);const addProduct=async productData=>{setIsLoading(true);try{var _processedImages$;// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,1000));// Generate unique ID\nconst newId=\"product-\".concat(Date.now(),\"-\").concat(Math.random().toString(36).substr(2,9));// Process images (in a real app, you'd upload to a server)\nconst processedImages=productData.images.map((image,index)=>({id:\"img-\".concat(newId,\"-\").concat(index),url:image.url,// In production, this would be the uploaded URL\nalt:productData.name,isPrimary:index===0}));// Create new product object\nconst newProduct={id:newId,name:productData.name,description:productData.description,shortDescription:productData.shortDescription,price:parseFloat(productData.price),discountPrice:productData.discountPrice?parseFloat(productData.discountPrice):null,currency:productData.currency,category:productData.category,subcategory:productData.subcategory,type:productData.type,stockCount:productData.type==='physical'?parseInt(productData.stockCount):null,sku:productData.sku,tags:productData.tags,keywords:productData.keywords,isActive:productData.isActive,isFeatured:productData.isFeatured,specifications:productData.specifications,images:processedImages,image:(_processedImages$=processedImages[0])===null||_processedImages$===void 0?void 0:_processedImages$.url,// Main image for backward compatibility\ninStock:productData.type==='digital'?true:parseInt(productData.stockCount)>0,rating:0,reviews:0,sold:0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};// Add to products list\nsetProducts(prevProducts=>[newProduct,...prevProducts]);setIsLoading(false);return{success:true,product:newProduct};}catch(error){setIsLoading(false);console.error('Error adding product:',error);return{success:false,error:error.message};}};const updateProduct=async(productId,updates)=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,500));setProducts(prevProducts=>prevProducts.map(product=>product.id===productId?_objectSpread(_objectSpread(_objectSpread({},product),updates),{},{updatedAt:new Date().toISOString()}):product));setIsLoading(false);return{success:true};}catch(error){setIsLoading(false);console.error('Error updating product:',error);return{success:false,error:error.message};}};const deleteProduct=async productId=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,500));setProducts(prevProducts=>prevProducts.filter(product=>product.id!==productId));setIsLoading(false);return{success:true};}catch(error){setIsLoading(false);console.error('Error deleting product:',error);return{success:false,error:error.message};}};const addCategory=async categoryData=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,500));const newId=\"category-\".concat(Date.now(),\"-\").concat(Math.random().toString(36).substr(2,9));const newCategory={id:newId,name:categoryData.name,description:categoryData.description,icon:categoryData.icon,subcategories:categoryData.subcategories||[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};setCategories(prevCategories=>[...prevCategories,newCategory]);setIsLoading(false);return{success:true,category:newCategory};}catch(error){setIsLoading(false);console.error('Error adding category:',error);return{success:false,error:error.message};}};const updateCategory=async(categoryId,updates)=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,500));setCategories(prevCategories=>prevCategories.map(category=>category.id===categoryId?_objectSpread(_objectSpread(_objectSpread({},category),updates),{},{updatedAt:new Date().toISOString()}):category));setIsLoading(false);return{success:true};}catch(error){setIsLoading(false);console.error('Error updating category:',error);return{success:false,error:error.message};}};const deleteCategory=async categoryId=>{setIsLoading(true);try{// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Check if any products use this category\nconst productsUsingCategory=products.filter(product=>product.category===categoryId);if(productsUsingCategory.length>0){throw new Error(\"Cannot delete category. \".concat(productsUsingCategory.length,\" products are using this category.\"));}setCategories(prevCategories=>prevCategories.filter(category=>category.id!==categoryId));setIsLoading(false);return{success:true};}catch(error){setIsLoading(false);console.error('Error deleting category:',error);return{success:false,error:error.message};}};const getProductById=productId=>{return products.find(product=>product.id===productId);};const getCategoryById=categoryId=>{return categories.find(category=>category.id===categoryId);};const getProductsByCategory=categoryId=>{return products.filter(product=>product.category===categoryId);};const searchProducts=query=>{const lowercaseQuery=query.toLowerCase();return products.filter(product=>product.name.toLowerCase().includes(lowercaseQuery)||product.description.toLowerCase().includes(lowercaseQuery)||product.tags.some(tag=>tag.toLowerCase().includes(lowercaseQuery))||product.keywords.toLowerCase().includes(lowercaseQuery));};const value={products,categories,isLoading,addProduct,updateProduct,deleteProduct,addCategory,updateCategory,deleteCategory,getProductById,getCategoryById,getProductsByCategory,searchProducts};return/*#__PURE__*/_jsx(ProductContext.Provider,{value:value,children:children});};export default ProductContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "products", "initialProducts", "categories", "initialCategories", "jsx", "_jsx", "ProductContext", "useProducts", "context", "Error", "ProductProvider", "_ref", "children", "setProducts", "setCategories", "isLoading", "setIsLoading", "savedProducts", "localStorage", "getItem", "savedCategories", "JSON", "parse", "error", "console", "setItem", "stringify", "addProduct", "productData", "_processedImages$", "Promise", "resolve", "setTimeout", "newId", "concat", "Date", "now", "Math", "random", "toString", "substr", "processedImages", "images", "map", "image", "index", "id", "url", "alt", "name", "isPrimary", "newProduct", "description", "shortDescription", "price", "parseFloat", "discountPrice", "currency", "category", "subcategory", "type", "stockCount", "parseInt", "sku", "tags", "keywords", "isActive", "isFeatured", "specifications", "inStock", "rating", "reviews", "sold", "createdAt", "toISOString", "updatedAt", "prevProducts", "success", "product", "message", "updateProduct", "productId", "updates", "_objectSpread", "deleteProduct", "filter", "addCategory", "categoryData", "newCategory", "icon", "subcategories", "prevCategories", "updateCategory", "categoryId", "deleteCategory", "productsUsingCategory", "length", "getProductById", "find", "getCategoryById", "getProductsByCategory", "searchProducts", "query", "lowercase<PERSON><PERSON>y", "toLowerCase", "includes", "some", "tag", "value", "Provider"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/ProductContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { products as initialProducts, categories as initialCategories } from '../data/products';\n\nconst ProductContext = createContext();\n\nexport const useProducts = () => {\n  const context = useContext(ProductContext);\n  if (!context) {\n    throw new Error('useProducts must be used within a ProductProvider');\n  }\n  return context;\n};\n\nexport const ProductProvider = ({ children }) => {\n  const [products, setProducts] = useState(initialProducts);\n  const [categories, setCategories] = useState(initialCategories);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Load products from localStorage on mount\n  useEffect(() => {\n    const savedProducts = localStorage.getItem('products');\n    const savedCategories = localStorage.getItem('categories');\n    \n    if (savedProducts) {\n      try {\n        setProducts(JSON.parse(savedProducts));\n      } catch (error) {\n        console.error('Error loading products from localStorage:', error);\n      }\n    }\n    \n    if (savedCategories) {\n      try {\n        setCategories(JSON.parse(savedCategories));\n      } catch (error) {\n        console.error('Error loading categories from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save products to localStorage whenever products change\n  useEffect(() => {\n    localStorage.setItem('products', JSON.stringify(products));\n  }, [products]);\n\n  // Save categories to localStorage whenever categories change\n  useEffect(() => {\n    localStorage.setItem('categories', JSON.stringify(categories));\n  }, [categories]);\n\n  const addProduct = async (productData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Generate unique ID\n      const newId = `product-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      \n      // Process images (in a real app, you'd upload to a server)\n      const processedImages = productData.images.map((image, index) => ({\n        id: `img-${newId}-${index}`,\n        url: image.url, // In production, this would be the uploaded URL\n        alt: productData.name,\n        isPrimary: index === 0\n      }));\n\n      // Create new product object\n      const newProduct = {\n        id: newId,\n        name: productData.name,\n        description: productData.description,\n        shortDescription: productData.shortDescription,\n        price: parseFloat(productData.price),\n        discountPrice: productData.discountPrice ? parseFloat(productData.discountPrice) : null,\n        currency: productData.currency,\n        category: productData.category,\n        subcategory: productData.subcategory,\n        type: productData.type,\n        stockCount: productData.type === 'physical' ? parseInt(productData.stockCount) : null,\n        sku: productData.sku,\n        tags: productData.tags,\n        keywords: productData.keywords,\n        isActive: productData.isActive,\n        isFeatured: productData.isFeatured,\n        specifications: productData.specifications,\n        images: processedImages,\n        image: processedImages[0]?.url, // Main image for backward compatibility\n        inStock: productData.type === 'digital' ? true : parseInt(productData.stockCount) > 0,\n        rating: 0,\n        reviews: 0,\n        sold: 0,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      // Add to products list\n      setProducts(prevProducts => [newProduct, ...prevProducts]);\n      \n      setIsLoading(false);\n      return { success: true, product: newProduct };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error adding product:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const updateProduct = async (productId, updates) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      setProducts(prevProducts =>\n        prevProducts.map(product =>\n          product.id === productId\n            ? { ...product, ...updates, updatedAt: new Date().toISOString() }\n            : product\n        )\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error updating product:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const deleteProduct = async (productId) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      setProducts(prevProducts =>\n        prevProducts.filter(product => product.id !== productId)\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error deleting product:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const addCategory = async (categoryData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newId = `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      \n      const newCategory = {\n        id: newId,\n        name: categoryData.name,\n        description: categoryData.description,\n        icon: categoryData.icon,\n        subcategories: categoryData.subcategories || [],\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      setCategories(prevCategories => [...prevCategories, newCategory]);\n      \n      setIsLoading(false);\n      return { success: true, category: newCategory };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error adding category:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const updateCategory = async (categoryId, updates) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      setCategories(prevCategories =>\n        prevCategories.map(category =>\n          category.id === categoryId\n            ? { ...category, ...updates, updatedAt: new Date().toISOString() }\n            : category\n        )\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error updating category:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const deleteCategory = async (categoryId) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      // Check if any products use this category\n      const productsUsingCategory = products.filter(product => product.category === categoryId);\n      if (productsUsingCategory.length > 0) {\n        throw new Error(`Cannot delete category. ${productsUsingCategory.length} products are using this category.`);\n      }\n      \n      setCategories(prevCategories =>\n        prevCategories.filter(category => category.id !== categoryId)\n      );\n      \n      setIsLoading(false);\n      return { success: true };\n    } catch (error) {\n      setIsLoading(false);\n      console.error('Error deleting category:', error);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const getProductById = (productId) => {\n    return products.find(product => product.id === productId);\n  };\n\n  const getCategoryById = (categoryId) => {\n    return categories.find(category => category.id === categoryId);\n  };\n\n  const getProductsByCategory = (categoryId) => {\n    return products.filter(product => product.category === categoryId);\n  };\n\n  const searchProducts = (query) => {\n    const lowercaseQuery = query.toLowerCase();\n    return products.filter(product =>\n      product.name.toLowerCase().includes(lowercaseQuery) ||\n      product.description.toLowerCase().includes(lowercaseQuery) ||\n      product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||\n      product.keywords.toLowerCase().includes(lowercaseQuery)\n    );\n  };\n\n  const value = {\n    products,\n    categories,\n    isLoading,\n    addProduct,\n    updateProduct,\n    deleteProduct,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    getProductById,\n    getCategoryById,\n    getProductsByCategory,\n    searchProducts\n  };\n\n  return (\n    <ProductContext.Provider value={value}>\n      {children}\n    </ProductContext.Provider>\n  );\n};\n\nexport default ProductContext;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC7E,OAASC,QAAQ,GAAI,CAAAC,eAAe,CAAEC,UAAU,GAAI,CAAAC,iBAAiB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEhG,KAAM,CAAAC,cAAc,cAAGV,aAAa,CAAC,CAAC,CAEtC,MAAO,MAAM,CAAAW,WAAW,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,OAAO,CAAGX,UAAU,CAACS,cAAc,CAAC,CAC1C,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,mDAAmD,CAAC,CACtE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,eAAe,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC1C,KAAM,CAACX,QAAQ,CAAEa,WAAW,CAAC,CAAGf,QAAQ,CAACG,eAAe,CAAC,CACzD,KAAM,CAACC,UAAU,CAAEY,aAAa,CAAC,CAAGhB,QAAQ,CAACK,iBAAiB,CAAC,CAC/D,KAAM,CAACY,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkB,aAAa,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACtD,KAAM,CAAAC,eAAe,CAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAE1D,GAAIF,aAAa,CAAE,CACjB,GAAI,CACFJ,WAAW,CAACQ,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC,CAAC,CACxC,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACnE,CACF,CAEA,GAAIH,eAAe,CAAE,CACnB,GAAI,CACFN,aAAa,CAACO,IAAI,CAACC,KAAK,CAACF,eAAe,CAAC,CAAC,CAC5C,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,CAAEA,KAAK,CAAC,CACrE,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxB,SAAS,CAAC,IAAM,CACdmB,YAAY,CAACO,OAAO,CAAC,UAAU,CAAEJ,IAAI,CAACK,SAAS,CAAC1B,QAAQ,CAAC,CAAC,CAC5D,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACAD,SAAS,CAAC,IAAM,CACdmB,YAAY,CAACO,OAAO,CAAC,YAAY,CAAEJ,IAAI,CAACK,SAAS,CAACxB,UAAU,CAAC,CAAC,CAChE,CAAC,CAAE,CAACA,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAyB,UAAU,CAAG,KAAO,CAAAC,WAAW,EAAK,CACxCZ,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,KAAAa,iBAAA,CACF;AACA,KAAM,IAAI,CAAAC,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,KAAK,YAAAC,MAAA,CAAcC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAAF,MAAA,CAAIG,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAEhF;AACA,KAAM,CAAAC,eAAe,CAAGb,WAAW,CAACc,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,IAAM,CAChEC,EAAE,QAAAZ,MAAA,CAASD,KAAK,MAAAC,MAAA,CAAIW,KAAK,CAAE,CAC3BE,GAAG,CAAEH,KAAK,CAACG,GAAG,CAAE;AAChBC,GAAG,CAAEpB,WAAW,CAACqB,IAAI,CACrBC,SAAS,CAAEL,KAAK,GAAK,CACvB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAM,UAAU,CAAG,CACjBL,EAAE,CAAEb,KAAK,CACTgB,IAAI,CAAErB,WAAW,CAACqB,IAAI,CACtBG,WAAW,CAAExB,WAAW,CAACwB,WAAW,CACpCC,gBAAgB,CAAEzB,WAAW,CAACyB,gBAAgB,CAC9CC,KAAK,CAAEC,UAAU,CAAC3B,WAAW,CAAC0B,KAAK,CAAC,CACpCE,aAAa,CAAE5B,WAAW,CAAC4B,aAAa,CAAGD,UAAU,CAAC3B,WAAW,CAAC4B,aAAa,CAAC,CAAG,IAAI,CACvFC,QAAQ,CAAE7B,WAAW,CAAC6B,QAAQ,CAC9BC,QAAQ,CAAE9B,WAAW,CAAC8B,QAAQ,CAC9BC,WAAW,CAAE/B,WAAW,CAAC+B,WAAW,CACpCC,IAAI,CAAEhC,WAAW,CAACgC,IAAI,CACtBC,UAAU,CAAEjC,WAAW,CAACgC,IAAI,GAAK,UAAU,CAAGE,QAAQ,CAAClC,WAAW,CAACiC,UAAU,CAAC,CAAG,IAAI,CACrFE,GAAG,CAAEnC,WAAW,CAACmC,GAAG,CACpBC,IAAI,CAAEpC,WAAW,CAACoC,IAAI,CACtBC,QAAQ,CAAErC,WAAW,CAACqC,QAAQ,CAC9BC,QAAQ,CAAEtC,WAAW,CAACsC,QAAQ,CAC9BC,UAAU,CAAEvC,WAAW,CAACuC,UAAU,CAClCC,cAAc,CAAExC,WAAW,CAACwC,cAAc,CAC1C1B,MAAM,CAAED,eAAe,CACvBG,KAAK,EAAAf,iBAAA,CAAEY,eAAe,CAAC,CAAC,CAAC,UAAAZ,iBAAA,iBAAlBA,iBAAA,CAAoBkB,GAAG,CAAE;AAChCsB,OAAO,CAAEzC,WAAW,CAACgC,IAAI,GAAK,SAAS,CAAG,IAAI,CAAGE,QAAQ,CAAClC,WAAW,CAACiC,UAAU,CAAC,CAAG,CAAC,CACrFS,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,CAAC,CACVC,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,GAAI,CAAAtC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAE,GAAI,CAAAxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CACpC,CAAC,CAED;AACA7D,WAAW,CAAC+D,YAAY,EAAI,CAACzB,UAAU,CAAE,GAAGyB,YAAY,CAAC,CAAC,CAE1D5D,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE6D,OAAO,CAAE,IAAI,CAAEC,OAAO,CAAE3B,UAAW,CAAC,CAC/C,CAAE,MAAO5B,KAAK,CAAE,CACdP,YAAY,CAAC,KAAK,CAAC,CACnBQ,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,MAAO,CAAEsD,OAAO,CAAE,KAAK,CAAEtD,KAAK,CAAEA,KAAK,CAACwD,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,KAAAA,CAAOC,SAAS,CAAEC,OAAO,GAAK,CAClDlE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAc,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtDlB,WAAW,CAAC+D,YAAY,EACtBA,YAAY,CAACjC,GAAG,CAACmC,OAAO,EACtBA,OAAO,CAAChC,EAAE,GAAKmC,SAAS,CAAAE,aAAA,CAAAA,aAAA,CAAAA,aAAA,IACfL,OAAO,EAAKI,OAAO,MAAEP,SAAS,CAAE,GAAI,CAAAxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC,GAC7DI,OACN,CACF,CAAC,CAED9D,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE6D,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOtD,KAAK,CAAE,CACdP,YAAY,CAAC,KAAK,CAAC,CACnBQ,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAEsD,OAAO,CAAE,KAAK,CAAEtD,KAAK,CAAEA,KAAK,CAACwD,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAK,aAAa,CAAG,KAAO,CAAAH,SAAS,EAAK,CACzCjE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAc,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtDlB,WAAW,CAAC+D,YAAY,EACtBA,YAAY,CAACS,MAAM,CAACP,OAAO,EAAIA,OAAO,CAAChC,EAAE,GAAKmC,SAAS,CACzD,CAAC,CAEDjE,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE6D,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOtD,KAAK,CAAE,CACdP,YAAY,CAAC,KAAK,CAAC,CACnBQ,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CAAEsD,OAAO,CAAE,KAAK,CAAEtD,KAAK,CAAEA,KAAK,CAACwD,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAO,WAAW,CAAG,KAAO,CAAAC,YAAY,EAAK,CAC1CvE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAc,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD,KAAM,CAAAE,KAAK,aAAAC,MAAA,CAAeC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAAF,MAAA,CAAIG,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAEjF,KAAM,CAAAgD,WAAW,CAAG,CAClB1C,EAAE,CAAEb,KAAK,CACTgB,IAAI,CAAEsC,YAAY,CAACtC,IAAI,CACvBG,WAAW,CAAEmC,YAAY,CAACnC,WAAW,CACrCqC,IAAI,CAAEF,YAAY,CAACE,IAAI,CACvBC,aAAa,CAAEH,YAAY,CAACG,aAAa,EAAI,EAAE,CAC/CjB,SAAS,CAAE,GAAI,CAAAtC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAE,GAAI,CAAAxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CACpC,CAAC,CAED5D,aAAa,CAAC6E,cAAc,EAAI,CAAC,GAAGA,cAAc,CAAEH,WAAW,CAAC,CAAC,CAEjExE,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE6D,OAAO,CAAE,IAAI,CAAEnB,QAAQ,CAAE8B,WAAY,CAAC,CACjD,CAAE,MAAOjE,KAAK,CAAE,CACdP,YAAY,CAAC,KAAK,CAAC,CACnBQ,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,CAAEsD,OAAO,CAAE,KAAK,CAAEtD,KAAK,CAAEA,KAAK,CAACwD,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAa,cAAc,CAAG,KAAAA,CAAOC,UAAU,CAAEX,OAAO,GAAK,CACpDlE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAc,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtDjB,aAAa,CAAC6E,cAAc,EAC1BA,cAAc,CAAChD,GAAG,CAACe,QAAQ,EACzBA,QAAQ,CAACZ,EAAE,GAAK+C,UAAU,CAAAV,aAAA,CAAAA,aAAA,CAAAA,aAAA,IACjBzB,QAAQ,EAAKwB,OAAO,MAAEP,SAAS,CAAE,GAAI,CAAAxC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC,GAC9DhB,QACN,CACF,CAAC,CAED1C,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE6D,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOtD,KAAK,CAAE,CACdP,YAAY,CAAC,KAAK,CAAC,CACnBQ,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,CAAEsD,OAAO,CAAE,KAAK,CAAEtD,KAAK,CAAEA,KAAK,CAACwD,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAe,cAAc,CAAG,KAAO,CAAAD,UAAU,EAAK,CAC3C7E,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,KAAM,IAAI,CAAAc,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAgE,qBAAqB,CAAG/F,QAAQ,CAACqF,MAAM,CAACP,OAAO,EAAIA,OAAO,CAACpB,QAAQ,GAAKmC,UAAU,CAAC,CACzF,GAAIE,qBAAqB,CAACC,MAAM,CAAG,CAAC,CAAE,CACpC,KAAM,IAAI,CAAAvF,KAAK,4BAAAyB,MAAA,CAA4B6D,qBAAqB,CAACC,MAAM,sCAAoC,CAAC,CAC9G,CAEAlF,aAAa,CAAC6E,cAAc,EAC1BA,cAAc,CAACN,MAAM,CAAC3B,QAAQ,EAAIA,QAAQ,CAACZ,EAAE,GAAK+C,UAAU,CAC9D,CAAC,CAED7E,YAAY,CAAC,KAAK,CAAC,CACnB,MAAO,CAAE6D,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOtD,KAAK,CAAE,CACdP,YAAY,CAAC,KAAK,CAAC,CACnBQ,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,CAAEsD,OAAO,CAAE,KAAK,CAAEtD,KAAK,CAAEA,KAAK,CAACwD,OAAQ,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAkB,cAAc,CAAIhB,SAAS,EAAK,CACpC,MAAO,CAAAjF,QAAQ,CAACkG,IAAI,CAACpB,OAAO,EAAIA,OAAO,CAAChC,EAAE,GAAKmC,SAAS,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAkB,eAAe,CAAIN,UAAU,EAAK,CACtC,MAAO,CAAA3F,UAAU,CAACgG,IAAI,CAACxC,QAAQ,EAAIA,QAAQ,CAACZ,EAAE,GAAK+C,UAAU,CAAC,CAChE,CAAC,CAED,KAAM,CAAAO,qBAAqB,CAAIP,UAAU,EAAK,CAC5C,MAAO,CAAA7F,QAAQ,CAACqF,MAAM,CAACP,OAAO,EAAIA,OAAO,CAACpB,QAAQ,GAAKmC,UAAU,CAAC,CACpE,CAAC,CAED,KAAM,CAAAQ,cAAc,CAAIC,KAAK,EAAK,CAChC,KAAM,CAAAC,cAAc,CAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,CAC1C,MAAO,CAAAxG,QAAQ,CAACqF,MAAM,CAACP,OAAO,EAC5BA,OAAO,CAAC7B,IAAI,CAACuD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAAC,EACnDzB,OAAO,CAAC1B,WAAW,CAACoD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAAC,EAC1DzB,OAAO,CAACd,IAAI,CAAC0C,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CAAC,CAAC,EACpEzB,OAAO,CAACb,QAAQ,CAACuC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,cAAc,CACxD,CAAC,CACH,CAAC,CAED,KAAM,CAAAK,KAAK,CAAG,CACZ5G,QAAQ,CACRE,UAAU,CACVa,SAAS,CACTY,UAAU,CACVqD,aAAa,CACbI,aAAa,CACbE,WAAW,CACXM,cAAc,CACdE,cAAc,CACdG,cAAc,CACdE,eAAe,CACfC,qBAAqB,CACrBC,cACF,CAAC,CAED,mBACEhG,IAAA,CAACC,cAAc,CAACuG,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAhG,QAAA,CACnCA,QAAQ,CACc,CAAC,CAE9B,CAAC,CAED,cAAe,CAAAN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}