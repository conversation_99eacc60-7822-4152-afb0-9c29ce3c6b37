{"ast": null, "code": "import { useState as i } from \"react\";\nimport { useIsoMorphicEffect as s } from './use-iso-morphic-effect.js';\nfunction f() {\n  var t;\n  let [e] = i(() => typeof window != \"undefined\" && typeof window.matchMedia == \"function\" ? window.matchMedia(\"(pointer: coarse)\") : null),\n    [o, c] = i((t = e == null ? void 0 : e.matches) != null ? t : !1);\n  return s(() => {\n    if (!e) return;\n    function n(r) {\n      c(r.matches);\n    }\n    return e.addEventListener(\"change\", n), () => e.removeEventListener(\"change\", n);\n  }, [e]), o;\n}\nexport { f as useIsTouchDevice };", "map": {"version": 3, "names": ["useState", "i", "useIsoMorphicEffect", "s", "f", "t", "e", "window", "matchMedia", "o", "c", "matches", "n", "r", "addEventListener", "removeEventListener", "useIsTouchDevice"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-is-touch-device.js"], "sourcesContent": ["import{useState as i}from\"react\";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(){var t;let[e]=i(()=>typeof window!=\"undefined\"&&typeof window.matchMedia==\"function\"?window.matchMedia(\"(pointer: coarse)\"):null),[o,c]=i((t=e==null?void 0:e.matches)!=null?t:!1);return s(()=>{if(!e)return;function n(r){c(r.matches)}return e.addEventListener(\"change\",n),()=>e.removeEventListener(\"change\",n)},[e]),o}export{f as useIsTouchDevice};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC;EAAC,IAAG,CAACC,CAAC,CAAC,GAACL,CAAC,CAAC,MAAI,OAAOM,MAAM,IAAE,WAAW,IAAE,OAAOA,MAAM,CAACC,UAAU,IAAE,UAAU,GAACD,MAAM,CAACC,UAAU,CAAC,mBAAmB,CAAC,GAAC,IAAI,CAAC;IAAC,CAACC,CAAC,EAACC,CAAC,CAAC,GAACT,CAAC,CAAC,CAACI,CAAC,GAACC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACK,OAAO,KAAG,IAAI,GAACN,CAAC,GAAC,CAAC,CAAC,CAAC;EAAC,OAAOF,CAAC,CAAC,MAAI;IAAC,IAAG,CAACG,CAAC,EAAC;IAAO,SAASM,CAACA,CAACC,CAAC,EAAC;MAACH,CAAC,CAACG,CAAC,CAACF,OAAO,CAAC;IAAA;IAAC,OAAOL,CAAC,CAACQ,gBAAgB,CAAC,QAAQ,EAACF,CAAC,CAAC,EAAC,MAAIN,CAAC,CAACS,mBAAmB,CAAC,QAAQ,EAACH,CAAC,CAAC;EAAA,CAAC,EAAC,CAACN,CAAC,CAAC,CAAC,EAACG,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIY,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}