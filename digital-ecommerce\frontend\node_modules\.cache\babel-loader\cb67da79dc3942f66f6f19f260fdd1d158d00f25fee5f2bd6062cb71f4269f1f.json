{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\AdminProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminProtectedRoute = ({\n  children,\n  requiredPermission = null,\n  requiredRole = null\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading,\n    hasPermission,\n    isRole\n  } = useAdmin();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to admin login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check for required permission\n  if (requiredPermission && !hasPermission(requiredPermission)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"You don't have permission to access this resource.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check for required role\n  if (requiredRole && !isRole(requiredRole)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"You don't have the required role to access this resource.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(AdminProtectedRoute, \"CQuF6iCckjpogFCSUPFaXWFSAak=\", false, function () {\n  return [useAdmin, useLocation];\n});\n_c = AdminProtectedRoute;\nexport default AdminProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAdmin", "jsxDEV", "_jsxDEV", "AdminProtectedRoute", "children", "requiredPermission", "requiredRole", "_s", "isAuthenticated", "isLoading", "hasPermission", "isRole", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/AdminProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAdmin } from '../contexts/AdminContext';\n\nconst AdminProtectedRoute = ({ children, requiredPermission = null, requiredRole = null }) => {\n  const { isAuthenticated, isLoading, hasPermission, isRole } = useAdmin();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"></div>\n      </div>\n    );\n  }\n\n  // Redirect to admin login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/admin/login\" state={{ from: location }} replace />;\n  }\n\n  // Check for required permission\n  if (requiredPermission && !hasPermission(requiredPermission)) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            You don't have permission to access this resource.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  // Check for required role\n  if (requiredRole && !isRole(requiredRole)) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            You don't have the required role to access this resource.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default AdminProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,kBAAkB,GAAG,IAAI;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5F,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC,aAAa;IAAEC;EAAO,CAAC,GAAGX,QAAQ,CAAC,CAAC;EACxE,MAAMY,QAAQ,GAAGb,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIU,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKW,SAAS,EAAC,+CAA+C;MAAAT,QAAA,eAC5DF,OAAA;QAAKW,SAAS,EAAC;MAAwE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAEV;;EAEA;EACA,IAAI,CAACT,eAAe,EAAE;IACpB,oBAAON,OAAA,CAACJ,QAAQ;MAACoB,EAAE,EAAC,cAAc;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1E;;EAEA;EACA,IAAIZ,kBAAkB,IAAI,CAACK,aAAa,CAACL,kBAAkB,CAAC,EAAE;IAC5D,oBACEH,OAAA;MAAKW,SAAS,EAAC,+CAA+C;MAAAT,QAAA,eAC5DF,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAT,QAAA,gBAC1BF,OAAA;UAAIW,SAAS,EAAC,uDAAuD;UAAAT,QAAA,EAAC;QAEtE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLf,OAAA;UAAGW,SAAS,EAAC,kCAAkC;UAAAT,QAAA,EAAC;QAEhD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIX,YAAY,IAAI,CAACK,MAAM,CAACL,YAAY,CAAC,EAAE;IACzC,oBACEJ,OAAA;MAAKW,SAAS,EAAC,+CAA+C;MAAAT,QAAA,eAC5DF,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAT,QAAA,gBAC1BF,OAAA;UAAIW,SAAS,EAAC,uDAAuD;UAAAT,QAAA,EAAC;QAEtE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLf,OAAA;UAAGW,SAAS,EAAC,kCAAkC;UAAAT,QAAA,EAAC;QAEhD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOb,QAAQ;AACjB,CAAC;AAACG,EAAA,CAnDIJ,mBAAmB;EAAA,QACuCH,QAAQ,EACrDD,WAAW;AAAA;AAAAuB,EAAA,GAFxBnB,mBAAmB;AAqDzB,eAAeA,mBAAmB;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}