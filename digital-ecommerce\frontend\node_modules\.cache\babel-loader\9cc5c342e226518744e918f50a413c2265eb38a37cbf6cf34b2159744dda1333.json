{"ast": null, "code": "/*!\n* tabbable 6.2.0\n* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE\n*/\n// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nvar candidateSelectors = ['input:not([inert])', 'select:not([inert])', 'textarea:not([inert])', 'a[href]:not([inert])', 'button:not([inert])', '[tabindex]:not(slot):not([inert])', 'audio[controls]:not([inert])', 'video[controls]:not([inert])', '[contenteditable]:not([contenteditable=\"false\"]):not([inert])', 'details>summary:first-of-type:not([inert])', 'details:not([inert])'];\nvar candidateSelector = /* #__PURE__ */candidateSelectors.join(',');\nvar NoElement = typeof Element === 'undefined';\nvar matches = NoElement ? function () {} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\nvar getRootNode = !NoElement && Element.prototype.getRootNode ? function (element) {\n  var _element$getRootNode;\n  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);\n} : function (element) {\n  return element === null || element === void 0 ? void 0 : element.ownerDocument;\n};\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nvar isInert = function isInert(node, lookUp) {\n  var _node$getAttribute;\n  if (lookUp === void 0) {\n    lookUp = true;\n  }\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'inert');\n  var inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  var result = inert || lookUp && node && isInert(node.parentNode); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nvar isContentEditable = function isContentEditable(node) {\n  var _node$getAttribute2;\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, 'contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nvar getCandidates = function getCandidates(el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nvar getCandidatesIteratively = function getCandidatesIteratively(elements, includeContainer, options) {\n  var candidates = [];\n  var elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    var element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      var assigned = element.assignedElements();\n      var content = assigned.length ? assigned : element.children;\n      var nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push.apply(candidates, nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates\n        });\n      }\n    } else {\n      // check candidate element\n      var validCandidate = matches.call(element, candidateSelector);\n      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      var shadowRoot = element.shadowRoot ||\n      // check for an undisclosed shadow\n      typeof options.getShadowRoot === 'function' && options.getShadowRoot(element);\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        var _nestedCandidates = getCandidatesIteratively(shadowRoot === true ? element.children : shadowRoot.children, true, options);\n        if (options.flatten) {\n          candidates.push.apply(candidates, _nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: _nestedCandidates\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift.apply(elementsToCheck, element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nvar hasTabIndex = function hasTabIndex(node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nvar getTabIndex = function getTabIndex(node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {\n      return 0;\n    }\n  }\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nvar getSortOrderTabIndex = function getSortOrderTabIndex(node, isScope) {\n  var tabIndex = getTabIndex(node);\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n  return tabIndex;\n};\nvar sortOrderedTabbables = function sortOrderedTabbables(a, b) {\n  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n};\nvar isInput = function isInput(node) {\n  return node.tagName === 'INPUT';\n};\nvar isHiddenInput = function isHiddenInput(node) {\n  return isInput(node) && node.type === 'hidden';\n};\nvar isDetailsWithSummary = function isDetailsWithSummary(node) {\n  var r = node.tagName === 'DETAILS' && Array.prototype.slice.apply(node.children).some(function (child) {\n    return child.tagName === 'SUMMARY';\n  });\n  return r;\n};\nvar getCheckedRadio = function getCheckedRadio(nodes, form) {\n  for (var i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\nvar isTabbableRadio = function isTabbableRadio(node) {\n  if (!node.name) {\n    return true;\n  }\n  var radioScope = node.form || getRootNode(node);\n  var queryRadios = function queryRadios(name) {\n    return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n  };\n  var radioSet;\n  if (typeof window !== 'undefined' && typeof window.CSS !== 'undefined' && typeof window.CSS.escape === 'function') {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error('Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s', err.message);\n      return false;\n    }\n  }\n  var checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\nvar isRadio = function isRadio(node) {\n  return isInput(node) && node.type === 'radio';\n};\nvar isNonTabbableRadio = function isNonTabbableRadio(node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nvar isNodeAttached = function isNodeAttached(node) {\n  var _nodeRoot;\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  var nodeRoot = node && getRootNode(node);\n  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  var attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;\n    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));\n    while (!attached && nodeRootHost) {\n      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;\n      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));\n    }\n  }\n  return attached;\n};\nvar isZeroArea = function isZeroArea(node) {\n  var _node$getBoundingClie = node.getBoundingClientRect(),\n    width = _node$getBoundingClie.width,\n    height = _node$getBoundingClie.height;\n  return width === 0 && height === 0;\n};\nvar isHidden = function isHidden(node, _ref) {\n  var displayCheck = _ref.displayCheck,\n    getShadowRoot = _ref.getShadowRoot;\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n  var isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n  if (!displayCheck || displayCheck === 'full' || displayCheck === 'legacy-full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      var originalNode = node;\n      while (node) {\n        var parentElement = node.parentElement;\n        var rootNode = getRootNode(node);\n        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nvar isDisabledFromFieldset = function isDisabledFromFieldset(node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    var parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (var i = 0; i < parentNode.children.length; i++) {\n          var child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *') ? true : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\nvar isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable(options, node) {\n  if (node.disabled ||\n  // we must do an inert look up to filter out any elements inside an inert ancestor\n  //  because we're limited in the type of selectors we can use in JSDom (see related\n  //  note related to `candidateSelectors`)\n  isInert(node) || isHiddenInput(node) || isHidden(node, options) ||\n  // For a details element with a summary, the summary element gets the focus\n  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n    return false;\n  }\n  return true;\n};\nvar isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable(options, node) {\n  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {\n    return false;\n  }\n  return true;\n};\nvar isValidShadowRootTabbable = function isValidShadowRootTabbable(shadowHostNode) {\n  var tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nvar sortByOrder = function sortByOrder(candidates) {\n  var regularTabbables = [];\n  var orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    var isScope = !!item.scopeParent;\n    var element = isScope ? item.scopeParent : item;\n    var candidateTabindex = getSortOrderTabIndex(element, isScope);\n    var elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements\n      });\n    }\n  });\n  return orderedTabbables.sort(sortOrderedTabbables).reduce(function (acc, sortable) {\n    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);\n    return acc;\n  }, []).concat(regularTabbables);\n};\nvar tabbable = function tabbable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n  }\n  return sortByOrder(candidates);\n};\nvar focusable = function focusable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));\n  }\n  return candidates;\n};\nvar isTabbable = function isTabbable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\nvar focusableCandidateSelector = /* #__PURE__ */candidateSelectors.concat('iframe').join(',');\nvar isFocusable = function isFocusable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\nexport { focusable, getTabIndex, isFocusable, isTabbable, tabbable };", "map": {"version": 3, "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_nestedCandidates", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable"], "sources": ["C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\node_modules\\tabbable\\src\\index.js"], "sourcesContent": ["// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA,IAAMA,kBAAkB,GAAG,CACzB,oBAAoB,EACpB,qBAAqB,EACrB,uBAAuB,EACvB,sBAAsB,EACtB,qBAAqB,EACrB,mCAAmC,EACnC,8BAA8B,EAC9B,8BAA8B,EAC9B,+DAA+D,EAC/D,4CAA4C,EAC5C,sBAAsB,CACvB;AACD,IAAMC,iBAAiB,kBAAmBD,kBAAkB,CAACE,IAAI,CAAC,GAAG,CAAC;AAEtE,IAAMC,SAAS,GAAG,OAAOC,OAAO,KAAK,WAAW;AAEhD,IAAMC,OAAO,GAAGF,SAAS,GACrB,YAAY,EAAE,GACdC,OAAO,CAACE,SAAS,CAACD,OAAO,IACzBD,OAAO,CAACE,SAAS,CAACC,iBAAiB,IACnCH,OAAO,CAACE,SAAS,CAACE,qBAAqB;AAE3C,IAAMC,WAAW,GACf,CAACN,SAAS,IAAIC,OAAO,CAACE,SAAS,CAACG,WAAW,GACvC,UAACC,OAAO;EAAA,IAAAC,oBAAA;EAAA,OAAKD,OAAO,KAAP,QAAAA,OAAO,wBAAAC,oBAAA,GAAPD,OAAO,CAAED,WAAW,cAAAE,oBAAA,KAApB,kBAAAA,oBAAA,CAAAC,IAAA,CAAAF,OAAuB,CAAC;AAAA,IACrC,UAACA,OAAO;EAAA,OAAKA,OAAO,KAAP,QAAAA,OAAO,KAAP,kBAAAA,OAAO,CAAEG,aAAa;AAAA;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAaC,IAAI,EAAEC,MAAM,EAAS;EAAA,IAAAC,kBAAA;EAAA,IAAfD,MAAM;IAANA,MAAM,GAAG,IAAI;EAAA;EAC3C;EACA;EACA;EACA,IAAME,QAAQ,GAAGH,IAAI,aAAJA,IAAI,wBAAAE,kBAAA,GAAJF,IAAI,CAAEI,YAAY,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAAL,IAAA,CAAAG,IAAI,EAAiB,OAAO,CAAC;EAC9C,IAAMK,KAAK,GAAGF,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,MAAM;;EAEpD;EACA;EACA;EACA;EACA;EACA,IAAMG,MAAM,GAAGD,KAAK,IAAKJ,MAAM,IAAID,IAAI,IAAID,OAAO,CAACC,IAAI,CAACO,UAAU,CAAE,CAAC;;EAErE,OAAOD,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaR,IAAI,EAAE;EAAA,IAAAS,mBAAA;EACxC;EACA;EACA;EACA,IAAMC,QAAQ,GAAGV,IAAI,aAAJA,IAAI,wBAAAS,mBAAA,GAAJT,IAAI,CAAEI,YAAY,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAAZ,IAAA,CAAAG,IAAI,EAAiB,iBAAiB,CAAC;EACxD,OAAOU,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,MAAM;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,EAAE,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;EAC5D;EACA;EACA,IAAIf,OAAO,CAACa,EAAE,CAAC,EAAE;IACf,OAAO,EAAE;EACX;EAEA,IAAIG,UAAU,GAAGC,KAAK,CAACzB,SAAS,CAAC0B,KAAK,CAACC,KAAK,CAC1CN,EAAE,CAACO,gBAAgB,CAACjC,iBAAiB,CACvC,CAAC;EACD,IAAI2B,gBAAgB,IAAIvB,OAAO,CAACO,IAAI,CAACe,EAAE,EAAE1B,iBAAiB,CAAC,EAAE;IAC3D6B,UAAU,CAACK,OAAO,CAACR,EAAE,CAAC;EACxB;EACAG,UAAU,GAAGA,UAAU,CAACD,MAAM,CAACA,MAAM,CAAC;EACtC,OAAOC,UAAU;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMM,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAC5BC,QAAQ,EACRT,gBAAgB,EAChBU,OAAO,EACP;EACA,IAAMR,UAAU,GAAG,EAAE;EACrB,IAAMS,eAAe,GAAGR,KAAK,CAACS,IAAI,CAACH,QAAQ,CAAC;EAC5C,OAAOE,eAAe,CAACE,MAAM,EAAE;IAC7B,IAAM/B,OAAO,GAAG6B,eAAe,CAACG,KAAK,EAAE;IACvC,IAAI5B,OAAO,CAACJ,OAAO,EAAE,KAAK,CAAC,EAAE;MAC3B;MACA;MACA;IACF;IAEA,IAAIA,OAAO,CAACiC,OAAO,KAAK,MAAM,EAAE;MAC9B;MACA,IAAMC,QAAQ,GAAGlC,OAAO,CAACmC,gBAAgB,EAAE;MAC3C,IAAMC,OAAO,GAAGF,QAAQ,CAACH,MAAM,GAAGG,QAAQ,GAAGlC,OAAO,CAACqC,QAAQ;MAC7D,IAAMC,gBAAgB,GAAGZ,wBAAwB,CAACU,OAAO,EAAE,IAAI,EAAER,OAAO,CAAC;MACzE,IAAIA,OAAO,CAACW,OAAO,EAAE;QACnBnB,UAAU,CAACoB,IAAI,CAAAjB,KAAA,CAAfH,UAAU,EAASkB,gBAAgB,CAAC;MACtC,CAAC,MAAM;QACLlB,UAAU,CAACoB,IAAI,CAAC;UACdC,WAAW,EAAEzC,OAAO;UACpBoB,UAAU,EAAEkB;QACd,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL;MACA,IAAMI,cAAc,GAAG/C,OAAO,CAACO,IAAI,CAACF,OAAO,EAAET,iBAAiB,CAAC;MAC/D,IACEmD,cAAc,IACdd,OAAO,CAACT,MAAM,CAACnB,OAAO,CAAC,KACtBkB,gBAAgB,IAAI,CAACS,QAAQ,CAACgB,QAAQ,CAAC3C,OAAO,CAAC,CAAC,EACjD;QACAoB,UAAU,CAACoB,IAAI,CAACxC,OAAO,CAAC;MAC1B;;MAEA;MACA,IAAM4C,UAAU,GACd5C,OAAO,CAAC4C,UAAU;MAClB;MACC,OAAOhB,OAAO,CAACiB,aAAa,KAAK,UAAU,IAC1CjB,OAAO,CAACiB,aAAa,CAAC7C,OAAO,CAAE;;MAEnC;MACA;MACA;MACA,IAAM8C,eAAe,GACnB,CAAC1C,OAAO,CAACwC,UAAU,EAAE,KAAK,CAAC,KAC1B,CAAChB,OAAO,CAACmB,gBAAgB,IAAInB,OAAO,CAACmB,gBAAgB,CAAC/C,OAAO,CAAC,CAAC;MAElE,IAAI4C,UAAU,IAAIE,eAAe,EAAE;QACjC;QACA;QACA;QACA;QACA;QACA;QACA,IAAME,iBAAgB,GAAGtB,wBAAwB,CAC/CkB,UAAU,KAAK,IAAI,GAAG5C,OAAO,CAACqC,QAAQ,GAAGO,UAAU,CAACP,QAAQ,EAC5D,IAAI,EACJT,OACF,CAAC;QAED,IAAIA,OAAO,CAACW,OAAO,EAAE;UACnBnB,UAAU,CAACoB,IAAI,CAAAjB,KAAA,CAAfH,UAAU,EAAS4B,iBAAgB,CAAC;QACtC,CAAC,MAAM;UACL5B,UAAU,CAACoB,IAAI,CAAC;YACdC,WAAW,EAAEzC,OAAO;YACpBoB,UAAU,EAAE4B;UACd,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA;QACAnB,eAAe,CAACJ,OAAO,CAAAF,KAAA,CAAvBM,eAAe,EAAY7B,OAAO,CAACqC,QAAQ,CAAC;MAC9C;IACF;EACF;EACA,OAAOjB,UAAU;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAM6B,WAAW,GAAG,SAAdA,WAAWA,CAAa5C,IAAI,EAAE;EAClC,OAAO,CAAC6C,KAAK,CAACC,QAAQ,CAAC9C,IAAI,CAACI,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAM2C,WAAW,GAAG,SAAdA,WAAWA,CAAa/C,IAAI,EAAE;EAClC,IAAI,CAACA,IAAI,EAAE;IACT,MAAM,IAAIgD,KAAK,CAAC,kBAAkB,CAAC;EACrC;EAEA,IAAIhD,IAAI,CAACiD,QAAQ,GAAG,CAAC,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IACE,CAAC,yBAAyB,CAACC,IAAI,CAAClD,IAAI,CAAC4B,OAAO,CAAC,IAC3CpB,iBAAiB,CAACR,IAAI,CAAC,KACzB,CAAC4C,WAAW,CAAC5C,IAAI,CAAC,EAClB;MACA,OAAO,CAAC;IACV;EACF;EAEA,OAAOA,IAAI,CAACiD,QAAQ;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAME,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAanD,IAAI,EAAEoD,OAAO,EAAE;EACpD,IAAMH,QAAQ,GAAGF,WAAW,CAAC/C,IAAI,CAAC;EAElC,IAAIiD,QAAQ,GAAG,CAAC,IAAIG,OAAO,IAAI,CAACR,WAAW,CAAC5C,IAAI,CAAC,EAAE;IACjD,OAAO,CAAC;EACV;EAEA,OAAOiD,QAAQ;AACjB,CAAC;AAED,IAAMI,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAaC,CAAC,EAAEC,CAAC,EAAE;EAC3C,OAAOD,CAAC,CAACL,QAAQ,KAAKM,CAAC,CAACN,QAAQ,GAC5BK,CAAC,CAACE,aAAa,GAAGD,CAAC,CAACC,aAAa,GACjCF,CAAC,CAACL,QAAQ,GAAGM,CAAC,CAACN,QAAQ;AAC7B,CAAC;AAED,IAAMQ,OAAO,GAAG,SAAVA,OAAOA,CAAazD,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAAC4B,OAAO,KAAK,OAAO;AACjC,CAAC;AAED,IAAM8B,aAAa,GAAG,SAAhBA,aAAaA,CAAa1D,IAAI,EAAE;EACpC,OAAOyD,OAAO,CAACzD,IAAI,CAAC,IAAIA,IAAI,CAAC2D,IAAI,KAAK,QAAQ;AAChD,CAAC;AAED,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa5D,IAAI,EAAE;EAC3C,IAAM6D,CAAC,GACL7D,IAAI,CAAC4B,OAAO,KAAK,SAAS,IAC1BZ,KAAK,CAACzB,SAAS,CAAC0B,KAAK,CAClBC,KAAK,CAAClB,IAAI,CAACgC,QAAQ,CAAC,CACpB8B,IAAI,CAAC,UAACC,KAAK;IAAA,OAAKA,KAAK,CAACnC,OAAO,KAAK,SAAS;GAAC;EACjD,OAAOiC,CAAC;AACV,CAAC;AAED,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,KAAK,EAAEC,IAAI,EAAE;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACvC,MAAM,EAAEyC,CAAC,EAAE,EAAE;IACrC,IAAIF,KAAK,CAACE,CAAC,CAAC,CAACC,OAAO,IAAIH,KAAK,CAACE,CAAC,CAAC,CAACD,IAAI,KAAKA,IAAI,EAAE;MAC9C,OAAOD,KAAK,CAACE,CAAC,CAAC;IACjB;EACF;AACF,CAAC;AAED,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAarE,IAAI,EAAE;EACtC,IAAI,CAACA,IAAI,CAACsE,IAAI,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAMC,UAAU,GAAGvE,IAAI,CAACkE,IAAI,IAAIxE,WAAW,CAACM,IAAI,CAAC;EACjD,IAAMwE,WAAW,GAAG,SAAdA,WAAWA,CAAaF,IAAI,EAAE;IAClC,OAAOC,UAAU,CAACpD,gBAAgB,CAChC,4BAA4B,GAAGmD,IAAI,GAAG,IACxC,CAAC;GACF;EAED,IAAIG,QAAQ;EACZ,IACE,OAAOC,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACC,GAAG,KAAK,WAAW,IACjC,OAAOD,MAAM,CAACC,GAAG,CAACC,MAAM,KAAK,UAAU,EACvC;IACAH,QAAQ,GAAGD,WAAW,CAACE,MAAM,CAACC,GAAG,CAACC,MAAM,CAAC5E,IAAI,CAACsE,IAAI,CAAC,CAAC;EACtD,CAAC,MAAM;IACL,IAAI;MACFG,QAAQ,GAAGD,WAAW,CAACxE,IAAI,CAACsE,IAAI,CAAC;KAClC,CAAC,OAAOO,GAAG,EAAE;MACZ;MACAC,OAAO,CAACC,KAAK,CACX,0IAA0I,EAC1IF,GAAG,CAACG,OACN,CAAC;MACD,OAAO,KAAK;IACd;EACF;EAEA,IAAMZ,OAAO,GAAGJ,eAAe,CAACS,QAAQ,EAAEzE,IAAI,CAACkE,IAAI,CAAC;EACpD,OAAO,CAACE,OAAO,IAAIA,OAAO,KAAKpE,IAAI;AACrC,CAAC;AAED,IAAMiF,OAAO,GAAG,SAAVA,OAAOA,CAAajF,IAAI,EAAE;EAC9B,OAAOyD,OAAO,CAACzD,IAAI,CAAC,IAAIA,IAAI,CAAC2D,IAAI,KAAK,OAAO;AAC/C,CAAC;AAED,IAAMuB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAalF,IAAI,EAAE;EACzC,OAAOiF,OAAO,CAACjF,IAAI,CAAC,IAAI,CAACqE,eAAe,CAACrE,IAAI,CAAC;AAChD,CAAC;;AAED;AACA,IAAMmF,cAAc,GAAG,SAAjBA,cAAcA,CAAanF,IAAI,EAAE;EAAA,IAAAoF,SAAA;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,QAAQ,GAAGrF,IAAI,IAAIN,WAAW,CAACM,IAAI,CAAC;EACxC,IAAIsF,YAAY,IAAAF,SAAA,GAAGC,QAAQ,cAAAD,SAAA,uBAARA,SAAA,CAAUG,IAAI;;EAEjC;EACA;EACA,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIH,QAAQ,IAAIA,QAAQ,KAAKrF,IAAI,EAAE;IAAA,IAAAyF,aAAA,EAAAC,qBAAA,EAAAC,mBAAA;IACjCH,QAAQ,GAAG,CAAC,EACV,CAAAC,aAAA,GAAAH,YAAY,cAAAG,aAAA,gBAAAC,qBAAA,GAAZD,aAAA,CAAc3F,aAAa,cAAA4F,qBAAA,eAA3BA,qBAAA,CAA6BE,QAAQ,CAACN,YAAY,CAAC,IACnDtF,IAAI,aAAJA,IAAI,gBAAA2F,mBAAA,GAAJ3F,IAAI,CAAEF,aAAa,cAAA6F,mBAAA,eAAnBA,mBAAA,CAAqBC,QAAQ,CAAC5F,IAAI,CAAC,CACpC;IAED,OAAO,CAACwF,QAAQ,IAAIF,YAAY,EAAE;MAAA,IAAAO,UAAA,EAAAC,cAAA,EAAAC,qBAAA;MAChC;MACA;MACA;MACAV,QAAQ,GAAG3F,WAAW,CAAC4F,YAAY,CAAC;MACpCA,YAAY,IAAAO,UAAA,GAAGR,QAAQ,cAAAQ,UAAA,uBAARA,UAAA,CAAUN,IAAI;MAC7BC,QAAQ,GAAG,CAAC,GAAAM,cAAA,GAACR,YAAY,cAAAQ,cAAA,gBAAAC,qBAAA,GAAZD,cAAA,CAAchG,aAAa,cAAAiG,qBAAA,eAA3BA,qBAAA,CAA6BH,QAAQ,CAACN,YAAY,CAAC;IAClE;EACF;EAEA,OAAOE,QAAQ;AACjB,CAAC;AAED,IAAMQ,UAAU,GAAG,SAAbA,UAAUA,CAAahG,IAAI,EAAE;EACjC,IAAAiG,qBAAA,GAA0BjG,IAAI,CAACkG,qBAAqB,EAAE;IAA9CC,KAAK,GAAAF,qBAAA,CAALE,KAAK;IAAEC,MAAM,GAAAH,qBAAA,CAANG,MAAM;EACrB,OAAOD,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC;AACpC,CAAC;AACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAarG,IAAI,EAAAsG,IAAA,EAAmC;EAAA,IAA/BC,YAAY,GAAAD,IAAA,CAAZC,YAAY;IAAE/D,aAAa,GAAA8D,IAAA,CAAb9D,aAAa;EAC5D;EACA;EACA;EACA;EACA;EACA,IAAIgE,gBAAgB,CAACxG,IAAI,CAAC,CAACyG,UAAU,KAAK,QAAQ,EAAE;IAClD,OAAO,IAAI;EACb;EAEA,IAAMC,eAAe,GAAGpH,OAAO,CAACO,IAAI,CAACG,IAAI,EAAE,+BAA+B,CAAC;EAC3E,IAAM2G,gBAAgB,GAAGD,eAAe,GAAG1G,IAAI,CAAC4G,aAAa,GAAG5G,IAAI;EACpE,IAAIV,OAAO,CAACO,IAAI,CAAC8G,gBAAgB,EAAE,uBAAuB,CAAC,EAAE;IAC3D,OAAO,IAAI;EACb;EAEA,IACE,CAACJ,YAAY,IACbA,YAAY,KAAK,MAAM,IACvBA,YAAY,KAAK,aAAa,EAC9B;IACA,IAAI,OAAO/D,aAAa,KAAK,UAAU,EAAE;MACvC;MACA;MACA,IAAMqE,YAAY,GAAG7G,IAAI;MACzB,OAAOA,IAAI,EAAE;QACX,IAAM4G,aAAa,GAAG5G,IAAI,CAAC4G,aAAa;QACxC,IAAME,QAAQ,GAAGpH,WAAW,CAACM,IAAI,CAAC;QAClC,IACE4G,aAAa,IACb,CAACA,aAAa,CAACrE,UAAU,IACzBC,aAAa,CAACoE,aAAa,CAAC,KAAK,IAAI;QAAA,EACrC;UACA;UACA;UACA,OAAOZ,UAAU,CAAChG,IAAI,CAAC;QACzB,CAAC,MAAM,IAAIA,IAAI,CAAC+G,YAAY,EAAE;UAC5B;UACA/G,IAAI,GAAGA,IAAI,CAAC+G,YAAY;SACzB,MAAM,IAAI,CAACH,aAAa,IAAIE,QAAQ,KAAK9G,IAAI,CAACF,aAAa,EAAE;UAC5D;UACAE,IAAI,GAAG8G,QAAQ,CAACvB,IAAI;QACtB,CAAC,MAAM;UACL;UACAvF,IAAI,GAAG4G,aAAa;QACtB;MACF;MAEA5G,IAAI,GAAG6G,YAAY;IACrB;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA,IAAI1B,cAAc,CAACnF,IAAI,CAAC,EAAE;MACxB;MACA;MACA;MACA;MACA,OAAO,CAACA,IAAI,CAACgH,cAAc,EAAE,CAACtF,MAAM;IACtC;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI6E,YAAY,KAAK,aAAa,EAAE;MAClC,OAAO,IAAI,CAAC;IACd;IACA;EACF,CAAC,MAAM,IAAIA,YAAY,KAAK,eAAe,EAAE;IAC3C;IACA;IACA;IACA;IACA;IACA,OAAOP,UAAU,CAAChG,IAAI,CAAC;EACzB;;EAEA;EACA;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,IAAMiH,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAajH,IAAI,EAAE;EAC7C,IAAI,kCAAkC,CAACkD,IAAI,CAAClD,IAAI,CAAC4B,OAAO,CAAC,EAAE;IACzD,IAAIrB,UAAU,GAAGP,IAAI,CAAC4G,aAAa;IACnC;IACA,OAAOrG,UAAU,EAAE;MACjB,IAAIA,UAAU,CAACqB,OAAO,KAAK,UAAU,IAAIrB,UAAU,CAAC2G,QAAQ,EAAE;QAC5D;QACA,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,UAAU,CAACyB,QAAQ,CAACN,MAAM,EAAEyC,CAAC,EAAE,EAAE;UACnD,IAAMJ,KAAK,GAAGxD,UAAU,CAACyB,QAAQ,CAACmF,IAAI,CAAChD,CAAC,CAAC;UACzC;UACA,IAAIJ,KAAK,CAACnC,OAAO,KAAK,QAAQ,EAAE;YAC9B;YACA;YACA,OAAOtC,OAAO,CAACO,IAAI,CAACU,UAAU,EAAE,sBAAsB,CAAC,GACnD,IAAI,GACJ,CAACwD,KAAK,CAAC6B,QAAQ,CAAC5F,IAAI,CAAC;UAC3B;QACF;QACA;QACA,OAAO,IAAI;MACb;MACAO,UAAU,GAAGA,UAAU,CAACqG,aAAa;IACvC;EACF;;EAEA;EACA;EACA,OAAO,KAAK;AACd,CAAC;AAED,IAAMQ,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAa7F,OAAO,EAAEvB,IAAI,EAAE;EAC/D,IACEA,IAAI,CAACkH,QAAQ;EACb;EACA;EACA;EACAnH,OAAO,CAACC,IAAI,CAAC,IACb0D,aAAa,CAAC1D,IAAI,CAAC,IACnBqG,QAAQ,CAACrG,IAAI,EAAEuB,OAAO,CAAC;EACvB;EACAqC,oBAAoB,CAAC5D,IAAI,CAAC,IAC1BiH,sBAAsB,CAACjH,IAAI,CAAC,EAC5B;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AAED,IAAMqH,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAa9F,OAAO,EAAEvB,IAAI,EAAE;EAC9D,IACEkF,kBAAkB,CAAClF,IAAI,CAAC,IACxB+C,WAAW,CAAC/C,IAAI,CAAC,GAAG,CAAC,IACrB,CAACoH,+BAA+B,CAAC7F,OAAO,EAAEvB,IAAI,CAAC,EAC/C;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AAED,IAAMsH,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,cAAc,EAAE;EAC1D,IAAMtE,QAAQ,GAAGH,QAAQ,CAACyE,cAAc,CAACnH,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;EACtE,IAAIyC,KAAK,CAACI,QAAQ,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;IACpC,OAAO,IAAI;EACb;EACA;EACA;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAMuE,WAAW,GAAG,SAAdA,WAAWA,CAAazG,UAAU,EAAE;EACxC,IAAM0G,gBAAgB,GAAG,EAAE;EAC3B,IAAMC,gBAAgB,GAAG,EAAE;EAC3B3G,UAAU,CAAC4G,OAAO,CAAC,UAAUR,IAAI,EAAEhD,CAAC,EAAE;IACpC,IAAMf,OAAO,GAAG,CAAC,CAAC+D,IAAI,CAAC/E,WAAW;IAClC,IAAMzC,OAAO,GAAGyD,OAAO,GAAG+D,IAAI,CAAC/E,WAAW,GAAG+E,IAAI;IACjD,IAAMS,iBAAiB,GAAGzE,oBAAoB,CAACxD,OAAO,EAAEyD,OAAO,CAAC;IAChE,IAAM9B,QAAQ,GAAG8B,OAAO,GAAGoE,WAAW,CAACL,IAAI,CAACpG,UAAU,CAAC,GAAGpB,OAAO;IACjE,IAAIiI,iBAAiB,KAAK,CAAC,EAAE;MAC3BxE,OAAO,GACHqE,gBAAgB,CAACtF,IAAI,CAAAjB,KAAA,CAArBuG,gBAAgB,EAASnG,QAAQ,CAAC,GAClCmG,gBAAgB,CAACtF,IAAI,CAACxC,OAAO,CAAC;IACpC,CAAC,MAAM;MACL+H,gBAAgB,CAACvF,IAAI,CAAC;QACpBqB,aAAa,EAAEW,CAAC;QAChBlB,QAAQ,EAAE2E,iBAAiB;QAC3BT,IAAI,EAAEA,IAAI;QACV/D,OAAO,EAAEA,OAAO;QAChBrB,OAAO,EAAET;MACX,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,OAAOoG,gBAAgB,CACpBG,IAAI,CAACxE,oBAAoB,CAAC,CAC1ByE,MAAM,CAAC,UAACC,GAAG,EAAEC,QAAQ,EAAK;IACzBA,QAAQ,CAAC5E,OAAO,GACZ2E,GAAG,CAAC5F,IAAI,CAAAjB,KAAA,CAAR6G,GAAG,EAASC,QAAQ,CAACjG,OAAO,CAAC,GAC7BgG,GAAG,CAAC5F,IAAI,CAAC6F,QAAQ,CAACjG,OAAO,CAAC;IAC9B,OAAOgG,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC,CACLE,MAAM,CAACR,gBAAgB,CAAC;AAC7B,CAAC;AAEK,IAAAS,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAE5G,OAAO,EAAE;EAC7CA,OAAO,GAAGA,OAAO,IAAI,EAAE;EAEvB,IAAIR,UAAU;EACd,IAAIQ,OAAO,CAACiB,aAAa,EAAE;IACzBzB,UAAU,GAAGM,wBAAwB,CACnC,CAAC8G,SAAS,CAAC,EACX5G,OAAO,CAACV,gBAAgB,EACxB;MACEC,MAAM,EAAEuG,8BAA8B,CAACe,IAAI,CAAC,IAAI,EAAE7G,OAAO,CAAC;MAC1DW,OAAO,EAAE,KAAK;MACdM,aAAa,EAAEjB,OAAO,CAACiB,aAAa;MACpCE,gBAAgB,EAAE4E;IACpB,CACF,CAAC;EACH,CAAC,MAAM;IACLvG,UAAU,GAAGJ,aAAa,CACxBwH,SAAS,EACT5G,OAAO,CAACV,gBAAgB,EACxBwG,8BAA8B,CAACe,IAAI,CAAC,IAAI,EAAE7G,OAAO,CACnD,CAAC;EACH;EACA,OAAOiG,WAAW,CAACzG,UAAU,CAAC;AAChC;AAEM,IAAAsH,SAAS,GAAG,SAAZA,SAASA,CAAaF,SAAS,EAAE5G,OAAO,EAAE;EAC9CA,OAAO,GAAGA,OAAO,IAAI,EAAE;EAEvB,IAAIR,UAAU;EACd,IAAIQ,OAAO,CAACiB,aAAa,EAAE;IACzBzB,UAAU,GAAGM,wBAAwB,CACnC,CAAC8G,SAAS,CAAC,EACX5G,OAAO,CAACV,gBAAgB,EACxB;MACEC,MAAM,EAAEsG,+BAA+B,CAACgB,IAAI,CAAC,IAAI,EAAE7G,OAAO,CAAC;MAC3DW,OAAO,EAAE,IAAI;MACbM,aAAa,EAAEjB,OAAO,CAACiB;IACzB,CACF,CAAC;EACH,CAAC,MAAM;IACLzB,UAAU,GAAGJ,aAAa,CACxBwH,SAAS,EACT5G,OAAO,CAACV,gBAAgB,EACxBuG,+BAA+B,CAACgB,IAAI,CAAC,IAAI,EAAE7G,OAAO,CACpD,CAAC;EACH;EAEA,OAAOR,UAAU;AACnB;AAEM,IAAAuH,UAAU,GAAG,SAAbA,UAAUA,CAAatI,IAAI,EAAEuB,OAAO,EAAE;EAC1CA,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB,IAAI,CAACvB,IAAI,EAAE;IACT,MAAM,IAAIgD,KAAK,CAAC,kBAAkB,CAAC;EACrC;EACA,IAAI1D,OAAO,CAACO,IAAI,CAACG,IAAI,EAAEd,iBAAiB,CAAC,KAAK,KAAK,EAAE;IACnD,OAAO,KAAK;EACd;EACA,OAAOmI,8BAA8B,CAAC9F,OAAO,EAAEvB,IAAI,CAAC;AACtD;AAEA,IAAMuI,0BAA0B,kBAAmBtJ,kBAAkB,CAClEgJ,MAAM,CAAC,QAAQ,CAAC,CAChB9I,IAAI,CAAC,GAAG,CAAC;AAEN,IAAAqJ,WAAW,GAAG,SAAdA,WAAWA,CAAaxI,IAAI,EAAEuB,OAAO,EAAE;EAC3CA,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB,IAAI,CAACvB,IAAI,EAAE;IACT,MAAM,IAAIgD,KAAK,CAAC,kBAAkB,CAAC;EACrC;EACA,IAAI1D,OAAO,CAACO,IAAI,CAACG,IAAI,EAAEuI,0BAA0B,CAAC,KAAK,KAAK,EAAE;IAC5D,OAAO,KAAK;EACd;EACA,OAAOnB,+BAA+B,CAAC7F,OAAO,EAAEvB,IAAI,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}