{"ast": null, "code": "function r() {\n  return {\n    before(_ref) {\n      let {\n        doc: e,\n        d: o\n      } = _ref;\n      o.style(e.documentElement, \"overflow\", \"hidden\");\n    }\n  };\n}\nexport { r as preventScroll };", "map": {"version": 3, "names": ["r", "before", "_ref", "doc", "e", "d", "o", "style", "documentElement", "preventScroll"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js"], "sourcesContent": ["function r(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{r as preventScroll};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM;IAACC,MAAMA,CAAAC,IAAA,EAAa;MAAA,IAAZ;QAACC,GAAG,EAACC,CAAC;QAACC,CAAC,EAACC;MAAC,CAAC,GAAAJ,IAAA;MAAEI,CAAC,CAACC,KAAK,CAACH,CAAC,CAACI,eAAe,EAAC,UAAU,EAAC,QAAQ,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOR,CAAC,IAAIS,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}