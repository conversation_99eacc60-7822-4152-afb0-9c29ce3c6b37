{"ast": null, "code": "import s, { createContext as E, useContext as h, useState as p } from \"react\";\nimport { Hidden as b, HiddenFeatures as M } from '../internal/hidden.js';\nimport * as f from '../utils/dom.js';\nimport { getOwnerDocument as v } from '../utils/owner.js';\nimport { useEvent as m } from './use-event.js';\nimport { useOwnerDocument as x } from './use-owner.js';\nfunction H({\n  defaultContainers: r = [],\n  portals: n,\n  mainTreeNode: o\n} = {}) {\n  let l = x(o),\n    u = m(() => {\n      var i, c;\n      let t = [];\n      for (let e of r) e !== null && (f.isElement(e) ? t.push(e) : \"current\" in e && f.isElement(e.current) && t.push(e.current));\n      if (n != null && n.current) for (let e of n.current) t.push(e);\n      for (let e of (i = l == null ? void 0 : l.querySelectorAll(\"html > *, body > *\")) != null ? i : []) e !== document.body && e !== document.head && f.isElement(e) && e.id !== \"headlessui-portal-root\" && (o && (e.contains(o) || e.contains((c = o == null ? void 0 : o.getRootNode()) == null ? void 0 : c.host)) || t.some(d => e.contains(d)) || t.push(e));\n      return t;\n    });\n  return {\n    resolveContainers: u,\n    contains: m(t => u().some(i => i.contains(t)))\n  };\n}\nlet a = E(null);\nfunction P({\n  children: r,\n  node: n\n}) {\n  let [o, l] = p(null),\n    u = y(n != null ? n : o);\n  return s.createElement(a.Provider, {\n    value: u\n  }, r, u === null && s.createElement(b, {\n    features: M.Hidden,\n    ref: t => {\n      var i, c;\n      if (t) {\n        for (let e of (c = (i = v(t)) == null ? void 0 : i.querySelectorAll(\"html > *, body > *\")) != null ? c : []) if (e !== document.body && e !== document.head && f.isElement(e) && e != null && e.contains(t)) {\n          l(e);\n          break;\n        }\n      }\n    }\n  }));\n}\nfunction y(r = null) {\n  var n;\n  return (n = h(a)) != null ? n : r;\n}\nexport { P as MainTreeProvider, y as useMainTreeNode, H as useRootContainers };", "map": {"version": 3, "names": ["s", "createContext", "E", "useContext", "h", "useState", "p", "Hidden", "b", "HiddenFeatures", "M", "f", "getOwnerDocument", "v", "useEvent", "m", "useOwnerDocument", "x", "H", "defaultContainers", "r", "portals", "n", "mainTreeNode", "o", "l", "u", "i", "c", "t", "e", "isElement", "push", "current", "querySelectorAll", "document", "body", "head", "id", "contains", "getRootNode", "host", "some", "d", "resolveContainers", "a", "P", "children", "node", "y", "createElement", "Provider", "value", "features", "ref", "MainTreeProvider", "useMainTreeNode", "useRootContainers"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/hooks/use-root-containers.js"], "sourcesContent": ["import s,{createContext as E,useContext as h,useState as p}from\"react\";import{Hidden as b,HiddenFeatures as M}from'../internal/hidden.js';import*as f from'../utils/dom.js';import{getOwnerDocument as v}from'../utils/owner.js';import{useEvent as m}from'./use-event.js';import{useOwnerDocument as x}from'./use-owner.js';function H({defaultContainers:r=[],portals:n,mainTreeNode:o}={}){let l=x(o),u=m(()=>{var i,c;let t=[];for(let e of r)e!==null&&(f.isElement(e)?t.push(e):\"current\"in e&&f.isElement(e.current)&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&f.isElement(e)&&e.id!==\"headlessui-portal-root\"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(d=>e.contains(d))||t.push(e));return t});return{resolveContainers:u,contains:m(t=>u().some(i=>i.contains(t)))}}let a=E(null);function P({children:r,node:n}){let[o,l]=p(null),u=y(n!=null?n:o);return s.createElement(a.Provider,{value:u},r,u===null&&s.createElement(b,{features:M.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=v(t))==null?void 0:i.querySelectorAll(\"html > *, body > *\"))!=null?c:[])if(e!==document.body&&e!==document.head&&f.isElement(e)&&e!=null&&e.contains(t)){l(e);break}}}}))}function y(r=null){var n;return(n=h(a))!=null?n:r}export{P as MainTreeProvider,y as useMainTreeNode,H as useRootContainers};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,MAAM,IAAIC,CAAC,EAACC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,OAAM,KAAIC,CAAC,MAAK,iBAAiB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAAC;EAACC,iBAAiB,EAACC,CAAC,GAAC,EAAE;EAACC,OAAO,EAACC,CAAC;EAACC,YAAY,EAACC;AAAC,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAACO,CAAC,CAAC;IAACE,CAAC,GAACX,CAAC,CAAC,MAAI;MAAC,IAAIY,CAAC,EAACC,CAAC;MAAC,IAAIC,CAAC,GAAC,EAAE;MAAC,KAAI,IAAIC,CAAC,IAAIV,CAAC,EAACU,CAAC,KAAG,IAAI,KAAGnB,CAAC,CAACoB,SAAS,CAACD,CAAC,CAAC,GAACD,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,GAAC,SAAS,IAAGA,CAAC,IAAEnB,CAAC,CAACoB,SAAS,CAACD,CAAC,CAACG,OAAO,CAAC,IAAEJ,CAAC,CAACG,IAAI,CAACF,CAAC,CAACG,OAAO,CAAC,CAAC;MAAC,IAAGX,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACW,OAAO,EAAC,KAAI,IAAIH,CAAC,IAAIR,CAAC,CAACW,OAAO,EAACJ,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC;MAAC,KAAI,IAAIA,CAAC,IAAG,CAACH,CAAC,GAACF,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACS,gBAAgB,CAAC,oBAAoB,CAAC,KAAG,IAAI,GAACP,CAAC,GAAC,EAAE,EAACG,CAAC,KAAGK,QAAQ,CAACC,IAAI,IAAEN,CAAC,KAAGK,QAAQ,CAACE,IAAI,IAAE1B,CAAC,CAACoB,SAAS,CAACD,CAAC,CAAC,IAAEA,CAAC,CAACQ,EAAE,KAAG,wBAAwB,KAAGd,CAAC,KAAGM,CAAC,CAACS,QAAQ,CAACf,CAAC,CAAC,IAAEM,CAAC,CAACS,QAAQ,CAAC,CAACX,CAAC,GAACJ,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACgB,WAAW,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACZ,CAAC,CAACa,IAAI,CAAC,CAAC,IAAEZ,CAAC,CAACa,IAAI,CAACC,CAAC,IAAEb,CAAC,CAACS,QAAQ,CAACI,CAAC,CAAC,CAAC,IAAEd,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,CAAC;MAAC,OAAOD,CAAC;IAAA,CAAC,CAAC;EAAC,OAAM;IAACe,iBAAiB,EAAClB,CAAC;IAACa,QAAQ,EAACxB,CAAC,CAACc,CAAC,IAAEH,CAAC,CAAC,CAAC,CAACgB,IAAI,CAACf,CAAC,IAAEA,CAAC,CAACY,QAAQ,CAACV,CAAC,CAAC,CAAC;EAAC,CAAC;AAAA;AAAC,IAAIgB,CAAC,GAAC3C,CAAC,CAAC,IAAI,CAAC;AAAC,SAAS4C,CAACA,CAAC;EAACC,QAAQ,EAAC3B,CAAC;EAAC4B,IAAI,EAAC1B;AAAC,CAAC,EAAC;EAAC,IAAG,CAACE,CAAC,EAACC,CAAC,CAAC,GAACnB,CAAC,CAAC,IAAI,CAAC;IAACoB,CAAC,GAACuB,CAAC,CAAC3B,CAAC,IAAE,IAAI,GAACA,CAAC,GAACE,CAAC,CAAC;EAAC,OAAOxB,CAAC,CAACkD,aAAa,CAACL,CAAC,CAACM,QAAQ,EAAC;IAACC,KAAK,EAAC1B;EAAC,CAAC,EAACN,CAAC,EAACM,CAAC,KAAG,IAAI,IAAE1B,CAAC,CAACkD,aAAa,CAAC1C,CAAC,EAAC;IAAC6C,QAAQ,EAAC3C,CAAC,CAACH,MAAM;IAAC+C,GAAG,EAACzB,CAAC,IAAE;MAAC,IAAIF,CAAC,EAACC,CAAC;MAAC,IAAGC,CAAC,EAAC;QAAC,KAAI,IAAIC,CAAC,IAAG,CAACF,CAAC,GAAC,CAACD,CAAC,GAACd,CAAC,CAACgB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACF,CAAC,CAACO,gBAAgB,CAAC,oBAAoB,CAAC,KAAG,IAAI,GAACN,CAAC,GAAC,EAAE,EAAC,IAAGE,CAAC,KAAGK,QAAQ,CAACC,IAAI,IAAEN,CAAC,KAAGK,QAAQ,CAACE,IAAI,IAAE1B,CAAC,CAACoB,SAAS,CAACD,CAAC,CAAC,IAAEA,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACS,QAAQ,CAACV,CAAC,CAAC,EAAC;UAACJ,CAAC,CAACK,CAAC,CAAC;UAAC;QAAK;MAAC;IAAC;EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASmB,CAACA,CAAC7B,CAAC,GAAC,IAAI,EAAC;EAAC,IAAIE,CAAC;EAAC,OAAM,CAACA,CAAC,GAAClB,CAAC,CAACyC,CAAC,CAAC,KAAG,IAAI,GAACvB,CAAC,GAACF,CAAC;AAAA;AAAC,SAAO0B,CAAC,IAAIS,gBAAgB,EAACN,CAAC,IAAIO,eAAe,EAACtC,CAAC,IAAIuC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}