{"ast": null, "code": "import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useUser}from'../contexts/UserContext';import{jsx as _jsx}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,requireAuth=true}=_ref;const{isAuthenticated,isLoading}=useUser();const location=useLocation();// Show loading spinner while checking authentication\nif(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"})});}// If authentication is required but user is not authenticated\nif(requireAuth&&!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}// If user is authenticated but trying to access auth pages (login, register)\nif(!requireAuth&&isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/account\",replace:true});}return children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useUser", "jsx", "_jsx", "ProtectedRoute", "_ref", "children", "requireAuth", "isAuthenticated", "isLoading", "location", "className", "to", "state", "from", "replace"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useUser } from '../contexts/UserContext';\n\nconst ProtectedRoute = ({ children, requireAuth = true }) => {\n  const { isAuthenticated, isLoading } = useUser();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"></div>\n      </div>\n    );\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // If user is authenticated but trying to access auth pages (login, register)\n  if (!requireAuth && isAuthenticated) {\n    return <Navigate to=\"/account\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElD,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAsC,IAArC,CAAEC,QAAQ,CAAEC,WAAW,CAAG,IAAK,CAAC,CAAAF,IAAA,CACtD,KAAM,CAAEG,eAAe,CAAEC,SAAU,CAAC,CAAGR,OAAO,CAAC,CAAC,CAChD,KAAM,CAAAS,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAE9B;AACA,GAAIS,SAAS,CAAE,CACb,mBACEN,IAAA,QAAKQ,SAAS,CAAC,0DAA0D,CAAAL,QAAA,cACvEH,IAAA,QAAKQ,SAAS,CAAC,wEAAwE,CAAM,CAAC,CAC3F,CAAC,CAEV,CAEA;AACA,GAAIJ,WAAW,EAAI,CAACC,eAAe,CAAE,CACnC,mBAAOL,IAAA,CAACJ,QAAQ,EAACa,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAEJ,QAAS,CAAE,CAACK,OAAO,MAAE,CAAC,CACpE,CAEA;AACA,GAAI,CAACR,WAAW,EAAIC,eAAe,CAAE,CACnC,mBAAOL,IAAA,CAACJ,QAAQ,EAACa,EAAE,CAAC,UAAU,CAACG,OAAO,MAAE,CAAC,CAC3C,CAEA,MAAO,CAAAT,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}