{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport ThemeToggle from './ThemeToggle';\nimport { useUser } from '../contexts/UserContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const {\n    theme,\n    getThemeClasses\n  } = useTheme();\n  // const { totalItems } = useCart(); // Available for future use\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigationItems = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? getThemeClasses('bg-white/95 backdrop-blur-md shadow-lg', 'bg-slate-900/95 backdrop-blur-md shadow-xl shadow-black/20') : 'bg-transparent'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                rotate: 360\n              },\n              transition: {\n                duration: 0.5\n              },\n              className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-2xl font-bold transition-colors duration-300 ${isScrolled ? getThemeClasses('text-gray-900', 'text-white') : 'text-white'}`,\n              children: \"ShopHub\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-8\",\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n              to: item.href,\n              className: `relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${isActive(item.href) ? isScrolled ? 'text-light-orange-600' : 'text-yellow-300' : isScrolled ? getThemeClasses('text-gray-700 hover:text-light-orange-600', 'text-gray-300 hover:text-light-orange-400') : 'text-white hover:text-yellow-300'}`,\n              children: [item.name, isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeTab\",\n                className: `absolute bottom-0 left-0 right-0 h-0.5 ${isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 21\n              }, this)]\n            }, item.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center flex-1 max-w-md mx-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 ${isScrolled ? getThemeClasses('text-gray-400', 'text-gray-400') : 'text-white/70'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${isScrolled ? getThemeClasses('bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300', 'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-2 focus:ring-light-orange-400') : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.1\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? getThemeClasses('text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50', 'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700') : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n              children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/account\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? getThemeClasses('text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50', 'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700') : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ThemeToggle, {\n              size: \"md\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: `relative p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: user.profilePicture,\n                  alt: \"Profile\",\n                  className: \"w-8 h-8 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 border-b border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: user === null || user === void 0 ? void 0 : user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/account\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"My Account\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/orders\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Order History\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/wishlist\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Wishlist\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: logout,\n                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\",\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: `px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\",\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(!isOpen),\n              className: `lg:hidden p-2 rounded-md transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600' : 'text-white hover:text-yellow-300'}`,\n              children: isOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto'\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: `lg:hidden backdrop-blur-md border-t transition-colors duration-300 ${getThemeClasses('bg-white/95 border-gray-200', 'bg-slate-900/95 border-slate-700')}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-6 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 ${getThemeClasses('text-gray-400', 'text-gray-400')}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-10 pr-4 py-3 rounded-lg focus:ring-2 transition-colors duration-300 ${getThemeClasses('bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-light-orange-300', 'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-light-orange-400')}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                onClick: () => setIsOpen(false),\n                className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${isActive(item.href) ? getThemeClasses('bg-light-orange-100 text-light-orange-700', 'bg-light-orange-900/20 text-light-orange-400') : getThemeClasses('text-gray-700 hover:bg-gray-100', 'text-gray-300 hover:bg-slate-700')}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 23\n                }, this)]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center justify-around pt-4 border-t transition-colors duration-300 ${getThemeClasses('border-gray-200', 'border-slate-700')}`,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `flex flex-col items-center space-y-1 transition-colors duration-300 ${getThemeClasses('text-gray-600 hover:text-light-orange-600', 'text-gray-400 hover:text-light-orange-400')}`,\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: `flex flex-col items-center space-y-1 transition-colors duration-300 ${getThemeClasses('text-gray-600 hover:text-light-orange-600', 'text-gray-400 hover:text-light-orange-400')}`,\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(ThemeToggle, {\n                  size: \"sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs transition-colors duration-300 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n                  children: \"Theme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs transition-colors duration-300 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n                  children: \"Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 lg:h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navigation, \"//Av9IAWCFMl1WVFtU308VKxjBQ=\", false, function () {\n  return [useLocation, useUser, useTheme];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ShoppingCart", "ThemeToggle", "useUser", "useTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Navigation", "_s", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "user", "isAuthenticated", "logout", "theme", "getThemeClasses", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navigationItems", "name", "href", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "to", "div", "whileHover", "rotate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "layoutId", "type", "placeholder", "value", "onChange", "e", "target", "button", "scale", "whileTap", "size", "profilePicture", "src", "alt", "firstName", "lastName", "email", "onClick", "opacity", "height", "exit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\nimport ThemeToggle from './ThemeToggle';\nimport { useUser } from '../contexts/UserContext';\nimport { useTheme } from '../contexts/ThemeContext';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { user, isAuthenticated, logout } = useUser();\n  const { theme, getThemeClasses } = useTheme();\n  // const { totalItems } = useCart(); // Available for future use\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? getThemeClasses(\n                'bg-white/95 backdrop-blur-md shadow-lg',\n                'bg-slate-900/95 backdrop-blur-md shadow-xl shadow-black/20'\n              )\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 lg:h-20\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3\">\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.5 }}\n                className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n              >\n                <ShoppingBagIcon className=\"w-6 h-6 text-white\" />\n              </motion.div>\n              <span className={`text-2xl font-bold transition-colors duration-300 ${\n                isScrolled\n                  ? getThemeClasses('text-gray-900', 'text-white')\n                  : 'text-white'\n              }`}>\n                ShopHub\n              </span>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${\n                    isActive(item.href)\n                      ? isScrolled\n                        ? 'text-light-orange-600'\n                        : 'text-yellow-300'\n                      : isScrolled\n                        ? getThemeClasses(\n                            'text-gray-700 hover:text-light-orange-600',\n                            'text-gray-300 hover:text-light-orange-400'\n                          )\n                        : 'text-white hover:text-yellow-300'\n                  }`}\n                >\n                  {item.name}\n                  {isActive(item.href) && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className={`absolute bottom-0 left-0 right-0 h-0.5 ${\n                        isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'\n                      }`}\n                    />\n                  )}\n                </Link>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 ${\n                    isScrolled\n                      ? getThemeClasses('text-gray-400', 'text-gray-400')\n                      : 'text-white/70'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search products...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className={`w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${\n                    isScrolled\n                      ? getThemeClasses(\n                          'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300',\n                          'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-2 focus:ring-light-orange-400'\n                        )\n                      : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'\n                  }`}\n                />\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                className={`p-2 rounded-full transition-colors duration-300 ${\n                  isScrolled\n                    ? getThemeClasses(\n                        'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50',\n                        'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700'\n                      )\n                    : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                }`}\n              >\n                <HeartIcon className=\"w-6 h-6\" />\n              </motion.button>\n\n              <Link to=\"/account\">\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className={`p-2 rounded-full transition-colors duration-300 ${\n                    isScrolled\n                      ? getThemeClasses(\n                          'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50',\n                          'text-gray-300 hover:text-light-orange-400 hover:bg-slate-700'\n                        )\n                      : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                  }`}\n                >\n                  <UserIcon className=\"w-6 h-6\" />\n                </motion.button>\n              </Link>\n\n              <ShoppingCart />\n\n              {/* Theme Toggle */}\n              <ThemeToggle size=\"md\" />\n\n              {/* User Account */}\n              {isAuthenticated ? (\n                <div className=\"relative group\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className={`relative p-2 rounded-full transition-colors duration-300 ${\n                      isScrolled\n                        ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                        : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                    }`}\n                  >\n                    {user?.profilePicture ? (\n                      <img\n                        src={user.profilePicture}\n                        alt=\"Profile\"\n                        className=\"w-8 h-8 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <UserIcon className=\"w-6 h-6\" />\n                    )}\n                  </motion.button>\n\n                  {/* User Dropdown */}\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                    <div className=\"p-3 border-b border-gray-100\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {user?.firstName} {user?.lastName}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                    <div className=\"py-1\">\n                      <Link\n                        to=\"/account\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        My Account\n                      </Link>\n                      <Link\n                        to=\"/orders\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Order History\n                      </Link>\n                      <Link\n                        to=\"/wishlist\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Wishlist\n                      </Link>\n                      <button\n                        onClick={logout}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-light-orange-50 hover:text-light-orange-600\"\n                      >\n                        Sign Out\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <Link to=\"/login\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-300 ${\n                        isScrolled\n                          ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50'\n                          : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                      }`}\n                    >\n                      Sign In\n                    </motion.button>\n                  </Link>\n                  <Link to=\"/register\">\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"px-3 py-1.5 bg-light-orange-500 text-white rounded-lg text-sm font-medium hover:bg-light-orange-600 transition-colors duration-300\"\n                    >\n                      Sign Up\n                    </motion.button>\n                  </Link>\n                </div>\n              )}\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600' \n                    : 'text-white hover:text-yellow-300'\n                }`}\n              >\n                {isOpen ? (\n                  <XMarkIcon className=\"w-6 h-6\" />\n                ) : (\n                  <Bars3Icon className=\"w-6 h-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className={`lg:hidden backdrop-blur-md border-t transition-colors duration-300 ${\n                getThemeClasses(\n                  'bg-white/95 border-gray-200',\n                  'bg-slate-900/95 border-slate-700'\n                )\n              }`}\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {/* Mobile Search */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className={`h-5 w-5 ${\n                      getThemeClasses('text-gray-400', 'text-gray-400')\n                    }`} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className={`w-full pl-10 pr-4 py-3 rounded-lg focus:ring-2 transition-colors duration-300 ${\n                      getThemeClasses(\n                        'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-light-orange-300',\n                        'bg-slate-700 text-white placeholder-gray-400 focus:bg-slate-600 focus:ring-light-orange-400'\n                      )\n                    }`}\n                  />\n                </div>\n\n                {/* Mobile Navigation Links */}\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setIsOpen(false)}\n                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${\n                        isActive(item.href)\n                          ? getThemeClasses(\n                              'bg-light-orange-100 text-light-orange-700',\n                              'bg-light-orange-900/20 text-light-orange-400'\n                            )\n                          : getThemeClasses(\n                              'text-gray-700 hover:bg-gray-100',\n                              'text-gray-300 hover:bg-slate-700'\n                            )\n                      }`}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ))}\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className={`flex items-center justify-around pt-4 border-t transition-colors duration-300 ${\n                  getThemeClasses('border-gray-200', 'border-slate-700')\n                }`}>\n                  <button className={`flex flex-col items-center space-y-1 transition-colors duration-300 ${\n                    getThemeClasses(\n                      'text-gray-600 hover:text-light-orange-600',\n                      'text-gray-400 hover:text-light-orange-400'\n                    )\n                  }`}>\n                    <HeartIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Wishlist</span>\n                  </button>\n                  <Link to=\"/account\" className={`flex flex-col items-center space-y-1 transition-colors duration-300 ${\n                    getThemeClasses(\n                      'text-gray-600 hover:text-light-orange-600',\n                      'text-gray-400 hover:text-light-orange-400'\n                    )\n                  }`}>\n                    <UserIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Account</span>\n                  </Link>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ThemeToggle size=\"sm\" />\n                    <span className={`text-xs transition-colors duration-300 ${\n                      getThemeClasses('text-gray-600', 'text-gray-400')\n                    }`}>Theme</span>\n                  </div>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ShoppingCart />\n                    <span className={`text-xs transition-colors duration-300 ${\n                      getThemeClasses('text-gray-600', 'text-gray-400')\n                    }`}>Cart</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16 lg:h-20\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,QAChB,6BAA6B;AACpC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMgC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGjB,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEkB,KAAK;IAAEC;EAAgB,CAAC,GAAGlB,QAAQ,CAAC,CAAC;EAC7C;;EAEAlB,SAAS,CAAC,MAAM;IACd,MAAMqC,YAAY,GAAGA,CAAA,KAAM;MACzBT,aAAa,CAACU,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAElC;EAAS,CAAC,EAC3C;IAAEgC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEjC;EAAQ,CAAC,EACtD;IAAE+B,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAEjC;EAAQ,CAAC,EAC7D;IAAE+B,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE/B;EAAsB,CAAC,EAC9D;IAAE6B,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEhC;EAAU,CAAC,CACvD;EAED,MAAMiC,QAAQ,GAAIC,IAAI,IAAKhB,QAAQ,CAACiB,QAAQ,KAAKD,IAAI;EAErD,oBACE3B,OAAA,CAAAE,SAAA;IAAA2B,QAAA,gBACE7B,OAAA,CAACjB,MAAM,CAAC+C,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,SAAS,EAAE,+DACT3B,UAAU,GACNS,eAAe,CACb,wCAAwC,EACxC,4DACF,CAAC,GACD,gBAAgB,EACnB;MAAAa,QAAA,gBAEH7B,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAL,QAAA,eACrD7B,OAAA;UAAKkC,SAAS,EAAC,gDAAgD;UAAAL,QAAA,gBAE7D7B,OAAA,CAACnB,IAAI;YAACsD,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAClD7B,OAAA,CAACjB,MAAM,CAACqD,GAAG;cACTC,UAAU,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cAC5BC,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9BN,SAAS,EAAC,8HAA8H;cAAAL,QAAA,eAExI7B,OAAA,CAACb,eAAe;gBAAC+C,SAAS,EAAC;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACb5C,OAAA;cAAMkC,SAAS,EAAE,qDACf3B,UAAU,GACNS,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,GAC9C,YAAY,EACf;cAAAa,QAAA,EAAC;YAEJ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP5C,OAAA;YAAKkC,SAAS,EAAC,uCAAuC;YAAAL,QAAA,EACnDP,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACxB9C,OAAA,CAACnB,IAAI;cAEHsD,EAAE,EAAEW,IAAI,CAACtB,IAAK;cACdU,SAAS,EAAE,yEACTR,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,GACfjB,UAAU,GACR,uBAAuB,GACvB,iBAAiB,GACnBA,UAAU,GACRS,eAAe,CACb,2CAA2C,EAC3C,2CACF,CAAC,GACD,kCAAkC,EACvC;cAAAa,QAAA,GAEFiB,IAAI,CAACvB,IAAI,EACTG,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,iBAClBxB,OAAA,CAACjB,MAAM,CAACqD,GAAG;gBACTW,QAAQ,EAAC,WAAW;gBACpBb,SAAS,EAAE,0CACT3B,UAAU,GAAG,qBAAqB,GAAG,eAAe;cACnD;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACF;YAAA,GAvBIE,IAAI,CAACvB,IAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5C,OAAA;YAAKkC,SAAS,EAAC,kDAAkD;YAAAL,QAAA,eAC/D7B,OAAA;cAAKkC,SAAS,EAAC,iBAAiB;cAAAL,QAAA,gBAC9B7B,OAAA;gBAAKkC,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnF7B,OAAA,CAACZ,mBAAmB;kBAAC8C,SAAS,EAAE,WAC9B3B,UAAU,GACNS,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,GACjD,eAAe;gBAClB;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEgD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAEzC,WAAY;gBACnB0C,QAAQ,EAAGC,CAAC,IAAK1C,cAAc,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDhB,SAAS,EAAE,mEACT3B,UAAU,GACNS,eAAe,CACb,wGAAwG,EACxG,0GACF,CAAC,GACD,iHAAiH;cACpH;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5C,OAAA;YAAKkC,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAC1C7B,OAAA,CAACjB,MAAM,CAACuE,MAAM;cACZjB,UAAU,EAAE;gBAAEkB,KAAK,EAAE;cAAI,CAAE;cAC3BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAI,CAAE;cACzBrB,SAAS,EAAE,mDACT3B,UAAU,GACNS,eAAe,CACb,oEAAoE,EACpE,8DACF,CAAC,GACD,oDAAoD,EACvD;cAAAa,QAAA,eAEH7B,OAAA,CAACV,SAAS;gBAAC4C,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAEhB5C,OAAA,CAACnB,IAAI;cAACsD,EAAE,EAAC,UAAU;cAAAN,QAAA,eACjB7B,OAAA,CAACjB,MAAM,CAACuE,MAAM;gBACZjB,UAAU,EAAE;kBAAEkB,KAAK,EAAE;gBAAI,CAAE;gBAC3BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAI,CAAE;gBACzBrB,SAAS,EAAE,mDACT3B,UAAU,GACNS,eAAe,CACb,oEAAoE,EACpE,8DACF,CAAC,GACD,oDAAoD,EACvD;gBAAAa,QAAA,eAEH7B,OAAA,CAACX,QAAQ;kBAAC6C,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEP5C,OAAA,CAACL,YAAY;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGhB5C,OAAA,CAACJ,WAAW;cAAC6D,IAAI,EAAC;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAGxB/B,eAAe,gBACdb,OAAA;cAAKkC,SAAS,EAAC,gBAAgB;cAAAL,QAAA,gBAC7B7B,OAAA,CAACjB,MAAM,CAACuE,MAAM;gBACZjB,UAAU,EAAE;kBAAEkB,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BrB,SAAS,EAAE,4DACT3B,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;gBAAAsB,QAAA,EAEFjB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,cAAc,gBACnB1D,OAAA;kBACE2D,GAAG,EAAE/C,IAAI,CAAC8C,cAAe;kBACzBE,GAAG,EAAC,SAAS;kBACb1B,SAAS,EAAC;gBAAmC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEF5C,OAAA,CAACX,QAAQ;kBAAC6C,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAChC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC,eAGhB5C,OAAA;gBAAKkC,SAAS,EAAC,kLAAkL;gBAAAL,QAAA,gBAC/L7B,OAAA;kBAAKkC,SAAS,EAAC,8BAA8B;kBAAAL,QAAA,gBAC3C7B,OAAA;oBAAGkC,SAAS,EAAC,mCAAmC;oBAAAL,QAAA,GAC7CjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,SAAS,EAAC,GAAC,EAACjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,QAAQ;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACJ5C,OAAA;oBAAGkC,SAAS,EAAC,uBAAuB;oBAAAL,QAAA,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD;kBAAK;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACN5C,OAAA;kBAAKkC,SAAS,EAAC,MAAM;kBAAAL,QAAA,gBACnB7B,OAAA,CAACnB,IAAI;oBACHsD,EAAE,EAAC,UAAU;oBACbD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP5C,OAAA,CAACnB,IAAI;oBACHsD,EAAE,EAAC,SAAS;oBACZD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP5C,OAAA,CAACnB,IAAI;oBACHsD,EAAE,EAAC,WAAW;oBACdD,SAAS,EAAC,4FAA4F;oBAAAL,QAAA,EACvG;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP5C,OAAA;oBACEgE,OAAO,EAAElD,MAAO;oBAChBoB,SAAS,EAAC,6GAA6G;oBAAAL,QAAA,EACxH;kBAED;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN5C,OAAA;cAAKkC,SAAS,EAAC,6BAA6B;cAAAL,QAAA,gBAC1C7B,OAAA,CAACnB,IAAI;gBAACsD,EAAE,EAAC,QAAQ;gBAAAN,QAAA,eACf7B,OAAA,CAACjB,MAAM,CAACuE,MAAM;kBACZjB,UAAU,EAAE;oBAAEkB,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BrB,SAAS,EAAE,6EACT3B,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;kBAAAsB,QAAA,EACJ;gBAED;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACP5C,OAAA,CAACnB,IAAI;gBAACsD,EAAE,EAAC,WAAW;gBAAAN,QAAA,eAClB7B,OAAA,CAACjB,MAAM,CAACuE,MAAM;kBACZjB,UAAU,EAAE;oBAAEkB,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BrB,SAAS,EAAC,oIAAoI;kBAAAL,QAAA,EAC/I;gBAED;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGD5C,OAAA;cACEgE,OAAO,EAAEA,CAAA,KAAM1D,SAAS,CAAC,CAACD,MAAM,CAAE;cAClC6B,SAAS,EAAE,2DACT3B,UAAU,GACN,2CAA2C,GAC3C,kCAAkC,EACrC;cAAAsB,QAAA,EAEFxB,MAAM,gBACLL,OAAA,CAACd,SAAS;gBAACgD,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjC5C,OAAA,CAACf,SAAS;gBAACiD,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA,CAAChB,eAAe;QAAA6C,QAAA,EACbxB,MAAM,iBACLL,OAAA,CAACjB,MAAM,CAACqD,GAAG;UACTL,OAAO,EAAE;YAAEkC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACnCjC,OAAO,EAAE;YAAEgC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAE;UACxCC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAChChC,SAAS,EAAE,sEACTlB,eAAe,CACb,6BAA6B,EAC7B,kCACF,CAAC,EACA;UAAAa,QAAA,eAEH7B,OAAA;YAAKkC,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAElC7B,OAAA;cAAKkC,SAAS,EAAC,UAAU;cAAAL,QAAA,gBACvB7B,OAAA;gBAAKkC,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnF7B,OAAA,CAACZ,mBAAmB;kBAAC8C,SAAS,EAAE,WAC9BlB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC;gBAChD;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEgD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAEzC,WAAY;gBACnB0C,QAAQ,EAAGC,CAAC,IAAK1C,cAAc,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDhB,SAAS,EAAE,iFACTlB,eAAe,CACb,2FAA2F,EAC3F,6FACF,CAAC;cACA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5C,OAAA;cAAKkC,SAAS,EAAC,WAAW;cAAAL,QAAA,EACvBP,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACxB9C,OAAA,CAACnB,IAAI;gBAEHsD,EAAE,EAAEW,IAAI,CAACtB,IAAK;gBACdwC,OAAO,EAAEA,CAAA,KAAM1D,SAAS,CAAC,KAAK,CAAE;gBAChC4B,SAAS,EAAE,mFACTR,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,GACfR,eAAe,CACb,2CAA2C,EAC3C,8CACF,CAAC,GACDA,eAAe,CACb,iCAAiC,EACjC,kCACF,CAAC,EACJ;gBAAAa,QAAA,gBAEH7B,OAAA,CAAC8C,IAAI,CAACrB,IAAI;kBAACS,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC5C,OAAA;kBAAMkC,SAAS,EAAC,aAAa;kBAAAL,QAAA,EAAEiB,IAAI,CAACvB;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAhB3CE,IAAI,CAACvB,IAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5C,OAAA;cAAKkC,SAAS,EAAE,iFACdlB,eAAe,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACrD;cAAAa,QAAA,gBACD7B,OAAA;gBAAQkC,SAAS,EAAE,uEACjBlB,eAAe,CACb,2CAA2C,EAC3C,2CACF,CAAC,EACA;gBAAAa,QAAA,gBACD7B,OAAA,CAACV,SAAS;kBAAC4C,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC5C,OAAA;kBAAMkC,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACT5C,OAAA,CAACnB,IAAI;gBAACsD,EAAE,EAAC,UAAU;gBAACD,SAAS,EAAE,uEAC7BlB,eAAe,CACb,2CAA2C,EAC3C,2CACF,CAAC,EACA;gBAAAa,QAAA,gBACD7B,OAAA,CAACX,QAAQ;kBAAC6C,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC5C,OAAA;kBAAMkC,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAO;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACP5C,OAAA;gBAAKkC,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnD7B,OAAA,CAACJ,WAAW;kBAAC6D,IAAI,EAAC;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzB5C,OAAA;kBAAMkC,SAAS,EAAE,0CACflB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAa,QAAA,EAAC;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACN5C,OAAA;gBAAKkC,SAAS,EAAC,sCAAsC;gBAAAL,QAAA,gBACnD7B,OAAA,CAACL,YAAY;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChB5C,OAAA;kBAAMkC,SAAS,EAAE,0CACflB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;kBAAAa,QAAA,EAAC;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGb5C,OAAA;MAAKkC,SAAS,EAAC;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAACxC,EAAA,CAnXID,UAAU;EAAA,QAIGrB,WAAW,EACce,OAAO,EACdC,QAAQ;AAAA;AAAAsE,EAAA,GANvCjE,UAAU;AAqXhB,eAAeA,UAAU;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}