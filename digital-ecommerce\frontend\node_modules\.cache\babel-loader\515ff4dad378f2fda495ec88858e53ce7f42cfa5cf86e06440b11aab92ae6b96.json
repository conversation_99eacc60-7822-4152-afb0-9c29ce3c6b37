{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AdminLoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { EyeIcon, EyeSlashIcon, ShieldCheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLoginPage = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    rememberMe: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const {\n    adminLogin,\n    isAuthenticated,\n    isLoading\n  } = useAdmin();\n  const {\n    getThemeClasses\n  } = useTheme();\n  const navigate = useNavigate();\n  const location = useLocation();\n  console.log('AdminLoginPage - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading);\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/admin/dashboard';\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, isLoading, navigate, from]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setError('');\n    console.log('Form submitted with:', formData);\n    const result = await adminLogin(formData.username, formData.password, formData.rememberMe);\n    console.log('Login result:', result);\n    if (result.success) {\n      console.log('Login successful, navigating to:', from);\n      navigate(from, {\n        replace: true\n      });\n    } else {\n      console.log('Login failed:', result.error);\n      setError(result.error);\n    }\n    setIsSubmitting(false);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${getThemeClasses('bg-gradient-to-br from-light-orange-50 to-white', 'bg-gradient-to-br from-slate-900 to-slate-800')}`,\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5\n      },\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0\n          },\n          animate: {\n            scale: 1\n          },\n          transition: {\n            delay: 0.2,\n            type: \"spring\",\n            stiffness: 200\n          },\n          className: \"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n            className: \"h-8 w-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: `mt-6 text-3xl font-bold ${getThemeClasses('text-gray-900', 'text-white')}`,\n          children: \"Admin Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `mt-2 text-sm ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n          children: \"Sign in to access the admin dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.form, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: `mt-8 space-y-6 p-8 rounded-xl shadow-xl ${getThemeClasses('bg-white border border-gray-200', 'bg-slate-800 border border-slate-700')}`,\n        onSubmit: handleSubmit,\n        children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n            className: \"h-5 w-5 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-red-700 dark:text-red-400\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: `block text-sm font-medium ${getThemeClasses('text-gray-700', 'text-gray-300')}`,\n              children: \"Username or Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"username\",\n              name: \"username\",\n              type: \"text\",\n              required: true,\n              value: formData.username,\n              onChange: handleInputChange,\n              className: `mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 transition-colors ${getThemeClasses('border-gray-300 bg-white text-gray-900 placeholder-gray-500', 'border-slate-600 bg-slate-700 text-white placeholder-gray-400')}`,\n              placeholder: \"Enter your username or email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: `block text-sm font-medium ${getThemeClasses('text-gray-700', 'text-gray-300')}`,\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: showPassword ? 'text' : 'password',\n                required: true,\n                value: formData.password,\n                onChange: handleInputChange,\n                className: `block w-full px-3 py-2 pr-10 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 transition-colors ${getThemeClasses('border-gray-300 bg-white text-gray-900 placeholder-gray-500', 'border-slate-600 bg-slate-700 text-white placeholder-gray-400')}`,\n                placeholder: \"Enter your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowPassword(!showPassword),\n                className: `absolute inset-y-0 right-0 pr-3 flex items-center ${getThemeClasses('text-gray-400 hover:text-gray-600', 'text-gray-500 hover:text-gray-300')}`,\n                children: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"rememberMe\",\n              name: \"rememberMe\",\n              type: \"checkbox\",\n              checked: formData.rememberMe,\n              onChange: handleInputChange,\n              className: \"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"rememberMe\",\n              className: `ml-2 block text-sm ${getThemeClasses('text-gray-700', 'text-gray-300')}`,\n              children: \"Remember me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isSubmitting,\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300\",\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Signing in...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this) : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mt-6 p-4 rounded-lg border-2 border-dashed ${getThemeClasses('border-gray-300 bg-gray-50', 'border-slate-600 bg-slate-700/50')}`,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: `text-sm font-medium mb-2 ${getThemeClasses('text-gray-700', 'text-gray-300')}`,\n            children: \"Demo Credentials:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-xs space-y-1 ${getThemeClasses('text-gray-600', 'text-gray-400')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Super Admin:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 20\n              }, this), \" admin / admin123\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Manager:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 20\n              }, this), \" manager / manager123\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLoginPage, \"1Tx05XG2uVDVFHLSWjSDPx5Yxfw=\", true, function () {\n  return [useAdmin, useNavigate, useLocation];\n});\n_c = AdminLoginPage;\nexport default AdminLoginPage;\nvar _c;\n$RefreshReg$(_c, \"AdminLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "motion", "EyeIcon", "EyeSlashIcon", "ShieldCheckIcon", "ExclamationTriangleIcon", "useAdmin", "jsxDEV", "_jsxDEV", "AdminLoginPage", "_s", "_location$state", "_location$state$from", "formData", "setFormData", "username", "password", "rememberMe", "showPassword", "setShowPassword", "error", "setError", "isSubmitting", "setIsSubmitting", "adminLogin", "isAuthenticated", "isLoading", "getThemeClasses", "useTheme", "navigate", "location", "console", "log", "from", "state", "pathname", "replace", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "handleSubmit", "preventDefault", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "stiffness", "form", "onSubmit", "htmlFor", "id", "required", "onChange", "placeholder", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AdminLoginPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { \n  EyeIcon, \n  EyeSlashIcon, \n  ShieldCheckIcon,\n  ExclamationTriangleIcon \n} from '@heroicons/react/24/outline';\nimport { useAdmin } from '../contexts/AdminContext';\n\nconst AdminLoginPage = () => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    rememberMe: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const { adminLogin, isAuthenticated, isLoading } = useAdmin();\n  const { getThemeClasses } = useTheme();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  console.log('AdminLoginPage - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading);\n\n  const from = location.state?.from?.pathname || '/admin/dashboard';\n\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, isLoading, navigate, from]);\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setError('');\n\n    console.log('Form submitted with:', formData);\n\n    const result = await adminLogin(formData.username, formData.password, formData.rememberMe);\n\n    console.log('Login result:', result);\n\n    if (result.success) {\n      console.log('Login successful, navigating to:', from);\n      navigate(from, { replace: true });\n    } else {\n      console.log('Login failed:', result.error);\n      setError(result.error);\n    }\n\n    setIsSubmitting(false);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${\n      getThemeClasses(\n        'bg-gradient-to-br from-light-orange-50 to-white',\n        'bg-gradient-to-br from-slate-900 to-slate-800'\n      )\n    }`}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n        className=\"max-w-md w-full space-y-8\"\n      >\n        {/* Header */}\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n          >\n            <ShieldCheckIcon className=\"h-8 w-8 text-white\" />\n          </motion.div>\n          <h2 className={`mt-6 text-3xl font-bold ${\n            getThemeClasses('text-gray-900', 'text-white')\n          }`}>\n            Admin Portal\n          </h2>\n          <p className={`mt-2 text-sm ${\n            getThemeClasses('text-gray-600', 'text-gray-400')\n          }`}>\n            Sign in to access the admin dashboard\n          </p>\n        </div>\n\n        {/* Login Form */}\n        <motion.form\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n          className={`mt-8 space-y-6 p-8 rounded-xl shadow-xl ${\n            getThemeClasses(\n              'bg-white border border-gray-200',\n              'bg-slate-800 border border-slate-700'\n            )\n          }`}\n          onSubmit={handleSubmit}\n        >\n          {error && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center space-x-3\"\n            >\n              <ExclamationTriangleIcon className=\"h-5 w-5 text-red-500\" />\n              <span className=\"text-sm text-red-700 dark:text-red-400\">{error}</span>\n            </motion.div>\n          )}\n\n          <div className=\"space-y-4\">\n            {/* Username/Email Field */}\n            <div>\n              <label htmlFor=\"username\" className={`block text-sm font-medium ${\n                getThemeClasses('text-gray-700', 'text-gray-300')\n              }`}>\n                Username or Email\n              </label>\n              <input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                required\n                value={formData.username}\n                onChange={handleInputChange}\n                className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 transition-colors ${\n                  getThemeClasses(\n                    'border-gray-300 bg-white text-gray-900 placeholder-gray-500',\n                    'border-slate-600 bg-slate-700 text-white placeholder-gray-400'\n                  )\n                }`}\n                placeholder=\"Enter your username or email\"\n              />\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label htmlFor=\"password\" className={`block text-sm font-medium ${\n                getThemeClasses('text-gray-700', 'text-gray-300')\n              }`}>\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className={`block w-full px-3 py-2 pr-10 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-light-orange-500 focus:border-light-orange-500 transition-colors ${\n                    getThemeClasses(\n                      'border-gray-300 bg-white text-gray-900 placeholder-gray-500',\n                      'border-slate-600 bg-slate-700 text-white placeholder-gray-400'\n                    )\n                  }`}\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className={`absolute inset-y-0 right-0 pr-3 flex items-center ${\n                    getThemeClasses('text-gray-400 hover:text-gray-600', 'text-gray-500 hover:text-gray-300')\n                  }`}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            {/* Remember Me */}\n            <div className=\"flex items-center\">\n              <input\n                id=\"rememberMe\"\n                name=\"rememberMe\"\n                type=\"checkbox\"\n                checked={formData.rememberMe}\n                onChange={handleInputChange}\n                className=\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"rememberMe\" className={`ml-2 block text-sm ${\n                getThemeClasses('text-gray-700', 'text-gray-300')\n              }`}>\n                Remember me\n              </label>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white font-semibold rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300\"\n          >\n            {isSubmitting ? (\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                <span>Signing in...</span>\n              </div>\n            ) : (\n              'Sign In'\n            )}\n          </button>\n\n          {/* Demo Credentials */}\n          <div className={`mt-6 p-4 rounded-lg border-2 border-dashed ${\n            getThemeClasses(\n              'border-gray-300 bg-gray-50',\n              'border-slate-600 bg-slate-700/50'\n            )\n          }`}>\n            <h4 className={`text-sm font-medium mb-2 ${\n              getThemeClasses('text-gray-700', 'text-gray-300')\n            }`}>\n              Demo Credentials:\n            </h4>\n            <div className={`text-xs space-y-1 ${\n              getThemeClasses('text-gray-600', 'text-gray-400')\n            }`}>\n              <div><strong>Super Admin:</strong> admin / admin123</div>\n              <div><strong>Manager:</strong> manager / manager123</div>\n            </div>\n          </div>\n        </motion.form>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default AdminLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,YAAY,EACZC,eAAe,EACfC,uBAAuB,QAClB,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAE2B,UAAU;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAC7D,MAAM;IAAEqB;EAAgB,CAAC,GAAGC,QAAQ,CAAC,CAAC;EACtC,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAE9B+B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEP,eAAe,EAAE,YAAY,EAAEC,SAAS,CAAC;EAE1F,MAAMO,IAAI,GAAG,EAAAtB,eAAA,GAAAmB,QAAQ,CAACI,KAAK,cAAAvB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBsB,IAAI,cAAArB,oBAAA,uBAApBA,oBAAA,CAAsBuB,QAAQ,KAAI,kBAAkB;EAEjErC,SAAS,CAAC,MAAM;IACd,IAAI2B,eAAe,IAAI,CAACC,SAAS,EAAE;MACjCG,QAAQ,CAACI,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACX,eAAe,EAAEC,SAAS,EAAEG,QAAQ,EAAEI,IAAI,CAAC,CAAC;EAEhD,MAAMI,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/C7B,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;IACH,IAAIpB,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMwB,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBvB,eAAe,CAAC,IAAI,CAAC;IACrBF,QAAQ,CAAC,EAAE,CAAC;IAEZU,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEnB,QAAQ,CAAC;IAE7C,MAAMkC,MAAM,GAAG,MAAMvB,UAAU,CAACX,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,EAAEH,QAAQ,CAACI,UAAU,CAAC;IAE1Fc,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,MAAM,CAAC;IAEpC,IAAIA,MAAM,CAACC,OAAO,EAAE;MAClBjB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEC,IAAI,CAAC;MACrDJ,QAAQ,CAACI,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC,CAAC,MAAM;MACLL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,MAAM,CAAC3B,KAAK,CAAC;MAC1CC,QAAQ,CAAC0B,MAAM,CAAC3B,KAAK,CAAC;IACxB;IAEAG,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,IAAIG,SAAS,EAAE;IACb,oBACElB,OAAA;MAAKyC,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D1C,OAAA;QAAKyC,SAAS,EAAC;MAAwE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAEV;EAEA,oBACE9C,OAAA;IAAKyC,SAAS,EAAE,4EACdtB,eAAe,CACb,iDAAiD,EACjD,+CACF,CAAC,EACA;IAAAuB,QAAA,eACD1C,OAAA,CAACP,MAAM,CAACsD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BZ,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAGrC1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1C,OAAA,CAACP,MAAM,CAACsD,GAAG;UACTC,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,OAAO,EAAE;YAAEG,KAAK,EAAE;UAAE,CAAE;UACtBF,UAAU,EAAE;YAAEG,KAAK,EAAE,GAAG;YAAEtB,IAAI,EAAE,QAAQ;YAAEuB,SAAS,EAAE;UAAI,CAAE;UAC3Df,SAAS,EAAC,sIAAsI;UAAAC,QAAA,eAEhJ1C,OAAA,CAACJ,eAAe;YAAC6C,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACb9C,OAAA;UAAIyC,SAAS,EAAE,2BACbtB,eAAe,CAAC,eAAe,EAAE,YAAY,CAAC,EAC7C;UAAAuB,QAAA,EAAC;QAEJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9C,OAAA;UAAGyC,SAAS,EAAE,gBACZtB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;UAAAuB,QAAA,EAAC;QAEJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN9C,OAAA,CAACP,MAAM,CAACgE,IAAI;QACVT,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC3Bd,SAAS,EAAE,2CACTtB,eAAe,CACb,iCAAiC,EACjC,sCACF,CAAC,EACA;QACHuC,QAAQ,EAAErB,YAAa;QAAAK,QAAA,GAEtB9B,KAAK,iBACJZ,OAAA,CAACP,MAAM,CAACsD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,KAAK,EAAE;UAAK,CAAE;UACrCH,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEK,KAAK,EAAE;UAAE,CAAE;UAClCb,SAAS,EAAC,mHAAmH;UAAAC,QAAA,gBAE7H1C,OAAA,CAACH,uBAAuB;YAAC4C,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5D9C,OAAA;YAAMyC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAE9B;UAAK;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CACb,eAED9C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAO2D,OAAO,EAAC,UAAU;cAAClB,SAAS,EAAE,6BACnCtB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;cAAAuB,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACE4D,EAAE,EAAC,UAAU;cACb7B,IAAI,EAAC,UAAU;cACfE,IAAI,EAAC,MAAM;cACX4B,QAAQ;cACR7B,KAAK,EAAE3B,QAAQ,CAACE,QAAS;cACzBuD,QAAQ,EAAEjC,iBAAkB;cAC5BY,SAAS,EAAE,uKACTtB,eAAe,CACb,6DAA6D,EAC7D,+DACF,CAAC,EACA;cACH4C,WAAW,EAAC;YAA8B;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAO2D,OAAO,EAAC,UAAU;cAAClB,SAAS,EAAE,6BACnCtB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;cAAAuB,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cAAKyC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1C,OAAA;gBACE4D,EAAE,EAAC,UAAU;gBACb7B,IAAI,EAAC,UAAU;gBACfE,IAAI,EAAEvB,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCmD,QAAQ;gBACR7B,KAAK,EAAE3B,QAAQ,CAACG,QAAS;gBACzBsD,QAAQ,EAAEjC,iBAAkB;gBAC5BY,SAAS,EAAE,wKACTtB,eAAe,CACb,6DAA6D,EAC7D,+DACF,CAAC,EACA;gBACH4C,WAAW,EAAC;cAAqB;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACF9C,OAAA;gBACEiC,IAAI,EAAC,QAAQ;gBACb+B,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9C+B,SAAS,EAAE,qDACTtB,eAAe,CAAC,mCAAmC,EAAE,mCAAmC,CAAC,EACxF;gBAAAuB,QAAA,EAEFhC,YAAY,gBACXV,OAAA,CAACL,YAAY;kBAAC8C,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEpC9C,OAAA,CAACN,OAAO;kBAAC+C,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC/B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9C,OAAA;YAAKyC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1C,OAAA;cACE4D,EAAE,EAAC,YAAY;cACf7B,IAAI,EAAC,YAAY;cACjBE,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE7B,QAAQ,CAACI,UAAW;cAC7BqD,QAAQ,EAAEjC,iBAAkB;cAC5BY,SAAS,EAAC;YAAmF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACF9C,OAAA;cAAO2D,OAAO,EAAC,YAAY;cAAClB,SAAS,EAAE,sBACrCtB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;cAAAuB,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9C,OAAA;UACEiC,IAAI,EAAC,QAAQ;UACbgC,QAAQ,EAAEnD,YAAa;UACvB2B,SAAS,EAAC,mUAAmU;UAAAC,QAAA,EAE5U5B,YAAY,gBACXd,OAAA;YAAKyC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzD1C,OAAA;cAAKyC,SAAS,EAAC;YAA2D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjF9C,OAAA;cAAA0C,QAAA,EAAM;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,GAEN;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT9C,OAAA;UAAKyC,SAAS,EAAE,8CACdtB,eAAe,CACb,4BAA4B,EAC5B,kCACF,CAAC,EACA;UAAAuB,QAAA,gBACD1C,OAAA;YAAIyC,SAAS,EAAE,4BACbtB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;YAAAuB,QAAA,EAAC;UAEJ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9C,OAAA;YAAKyC,SAAS,EAAE,qBACdtB,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,EAChD;YAAAuB,QAAA,gBACD1C,OAAA;cAAA0C,QAAA,gBAAK1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qBAAiB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzD9C,OAAA;cAAA0C,QAAA,gBAAK1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yBAAqB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC5C,EAAA,CApPID,cAAc;EAAA,QAUiCH,QAAQ,EAE1CP,WAAW,EACXC,WAAW;AAAA;AAAA0E,EAAA,GAbxBjE,cAAc;AAsPpB,eAAeA,cAAc;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}