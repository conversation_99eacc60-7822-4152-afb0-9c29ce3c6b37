{"ast": null, "code": "import { createContext as r, useContext as o, use<PERSON>emo as u } from \"react\";\nimport { useOnUnmount as i } from '../../hooks/use-on-unmount.js';\nimport { MenuMachine as c } from './menu-machine.js';\nconst a = r(null);\nfunction p(t) {\n  let n = o(a);\n  if (n === null) {\n    let e = new Error(`<${t} /> is missing a parent <Menu /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(e, s), e;\n  }\n  return n;\n}\nfunction s({\n  id: t,\n  __demoMode: n = !1\n}) {\n  let e = u(() => c.new({\n    id: t,\n    __demoMode: n\n  }), []);\n  return i(() => e.dispose()), e;\n}\nexport { a as MenuContext, s as useMenuMachine, p as useMenuMachineContext };", "map": {"version": 3, "names": ["createContext", "r", "useContext", "o", "useMemo", "u", "useOnUnmount", "i", "MenuMachine", "c", "a", "p", "t", "n", "e", "Error", "captureStackTrace", "s", "id", "__demoMode", "new", "dispose", "MenuContext", "useMenuMachine", "useMenuMachineContext"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@headlessui/react/dist/components/menu/menu-machine-glue.js"], "sourcesContent": ["import{createContext as r,useContext as o,use<PERSON>emo as u}from\"react\";import{useOnUnmount as i}from'../../hooks/use-on-unmount.js';import{MenuMachine as c}from'./menu-machine.js';const a=r(null);function p(t){let n=o(a);if(n===null){let e=new Error(`<${t} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,s),e}return n}function s({id:t,__demoMode:n=!1}){let e=u(()=>c.new({id:t,__demoMode:n}),[]);return i(()=>e.dispose()),e}export{a as MenuContext,s as useMenuMachine,p as useMenuMachineContext};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,mBAAmB;AAAC,MAAMC,CAAC,GAACT,CAAC,CAAC,IAAI,CAAC;AAAC,SAASU,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACV,CAAC,CAACO,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,IAAIH,CAAC,6CAA6C,CAAC;IAAC,MAAMG,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACG,CAAC,CAAC,EAACH,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAC;EAACC,EAAE,EAACN,CAAC;EAACO,UAAU,EAACN,CAAC,GAAC,CAAC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACT,CAAC,CAAC,MAAII,CAAC,CAACW,GAAG,CAAC;IAACF,EAAE,EAACN,CAAC;IAACO,UAAU,EAACN;EAAC,CAAC,CAAC,EAAC,EAAE,CAAC;EAAC,OAAON,CAAC,CAAC,MAAIO,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,EAACP,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIY,WAAW,EAACL,CAAC,IAAIM,cAAc,EAACZ,CAAC,IAAIa,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}